#include "EatStateAction.h"
#include "DefManagerProxy.h"
#include "PlayerAttrib.h"
#include "SoundComponent.h"
#include "WorldManager.h"
#include "SandboxIdDef.h"

EatStateAction::EatStateAction(ClientPlayer* pPlayer)
: ActionBase(pPlayer)
{
}

EatStateAction::~EatStateAction()
{

}

bool EatStateAction::eatFood(int status, int itemId, float score)
{
	const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(itemId);
	if (fooddef)
	{
		auto pWorld = mpCtrl->getWorld();
		if (status == PLAYEROP_STATUS_BEGIN)
		{
			mpCtrl->setOperate(PLAYEROP_EATFOOD, fooddef->UseTime, itemId);
			mpCtrl->useItemOnTrigger(itemId);
		}
		else
		{
			assert(status == PLAYEROP_STATUS_END || status == PLAYEROP_STATUS_CANCEL);

			if (status == PLAYEROP_STATUS_END)
			{
				mpCtrl->addOWScore(score);
				//LOG_INFO("ClientPlayer::eatFood(): END itemid = %d", itemid);
				if (pWorld && !pWorld->isRemoteMode())
				{
					mpCtrl->getPlayerAttrib()->eatFood(itemId);
					mpCtrl->addAchievement(3, ACHIEVEMENT_USEITEM, itemId, 1);
					mpCtrl->updateTaskSysProcess(TASKSYS_USE_ITEM, itemId);
					if (itemId == 12502)
					{
						mpCtrl->checkNewbieWorldProgress(19, "eatBread");
					}

					ObserverEvent_ActorItem obevent(mpCtrl->getUin(), itemId, 1);
					ObserverEventManager::getSingleton().OnTriggerEvent("Item.expend", &obevent);

					mpCtrl->breakHorseInvisible();	

				}

				if (g_WorldMgr && mpCtrl->hasUIControl())
				{
					g_WorldMgr->syncFestivalActivitiesEventToClient(mpCtrl, "eatfood", itemId);
				}
				//mpCtrl->playSound("misc.burp", 1.0f, 1.0f);
				// auto sound = mpCtrl->getSoundComponent();
				// if (sound)
				// {
				// 	if (itemId != ITEM_BANDAGE)
				// 	{
				// 		sound->playSound("misc.burp", 1, 1, 4);
				// 	}
					
				// }
			}

// #ifdef IWORLD_SERVER_BUILD
			mpCtrl->onOperateEnded();
// #endif
		}

		return true;

	}
	return false;
}

