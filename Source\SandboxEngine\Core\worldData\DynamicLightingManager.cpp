#include "DynamicLightingManager.h"
#include "world.h"
#include "IPlayerControl.h"
#include "chunk.h"
#include "Optick/optick.h"
#include "special_blockid.h"
#include "SandboxIdDef.h"

DynamicLightingManager::DynamicLightingManager(World* world)
    : m_World(world)
    , m_LastPlayerPos(0, -1, 0)
    , m_CurrentTick(0)
    , m_LastLocalHeldLightPos(0, -1, 0)
    , m_LastLocalHeldItemID(0)
    , m_HasLocalHeldLight(false)
    , m_ForceUpdate(false)
{
}

DynamicLightingManager::~DynamicLightingManager()
{
    m_LightSources.clear();
}

void DynamicLightingManager::tick()
{
    OPTICK_EVENT();
    
    m_CurrentTick++;
    
    // 获取当前玩家位置
    IPlayerControl* player = GetIPlayerControl();
    WCoord currentPlayerPos(0, -1, 0);
    if (player)
    {
        currentPlayerPos = CoordDivBlock(player->GetPlayerControlPosition());
    }
    
    // 先更新本地玩家手持物品光照（使用实时位置）
    updateLocalHeldItemLighting();
    
    // 处理其他客户端的手持光源
    for (auto& pair : m_OtherClientHeldLights)
    {
        processOtherClientHeldLight(pair.first, pair.second);
    }
    
    // 检查玩家位置是否需要重新计算优先级（移动超过4个方块）
    if (player && currentPlayerPos != m_LastPlayerPos)
    {
        onPlayerMove(currentPlayerPos);
    }
    
    // 处理更新队列
    processUpdateQueue();
}

void DynamicLightingManager::onPlayerMove(const WCoord& newPos)
{
    OPTICK_EVENT();
    
    WCoord deltaPos = newPos - m_LastPlayerPos;
    float moveDistance = sqrt(deltaPos.x * deltaPos.x + deltaPos.z * deltaPos.z);
    
    // 只有移动距离超过一定阈值才更新优先级
    if (moveDistance > 4.0f || m_LastPlayerPos.y == -1) // 首次设置或移动超过4个方块
    {
        updatePriorities(newPos);
        m_LastPlayerPos = newPos;
    }
}

void DynamicLightingManager::updatePriorities(const WCoord& playerPos)
{
    OPTICK_EVENT();
    
    // 清空更新队列
    for (int i = 0; i < 4; i++)
    {
        m_UpdateQueue[i].clear();
    }
    
    // 重新计算所有光源的优先级
    for (auto& pair : m_LightSources)
    {
        LightSource& light = pair.second;
        LightingPriority oldPriority = light.priority;
        light.priority = calculatePriority(light.position, playerPos);
        
        // 如果优先级提升，标记为需要更新
        if (light.priority < oldPriority)
        {
            light.isDirty = true;
        }
        
        // 添加到对应的更新队列
        if (light.isDirty && shouldUpdate(light))
        {
            m_UpdateQueue[(int)light.priority].push_back(light.position);
        }
    }
}

DynamicLightingManager::LightingPriority DynamicLightingManager::calculatePriority(
    const WCoord& lightPos, const WCoord& playerPos)
{
    WCoord delta = lightPos - playerPos;
    float distance = sqrt(delta.x * delta.x + delta.z * delta.z);
    
    // 基于距离的分级
    if (distance <= 32.0f)      // 2 chunk 范围
        return LightingPriority::HIGH;
    else if (distance <= 64.0f) // 4 chunk 范围
        return LightingPriority::MEDIUM;
    else if (distance <= 128.0f) // 8 chunk 范围
        return LightingPriority::LOW;
    else
        return LightingPriority::FROZEN;
}

void DynamicLightingManager::processUpdateQueue()
{
    OPTICK_EVENT();
    
    // 按优先级处理更新队列
    for (int priority = 0; priority < 4; priority++)
    {
        int maxUpdates = m_MaxUpdatesPerFrame[priority];
        int processed = 0;
        
        auto& queue = m_UpdateQueue[priority];
        for (auto it = queue.begin(); it != queue.end() && processed < maxUpdates; )
        {
            const WCoord& pos = *it;
            auto lightIt = m_LightSources.find(pos);
            
            if (lightIt != m_LightSources.end())
            {
                LightSource& light = lightIt->second;
                
                // 检查冷却时间
                if (shouldUpdate(light))
                {
                    updateLightSource(pos);
                    light.lastUpdateTick = m_CurrentTick;
                    light.isDirty = false;
                    processed++;
                }
                it = queue.erase(it);
            }
            else
            {
                it = queue.erase(it);
            }
        }
    }
}

bool DynamicLightingManager::shouldUpdate(const LightSource& light) const
{
    int priority = (int)light.priority;
    uint32_t cooldown = m_UpdateCooldown[priority];
    
    return (m_CurrentTick - light.lastUpdateTick) >= cooldown;
}

void DynamicLightingManager::updateLightSource(const WCoord& pos)
{
    if (!m_World)
        return;
    
    // 获取光源信息
    auto it = m_LightSources.find(pos);
    if (it != m_LightSources.end())
    {
        const LightSource& light = it->second;
        // 设置光源值
        m_World->setBlockLightEx(pos, light.lightLevel, false);
    }
    
    // 更新光照
    m_World->blockLightingChange(1, pos);
}

void DynamicLightingManager::addLightSource(const WCoord& pos, int lightLevel, int blockType)
{
    LightSource light;
    light.position = pos;
    light.lightLevel = lightLevel;
    light.blockType = blockType;
    light.isDirty = true;
    light.lastUpdateTick = 0;
    
    // 计算初始优先级
    IPlayerControl* player = GetIPlayerControl();
    if (player)
    {
        WCoord playerPos = CoordDivBlock(player->GetPlayerControlPosition());
        light.priority = calculatePriority(pos, playerPos);
    }
    else
    {
        light.priority = LightingPriority::MEDIUM;
    }
    
    m_LightSources[pos] = light;
    
    // 立即设置光源值并更新光照
    if (m_World)
    {
        m_World->setBlockLightEx(pos, lightLevel, false);
        m_World->blockLightingChange(1, pos);
    }
    
    // 添加到更新队列
    if (shouldUpdate(light))
    {
        m_UpdateQueue[(int)light.priority].push_back(pos);
    }
}

void DynamicLightingManager::removeLightSource(const WCoord& pos)
{
    auto it = m_LightSources.find(pos);
    if (it != m_LightSources.end())
    {
        // 清除光照值
        if (m_World)
        {
            m_World->setBlockLightEx(pos, 0, false);
            m_World->blockLightingChange(1, pos);
        }
        
        m_LightSources.erase(it);
        
        // 从更新队列中移除
        for (int i = 0; i < 4; i++)
        {
            auto& queue = m_UpdateQueue[i];
            queue.erase(std::remove(queue.begin(), queue.end(), pos), queue.end());
        }
    }
}

void DynamicLightingManager::markLightSourceDirty(const WCoord& pos)
{
    auto it = m_LightSources.find(pos);
    if (it != m_LightSources.end())
    {
        LightSource& light = it->second;
        light.isDirty = true;
        
        // 添加到对应的更新队列
        if (shouldUpdate(light))
        {
            m_UpdateQueue[(int)light.priority].push_back(pos);
        }
    }
}

void DynamicLightingManager::setForceUpdate(bool b)
{
    m_ForceUpdate = b;
}

void DynamicLightingManager::updateLocalHeldItemLighting()
{
    OPTICK_EVENT();
    
    if (!m_World)
        return;
        
    IPlayerControl* player = GetIPlayerControl();
    if (!player)
        return;
        
    // 获取玩家当前手持物品和位置
    int currentItemID = player->GetPlayerControlCurToolID();
    WCoord currentPlayerPos = CoordDivBlock(player->GetPlayerControlPosition());
    
    // 检查是否为火把类物品
    bool isHoldingTorch = (currentItemID == BLOCK_TORCH || 
                          currentItemID == BLOCK_SMALL_TORCH || 
                          currentItemID == ITEM_SOCTORCH);
    
    // 情况1：之前有光源，现在不需要光源
    if (m_HasLocalHeldLight && !isHoldingTorch)
    {
        removeLightSource(m_LastLocalHeldLightPos);
        m_HasLocalHeldLight = false;
        m_LastLocalHeldItemID = currentItemID;
        return;
    }
    
    // 情况2：之前没有光源，现在需要光源
    if (!m_HasLocalHeldLight && isHoldingTorch)
    {
        // 获取火把的光照等级
        int lightLevel = 14; // 火把默认光照等级
        if (currentItemID == BLOCK_SMALL_TORCH)
        {
            lightLevel = 10; // 小火把光照等级较低
        }
        
        addLightSource(currentPlayerPos, lightLevel, currentItemID);
        m_HasLocalHeldLight = true;
        m_LastLocalHeldLightPos = currentPlayerPos;
        m_LastLocalHeldItemID = currentItemID;
        return;
    }
    
    // 情况3：之前有光源，现在也需要光源
    if (m_HasLocalHeldLight && isHoldingTorch)
    {
        // 检查是否需要更新位置或物品类型
        bool needUpdate = (currentPlayerPos != m_LastLocalHeldLightPos) || 
                         (currentItemID != m_LastLocalHeldItemID);
        if (!needUpdate) {
            if (m_ForceUpdate)
                needUpdate = true;
        }
        else {
            if (m_ForceUpdate)
                m_ForceUpdate = false;
        }
        
        if (needUpdate)
        {
            // 移除旧光源
            removeLightSource(m_LastLocalHeldLightPos);
            
            // 获取新的光照等级
            int lightLevel = 14;
            if (currentItemID == BLOCK_SMALL_TORCH)
            {
                lightLevel = 10;
            }
            
            // 添加新光源
            addLightSource(currentPlayerPos, lightLevel, currentItemID);
            
            m_LastLocalHeldLightPos = currentPlayerPos;
            m_LastLocalHeldItemID = currentItemID;
        }
    }
}



void DynamicLightingManager::updateOtherClientHeldLight(int clientId, const WCoord& position, int itemId)
{
    auto it = m_OtherClientHeldLights.find(clientId);
    
    if (it == m_OtherClientHeldLights.end())
    {
        // 新客户端
        if (isLightEmittingItem(itemId))
        {
            HeldLightInfo heldLight;
            heldLight.position = position;
            heldLight.itemId = itemId;
            heldLight.lightLevel = getItemLightLevel(itemId);
            heldLight.lastPosition = position;
            
            m_OtherClientHeldLights[clientId] = heldLight;
            
            // 立即添加光源
            addLightSource(position, heldLight.lightLevel, itemId);
        }
    }
    else
    {
        HeldLightInfo& heldLight = it->second;
        
        // 检查物品是否改变
        if (heldLight.itemId != itemId)
        {
            // 移除旧光源
            removeLightSource(heldLight.position);
            
            if (isLightEmittingItem(itemId))
            {
                // 更新光源信息
                heldLight.itemId = itemId;
                heldLight.lightLevel = getItemLightLevel(itemId);
                heldLight.position = position;
                heldLight.lastPosition = position;
                
                // 添加新光源
                addLightSource(position, heldLight.lightLevel, itemId);
            }
            else
            {
                // 新物品不发光，移除记录
                m_OtherClientHeldLights.erase(it);
            }
        }
        else if (isLightEmittingItem(itemId))
        {
            // 物品相同且发光，更新位置
            heldLight.position = position;
        }
        else
        {
            // 物品相同但不发光，移除记录
            removeLightSource(heldLight.position);
            m_OtherClientHeldLights.erase(it);
        }
    }
}

void DynamicLightingManager::removeOtherClientHeldLight(int clientId)
{
    auto it = m_OtherClientHeldLights.find(clientId);
    if (it != m_OtherClientHeldLights.end())
    {
        // 移除光源
        removeLightSource(it->second.position);
        
        // 移除记录
        m_OtherClientHeldLights.erase(it);
    }
}

void DynamicLightingManager::clearAllOtherClientHeldLights()
{
    for (const auto& pair : m_OtherClientHeldLights)
    {
        removeLightSource(pair.second.position);
    }
    m_OtherClientHeldLights.clear();
}

void DynamicLightingManager::processOtherClientHeldLight(int clientId, HeldLightInfo& heldLight)
{
    // 检查位置是否发生变化
    if (heldLight.position != heldLight.lastPosition)
    {
        // 移除旧位置的光源
        removeLightSource(heldLight.lastPosition);
        
        // 在新位置添加光源
        addLightSource(heldLight.position, heldLight.lightLevel, heldLight.itemId);
        
        // 更新最后位置
        heldLight.lastPosition = heldLight.position;
    }
}

int DynamicLightingManager::getItemLightLevel(int itemId) const
{
    switch (itemId)
    {
        case BLOCK_TORCH:
        case ITEM_SOCTORCH:
            return 14;
        case BLOCK_SMALL_TORCH:
            return 10;
        default:
            return 0;
    }
}

bool DynamicLightingManager::isLightEmittingItem(int itemId) const
{
    return (itemId == BLOCK_TORCH || 
            itemId == BLOCK_SMALL_TORCH || 
            itemId == ITEM_SOCTORCH);
}