syntax = "proto2";
import "proto_common.proto";
package game.ch;

message P<PERSON>_<PERSON><PERSON>eatCH
{
	optional uint64 BeatCode = 1;
	optional uint64 server_time = 2;
	optional uint64 client_time = 3;
	optional bytes aceinfo = 4;
}

message PB_SyncChunkDataCH
{
	optional game.common.PB_Vector3 SectionCoord = 1;
	optional string trunkmd5 = 2;
}

message PB_RoleEnterWorldCH
{
	optional int32 Uin = 1;
	optional int32 GeniusLv = 2;
	optional int32 UICtrlMode = 3;
	optional game.common.PB_RoleInfo RoleInfo = 4;
	optional game.common.PB_PlayerVipInfo VipInfo = 5;
	optional int32 lang = 6;
	optional int32 apiid = 7;
	optional int32 reserved = 8;
	optional string  Auth = 9;
	//云服埋点需要 增加
	optional string country = 10;	
	optional int32 cltversion = 11;
	optional string session_id = 12;
	optional string game_session_id = 13;
	optional int32 specify_team = 14;
	optional game.common.PB_SOCTEAMDATA SocTeamData = 15;
}

message PB_Role<PERSON>eaveWorldCH
{
	optional int32 Uin = 1;
}

message <PERSON><PERSON>_<PERSON><PERSON><PERSON>CH
{
	optional game.common.PB_MoveMotion MoveMotion = 1;
	optional game.common.PB_Vector3 AddMotion = 2;
	optional int32 rentToken = 3;
	optional float speed = 4;
	optional game.common.PB_Vector3 VehiclePos = 5;
}

message PB_TrainMoveCH
{
	optional uint64 ObjID = 1;
	optional uint32 MapID = 2;
	optional int32 CarReverse = 3;
	optional int32 OutIndex = 4;
	optional float CurveT = 5;
	optional float MotionX = 6;
	optional float MotionY = 7;
	optional float MotionZ = 8;
	optional game.common.PB_Vector3 RailKnot = 9;
}

message PB_ActorTeleportCH
{
	optional int64 ObjID = 1;
	optional int32 TargetMap = 2;   
	optional game.common.PB_Vector3 TargetPos = 3;
}

message PB_GunInfoCH
{
	optional float CurSpread = 1;
	optional float CurJaw = 2;
	optional float CurPitch = 3;
	optional game.common.PB_Vector3 CurPos = 4;
}

message PB_SetInfoCH
{
	optional int32 Color = 1;
}

message PB_ItemGridUserData
{
	optional int32 Uin = 1;
	optional int32 GridIndex = 2;
	optional string UserDataStr = 3;
	optional int32 Type = 4;
}

message PB_BlockInteractCH
{
	optional int32 face = 1;
	optional int32 colptx = 2;
	optional int32 colpty = 3;
	optional int32 colptz = 4;
	optional game.common.PB_Vector3 blockpos = 5;
	optional int32 BlueprintId = 6;
	optional int32 BlueprintPlaceDirIdx = 7;
}

message PB_BlockPunchCH
{
	optional int32 status = 1;
	optional int32 face = 2;
	optional int32 digmethod = 3;
	optional game.common.PB_Vector3 blockpos = 4;
	optional uint64 vehicleObjID = 5 [default=0];
	optional uint32 clienttick = 6;
}

message PB_BlockAttackCH
{
    optional game.common.PB_Vector3 blockpos = 1;
    optional int32 dgmethod = 2;
	optional uint32 clienttick = 3;
}

message PB_ItemUseCH
{
	optional int32 itemid = 1;
	optional int32 status = 2;
	optional int32 shift = 3;
	optional float CurSpread = 4;
	optional float CurYaw = 5;
	optional float CurPitch = 6;
	optional game.common.PB_Vector3 CurPos = 7;
    optional uint32 usetick = 8; // 使用时的tick
	optional int32 itemindex = 9; // 道具索引，用于验证
	optional uint32 fireInterval = 10; // 用于同步射击间隔
}

message PB_SpecialSkillCH
{
	optional int32 status = 1;
}

message PB_SetHookCH
{
	optional uint64 ObjID = 1;
	optional uint64 hookID = 2;
}

message PB_ItemSkillUseCH
{
	optional int32 itemid = 1;
	optional int32 status = 2;
	optional int32 skillid = 3; 
	optional float CurSpread = 4;
	optional game.common.PB_Vector3 CurPos = 5;
	optional float CurDirX = 6; 
	optional float CurDirY = 7; 
	optional float CurDirZ = 8; 
	optional string ClientParam = 9;
	optional int32 itemindex = 10;  // 用于验证道具索引
}

message PB_ActorInteractCH
{
	optional int32 itype = 1;
	optional uint64 target = 2;
	optional int32 iplot = 3;
	optional float CurYaw = 4;
	optional float CurPitch = 5;
	optional game.common.PB_Vector3 CurPos = 6;
	optional game.common.PB_Vector3 CollidePos = 7;
}

// 鼠标的按键事件类型
enum PCMouseKeyType {
	left = 0;		// 左键
	right = 1;		// 右键
}

// 鼠标按下台起类型
enum PCMouseEventType {
	down = 0;		// 按下
	up = 1;			// 抬起
	click = 2;		// 点击
}

message PB_PCMouseEventCH
{
	optional uint64 target = 1;
	optional PCMouseKeyType keyType = 2;
	optional PCMouseEventType eventType = 3;	
}

message PB_RClickUpInteractCH
{
	optional uint64 target = 1;
}

message PB_ActorAnimCH
{
	optional int32 anim = 1;
	optional int32 anim1 = 2;
	optional int32 actid = 3;
	optional int32 actidTrigger = 4;
	optional bool sideAct = 5; //是否装扮互动副动作
	optional int32 animSeq = 6; // 序列号，标记是否是同一个动作
	optional int32 isLoop = 7; // 是否重复播放
	optional int32 animweapon = 8;
	optional bool send_immediate = 9;  // 立刻广播给他人
}

message PB_BackPackGridSwapCH
{
	optional int32 FromGridId = 1;
	optional int32 ToGridId = 2;
}

message PB_BackPackMoveItemCH
{
	optional int32 FromIndex = 1;
	optional int32 ToIndex = 2;
	optional int32 Num = 3;
}

message PB_BackPackGridDiscardCH
{
	optional int32 GridId = 1;
	optional int32 Num = 2;
}

message PB_BackPackEquipWeaponCH
{
	optional int32 GridId = 1;
}

message PB_EquipWeaponCH
{
	required int32 itemId = 1;
	optional int32 uin = 2;
}

message PB_NeedContainerPasswordCH
{
	optional game.common.PB_Vector3 Pos = 1;
	optional int32 Password = 2;
	optional uint64 VehicleObjID = 3;
}

message PB_CloseContainerCH
{
	optional int32 BaseIndex = 1;
}

message PB_SetContainerTextCH
{
	optional int32 BaseIndex = 1;
	optional string Text = 2;
}

message PB_BackPackStoreCH
{
	optional int32 FromIndex = 1;
	optional int32 Num = 2;
}

message PB_BackPackLootCH
{
	optional int32 FromIndex = 1;
	optional int32 Num = 2;
}

message PB_BackPackSortCH
{
	optional int32 BaseIndex = 1;
}

message PB_BackPackSetItemCH
{
	optional int32 ItemId = 1;
	optional int32 ToIndex = 2;
	optional int32 Num = 3;
}

message PB_BackPackSetItemWithoutLimitCH
{
	optional int32 ItemId = 1;
	optional int32 ToIndex = 2;
	optional int32 Num = 3;
	optional string Userdata_Str = 4;
}

message PB_StorageBoxSortCH
{
	optional int32 BaseIndex = 1;
}

message PB_CraftItemCH
{
	optional int32 CraftId = 1;
	optional int32 Num = 2;
}

message PB_EnchantItemCH
{
	optional int32 GridIndex = 1;
	optional int32 FrmGridIndex = 2;
	repeated int32 EnchantIds = 3;
}

message PB_EnchantItemRandomCH
{
	optional int32 GridIndex = 1;
}

message PB_RuneOperateCH
{
	optional int32 OpType = 1;
	optional int32 Index1 = 2;
	optional int32 Index2 = 3;
	optional int32 Index3 = 4;
	optional int32 Index4 = 5;
}

message PB_RepairItemCH
{
	optional int32 GridIndex = 1;
	optional int32 MaterialID = 2;
	optional int32 UseNum = 3;
}

message PB_GunDoReloadCH
{
	optional int32 BulletID = 1;
	optional int32 Num = 2;
	optional uint32 usetick = 3; // 使用时的tick
	optional bool isCustomGun = 4;//是否是新的枪
	optional bool noCheck = 5;//不需要检查，技能触发不扣个数，不检查数量
	optional int32 curShortcut = 6; //当前手持的快捷栏位置（校验用）
}

message PB_AccountHorseCH
{
	optional int32 HorseID = 1;
	optional int32 CmdType = 2;
	optional int32 CmdData = 3;
}

message PB_ActorReviveCH
{
	optional uint64 ObjID = 1;
	optional int32 Type = 2;
}

message PB_JruisdicTionCH
{
	optional int32 Uin = 1;
}

message PB_ChatCH
{
	optional int32 ChatType = 1;
	optional int32 TargetUin = 2;
	optional string Content = 3;
	optional int32 Language = 4;
	optional string Extend = 5;
	optional string Translate = 6; // 主机翻译信息	
	optional string wwtk1 = 7; //
	optional string wwtk2 = 8; //
	optional string wwParam = 9; // 给主机代发wordwall用
}

message PB_ActorInviteCH
{
	optional int32 InviteType = 1;
	optional int32 TargetUin = 2;
	optional int32 ActID = 3;
	optional int32 inviterPosX = 4;
	optional int32 inviterPosZ = 5;
}

message PB_PlayerMountActorCH
{
	optional uint64 ActorID = 1;
	optional bool IsShapeShift = 2;
	optional int32 InteractBlockId = 3;
}

message PB_PlayerMoveInputCH
{
	optional float MoveForward = 1;
	optional float MoveStrafing = 2;
	optional int32 Jumping = 3;
	optional int32 Sneaking = 4;
}

message PB_PlayerSleepCH
{
	optional int32 Flags = 1;
}

message PB_NpcTradeCH
{
	optional int32 OpType = 1;
	optional int32 Index = 2;
	optional int32 WatchAD = 3;
	optional int32 RewardNum = 4;
}


message PB_YMVoiceCH
{
	optional int32 YMMemberID = 1;
	optional int32 YMSpeakerSwitch = 2;
	optional int32 YMMicSwitch = 3;
	optional int32 YMMemberRole = 4;
}

message PB_GVChangeRoleCH
{
	optional int32 ChangeResult = 1;
}

message PB_YMChangeRoleCH
{
	optional int32 ChangeResult = 1;
}

message PB_GetAccountItemsCH
{
	optional int32 ItemId = 1;
	optional int32 Num = 2;
}

message PB_SpecialItemUseCH
{
	optional sint32 GridIndex = 1;
	optional int32 ItemId = 2;
	optional int32 ItemNum = 3;
}

message PB_SetSpectatorModeCH
{
	optional int32 Uin = 1;
	optional int32 SpectatorMode = 2;
}

message PB_SetSpectatorTypeCH
{
	optional int32 Uin = 1;
	optional int32 SpectatorType = 2;
}

message PB_SetSpectatorPlayerCH
{
    optional int32 SpectatorUin = 1;
    optional int32 ToSpectatorUin = 2;
}

message PB_SetPlayerModelAniCH
{
    optional int32 SpectatorUin = 1;
	optional int32 ToSpectatorUin = 2;
	optional int32 ModelAnimalType = 3; 
    optional int32 ModelAnimalExt = 4;   
}

message PB_SendMyViewmodeToSpectatorCH
{
    optional int32 SpectatorUin = 1;
	optional int32 ToSpectatorUin = 2;
	optional int32 MyViewmode = 3;  
}

message PB_SetBobbingToSpectatorCH
{
    optional int32 SpectatorUin = 1;
	optional int32 ToSpectatorUin = 2;
	optional int32 Bobbing = 3;  
}

message PB_BallOperateCH
{
	optional int32 Type = 1;
	optional uint64 ActorID = 2;
	optional int32 ExtendData = 3;
}

message PB_RocketTeleportCH
{
	optional int32 MapId = 1;
}

message PB_CloseDialogueCH
{
}

message PB_AnswerTaskCH
{
	optional int32 TaskID = 1;
	optional int32 PlotID = 2;
	optional int32 Type = 3;
}

message PB_CompleteTaskCH
{
	optional int32 TaskID = 1;
}

message PB_PlayActCH
{
	optional int32 ActID = 1;
	optional int32 ActIDTrigger = 2;
}

message PB_BluePrintPreBlockCH
{
	optional game.common.PB_Vector3 blockpos = 1;
}

message PB_GravityOperateCH
{
	optional int32 Type = 1;
	optional uint64 ActorID = 2;
	optional int32 ExtendData = 3;
}

message PB_MakeCustomModelCH
{
	optional game.common.PB_Vector3 Point = 1;
	optional string ModelName = 2;
	optional string ModelDesc = 3;
	optional int32 ModelType = 4;
}

message PB_SelectMobSpawnerCH
{
	optional game.common.PB_Vector3 Point = 1;
	optional int32 MobResID = 2;
	optional int32 SpawnCount = 3;
	optional int32 MaxNearbyMobs = 4;
	optional int32 SpawnWide = 5;
	optional int32 SpawnHigh = 6;
	optional int32 MinSpawnDelay = 7;
	optional int32 IsNumberDetection = 8;
	optional int32 IsSpawnDelay = 9;
}

message PB_GetNpcShopInfoCH
{
	optional int32 ShopID = 1;
}

message PB_BuyNpcShopItemCH
{
	optional int32 ShopID = 1;
	optional int32 SkuID = 2;
	optional int32 BuyCount = 3;
}

message PB_CloseEditActorModelCH
{
	repeated game.common.PB_ActorOneBoneModelData BoneModels = 1;
	optional int32 OperateType = 2;
	optional int32 ModelType = 3;
	optional string ModelName = 4;
	optional bool SkinDisplay = 5;
}

message PB_PackGiftNotifyItemChgCH
{
	optional int32 ShortCutIdx = 1;
	optional int32 CostItemInfo = 2;
	repeated int32 addlist = 3;
}
message PB_VehiclePreBlockCH
{
	optional game.common.PB_Vector3 blockpos = 1;
}

message PB_VehicleItemUseCH
{
	optional int32 ShortCutIdx = 1;
	optional int32 dir = 2;
	optional game.common.PB_Vector3 pos = 3;
}

message PB_VehicleStartBlockCH
{
	optional game.common.PB_Vector3 blockpos = 1;
}

message PB_VehicleAttribChangeCH
{
	optional uint64 ObjID = 1;
    optional int32 Fuel = 2;
	optional int32 PartIndex = 3;
	optional int32 EngineState = 4;
}

message PB_WorkshopItemInfoCH
{
	optional game.common.PB_Vector3 ContainerPos = 1;
}

message PB_PlayerVehicleMoveInputCH
{
	optional float Accel = 1;
	optional float Brake = 2;
	optional float Left  = 3;
	optional float Right = 4;
}

message PB_PlayerResetVehicleCH
{
	optional uint64 ActorID = 1;
}

message PB_PlayerMotionStateChangeCH
{
	optional int32 StateType = 1;
	optional bool StateSwitch = 2;
}

message PB_PlayerClickCH
{
	optional uint64 ObjID = 1;
	optional int32 ActorID = 2;
	optional int32 BlockID = 3;
	optional game.common.PB_Vector3 BlockPos = 4;
}

message PB_PlayerSelectShortcutCH
{
	required uint64 objid = 1;
	required uint32 index = 2;
}

message PB_TRIGGERPLAYERATTRICH
{
	repeated int32 ObActorAttrList = 1;
}

message PB_ReqDownLoadResUrlCH
{
	optional int32 Type = 1;
	optional string ExternData = 2;
}

message PB_CloseFullyCustomModelUICH
{
	optional int32 OperateType = 1;
	optional string Url = 2;
	optional string Skey = 3;
	optional int32 version = 4;
	optional string Name = 5;
	optional string Desc = 6;
}

message PB_PlayerNavFinishedCH
{
   optional int32 objid = 1;
}

message PB_VehicleAssembleLineCH
{
	optional uint64 ObjID = 1;
	optional int32 from = 2;
	optional int32 to = 3;
}

message PB_VehicleAssembleLineOperateCH
{
	optional uint64 ObjID = 1;
	optional int32 Blockid = 2;
	optional game.common.PB_Vector3 BlockPos = 3;
	optional bool IsClicked = 4;
	optional int32 Type = 5;
	optional int32 KeyId = 6;
}

message PB_UpdateActionerDataCH
{
	optional uint64 ObjID = 1;
	optional game.common.PB_Vector3 BlockPos = 2;
	optional string datastr = 3;
}

message PB_CSPlayerPermitCH
{
    optional int32  Uin = 1;
	optional int32  Bit = 2;
	optional bool   BitVal = 3;
	optional int32  BanItem = 4;
	optional bool   IsBan = 5;
}

message PB_VehicleWorkshopLineCH
{
	optional game.common.PB_Vector3 ContainerPos = 1;
	optional game.common.PB_Vector3 FromPos = 2;
	optional game.common.PB_Vector3 ToPos = 3;
}

message PB_CSChangePlayerTeamCH
{
    optional int32  Uin = 1;
	optional int32  TeamId = 2;
}


message PB_CSRentRoomAutoMuteCH
{
    optional uint32 SpamPreventionMinutes = 1;
}

message PB_VehicleAssembleLineUpdateCH
{
    optional game.common.PB_Vector3 BlockPos = 1;
}

message PB_MapEditHandleCH
{
    optional int32 EditMode = 1;
	optional int32 ShapeType = 2;
	optional int32 HandleType = 3;
	optional int32 ExtraParamLength = 4;
	optional int32 ExtraParamWidth = 5;
	optional int32 ExtraParamHeight = 6;
	optional int32 ExtraParamRadius = 7;
	optional bool  ExtraParamIsSolid = 8;
	optional int32 ExtraParamScale = 9;
	optional bool  ExtraParamIsContinue = 10;
	optional int32 ExtraParamDir = 11;
	optional int32 ExtraParamFillerId = 12;
	optional int32 ExtraParamReplaceId = 13;
	optional int32 ExtraParamDistance = 14;
	optional int32 ExtraParamOffsetX = 15;
	optional int32 ExtraParamOffsetY = 16;
	optional int32 ExtraParamOffsetZ = 17;
	optional int32 ExtraParamRotateAngle = 18;
	optional int32 ExtraParamTwoPointCPDir = 19;
	optional bool  ExtraParamIsOnlyFillAtmosphere = 20;
	optional game.common.PB_Vector3 CenterPos = 21;
	optional game.common.PB_Vector3 BeginPos = 22;
	optional game.common.PB_Vector3 EndPos = 23;
	optional int32 SaveCmd = 24;
}

message PB_MapEditRevokeCH
{
	optional bool  Revoke = 1;
}

message PB_CloudRoomOwnerStartGameCH
{
}

message PB_CSKickOffDataCH
{
    optional int32  Uin = 1;
    optional int32	KickerType = 2;
}

message PB_UsePackingFCMItemCH
{
    optional int32 ItemId = 1;
    optional game.common.PB_Vector3 UsePos = 2;
}

message PB_CreatePackingCMCH
{
    optional game.common.PB_Vector3 OffsetPos = 1;
	optional int32 RotateType = 2;
	optional string Name = 3;
	optional string Desc = 4;
	optional int32 CreateType = 5;
	optional game.common.PB_Vector3 StartPos = 6;
	optional game.common.PB_Vector3 EndPos = 7;
}

message PB_InputContentCH
{
	optional uint64 objId = 1;
	optional string content = 2;
}

message PB_InputKeyCH
{
	optional uint64 objId = 1;
	optional int32 keyType = 2;
	optional string eventType = 3;
}

message PB_SensorContainerDataCH
{
	optional game.common.PB_Vector3 BlockPos = 1;
	optional int32 SensorValue = 2;
	optional bool  IsBreverse = 3;
	optional uint64 ObjID = 4;
}

message PB_PlayerCarryActorCH
{
	optional uint64 ActorID = 1;
	optional game.common.PB_Vector3 Pos = 2;
}

message PB_VillagerModifyName
{
	optional uint64 ObjId = 1;
	optional string Name = 2;
}

message PB_PlayerGotoPosCH
{
	optional uint64 ObjId = 1;
	optional game.common.PB_Vector3 Pos = 2;
}

message PB_CustomModelPrepareCH
{
	optional int32 Index = 2;
	optional int32 HaveFile = 3;
}


message PB_MoveMobBackpackItemCH
{
	required int32 gridIndex = 1;
	required int32 moveType = 2;
}

message PB_InteractMobBackpackItemCH
{
	required int32 fromIndex = 1;
	required int32 toIndex = 2;
}

message PB_AltarLuckyDrawCH
{
	optional game.common.PB_Vector3 Pos = 1;
}

message PB_PlayerRestoreTransformSkinCH
{
}

message PB_PlayerDeformationSkinCH
{
	optional uint64 ActorID = 1;
	optional bool IsShapeShift = 2;
}

message PB_PlayerResetDeformationCH
{
	optional uint64 ActorID = 1;
	optional bool IsShapeShift = 2;
}

message PB_PlayerBaseAttrCH
{
	optional float CurHP = 1;
	optional float MaxHP = 2;
	optional float CurFoodLv = 3;
	optional float MaxFoodLv = 4;
	optional float BaseSpeed = 5;

	optional float AttackPunch = 6;
	optional float AttackRange = 7;
	optional float DefensePunch = 8;
	optional float DefenseRange = 9;

	optional int32 CurExp = 10;
	optional int32 CurSexp = 11;
	optional int32 CurLevel = 12;

	optional float OverflowHP = 13;

	optional float CurStrength = 14;
	optional float MaxStrength = 15;
	optional float OverflowStrength = 16;
}

message PB_PlayerArchEntityCH
{
	optional bytes UserData = 1;
}

message PB_PrayTreeTimeCH
{
	optional int32 uin = 1;
	optional int32 stage = 2;
}
message PB_SUMMONPETCH
{
	optional int32 monsterid = 1;
	optional string serverid = 2;
	optional int32 petid = 3;
	optional int32 stage = 4;
	optional int32 quality = 5;
    optional string petname = 6;
}

message PB_HomeLandRanchUpdateAnimalStateCH
{
	optional uint64 objid = 1;
	optional int32	enterstate = 2;
	optional string serverid = 3;
}

message PB_RequestModelName
{
	optional string modelID = 1;
}


message PB_VoiceInformCH
{
	required uint32 uin = 1;
	required uint32 type = 2;
	optional string voiceId = 3;
	required uint32 reportUin = 4;
	optional string node = 5;
	optional string dir = 6;
}

message PB_FurnaceTemperatureCH
{
	optional game.common.PB_Vector3 BlockPos = 1;
	required int32 lev = 2;
}

message PB_PlayerTakeContainerGridItemCH
{
	optional int32 x = 1; 
	optional int32 y = 2;
	optional int32 z = 3;
	optional int32 gridIndex = 4;
}

message PB_PlayerPotSetMakeCH
{
	optional uint64 objid = 1;
	optional int32 x = 2; 
	optional int32 y = 3;
	optional int32 z = 4;
	optional bool make = 5;
	optional int32 craftID = 6;
	optional int32 num = 7;
}

message PB_PlayerRevivePointCH
{
	optional int32 uin = 1;
	optional int32 mapid = 2;
	optional game.common.PB_Vector3 revivepoint = 3;
	optional game.common.PB_Vector3 spawnpoint = 4;
}

message PB_BlockExploitCH
{
	optional int32 status = 1;
	optional int32 face = 2;
	optional game.common.PB_Vector3 blockpos = 3;
	optional int32 picktype = 4;
}

message PB_PlayerTransferByStarStationCH
{
	required int32 uin = 1;
	required int32 srcStarStationID = 2;
	required game.common.PB_Vector3 cabinPos = 3;
	required int32 destStarStationID = 4;
	required int32 destMapID = 5;
}

message PB_ActivateStarStationCH
{
	required int32 mapID = 1;
	required game.common.PB_Vector3 consolePos = 2;
}

message PB_UpgradeStarStationCabinCH
{
	required int32 starStationID = 1;
	required game.common.PB_Vector3 cabinPos = 2;
}

message PB_BackPackRemoveItemItemCH
{
	optional int32 GridIndex = 1;
	optional int32 Num = 2;
}

message PB_StarStationTransferDeductFeeCH
{
	required int32 playerUin = 1;
	required int32 destStarStationId = 2;
	optional int32 srcStarStationId = 3;
	optional game.common.PB_Vector3 cabinPos = 4;
	optional int32 destMapId = 5;
	optional uint32 costStar = 6;
	optional uint32 transferType = 7;
}

message PB_GainItemsToBackPackCH
{
	required int32 playerUin = 1;
	required int32 itemId = 2;
	required int32 itemNum = 3;

}

message PB_GainItemsUserDatastrToBackPackCH
{
	required int32 playerUin = 1;
	required int32 itemId = 2;
	required int32 itemNum = 3;
	optional string userdata_str = 4;
}

message PB_UseMusicYuPuCH
{
	required int32 playerUin = 1;
	required int32 itemId = 2;
	required int32 itemindex = 3;
	required int32 itemNum = 4;
}

message PB_BuyAdShopGoods
{
	required int32 tabid = 1;
	required int32 goodid = 2;
	required int32 step = 3;
	optional bool success = 4;
}

message PB_ExchangeItemsToBackPackCH
{
	required int32 playerUin = 1;
	required int32 useItemId = 2;
	required int32 useItemNum = 3;
	required int32 gainItemId = 4;
	required int32 gainItemNum = 5;
	required int32 opertype = 6;
}

message PB_AchievementUpdateCH
{
	repeated game.common.PB_AchievementInfo achievementList = 1;
}

message PB_CoustomUIEvent
{
	required int32 opertype = 1;
	optional string uiid = 2;
	optional string elementid = 3;
	optional string content = 4;
}


message PB_AddExpCH
{
	required int32 op = 1;
	required int32 starNum = 2;
}

message PB_AddStarCH
{
	required int32 starNum = 1;
}
message PB_OneHomelandFooderRanchAnimalCH
{
	optional int32	SeedID = 1;
	optional int32	GrowthTime = 2;
	optional int32	FeedTime = 3;
	optional int32	Stage = 4;
	optional string	Serverid = 5;
	optional int32	SowTime = 6;
	optional int32	FooderLevel = 7;
	optional int32	FooderInterval = 8;
	optional int32	FooderID = 9;
	optional int32	FooderTime = 10;
	optional int32	FooderTimeStatr = 11;
	optional string	FooderDesc = 12;
}
message PB_UseHearthCH
{
	required int32 playerUin = 1;
	required game.common.PB_Vector3 hearthPos = 2;
	optional game.common.PB_Vector3 playerPos = 3;
	required bool isUse = 4; 
}

message PB_ExposePosChangeCH
{
	required bool isExpose = 1;
}


message PB_HomeLandMenuBuyCH
{
	
}

message PB_HomeLandSpecialFurnitureBuyCH
{
	
}

message PB_HomeLandShopCellCH
{
	optional int32  uin = 1;
	optional int32	itemid = 2;
	optional int32	num = 3;
}

message PB_AnswerLanternBird_CH
{
	optional int64  uin = 1;
	optional int32	answer = 2;
}

message PB_ChangeQQMusicPlayerCH
{
	required int32  type = 1;
	optional int32	musicId = 2;
	optional bool	state = 3;
	optional int32	volume = 4;
	optional int32	playMode = 5;
	optional bool	isOpen = 6;
}

message PB_PlayeCloseUICH
{
	required string uiName = 1;
	required string uiParam = 2;
}

message PB_PlaySkinActCH
{
	optional int32 ActID = 1;
	optional int32 ActIDTrigger = 2;
	optional int32 InviteUin = 3;  //邀请者UIN
	optional int32 AcceptUin = 4;  //接受者UIN
}

message PB_ActorStopSkinActCH
{
	optional int32 ActorID1 = 1;
	optional int32 ActorID2 = 2;
}

message PB_ChangeQQMusicClubCH
{
	required int32  type = 1;
	optional int32	fraction = 2;
	optional int32	uin = 3;
	optional string	name = 4;
	optional int32 actionId = 5;
	optional bool enterArea = 6;
	optional int32 mapId = 7;
	optional int32 itemId = 8;
	optional int32 itemNum = 9;
}

message PB_ActorStopAnimCH
{
	required int32 anim = 1;
	optional uint64 actorid = 2;
	optional bool isSeq = 3 [default = false];
}

message PB_MiniClubMusicPlayerCH
{
	required int32  type = 1;
	optional int32	musicId = 2;
	optional bool	state = 3;
	optional int32	volume = 4;
	optional int32	playMode = 5;
	optional bool	isOpen = 6;
}

message PB_SprayPaintInfoCH
{
	required int32  paintid = 1;
}

message PB_SyncClientActionLogCH
{
	required bool cheat = 1;    // 是否cheat 服务器回根据此字段踢人
	required string event = 2;
	required string detail = 3;
}

// 获取成就任务奖励
message PB_GetAchievementAwardCH
{
	required int32 taskId = 1;
}

// 上报一些特定信息，用于服务器逻辑校验
message PB_UploadCheckInfoCH
{
	required int32 info_type = 1;   // 消息类型 C++定义
	required bytes detail = 2;
}

// 请求领取心愿商人累计道具奖励
message PB_GetAdShopExtraAwardCH
{
	required int32 award_id = 1;
	required int32 item_id = 2;
	required int32 item_count = 3;
}

// 请求提取仓库道具
message PB_ExtractStoreItemCH
{
	required int32 store_index = 1;
	required int32 item_id = 2;
	required int32 item_count = 3;
}


message PB_PlayEffectCH
{
	optional int32 EffectType = 1;
	optional game.common.PB_EffectSoundNew SoundNew = 2;
	optional int32 effectScale = 3;
	optional game.common.PB_EffectStringActorBody StringActorBody = 4;
}

//野人伙伴听见乐器演奏触发跳舞
message PB_DanceByPlayingCH
{
	required uint64 mobUin = 1;
	required int32 playerUin = 2;
}

//乐器停止演奏触发野人伙伴停止跳舞
message PB_StopDanceByPlayingCH
{
	required uint64 mobUin = 1;
	required int32 playerUin = 2;
}

message PB_PvpActivityConfigCH
{
	optional uint32 activityId = 1;
	optional string reportAddr = 2;
	optional uint32 start_time = 3;
	optional uint32 end_time = 4;
	optional int32  mapId = 5;	
	optional string commParam = 6;
	optional int32 timeZone = 7;
	optional string extraRule = 8;
}

//触发player动作（可以通用）
message PB_StartActCH
{
	required int32 playerUin = 1;
	required int32 playingState = 2;
	required int32 actID = 3;
}

//停止player动作（可以通用）
message PB_StopActCH
{
	required int32 playerUin = 1;
	required int32 actID = 2;
}

message PB_TopBrandCH
{
	optional int32 targetUin = 1;
	optional string brandName = 2;
}

message PB_STARTFISHINGCH
{
	required game.common.PB_Vector3 TargetPos = 1;
}

message PB_ENDFISHINGCH
{
	
}

message PB_QUITFISHINGCH
{
	
}

message PB_EndPlayFishCH
{

}

message PB_UploadClientInfoCH
{
	required string info = 1;
}

enum CheatCheckType
{
	CCT_JumpHeight = 1;
	CCT_TackleRange = 2;  // 足球模式 滑铲
	CCT_GrabRange = 3;  // 篮球模式 抢断
	CCT_DribbleRange = 4;  // 篮球模式 运球突破
	CCT_Clip = 5;  // 穿墙
}
message PB_CheatCheatSubJump
{
	required float jumpHeight = 1;
	optional bool onground = 2;  // 是否在地上
	optional bool airjump = 3;  // 是否连跳
}
message PB_CheatCheatSubTackle
{
	required float range = 1;
}
message PB_CheatCheatSubGrab
{
	required float range = 1;
}
message PB_CheatCheatSubDribble
{
	required float range = 1;
}
message PB_CheatCheckSubClip
{
	optional float radius = 1;
	optional float half_height = 2;
	optional int32 bound_height = 3;
	optional int32 bound_size = 4;
}
message PB_HostCheckCheat
{
	required CheatCheckType checkType = 1;
	optional PB_CheatCheatSubJump jump = 2;
	optional PB_CheatCheatSubTackle tackle = 3;
	optional PB_CheatCheatSubGrab grab = 4;
	optional PB_CheatCheatSubDribble dribble = 5;
	optional PB_CheatCheckSubClip clip = 6;
}

message PB_BlockDataCH
{
	optional int32 x = 1;
	optional int32 y = 2;
	optional int32 z = 3;
	optional int32 mapid = 4;
	optional string text = 5;
}

message PB_PushSnowBallOperateCH
{
	optional int32 Type = 1;
	optional uint64 ActorID = 2;
	optional uint64 ExtendData = 3;
	required game.common.PB_Vector3 TargetPos = 4;
}

message PB_PlayWeaponEffectCH
{
	optional string EffectName = 1;
	optional int32 EffectID = 2;
	optional int32 EffectScale = 3;
	optional int32 EffectStatus = 4;
	optional uint64 ObjId = 5;
}

message PB_ActorPlayAnimCH
{
	required uint64 objId = 1;
	required int32 seqId = 2;
	optional int32 loop = 3;
	optional float speed = 4;
	optional int32 layer = 5;
	optional int32 preSeqId = 6;
	optional int32 preLayer = 7;
	optional bool triggerAttack = 8 [default = false];
}

message PB_ActorAttackCH
{
	required uint64 objId = 1;
	repeated uint64 targetIds = 2;
	required string attackDefName = 3;
	optional bool triggerAttackHit = 4 [default = false];
}

message PB_ActorDefanceStateCH
{
	required uint64 objId = 1;
	required bool defanceState = 2;
}

message PB_MoveTick
{
	optional uint32 opera = 1;
	optional sint32 yaw = 2;
	optional sint32 pitch = 3;
}

message PB_FlagOn
{
	required uint32 type = 1;
	required bool on = 2;
}
message PB_MoveSyncCH
{
	required uint32 id = 1;
	optional PB_MoveTick move_opera = 2;
	optional game.common.PB_Vector3 pos = 3;
	optional game.common.PB_Vector3 motion = 4;
	repeated PB_FlagOn flag_change = 5;
	optional uint64 tick = 6;
}

message PB_NewRepairItemCH
{
	optional int32 TgtGridIdx = 1;
	optional int32 RepairDur = 2;
	optional int32 mat1Id = 3;
	optional int32 mat2Id = 4;
	optional int32 RepairCount = 5;
	optional int32 StarCount = 6;
}

message PB_ActorPlaySoundCH
{
	required string name = 1;
	required float volume = 2;
	required float pitch = 3;
	required bool fixpitch = 4;
}

message PB_GunRayInfo
{
	required game.common.PB_Vector3f pos = 1;//射线发射位置
	required game.common.PB_Vector3f dir = 2;//射线方向
	optional int32 range = 3;
	optional game.common.PB_Vector3f muzzle = 4;//枪口在世界中的位置
}

message PB_ActorShootCH
{
	required uint32 gunId = 1;//枪的id
	optional uint32 bulletId = 2;//子弹的id
	repeated PB_GunRayInfo rayInfos = 3;//开一枪的所有弹片的射线信息
	optional bool isAim = 4;//是否是瞄准状态
	optional uint32 projectileId = 5;//投掷物的id
	optional uint32 useTick = 6;//开枪的usetick
}

message PB_PlayerTransferCH
{
	required int32 uin = 1;
	required game.common.PB_Vector3 targetpos = 2;
	required int32 destMapID = 3;
}

message PB_ResetPosResponeCH
{
	required uint64 tick = 1;
}

message PB_ActorFireworkCH
{
	required uint32 fireworkID = 1;//烟花的id
}


message PB_PlayerReviveRequestCH
{
    required uint64 player_id = 1;  // 救援者的ID
    required uint64 target_id = 2;  // 被救援者的ID
    required int32 action = 3;      // 1=开始救援, 2=取消救援, 3=完成救援
}

message PB_PlayerReviveProgressCH
{
    required uint64 player_id = 1;  // 救援者的ID
    required uint64 target_id = 2;  // 被救援者的ID
    required int32 progress_ticks = 3;  // 救援进度(以tick为单位)
}

// 客户端请求：添加任务
message PB_CraftingQueueAddTaskCH {
    required int32 crafting_id = 1;  // 合成配方ID
    required int32 count = 2;       // 合成物品数量
}

// 客户端请求：删除任务
message PB_CraftingQueueRemoveTaskCH {
    required int32 task_index = 1; // 要删除的任务在队列中的索引
}

// 客户端请求：交换任务
message PB_CraftingQueueSwapTaskCH {
    required int32 index1 = 1; // 第一个任务的索引
    required int32 index2 = 2; // 第二个任务的索引
}

message PB_PlayerCustomCH {
	required int32 type = 1;
	required string data = 2;
}

message PB_DecompositionCH {
	required int32 type = 1;

	required int32 x = 2;
	required int32 y = 3;
	required int32 z = 4;

	required int32 data = 5;
}

message PB_AllSingleBuildDataCH {
	required int32 uin = 1;
}

message PB_ResearchCH {
	optional int32 x = 1;
	optional int32 y = 2;
	optional int32 z = 3;
	optional int32 data = 4;
}

message PB_TechBlueprintCH {
	
}

message PB_DrinkWaterCH {
	required int32 x = 1;
	required int32 y = 2;
	required int32 z = 3;
	required int32 type = 4;
	required int32 itemid = 5;
}

message PB_FillWaterCH {
	required int32 x = 1;
	required int32 y = 2;
	required int32 z = 3;
	required int32 type = 4;
	required int32 grididx = 5;
}

message PB_ProcessBuildBlockCH {
	required int32 x = 1;
	required int32 y = 2;
	required int32 z = 3;
	required int32 type = 4;
	optional int32 level = 5;
}

message PB_SocTeamTagDataCH {
	required int32 type = 1;
	required int32 x = 2;
	required int32 z = 3;
	required int32 data = 4;
	optional string tagname = 5;
}

message PB_MachineSourceActiveCH {
	required int32 x = 1;
	required int32 y = 2;
	required int32 z = 3;
}

message PB_ClientCommonInfoCH {
	required game.common.PB_ClientCommonInfo info = 1;
}