--声明
local ObjInfoEditView = Class("ObjInfoEditView",ClassList["UIBaseView"])

--创建
function ObjInfoEditView:Create(param)
	return ClassList["ObjInfoEditView"].new(param)
end

--初始化
function ObjInfoEditView:Init()

end

--刷新面板
function ObjInfoEditView:UpdateLeftPanelInfo(info)
	if info then 
		if info.name then
			self.m_itemName:setText(info.name)
		end
		if info.objType == -1 and info.objId then
			local tex = MiniuiGetItemIconTexture(info.objId)
			self.m_itemIcon:setTexture(tex)
		end
		if info.objType > -1 and info.defId then
			local def = info.monsterdef
			if tonumber(def.ModelType) == 3 then
				--如果是Avatar定制模型，取图片的方式不一样
				local model = string.sub(def.Model,2,string.len(def.Model))
				local args = FrameStack.cur()
				if args.isMapMod then
					MiniuiAvatarSetIconByID(def, self.m_itemIcon)
				else
					MiniuiAvatarSetIconByIDEx(model, self.m_itemIcon)
				end
			elseif tonumber(def.ModelType) == MONSTER_CUSTOM_MODEL then
				local tex = MiniuiGetModelIconTexture(def.Model, ACTOR_MODEL) or 1
				self.m_itemIcon:setTexture(tex)
			elseif tonumber(def.ModelType) == MONSTER_FULLY_CUSTOM_MODEL then
				--完全自定义模型
				local tex = MiniuiGetModelIconTexture(def.Model, FULLY_ACTOR_MODEL) or 1
				self.m_itemIcon:setTexture(tex)
			elseif  tonumber(def.ModelType) == MONSTER_IMPORT_MODEL then -- 导入模型
				local tex = MiniuiGetModelIconTexture(def.Model, IMPORT_ACTOR_MODEL) or 1
				self.m_itemIcon:setTexture(tex)
			else
				MiniuiSetActorIcon(self.m_itemIcon, def.ID)
			end
		end
	end 
end

--刷新面板
function ObjInfoEditView:UpdateRightListInfo(info)
	self.m_dataList = {}
	if info then
		local content = ""
		local font_index = 1
		local type_index = 0
		if info.objType and info.objType >= 0 then
			type_index = 1
		end

		if info.objId then
			content = self.define.infoDefine[type_index].idPrefix .. info.objId
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
		end

		if info.defId then
			content = GetS(self.define.infoDefine[type_index].defIDPrefix)..": "..info.defId
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
		end

		if info.objType then
			local typeSuffix = ""
			typeSuffix = self.define.typeDefine[info.objType + 2]
			content = GetS(self.define.infoDefine[type_index].typePrefix)..": ".. tostring(info.objType)
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
		end

		if info.teamId then
			local teamSuffix = ""
			teamSuffix = self.define.teamDefine[info.teamId + 1]
			content = GetS(self.define.infoDefine[type_index].teamPrefix)..": ".. GetS(teamSuffix)
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
		end

		if info.hp then
			content = GetS(self.define.infoDefine[type_index].hpPrefix)..": ".. info.hp
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
		end

		if info.monsterdef and info.showattack then
			if info.showattack then
				local atkpoint = 0
				if info.monsterdef.AttackType >= 0 and info.monsterdef.AttackType <= 2 then
					atkpoint = info.monsterdef.Attacks[info.monsterdef.AttackType]
				end
				content = GetS(self.define.infoDefine[type_index].attack) ..": ".. atkpoint
				table.insert(self.m_dataList, content)
				font_index = font_index + 1
				if info.attacktype > -1 then
					content = GetS(self.define.infoDefine[type_index].attacktype) ..": ".. GetS(self.define.attackType[info.attacktype])
					table.insert(self.m_dataList, content)
					font_index = font_index + 1
					content = GetS(self.define.infoDefine[type_index].attackmode) ..": ".. GetS(self.define.attackMode[info.attackmode])
					table.insert(self.m_dataList, content)
					font_index = font_index + 1
				end
			end

			if info.monsterdef.DropItem[0] > 0 then
				content = GetS(self.define.infoDefine[type_index].isdrop) ..": ".. GetS(self.define.drop[1])
				table.insert(self.m_dataList, content)
			else
				content = GetS(self.define.infoDefine[type_index].isdrop) ..": ".. GetS(self.define.drop[0])
				table.insert(self.m_dataList, content)
			end
			font_index = font_index + 1
		end

		if info.blockDef then
			content = GetS(self.define.infoDefine[type_index].explodeAti)..": ".. info.blockDef.AntiExplode
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
			content = GetS(self.define.infoDefine[type_index].hardness)..": ".. info.blockDef.Hardness
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
			
			local slipperness = info.blockDef.Slipperiness
			--判断是否为整型，有小数的话保留一位小数
			if (slipperness % 1) ~= 0 then
				slipperness = string.format("%.1f",slipperness)
			end
			content = GetS(self.define.infoDefine[type_index].slipperiness)..": ".. slipperness
			table.insert(self.m_dataList, content)
			font_index = font_index + 1

			content = GetS(self.define.infoDefine[type_index].burnSpeed)..": ".. info.blockDef.BurnSpeed
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
			content = GetS(self.define.infoDefine[type_index].catchFire)..": ".. info.blockDef.CatchFire
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
			content = GetS(self.define.infoDefine[type_index].lightSrc)..": ".. info.blockDef.LightSrc
			table.insert(self.m_dataList, content)
			font_index = font_index + 1

			content = GetS(self.define.infoDefine[type_index].moveCollide)..": ".. GetS(self.define.moveCollide[info.blockDef.MoveCollide])
			table.insert(self.m_dataList, content)
			font_index = font_index + 1

			local breakid = info.blockDef.Breakable and self.define.breakable[1] or self.define.breakable[0]
			content = GetS(self.define.infoDefine[type_index].breakable)..": ".. GetS(breakid)
			table.insert(self.m_dataList, content)
			font_index = font_index + 1
		end

		self.m_rightList:setNumItems(font_index)
	end
end

--刷新按钮状态
function ObjInfoEditView:UpdateBtnState(info)
	if info then 
		local def = nil 
		if info.objType == -1 then 
			def = info.blockDef
		else
			def = info.monsterdef
		end

		if def and def.EditType and def.EditType ~= 0 then
			--可编辑
			self.m_propertyBtn:setTouchable(true)
			self.m_propertyBtn:setGrayed(false)

			self.m_addTriggerBtn:setTouchable(true)
			self.m_addTriggerBtn:setGrayed(false)

			self.m_addScriptBtn:setTouchable(true)
			self.m_addScriptBtn:setGrayed(false)
		else
			--不可编辑
			self.m_propertyBtn:setTouchable(false)
			self.m_propertyBtn:setGrayed(true)

			self.m_addTriggerBtn:setTouchable(false)
			self.m_addTriggerBtn:setGrayed(true)

			self.m_addScriptBtn:setTouchable(false)
			self.m_addScriptBtn:setGrayed(true)
		end 
	end 
end

function ObjInfoEditView:RightListItemRenderer(comp, idx, obj)
	local index = idx + 1
	if not self.m_dataList[index] then
		return
	end

	local btn = tolua.cast(obj, "miniui.GButton")
	if btn then
		btn:setTitle(self.m_dataList[index])
	end
end