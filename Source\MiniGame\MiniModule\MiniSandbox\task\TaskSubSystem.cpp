#include "TaskSubSystem.h"
#include "OgreUtils.h"
#include "DefManagerProxy.h"
#include "WorldManager.h"
#include "File/FileManager.h"
#include "WorldTask_generated.h"
#include "PlayerControl.h"
//#include "TaskNetSubSystem.h"
#include "chunkio.h"
#include "WorldManager.h"
#include "RoleWorldTask_generated.h"
#include "IWorldConfigProxy.h"
#include "SandBoxManager.h"
#ifdef IWORLD_SERVER_BUILD
#include "ZmqProxy.h"
#include "proto_gs2ds.pb.h"
#endif
//#include "GameNetManager.h"
#include "PermitsSubSystem.h"
#include "AchievementData.h"
#include "Plugin.h"
#include "ClientInfoProxy.h"
#include "GameEvent.h"
#include "backpack.h"
#include "ModPackMgr.h"
using namespace Rainbow;
using namespace MINIW;
using namespace MNSandbox;

static bool isTestDeveloper = false;//测试开发者
#define MAX_MAIN_TASK 1999999

TaskSubSystem::TaskSubSystem(PluginManager* p)
{
	m_TrackingDistance = 64;
	m_Dirty = false;
	m_pPluginManager = p;
	Clear();
}

TaskSubSystem::~TaskSubSystem()
{
	for (std::map<std::string, MNSandbox::Callback>::iterator it = m_EventCallBacks.begin(); it != m_EventCallBacks.end(); ++it)
	{
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe(it->first, it->second);
	}
}

bool TaskSubSystem::Awake()
{
	return true;
}

bool TaskSubSystem::Init()
{
	m_pPluginManager->GetScriptVM()->setUserTypePointer("TaskSubSystem", "TaskSubSystem", this);
	//联机
	//m_pTaskNetSubSystem = m_pPluginManager->FindSubsystem<TaskNetSubSystem>();
	CreateModuleEvent();
	GreatSubscribeEvent();
	return true;
}

bool TaskSubSystem::Execute(float dtime)
{
	if (!m_checkTaskDeque.empty())
	{
		auto& taskDeque = m_checkTaskDeque.front();
		if (CheckDeque(taskDeque.objid, taskDeque.type, taskDeque.target1, taskDeque.target2, taskDeque.goalnum))
		{
			m_IsCheckSave = true;
		}
		//最后处理
		if (m_IsCheckSave && m_checkTaskDeque.size() == 1)
		{
			m_Dirty = true;
			m_IsCheckSave = false;

			if (!HasDirtyTaskUin(taskDeque.objid))
				m_DirtyTaskUins.push_back(taskDeque.objid);

			if (m_mapPlayerObjectiveDirtyList.size() > 0 && !HasDirtyObjectiveTaskUin(taskDeque.objid))
			{
				m_DirtyObjectiveTaskUins.push_back(taskDeque.objid);
			}
		}
		m_checkTaskDeque.pop_front();
	}

	if (g_pPlayerCtrl && g_pPlayerCtrl->getWorld() && g_pPlayerCtrl->getWorld()->isRemoteMode())
	{
		Update2Server();
	}
	else
	{
		Update2Client();
	}
	return true;
}

bool TaskSubSystem::Shut()
{
	return true;
}

TaskSubSystem* TaskSubSystem::GetTaskSubSystem()
{
	static TaskSubSystem* s_TaskSubSystem = GET_SUB_SYSTEM(TaskSubSystem);
	if (s_TaskSubSystem)
	{
		return s_TaskSubSystem;
	}
	return nullptr;
}
bool TaskSubSystem::CreateModuleEvent()
{
	typedef ListenerFunctionRef<WCoord, long long> ListenerBlockFertilize;
	AutoRef<ListenerBlockFertilize> listenerBlockFertilize = SANDBOX_NEW(ListenerBlockFertilize, [&](WCoord pos, long long objid) -> void {
		IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(objid);
		if (iplayer && iplayer->GetPlayerWorld())
		{
			ClientPlayer* player = iplayer->GetPlayer();
			player->updateTaskSysProcess(TASKSYS_FERTI, player->getCurToolID());
		}
		});
	MNSandbox::GetGlobalEvent().Subscribe("BlockFertilize", listenerBlockFertilize);
	return true;
}

void TaskSubSystem::GreatSubscribeEvent() {

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_CheckCommonSyncTask");
	MNSandbox::Callback ccst = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_CheckCommonSyncTask", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {
		int type = (int)context.GetData_Number("type");
		WCoord* totempos = (WCoord*)context.GetData_Userdata("WCoord", "trackPos");
		int target1 = (int)context.GetData_Number("target1");
		int target2 = (int)context.GetData_Number("target2");
		int goalnum = (int)context.GetData_Number("goalnum");
		CheckCommonSyncTask(type, *totempos, target1, target2, goalnum);
		return MNSandbox::SandboxResult(nullptr, true);
		});

	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_CheckCommonSyncTask", ccst));

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_LoadWorldTask");
	MNSandbox::Callback lwt = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_LoadWorldTask", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {
		double owid = context.GetData_Number("owid");
		LoadWorldTask(owid);
		return MNSandbox::SandboxResult(nullptr, true);
		});

	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_LoadWorldTask", lwt));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_Clear");
	MNSandbox::Callback ccb = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_Clear", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {
		Clear();
		return MNSandbox::SandboxResult(nullptr, true);
		});

	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_Clear", ccb));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_TrySaveAllWorldTask");
	MNSandbox::Callback tsawt = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_TrySaveAllWorldTask", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {
		double owid = context.GetData_Number("owid");
		ChunkIOMgr* iomgr = (ChunkIOMgr*)context.GetData_Userdata("ChunkIOMgr", "iomgr");
		TrySaveAllWorldTask(owid, iomgr);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_TrySaveAllWorldTask", tsawt));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_OnPlayerLeaveAch");
	MNSandbox::Callback opla = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_OnPlayerLeaveAch", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {
		double uin = context.GetData_Number("uin");
		OnPlayerLeaveAch(uin);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_OnPlayerLeaveAch", opla));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_OnEnterWorld");
	MNSandbox::Callback oew = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_OnEnterWorld", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {
		double worldId = context.GetData_Number("worldId");
		double objId = context.GetData_Number("objId");
		OnEnterWorld(worldId, objId);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_OnEnterWorld", oew));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_InsertCheckDeque");
	MNSandbox::Callback icd = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_InsertCheckDeque", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {

		double objId = context.GetData_Number("objId");
		int type = context.GetData_Number("type");
		int target1 = context.GetData_Number("target1");
		int target2 = context.GetData_Number("target2");
		int goalNum = context.GetData_Number("goalNum");
		InsertCheckDeque(objId, type, target1, target2, goalNum);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_InsertCheckDeque", icd));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_OnLoadRoleWorldTask");
	MNSandbox::Callback olrwt = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_OnLoadRoleWorldTask", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {

		double uin = context.GetData_Number("uin");
		double wid = context.GetData_Number("wid");
		const void* data = (const void*)context.GetData_Userdata("data");
		int len = context.GetData_Number("len");

		OnLoadRoleWorldTask(uin, wid, data, len);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_OnLoadRoleWorldTask", olrwt));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_GetTaskState");
	MNSandbox::Callback gts = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_GetTaskState", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {

		double objId = context.GetData_Number("objId");
		double taskId = context.GetData_Number("taskId");
		return MNSandbox::SandboxResult(nullptr, true).SetData_Number("taskState", GetTaskState(objId, taskId));
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_GetTaskState", gts));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_CMDTaskIfo");
	MNSandbox::Callback cmdti = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_CMDTaskIfo", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {

		double objId = context.GetData_Number("objId");
		double taskId = context.GetData_Number("taskId");
		CMDTaskIfo(objId, taskId);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_CMDTaskIfo", cmdti));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_CheckTask");
	MNSandbox::Callback ct = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_CheckTask", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {

		double objId = context.GetData_Number("objId");
		int type = context.GetData_Number("type");
		int target1 = context.GetData_Number("target1");
		int target2 = context.GetData_Number("target2");
		int goalNum = context.GetData_Number("goalNum");;
		CheckTask(objId, type, target1, target2, goalNum);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_CheckTask", ct));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_AddNeedSave");
	MNSandbox::Callback ans = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_AddNeedSave", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {

		double uin = context.GetData_Number("uin");
		AddNeedSave(uin);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_AddNeedSave", ans));

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("TaskSubSystem_OnLeaveWorld");
	MNSandbox::Callback olw = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("TaskSubSystem_OnLeaveWorld", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {

		int objid = context.GetData_Number("objid");
		OnLeaveWorld(objid);
		return MNSandbox::SandboxResult(nullptr, true);
		});
	m_EventCallBacks.insert(std::pair< std::string, MNSandbox::Callback>("TaskSubSystem_OnLeaveWorld", olw));

}

void TaskSubSystem::Update2Client() // 主机向客机同步任务数据
{
	// iter first:uin second: set<任务ID>
	for (auto iter = m_mapPlayerDirtyList.begin(); iter != m_mapPlayerDirtyList.end(); iter++)
	{
		// player_iter firs:uin second:map<任务ID, 任务数据>
		auto player_iter = m_mapPlayerTaskInfo.find(iter->first);

		if (player_iter == m_mapPlayerTaskInfo.end())
		{
			LOG_WARNING("AchievementManager::updateData find role data error uin=%lld", iter->first);
			continue;
		}
		jsonxx::Array listInfo;
		// id_iter: 任务ID
		for (auto id_iter = iter->second.begin(); id_iter != iter->second.end(); id_iter++)
		{
			// 在玩家的任务数据里找记录的ID
			// ach_iter first:任务ID second:任务数据
			auto ach_iter = player_iter->second.find(*id_iter);
			if (ach_iter == player_iter->second.end())
			{
				LOG_WARNING("AchievementManager::updateData find ach data error uin=%lld id=", iter->first);
				continue;
			}

			if (!ach_iter->second.taskDef)
				continue;
			jsonxx::Object info;
			char objid_str[128];
			sprintf(objid_str, "%lld", iter->first);
			info << "playerid" << objid_str;
			info << "taskid" << ach_iter->second.taskDef->ID;
			info << "taskstate" << ach_iter->second.taskState;
			info << "rewardstate" << ach_iter->second.rewardState;
			info << "arrynum" << ach_iter->second.arryNum;
			info << "completeyear" << ach_iter->second.completeYear;
			info << "completemonth" << ach_iter->second.completeMonth;
			info << "completeday" << ach_iter->second.completeDay;
			info << "rewardRet" << ach_iter->second.rewardRet;
			listInfo << info;
		}
		jsonxx::Object context;
		context << "msg" << listInfo;
		GetSandBoxManager().sendToClient(iter->first, "PB_TASK_SYNC_HC", context.bin(), context.binLen());
		m_uinTaskNeedSave.insert(iter->first);
	}
	m_mapPlayerDirtyList.clear();

	//目标任务同步
	for (auto iter = m_mapPlayerObjectiveDirtyList.begin(); iter != m_mapPlayerObjectiveDirtyList.end(); iter++)
	{
		auto player_iter = m_mapPlayerObjectiveTaskInfo.find(iter->first);
		if (player_iter == m_mapPlayerObjectiveTaskInfo.end())
		{
			continue;
		}
		jsonxx::Array listInfo;
		// id_iter: 任务ID
		for (auto id_iter = iter->second.begin(); id_iter != iter->second.end(); id_iter++)
		{
			auto ach_iter = player_iter->second.find(*id_iter);
			if (ach_iter == player_iter->second.end())
			{
				continue;
			}
			if (!ach_iter->second.objectiveDef)
				continue;
			jsonxx::Object info;

			char objid_str[128];
			sprintf(objid_str, "%lld", iter->first);
			info << "playerid" << objid_str;
			info << "objectiveid" << ach_iter->second.objectiveDef->ID;
			info << "rewardstate" << ach_iter->second.rewardState;
			info << "rewardRet" << ach_iter->second.rewardRet;
			listInfo << info;
		}
		jsonxx::Object context;
		context << "msg" << listInfo;
		GetSandBoxManager().sendToClient(iter->first, "PB_TASK_OBJECTIVE_SYNC_HC", context.bin(), context.binLen());
		m_uinTaskNeedSave.insert(iter->first);
	}
	m_mapPlayerObjectiveDirtyList.clear();

	//追踪
	for (auto iter = m_mapPlayerTrackDirtyList.begin(); iter != m_mapPlayerTrackDirtyList.end(); iter++)
	{
		auto player_iter = m_mapPlayerTrackInfo.find(iter->first);
		if (player_iter == m_mapPlayerTrackInfo.end())
		{
			continue;
		}
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", iter->first);
		context << "playerid" << objid_str;
		context << "trackid" << player_iter->second;
		GetSandBoxManager().sendToClient(iter->first, "PB_TASK_TRACK_SYNC_HC", context.bin(), context.binLen());
		m_uinTaskNeedSave.insert(iter->first);
	}
	m_mapPlayerTrackDirtyList.clear();
}
void TaskSubSystem::Update2Server() // 客机向主机同步任务数据
{
	if (!g_pPlayerCtrl) {
		return;
	}
	// iter first:uin second: set<任务ID>
	for (auto iter = m_mapPlayerDirtyList.begin(); iter != m_mapPlayerDirtyList.end(); iter++)
	{
		if (g_pPlayerCtrl->getObjId() != iter->first)
		{
			// 只允许发送自己的任务
			return;
		}
		auto player_iter = m_mapPlayerTaskInfo.find(iter->first);

		if (player_iter == m_mapPlayerTaskInfo.end())
		{
			LOG_WARNING("AchievementManager::updateData find role data error uin=%lld", iter->first);
			continue;
		}
		jsonxx::Array listInfo;
		// id_iter: 任务ID
		for (auto id_iter = iter->second.begin(); id_iter != iter->second.end(); id_iter++)
		{
			// 在玩家的任务数据里找记录的ID
			// ach_iter first:任务ID second:任务数据
			auto ach_iter = player_iter->second.find(*id_iter);
			if (ach_iter == player_iter->second.end())
			{
				LOG_WARNING("AchievementManager::updateData find ach data error uin=%lld id=", iter->first);
				continue;
			}

			if (!ach_iter->second.taskDef)
				continue;

			jsonxx::Object info;
			char objid_str[128];
			sprintf(objid_str, "%lld", iter->first);
			info << "playerid" << objid_str;
			info << "taskid" << ach_iter->second.taskDef->ID;
			info << "taskstate" << ach_iter->second.taskState;
			info << "rewardstate" << ach_iter->second.rewardState;
			info << "arrynum" << ach_iter->second.arryNum;
			info << "completeyear" << ach_iter->second.completeYear;
			info << "completemonth" << ach_iter->second.completeMonth;
			info << "completeday" << ach_iter->second.completeDay;
			listInfo << info;
		}
		jsonxx::Object context;
		context << "msg" << listInfo;
		GetSandBoxManager().sendToHost("PB_TASK_UPDATE_CH", context.bin(), context.binLen());
		m_uinTaskNeedSave.insert(iter->first);
	}
	m_mapPlayerDirtyList.clear();

	//目标任务同步
	for (auto iter = m_mapPlayerObjectiveDirtyList.begin(); iter != m_mapPlayerObjectiveDirtyList.end(); iter++)
	{
		if (g_pPlayerCtrl->getObjId() != iter->first)
		{
			// 只允许发送自己的任务
			return;
		}
		auto player_iter = m_mapPlayerObjectiveTaskInfo.find(iter->first);
		if (player_iter == m_mapPlayerObjectiveTaskInfo.end())
		{
			continue;
		}
		jsonxx::Array listInfo;
		// id_iter: 任务ID
		for (auto id_iter = iter->second.begin(); id_iter != iter->second.end(); id_iter++)
		{
			auto ach_iter = player_iter->second.find(*id_iter);
			if (ach_iter == player_iter->second.end())
			{
				continue;
			}
			if (!ach_iter->second.objectiveDef)
				continue;

			jsonxx::Object info;
			char objid_str[128];
			sprintf(objid_str, "%lld", iter->first);
			info << "playerid" << objid_str;
			info << "objectiveid" << ach_iter->second.objectiveDef->ID;
			info << "rewardstate" << ach_iter->second.rewardState;
			listInfo << info;
		}
		jsonxx::Object context;
		context << "msg" << listInfo;
		GetSandBoxManager().sendToHost("PB_TASK_OBJECTIVE_UPDATE_CH", context.bin(), context.binLen());
		m_uinTaskNeedSave.insert(iter->first);
	}
	m_mapPlayerObjectiveDirtyList.clear();

	//追踪
	for (auto iter = m_mapPlayerTrackDirtyList.begin(); iter != m_mapPlayerTrackDirtyList.end(); iter++)
	{
		auto player_iter = m_mapPlayerTrackInfo.find(iter->first);
		if (player_iter == m_mapPlayerTrackInfo.end())
		{
			continue;
		}
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", iter->first);
		context << "playerid" << objid_str;
		context << "trackid" << player_iter->second;
		GetSandBoxManager().sendToHost("PB_TASK_TRACK_SYNC_CH", context.bin(), context.binLen());
		m_uinTaskNeedSave.insert(iter->first);
	}
	m_mapPlayerTrackDirtyList.clear();
}
void TaskSubSystem::SyncTaskData(long long objid)
{
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return;
	auto& playerAchievement = m_mapPlayerTaskInfo[objid];
	auto iter = playerAchievement.begin();

	jsonxx::Array listInfo;
	while (iter != playerAchievement.end())
	{
		if (iter->second.taskDef)
		{
			jsonxx::Object info;
			//char objid_str[128];
			//sprintf(objid_str, "%lld", objid);
			//info << "playerid" << objid_str;
			//info << "taskid" << iter->second.taskDef->ID;
			//info << "taskstate" << iter->second.taskState;
			//info << "rewardstate" << iter->second.rewardState;
			//info << "arrynum" << iter->second.arryNum;
			//info << "completeyear" << iter->second.completeYear;
			//info << "completemonth" << iter->second.completeMonth;
			//info << "completeday" << iter->second.completeDay;
			static thread_local char jsonBuffer[512];
			snprintf(jsonBuffer, sizeof(jsonBuffer),
				R"({"playerid":"%lld","taskid":%d,"taskstate":%d,"rewardstate":%d,"arrynum":%d,"completeyear":%d,"completemonth":%d,"completeday":%d})",
				objid,
				iter->second.taskDef->ID,
				iter->second.taskState,
				iter->second.rewardState,
				iter->second.arryNum,
				iter->second.completeYear,
				iter->second.completeMonth,
				iter->second.completeDay
			);

			if (info.parse(jsonBuffer))
				listInfo << info;
		}
		iter++;
	}
	if (listInfo.size() > 0)
	{
		jsonxx::Object context;
		context << "msg" << listInfo;
		GetSandBoxManager().sendToClient(objid, "PB_TASK_INITDATA_HC", context.bin(), context.binLen());
	}
}

void TaskSubSystem::HandleTaskUpdate2Client(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	if (obj->has<jsonxx::Array>("msg"))
	{
		jsonxx::Array& msg = (jsonxx::Array&)obj->get<jsonxx::Array>("msg");
		for (int i = 0; i < msg.size(); i++)
		{
			auto data = msg.get<jsonxx::Object>(i);
			if (!GetDefManagerProxy()->getSurviveTaskDef(data.get<jsonxx::Number>("taskid")))
			{
				continue;
			}
			long long playerid = 0;
			std::string objidstring = data.get<jsonxx::String>("playerid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> playerid))
			{
				continue;
			}
			if (m_mapPlayerTaskInfo.find(playerid) == m_mapPlayerTaskInfo.end())
				continue;
			auto& playerAchie = m_mapPlayerTaskInfo[playerid];
			auto& taskInfo = playerAchie[data.get<jsonxx::Number>("taskid")];
			int frontNum = taskInfo.arryNum;
			int frontState = taskInfo.taskState;
			int frontRewardState = taskInfo.rewardState;
			int frontRewardRet = taskInfo.rewardRet;
			taskInfo.taskState = data.get<jsonxx::Number>("taskstate");
			taskInfo.arryNum = data.get<jsonxx::Number>("arrynum");
			taskInfo.rewardState = data.get<jsonxx::Number>("rewardstate");
			taskInfo.taskDef = GetDefManagerProxy()->getSurviveTaskDef(data.get<jsonxx::Number>("taskid"));
			taskInfo.completeYear = data.get<jsonxx::Number>("completeyear");
			taskInfo.completeMonth = data.get<jsonxx::Number>("completemonth");
			taskInfo.completeDay = data.get<jsonxx::Number>("completeday");
			taskInfo.rewardRet = data.get<jsonxx::Number>("rewardRet");
			GetGameEventQue().postTaskUpdate(taskInfo.taskDef->ID);
			if (frontRewardRet != taskInfo.rewardRet)
			{
				//小任务领取
				if (frontRewardState == TASK_CAN_RECEIVE && taskInfo.rewardState == TASK_RECEIVED)
				{
					GetGameEventQue().postTaskReward(1, taskInfo.taskDef->ID, true);
				}
				else
				{
					GetGameEventQue().postTaskReward(1, taskInfo.taskDef->ID, false);
				}
			}
			if (frontNum < taskInfo.taskDef->GoalNum && taskInfo.arryNum >= taskInfo.taskDef->GoalNum)
			{
				GetGameEventQue().postTaskComplete(taskInfo.taskDef->ID);

				if (taskInfo.taskDef->RewardDistributionType == TASK_REWARD_RECEIVE_AUTO)
				{
					GetTaskReward(playerid, taskInfo.taskDef->ID);
				}
			}
		}
	}
}
void TaskSubSystem::HandleTaskUpdate2Host(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	if (obj->has<jsonxx::Array>("msg"))
	{
		jsonxx::Array& msg = (jsonxx::Array&)obj->get<jsonxx::Array>("msg");
		for (int i = 0; i < msg.size(); i++)
		{
			auto data = msg.get<jsonxx::Object>(i);
			long long playerid = 0;
			std::string objidstring = data.get<jsonxx::String>("playerid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> playerid))
			{
				continue;
			}
			if (m_mapPlayerTaskInfo.find(playerid) == m_mapPlayerTaskInfo.end())
				continue;
			auto& playerAchie = m_mapPlayerTaskInfo[playerid];
			auto& achievementInfo = playerAchie[data.get<jsonxx::Number>("taskid")];
			achievementInfo.taskState = data.get<jsonxx::Number>("taskstate");
			achievementInfo.arryNum = data.get<jsonxx::Number>("arrynum");
			achievementInfo.rewardState = data.get<jsonxx::Number>("rewardstate");
			achievementInfo.taskDef = GetDefManagerProxy()->getSurviveTaskDef(data.get<jsonxx::Number>("taskid"));
			achievementInfo.completeYear = data.get<jsonxx::Number>("completeyear");
			achievementInfo.completeMonth = data.get<jsonxx::Number>("completemonth");
			achievementInfo.completeDay = data.get<jsonxx::Number>("completeday");
			m_mapPlayerDirtyList[playerid].insert(data.get<jsonxx::Number>("taskid"));

			if (!HasDirtyTaskUin(playerid))
				m_DirtyTaskUins.push_back(playerid);
		}
	}
	m_Dirty = true;
}

void TaskSubSystem::HandleTaskInit2Client(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	if (obj->has<jsonxx::Array>("msg"))
	{
		jsonxx::Array& msg = (jsonxx::Array&)obj->get<jsonxx::Array>("msg");
		for (int i = 0; i < msg.size(); i++)
		{
			auto data = msg.get<jsonxx::Object>(i);
			if (!GetDefManagerProxy()->getSurviveTaskDef(data.get<jsonxx::Number>("taskid")))
			{
				continue;
			}
			long long playerid = 0;
			std::string objidstring = data.get<jsonxx::String>("playerid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> playerid))
			{
				continue;
			}
			auto& playerAchie = m_mapPlayerTaskInfo[playerid];
			auto& achievementInfo = playerAchie[data.get<jsonxx::Number>("taskid")];
			achievementInfo.taskState = data.get<jsonxx::Number>("taskstate");
			achievementInfo.arryNum = data.get<jsonxx::Number>("arrynum");
			achievementInfo.rewardState = data.get<jsonxx::Number>("rewardstate");
			achievementInfo.taskDef = GetDefManagerProxy()->getSurviveTaskDef(data.get<jsonxx::Number>("taskid"));
			achievementInfo.completeYear = data.get<jsonxx::Number>("completeyear");
			achievementInfo.completeMonth = data.get<jsonxx::Number>("completemonth");
			achievementInfo.completeDay = data.get<jsonxx::Number>("completeday");
		}
	}
}

void TaskSubSystem::SyncObjectiveData(long long objid)
{
	if (m_mapPlayerObjectiveTaskInfo.find(objid) == m_mapPlayerObjectiveTaskInfo.end())
		return;
	auto iter = m_mapPlayerObjectiveTaskInfo[objid].begin();

	jsonxx::Array listInfo;
	while (iter != m_mapPlayerObjectiveTaskInfo[objid].end())
	{
		if (iter->second.objectiveDef)
		{
			jsonxx::Object info;
			char objid_str[128];
			sprintf(objid_str, "%lld", objid);
			info << "playerid" << objid_str;
			info << "objectiveid" << iter->second.objectiveDef->ID;
			info << "rewardstate" << iter->second.rewardState;
			listInfo << info;
		}
		iter++;
	}
	if (listInfo.size() > 0)
	{
		jsonxx::Object context;
		context << "msg" << listInfo;
		GetSandBoxManager().sendToClient(objid, "PB_TASK_OBJECTIVE_INITDATA_HC", context.bin(), context.binLen());
	}
}
void TaskSubSystem::HandleObjectiveTaskUpdate2Client(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	if (obj->has<jsonxx::Array>("msg"))
	{
		jsonxx::Array& msg = (jsonxx::Array&)obj->get<jsonxx::Array>("msg");
		for (int i = 0; i < msg.size(); i++)
		{
			auto data = msg.get<jsonxx::Object>(i);

			if (!GetDefManagerProxy()->getSurviveObjectiveDef(data.get<jsonxx::Number>("objectiveid")))
			{
				continue;
			}
			long long playerid = 0;
			std::string objidstring = data.get<jsonxx::String>("playerid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> playerid))
			{
				continue;
			}
			if (m_mapPlayerObjectiveTaskInfo.find(playerid) == m_mapPlayerObjectiveTaskInfo.end())
				continue;
			auto& playerAchie = m_mapPlayerObjectiveTaskInfo[playerid];
			auto& objectiveInfo = playerAchie[data.get<jsonxx::Number>("objectiveid")];
			int frontRewardState = objectiveInfo.rewardState;
			int frontRewardRet = objectiveInfo.rewardRet;
			objectiveInfo.rewardState = data.get<jsonxx::Number>("rewardstate");
			objectiveInfo.objectiveDef = GetDefManagerProxy()->getSurviveObjectiveDef(data.get<jsonxx::Number>("objectiveid"));
			objectiveInfo.rewardRet = data.get<jsonxx::Number>("rewardRet");
			GetGameEventQue().postObjectiveTaskUpdate(objectiveInfo.objectiveDef->ID);
			if (frontRewardRet != objectiveInfo.rewardRet)
			{
				//大任务领取
				if (frontRewardState == TASK_CAN_RECEIVE && objectiveInfo.rewardState == TASK_RECEIVED)
				{
					GetGameEventQue().postTaskReward(2, objectiveInfo.objectiveDef->ID, true);
				}
				else
				{
					GetGameEventQue().postTaskReward(2, objectiveInfo.objectiveDef->ID, false);
				}
			}
			//完成
			if (frontRewardState == TASK_UNRECEIVE && objectiveInfo.rewardState == TASK_CAN_RECEIVE)
			{
				GetGameEventQue().postObjectiveTaskComplete(objectiveInfo.objectiveDef->ID);
			}
		}
	}
}
void TaskSubSystem::HandleObjectiveTaskUpdate2Host(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	if (obj->has<jsonxx::Array>("msg"))
	{
		jsonxx::Array& msg = (jsonxx::Array&)obj->get<jsonxx::Array>("msg");
		for (int i = 0; i < msg.size(); i++)
		{
			auto data = msg.get<jsonxx::Object>(i);
			long long playerid = 0;
			std::string objidstring = data.get<jsonxx::String>("playerid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> playerid))
			{
				continue;
			}
			if (m_mapPlayerObjectiveTaskInfo.find(playerid) == m_mapPlayerObjectiveTaskInfo.end())
				continue;
			int id = data.get<jsonxx::Number>("objectiveid");
			auto& playerAchie = m_mapPlayerObjectiveTaskInfo[playerid];
			auto& achievementInfo = playerAchie[id];
			achievementInfo.rewardState = data.get<jsonxx::Number>("rewardstate");
			achievementInfo.objectiveDef = GetDefManagerProxy()->getSurviveObjectiveDef(id);
			m_mapPlayerObjectiveDirtyList[playerid].insert(id);

			if (!HasDirtyObjectiveTaskUin(playerid))
			{
				m_DirtyObjectiveTaskUins.push_back(playerid);
			}
		}
	}
	m_Dirty = true;
}
void TaskSubSystem::HandleObjectiveTaskInit2Client(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	if (obj->has<jsonxx::Array>("msg"))
	{
		jsonxx::Array& msg = (jsonxx::Array&)obj->get<jsonxx::Array>("msg");
		for (int i = 0; i < msg.size(); i++)
		{
			auto data = msg.get<jsonxx::Object>(i);
			if (!GetDefManagerProxy()->getSurviveObjectiveDef(data.get<jsonxx::Number>("objectiveid")))
			{
				continue;
			}
			long long playerid = 0;
			std::string objidstring = data.get<jsonxx::String>("playerid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> playerid))
			{
				continue;
			}
			auto& playerAchie = m_mapPlayerObjectiveTaskInfo[playerid];
			auto& achievementInfo = playerAchie[data.get<jsonxx::Number>("objectiveid")];
			achievementInfo.rewardState = data.get<jsonxx::Number>("rewardstate");
			achievementInfo.objectiveDef = GetDefManagerProxy()->getSurviveObjectiveDef(data.get<jsonxx::Number>("objectiveid"));
		}
	}
}
void TaskSubSystem::HandleTaskTrackInit2Client(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	int objid = 0;
	int trackid = 0;
	if (obj->has<jsonxx::String>("playerid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("playerid");
		std::stringstream sstr;
		sstr << objidstring;
		if (!(sstr >> objid))
			return;
	}
	if (obj->has<jsonxx::Number>("trackid"))
	{
		trackid = (int)obj->get<jsonxx::Number>("trackid");
	}
	m_mapPlayerTrackInfo[objid] = trackid;
}

void TaskSubSystem::HandleTaskTrackUpdate2Client(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	int objid = 0;
	int trackid = 0;
	if (obj->has<jsonxx::String>("playerid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("playerid");
		std::stringstream sstr;
		sstr << objidstring;
		if (!(sstr >> objid))
			return;
	}
	if (obj->has<jsonxx::Number>("trackid"))
	{
		trackid = (int)obj->get<jsonxx::Number>("trackid");
	}
	m_mapPlayerTrackInfo[objid] = trackid;

	GetGameEventQue().postTaskTrack(trackid);
}
void TaskSubSystem::HandleTaskTrackUpdate2Host(void* context)
{
	jsonxx::Object* obj = (jsonxx::Object*)context;
	int objid = 0;
	int trackid = 0;
	if (obj->has<jsonxx::String>("playerid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("playerid");
		std::stringstream sstr;
		sstr << objidstring;
		if (!(sstr >> objid))
			return;
	}
	if (obj->has<jsonxx::Number>("trackid"))
	{
		trackid = (int)obj->get<jsonxx::Number>("trackid");
	}
	m_mapPlayerTrackInfo[objid] = trackid;

	m_Dirty = true;
}

void TaskSubSystem::Clear()
{
	m_mapPlayerTaskInfo.clear();
	m_mapPlayerDirtyList.clear();
	m_mapPlayerObjectiveTaskInfo.clear();
	m_mapPlayerObjectiveDirtyList.clear();
	m_mapPlayerTrackDirtyList.clear();
	m_mapPlayerTrackInfo.clear();
	m_checkTaskDeque.clear();
	m_uinTaskNeedSave.clear();
	m_IsCheckSave = false;
	ClearDirtyState();
}
void TaskSubSystem::OnEnterWorld(long long world_id, int objid)
{
#ifdef DEDICATED_SERVER
	//云服编辑模式不加载
	auto m_gameMode = GetWorldManagerPtr()->getGameMode();
	if (m_gameMode == OWTYPE_GAMEMAKER)
		return;
#endif
	//任务列表
	int size = GetDefManagerProxy()->getSurviveTaskDefCsvNum();
	int frontSize = GetDefManagerProxy()->getSurviveTaskFrontSize();
	std::set<int> taskInfoIds;
	std::set<int> taskActivateIds;
	for (int i = 0; i < size; i++)
	{
		auto taskDef = GetDefManagerProxy()->getSurviveTaskDefByIndex(i);
		if (m_mapPlayerTaskInfo[objid].find(taskDef->ID) != m_mapPlayerTaskInfo[objid].end())
			continue;
		auto objectiveDef = GetDefManagerProxy()->getSurviveObjectiveDef(taskDef->ObjectiveID);
		if (!objectiveDef)
			continue;
		if (objectiveDef->Group == 0) //关闭状态
			continue;
		TaskSysInfo info;
		info.taskDef = taskDef;
		info.taskState = TASK_LOCK;
		info.rewardState = 0;
		info.arryNum = 0;
		info.completeYear = 0;
		info.completeMonth = 0;
		info.completeDay = 0;
		info.rewardRet = 0;
		taskInfoIds.insert(taskDef->ID);
		taskActivateIds.insert(taskDef->ObjectiveID);
		m_mapPlayerTaskInfo[objid][taskDef->ID] = info;
	}

	if (g_WorldMgr && !g_WorldMgr->isRemote())
	{
		//激活
		for (auto iter = taskActivateIds.begin(); iter != taskActivateIds.end(); iter++)
		{
			auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(*iter);
			for (int i = 0; i < taskVec.size(); i++)
			{
				Activate(objid, taskVec[i]);
			}
		}
		for (auto iter = taskInfoIds.begin(); iter != taskInfoIds.end(); iter++)
		{
			//兼容旧成就系统数据嵌入
			MergeAchievementData(objid, m_mapPlayerTaskInfo[objid][*iter]);
		}
		if (!taskInfoIds.empty())
		{
			size = GetDefManagerProxy()->getSurviveObjectiveDefCsvNum();
			for (int i = 0; i < size; i++)
			{
				auto objectiveDef = GetDefManagerProxy()->getSurviveObjectiveDefByIndex(i);
				if (m_mapPlayerObjectiveTaskInfo[objid].find(objectiveDef->ID) != m_mapPlayerObjectiveTaskInfo[objid].end())
					continue;
				FinishObjectiveTypeTask(objid, objectiveDef->ID, TASKSYS_AUTO_FINISH);
			}
		}
	}
	SyncTaskData(objid);

	//目标信息
	size = GetDefManagerProxy()->getSurviveObjectiveDefCsvNum();
	int min_objectiveID = 1000000;
	for (int i = 0; i < size; i++)
	{
		auto objectiveDef = GetDefManagerProxy()->getSurviveObjectiveDefByIndex(i);
		if (m_mapPlayerObjectiveTaskInfo[objid].find(objectiveDef->ID) != m_mapPlayerObjectiveTaskInfo[objid].end())
			continue;
		if (objectiveDef->Group == 0)//关闭状态
			continue;
		TaskSysObjectiveInfo info;
		info.objectiveDef = objectiveDef;
		//一组任务完成
		if (IsObjectiveAccomplish(objid, objectiveDef->ID))
		{
			info.rewardState = TASK_CAN_RECEIVE;
		}
		else
		{
			info.rewardState = TASK_UNRECEIVE;
			if (objectiveDef->ID < min_objectiveID && objectiveDef->ID != 1001) min_objectiveID = objectiveDef->ID;
		}
		info.rewardRet = 0;
		info.oldLock = 0;
		if (GetObjectiveFinishNum(objid, objectiveDef->ID) > 0) info.oldLock = 1;
		m_mapPlayerObjectiveTaskInfo[objid][objectiveDef->ID] = info;
	}
	SyncObjectiveData(objid);
	if (m_mapPlayerTrackInfo.find(objid) == m_mapPlayerTrackInfo.end())
	{
		//int trackid = 1002001;
		int trackid = GetObjectiveTaskMinID(objid, min_objectiveID);
		if (trackid >= MAX_MAIN_TASK) trackid = 1002001;
		if (GetTaskState(objid, trackid) == TASK_COMPLETE)
		{
			//成就把前置给覆盖了给个0
			m_mapPlayerTrackInfo[objid] = 0;
		}
		else
		{
			m_mapPlayerTrackInfo[objid] = trackid;
		}
	}
	if (!taskInfoIds.empty())
	{
		int trackid = GetObjectiveTaskMinID(objid, min_objectiveID);
		if (trackid >= MAX_MAIN_TASK) trackid = 1002001;
		if (GetTaskState(objid, trackid) == TASK_COMPLETE)
		{
			m_mapPlayerTrackInfo[objid] = 0;
		}
		else
		{
			m_mapPlayerTrackInfo[objid] = trackid;
		}
	}

	if (m_mapPlayerTrackInfo[objid] == 0)
	{
		this->LastCheckToGetObjtiveTask(objid, min_objectiveID);
	}

	//追踪信息
	SyncTrackData(objid);
}

void TaskSubSystem::MergeAchievementData(long long objid, TaskSysInfo& info)
{
	auto taskDef = info.taskDef;
	if (taskDef->OldTaskID > 0)
	{
		auto achInfo = GetIWorldConfigProxy()->getAccountAchievementInfo(objid, taskDef->OldTaskID);
		if (achInfo)
		{
			if (achInfo->rewardState >= REWARD_CAN_RECEIVE)
			{
				info.arryNum = taskDef->GoalNum;
				info.taskState = TASK_COMPLETE;
				//if (achInfo->rewardState == REWARD_CAN_RECEIVE)
				//{
				info.rewardState = TASK_CAN_RECEIVE;
				if (info.taskDef->RewardDistributionType == TASK_REWARD_RECEIVE_AUTO)
					GetTaskReward(objid, info.taskDef->ID);
				//}
				//else
				//{
					//info.rewardState = TASK_RECEIVED;
				//}
				info.completeDay = achInfo->completeDay;
				info.completeMonth = achInfo->completeMonth;
				info.completeYear = achInfo->completeYear;
				FinishAllFrontTask(objid, info);
			}
			else if (achInfo->achievementState <= ACTIVATE_UNCOMPLETE && info.taskState < TASK_COMPLETE)
			{
				if (info.arryNum < achInfo->arryNum)
					info.arryNum = achInfo->arryNum;
				if (info.arryNum >= taskDef->GoalNum)
				{
					info.arryNum = taskDef->GoalNum;
					info.taskState = TASK_COMPLETE;
				}
				else
				{
					if (achInfo->achievementState == ACTIVATE_UNCOMPLETE)
						info.taskState = TASK_UNCOMPLETE;
					else if (achInfo->achievementState == UNLOCK_UNACTIVATE && info.taskState < TASK_UNCOMPLETE)
						info.taskState = TASK_UNCOMPLETE;
					//else if (achInfo->achievementState == LOCK)
					//	info.taskState = TASK_LOCK;
				}
				if (info.taskState == TASK_COMPLETE)
				{
					time_t t = time(NULL);
					struct tm* stm = localtime(&t);
					if (stm)
					{
						info.completeYear = stm->tm_year + 1900;
						info.completeMonth = stm->tm_mon + 1;
						info.completeDay = stm->tm_mday;
					}
					FinishAllFrontTask(objid, info);
					info.rewardState = TASK_CAN_RECEIVE;
					if (info.taskDef->RewardDistributionType == TASK_REWARD_RECEIVE_AUTO)
						GetTaskReward(objid, info.taskDef->ID);

				}
			}
			//else if (achInfo->achievementState == UNLOCK_UNACTIVATE && info.taskState < TASK_UNCOMPLETE)
			//{
			//	if (info.arryNum < achInfo->arryNum)
			//		info.arryNum = achInfo->arryNum;
			//	info.taskState = UNLOCK_UNACTIVATE;
			//}
		}
	}
}

void TaskSubSystem::FinishAllFrontTask(long long objid, TaskSysInfo& info)
{
	int frontSize = GetDefManagerProxy()->getSurviveTaskFrontSize();
	for (int i = 0; i < frontSize; i++)
	{
		if (info.taskDef->FrontIDs[i] > 0)
		{
			const SurviveTaskDef* frontDef = GetDefManagerProxy()->getSurviveTaskDef(info.taskDef->FrontIDs[i]);
			if (m_mapPlayerTaskInfo[objid].find(frontDef->ID) != m_mapPlayerTaskInfo[objid].end())
			{
				auto& frontInfo = m_mapPlayerTaskInfo[objid][frontDef->ID];
				if (frontInfo.rewardState == TASK_RECEIVED) continue;
				if (frontInfo.taskDef->OldTaskID > 0)
				{
					MergeAchievementData(objid, frontInfo);
					//if (frontInfo.taskState < TASK_COMPLETE)
					//continue;
				}
				else
				{
					frontInfo.rewardState = TASK_CAN_RECEIVE;
				}
				frontInfo.arryNum = frontDef->GoalNum;
				frontInfo.taskState = TASK_COMPLETE;

				if (frontInfo.completeYear == 0)
				{
					frontInfo.completeYear = info.completeYear;
					frontInfo.completeMonth = info.completeMonth;
					frontInfo.completeDay = info.completeDay;
				}

				if (frontInfo.rewardState == TASK_CAN_RECEIVE && frontDef->RewardDistributionType == TASK_REWARD_RECEIVE_AUTO)
				{
					GetTaskReward(objid, frontDef->ID);
				}
				FinishAllFrontTask(objid, frontInfo);
			}
			else
			{
				assert(false);
			}
		}
	}
}

void TaskSubSystem::FinishObjectiveTypeTask(long long objid, int objectiveID, int type)
{
	const auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
	for (int i = 0; i < taskVec.size(); i++)
	{
		auto def = GetDefManagerProxy()->getSurviveTaskDef(taskVec[i]);
		if (def->Type == TASKSYS_AUTO_FINISH)
		{
			int frontsize = GetDefManagerProxy()->getSurviveTaskFrontSize();
			int num = 0;
			int size = 0;
			for (int i = 0; i < frontsize; i++)
			{
				if (def->FrontIDs[i] > 0)
				{
					size++;
					int state = GetTaskState(objid, def->FrontIDs[i]);
					if (state == TASK_COMPLETE)
					{
						num++;
					}
				}
			}
			if (num >= size)
			{
				if (m_mapPlayerTaskInfo[objid].find(def->ID) != m_mapPlayerTaskInfo[objid].end())
				{
					auto& info = m_mapPlayerTaskInfo[objid][def->ID];
					info.arryNum = def->GoalNum;
					info.taskState = TASK_COMPLETE;

					if (info.completeYear == 0)
					{
						time_t t = time(NULL);
						struct tm* stm = localtime(&t);
						if (stm)
						{
							info.completeYear = stm->tm_year + 1900;
							info.completeMonth = stm->tm_mon + 1;
							info.completeDay = stm->tm_mday;
						}
					}

					if (info.rewardState == TASK_CAN_RECEIVE && def->RewardDistributionType == TASK_REWARD_RECEIVE_AUTO)
					{
						GetTaskReward(objid, def->ID);
					}
				}
			}
		}
	}
}

void TaskSubSystem::SyncTrackData(long long objid)
{
	auto iter = m_mapPlayerTrackInfo.find(objid);
	if (iter == m_mapPlayerTrackInfo.end())
		return;

	jsonxx::Object context;
	char objid_str[128];
	sprintf(objid_str, "%lld", objid);
	context << "playerid" << objid_str;
	context << "trackid" << iter->second;
	GetSandBoxManager().sendToClient(objid, "PB_TASK_TRACK_INITDATA_HC", context.bin(), context.binLen());
}
bool TaskSubSystem::LoadWorldTaskFromData(const void* buf, int buflen)
{
	bool _needSyncOffline = false;
#ifndef DEDICATED_SERVER
	const char* rootpath = "data";
	char frompath[256], topath[256];
	sprintf(frompath, "%s/w%lld/roles/u%d.p", rootpath, m_curWorldId, 1);
	if (!GetFileManager().IsFileExistWritePath(frompath))
	{
		_needSyncOffline = true;
	}
#endif
	flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
	if (!FBSave::VerifyWorldTaskSysBuffer(verifier))
	{
		return false;
	}
	const FBSave::WorldTaskSys* wa = FBSave::GetWorldTaskSys(buf);
	if (wa == NULL)
	{
		return false;
	}
	if (wa->tasklist())
	{
		auto* list = wa->tasklist();
		for (auto listIter = list->begin(); listIter != list->end(); listIter++)
		{
			auto objectId = _needSyncOffline && listIter->objid() == 1 ? GetIClientInfo().getUin() : listIter->objid();
			for (auto itemIter = listIter->itemlist()->items()->begin(); itemIter != listIter->itemlist()->items()->end(); itemIter++)
			{
				int taskID = itemIter->id();
				auto& taskInfo = m_mapPlayerTaskInfo[objectId][taskID];
				taskInfo.taskState = itemIter->state();
				taskInfo.arryNum = itemIter->arrynum();
				taskInfo.rewardState = itemIter->getaward();
				taskInfo.completeYear = itemIter->completeyear();
				taskInfo.completeMonth = itemIter->completemonth();
				taskInfo.completeDay = itemIter->completeday();
				taskInfo.rewardRet = 0;
				taskInfo.taskDef = GetDefManagerProxy()->getSurviveTaskDef(taskID);
			}
		}
	}
	if (wa->objectivelist())
	{
		auto* list = wa->objectivelist();
		for (auto listIter = list->begin(); listIter != list->end(); listIter++)
		{
			auto objectId = _needSyncOffline && listIter->objid() == 1 ? GetIClientInfo().getUin() : listIter->objid();
			for (auto itemIter = listIter->itemlist()->items()->begin(); itemIter != listIter->itemlist()->items()->end(); itemIter++)
			{
				auto& taskInfo = m_mapPlayerObjectiveTaskInfo[objectId][itemIter->id()];
				taskInfo.rewardState = itemIter->getaward();
				taskInfo.objectiveDef = GetDefManagerProxy()->getSurviveObjectiveDef(itemIter->id());
				taskInfo.rewardRet = 0;
				taskInfo.oldLock = itemIter->oldLock();
			}
		}
	}
	if (wa->tracklist())
	{
		auto* list = wa->tracklist();
		for (auto listIter = list->begin(); listIter != list->end(); listIter++)
		{
			auto objectId = _needSyncOffline && listIter->objid() == 1 ? GetIClientInfo().getUin() : listIter->objid();
			m_mapPlayerTrackInfo.insert(std::make_pair(objectId, listIter->trackid()));
		}
	}
	return true;
}

bool TaskSubSystem::LoadWorldTask(long long owid)
{
	m_curWorldId = owid;
	Clear();
#ifdef IWORLD_SERVER_BUILD
	if (!GetClientInfoProxy()->isRentServerMode())
	{
		// 云服在玩家登录后通过dataserver加载此数据
		return true;
	}
#endif
	std::string rootpath = "data";
	char path[256];
	sprintf(path, "%s/w%lld/wtask.fb", rootpath.c_str(), owid);
	int buflen;
	void* buf = ReadWholeFile(path, buflen);
	if (buf == NULL) return false;

	bool ret = LoadWorldTaskFromData(buf, buflen);
	free(buf);
	return ret;
}
bool TaskSubSystem::SaveWorldATask(long long owid, ChunkIOMgr* iomgr)
{
	m_Dirty = false;
	flatbuffers::FlatBufferBuilder builder;
	flatbuffers::Offset<FBSave::WorldSurviveTask> surviveTask = 0;
	std::vector<flatbuffers::Offset<FBSave::WorldSurviveTaskItem>> surviveTaskList;
	auto taskIter = m_mapPlayerTaskInfo.begin();
	while (taskIter != m_mapPlayerTaskInfo.end())
	{
		std::vector<flatbuffers::Offset<FBSave::SurviveTask>>tempVec;
		auto iter2 = taskIter->second.begin();
		while (iter2 != taskIter->second.end())
		{
			if ((iter2->second.arryNum == 0 && iter2->second.taskState == 0 && iter2->second.rewardState == 0) || iter2->second.taskDef == nullptr)
			{
				iter2++;
				continue;
			}
			tempVec.push_back(FBSave::CreateSurviveTask(builder, iter2->second.taskDef->ID,
				iter2->second.arryNum, iter2->second.taskState, iter2->second.rewardState, iter2->second.completeYear,
				iter2->second.completeMonth, iter2->second.completeDay));
			iter2++;
		}
		surviveTask = FBSave::CreateWorldSurviveTask(builder, builder.CreateVector(tempVec));
		surviveTaskList.push_back(FBSave::CreateWorldSurviveTaskItem(builder, surviveTask, taskIter->first));
		taskIter++;
	}

	flatbuffers::Offset<FBSave::WorldSurviveObjective> surviveObjective = 0;
	std::vector<flatbuffers::Offset<FBSave::WorldSurviveObjectiveItem>> surviveObjectiveList;
	auto objectiveIter = m_mapPlayerObjectiveTaskInfo.begin();
	while (objectiveIter != m_mapPlayerObjectiveTaskInfo.end())
	{
		std::vector<flatbuffers::Offset<FBSave::SurviveObjective>> tempVec;
		auto iter2 = objectiveIter->second.begin();
		while (iter2 != objectiveIter->second.end())
		{
			if ((iter2->second.rewardState == 0) || iter2->second.objectiveDef == nullptr)
			{
				iter2++;
				continue;
			}
			tempVec.push_back(FBSave::CreateSurviveObjective(builder, iter2->second.objectiveDef->ID, iter2->second.rewardState, iter2->second.oldLock));
			iter2++;
		}
		surviveObjective = FBSave::CreateWorldSurviveObjective(builder, builder.CreateVector(tempVec));
		surviveObjectiveList.push_back(FBSave::CreateWorldSurviveObjectiveItem(builder, surviveObjective, objectiveIter->first));
		objectiveIter++;
	}

	std::vector<flatbuffers::Offset<FBSave::WorldTrackItem>> trackList;
	auto trackIter = m_mapPlayerTrackInfo.begin();
	while (trackIter != m_mapPlayerTrackInfo.end())
	{
		trackList.push_back(FBSave::CreateWorldTrackItem(builder, trackIter->second, trackIter->first));
		trackIter++;
	}

	auto wa = FBSave::CreateWorldTaskSys(builder, builder.CreateVector(surviveTaskList), builder.CreateVector(surviveObjectiveList), builder.CreateVector(trackList));
	builder.Finish(wa);
	if (iomgr)
	{
		if (IsFlatBufferCompleteZero(builder.GetBufferPointer(), builder.GetSize()))
			return false;

		iomgr->pushCmd(CIOCMD_SAVEWATASK, builder.GetBufferPointer(), builder.GetSize());
		return true;
	}
	else
	{
		std::string rootpath = "data";
		char path[256];
		sprintf(path, "%s/w%lld/wtask.fb", rootpath.c_str(), owid);
		return GetFileManager().SaveToWritePath(path, builder.GetBufferPointer(), builder.GetSize(), false);
	}
	return true;
}

std::string TaskSubSystem::GeObjectiveIDsByGroup(int group)const
{
	std::string strIDs;
	char tempID[64];
	int size = GetDefManagerProxy()->getSurviveObjectiveDefCsvNum();
	for (int i = 0; i < size; i++)
	{
		auto taskDef = GetDefManagerProxy()->getSurviveObjectiveDefByIndex(i);
		if (group == taskDef->Group)
		{
			sprintf(tempID, "%d,", taskDef->ID);
			strIDs += tempID;
		}
	}
	if (!strIDs.empty())
	{
		strIDs = strIDs.substr(0, strIDs.length() - 1);
	}
	return strIDs;
}

std::string TaskSubSystem::GeTaskIDsByObjectiveID(int objectiveID)const
{
	std::string strIDs;
	char tempID[64];
	const auto surviveTask = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
	for (int i = 0; i < surviveTask.size(); i++)
	{
		sprintf(tempID, "%d,", surviveTask[i]);
		strIDs += tempID;
	}
	if (!strIDs.empty())
	{
		strIDs = strIDs.substr(0, strIDs.length() - 1);
	}
	return strIDs;
}

int TaskSubSystem::GetObjectiveIDByTaskID(int taskID)const
{
	auto taskDef = GetDefManagerProxy()->getSurviveTaskDef(taskID);
	if (taskDef && taskDef->ID == taskID)
		return taskDef->ObjectiveID;
	return 0;
}

int TaskSubSystem::GetTaskState(long long objid, int taskID)
{
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return -1;
	auto& achievementInfos = m_mapPlayerTaskInfo[objid];
	if (achievementInfos.find(taskID) == achievementInfos.end())
		return -1;
	return achievementInfos[taskID].taskState;
}

int TaskSubSystem::GetTaskRewardState(long long objid, int taskID)
{
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return -1;
	auto& achievementInfos = m_mapPlayerTaskInfo[objid];
	if (achievementInfos.find(taskID) == achievementInfos.end())
		return -1;
	auto& target = achievementInfos[taskID];
	return target.rewardState;
}

int TaskSubSystem::GetObjectiveCurProgress(int objectiveID)
{
	if (g_pPlayerCtrl == nullptr)
	{
		return 0;
	}
	int num = 0;
	long long objId = g_pPlayerCtrl->getObjId();
	const auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
	for (auto iter = taskVec.begin(); iter != taskVec.end(); iter++)
	{
		int state = GetTaskState(objId, *iter);
		//已完成
		if (state == TASK_COMPLETE)
		{
			num++;
		}
	}
	return num;
}

int TaskSubSystem::GetObjectiveTotalProgress(int objectiveID)
{
	if (g_pPlayerCtrl == nullptr)
	{
		return 0;
	}
	return GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID).size();
}

bool TaskSubSystem::IsShowObjective(int objectiveID)
{
	if (g_pPlayerCtrl == nullptr)
		return false;
	long long objId = g_pPlayerCtrl->getObjId();
	auto iter = m_mapPlayerObjectiveTaskInfo.find(objId);
	if (iter == m_mapPlayerObjectiveTaskInfo.end())
		return false;
	if (iter->second.find(objectiveID) == iter->second.end())
		return false;
	auto& target = iter->second[objectiveID];
	if (target.oldLock == 1) return true;
	if (target.objectiveDef->Group == 0)//关闭状态
	{
		return false;
	}
	/* DisplayMode = 0或不填
	- 选项卡默认显示，玩家在列表中能直接看到
	DisplayMode = 1
	- 完成所有前置任务后显示（FrontID1、FrontID2、FrontID3）
	- 未达成条件前玩家在列表中无法看见此类目标
	 DisplayMode = 2
	- 完成任意前置任务后显示（FrontID1、FrontID2、FrontID3）
	- 未达成条件前玩家在列表中无法看见此类目标
	 DisplayMode = 3
	- 完成目标内任意任务后显示*/

	if (target.objectiveDef->DisplayMode == 0)
	{
		return true;
	}
	else if (target.objectiveDef->DisplayMode == 1)
	{
		int num = 0;
		int size = GetDefManagerProxy()->getSurviveObjectiveFrontTaskNum(target.objectiveDef->ID);
		for (int i = 0; i < size; i++)
		{
			int id = target.objectiveDef->FrontIDs[i];
			if (id > 0)
			{
				int state = GetTaskState(objId, id);
				//已完成
				if (state == TASK_COMPLETE)
				{
					num++;
				}
			}
		}
		return num >= size;
	}
	else if (target.objectiveDef->DisplayMode == 2)
	{
		int num = 0;
		int size = GetDefManagerProxy()->getSurviveObjectiveFrontTaskNum(target.objectiveDef->ID);
		for (int i = 0; i < size; i++)
		{
			int id = target.objectiveDef->FrontIDs[i];
			if (id > 0)
			{
				int state = GetTaskState(objId, id);
				//已完成
				if (state == TASK_COMPLETE)
				{
					num++;
				}
			}
		}
		return num > 0 || size == 0;
	}
	else if (target.objectiveDef->DisplayMode == 3)
	{
		int num = 0;
		const auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(target.objectiveDef->ID);
		for (int i = 0; i < taskVec.size(); i++)
		{
			int state = GetTaskState(objId, taskVec[i]);
			//已完成
			if (state == TASK_COMPLETE)
			{
				num++;
			}
		}
		return num > 0 || taskVec.size() == 0;
	}
	return false;
}

//解锁
bool TaskSubSystem::IsUnlockObjective(long long objid, int objectiveID)
{
	auto iter = m_mapPlayerObjectiveTaskInfo.find(objid);
	if (iter == m_mapPlayerObjectiveTaskInfo.end())
		return false;
	if (iter->second.find(objectiveID) == iter->second.end())
		return false;
	auto& target = iter->second[objectiveID];
	if (target.oldLock == 1) return true;
	int frontSize = GetDefManagerProxy()->getSurviveObjectiveFrontSize();
	if (target.objectiveDef->Group == 0)//关闭状态
	{
		return false;
	}
	if (target.objectiveDef->UnlockType == 0)
	{
		return true;
	}
	if (target.objectiveDef->UnlockType == 1)
	{
		int num = 0;
		int size = 0;
		for (int i = 0; i < frontSize; i++)
		{
			int id = target.objectiveDef->FrontIDs[i];
			if (id > 0)
			{
				int state = GetTaskState(objid, id);
				//已完成
				if (state == TASK_COMPLETE)
				{
					num++;
				}
				size++;
			}
		}
		return num >= size;
	}
	return false;
}

std::string TaskSubSystem::GetTaskCompleteDate(long long objid, int id)
{
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return std::string();
	auto& achievementInfos = m_mapPlayerTaskInfo[objid];
	if (achievementInfos.find(id) == achievementInfos.end())
		return std::string();
	auto& target = achievementInfos[id];
	jsonxx::Object dateObj;
	dateObj << "mon" << target.completeMonth;
	dateObj << "day" << target.completeDay;
	dateObj << "year" << target.completeYear;
	return dateObj.json_nospace();
}

int TaskSubSystem::GetTaskArryNum(long long objid, int taskID)
{
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return 0;
	auto& achievementInfos = m_mapPlayerTaskInfo[objid];
	if (achievementInfos.find(taskID) == achievementInfos.end())
		return 0;
	return achievementInfos[taskID].arryNum;
}

void TaskSubSystem::GetDirtyTaskUins(std::vector<int>& uinList)
{
	for (size_t i = 0; i < m_DirtyTaskUins.size(); i++)
	{
		uinList.push_back(m_DirtyTaskUins[i]);
	}
}

std::string TaskSubSystem::GetDirtyTaskInfo(int uin)
{
	if (m_mapPlayerTaskInfo.find(uin) == m_mapPlayerTaskInfo.end())
		return "";

	auto& playerAchievement = m_mapPlayerTaskInfo[uin];
	auto iter = playerAchievement.begin();

	jsonxx::Array listInfo;
	while (iter != playerAchievement.end())
	{
		if (!iter->second.taskDef)
		{
			iter++;
			continue;
		}

		int frontSize = GetDefManagerProxy()->getFrontTaskNum(iter->second.taskDef->ID);
		int state = iter->second.taskState;
		if (iter->second.arryNum != 0 || ((state == TASK_UNCOMPLETE && frontSize > 0) || state == TASK_COMPLETE || state == TASK_UNLOCK_UNTASK) || iter->second.rewardState != 0)
		{
			jsonxx::Object info;
			info << "id" << iter->second.taskDef->ID;
			info << "rs" << iter->second.rewardState;
			if (iter->second.rewardState == TASK_RECEIVED)
			{
				info << "y" << iter->second.completeYear;
				info << "m" << iter->second.completeMonth;
				info << "d" << iter->second.completeDay;
			}
			else
			{
				info << "ts" << iter->second.taskState;
				if (iter->second.taskState == TASK_UNCOMPLETE)
				{
					info << "num" << iter->second.arryNum;
				}
			}
	
			listInfo << info;
		}
		iter++;
	}
	if (listInfo.size() <= 0)
		return "";

	jsonxx::Object context;
	context << "info" << listInfo;

	return context.json_nospace();
}

void TaskSubSystem::GetDirtyObjectiveTaskUins(std::vector<int>& uinList)
{
	for (size_t i = 0; i < m_DirtyObjectiveTaskUins.size(); i++)
	{
		uinList.push_back(m_DirtyObjectiveTaskUins[i]);
	}
}

std::string TaskSubSystem::GetDirtyObjectiveTaskInfo(int uin)
{
	if (m_mapPlayerObjectiveTaskInfo.find(uin) == m_mapPlayerObjectiveTaskInfo.end())
		return "";

	auto iter = m_mapPlayerObjectiveTaskInfo[uin].begin();

	jsonxx::Array listInfo;
	while (iter != m_mapPlayerObjectiveTaskInfo[uin].end())
	{
		if (iter->second.objectiveDef && iter->second.rewardState != 0)
		{
			jsonxx::Object info;
			info << "id" << iter->second.objectiveDef->ID;
			info << "rs" << iter->second.rewardState;
			listInfo << info;
		}
		iter++;
	}
	if (listInfo.size() <= 0)
		return "";

	jsonxx::Object context;
	context << "info" << listInfo;
	return context.json_nospace();
}

void TaskSubSystem::ClearDirtyState()
{
	m_DirtyTaskUins.clear();
	m_DirtyObjectiveTaskUins.clear();
}

bool TaskSubSystem::loadTaskInfoByJson(int uin, std::string jsonStr)
{
	if (m_mapPlayerTaskInfo.find(uin) == m_mapPlayerTaskInfo.end())
		return false;

	jsonxx::Object obj;
	bool ret = obj.parse(jsonStr);
	if (!ret)
		return false;

	if (!obj.has<jsonxx::Array>("info"))
		return false;

	jsonxx::Array& msg = (jsonxx::Array&)obj.get<jsonxx::Array>("info");
	for (int i = 0; i < msg.size(); i++)
	{
		auto data = msg.get<jsonxx::Object>(i);

		auto& playerAchie = m_mapPlayerTaskInfo[uin];
		int id = data.get<jsonxx::Number>("id");
		auto& achievementInfo = playerAchie[id];
		achievementInfo.rewardState = data.get<jsonxx::Number>("rs");
		if (achievementInfo.rewardState == TASK_RECEIVED)
		{
			achievementInfo.completeYear = data.get<jsonxx::Number>("y");
			achievementInfo.completeMonth = data.get<jsonxx::Number>("m");
			achievementInfo.completeDay = data.get<jsonxx::Number>("d");
			achievementInfo.taskState = TASK_COMPLETE;
		}
		else
		{
			achievementInfo.taskState = data.get<jsonxx::Number>("ts");
		}

		achievementInfo.arryNum = 0;
		if (achievementInfo.taskState == TASK_COMPLETE)
		{
			if(achievementInfo.taskDef)
				achievementInfo.arryNum = achievementInfo.taskDef->GoalNum;
		}
		else if (data.has<jsonxx::Number>("num"))
		{
			achievementInfo.arryNum = data.get<jsonxx::Number>("num");
		}

		m_mapPlayerDirtyList[uin].insert(id);
	}

	//设置追踪任务
	bool notSetTrack = true;
	auto iter = m_mapPlayerTrackInfo.find(uin);
	if (iter != m_mapPlayerTrackInfo.end() && GetTaskState(uin, iter->second) != TASK_COMPLETE)  //当前追踪的任务是可追踪的
		notSetTrack = false;

	if (notSetTrack)
	{
		for (int i = 0; i < msg.size(); i++)
		{
			auto data = msg.get<jsonxx::Object>(i);
			auto& playerAchie = m_mapPlayerTaskInfo[uin];
			int id = data.get<jsonxx::Number>("id");
			auto& achievementInfo = playerAchie[id];


			if (notSetTrack && achievementInfo.taskState == TASK_COMPLETE && achievementInfo.taskDef && achievementInfo.taskDef->NextTrack > 0) //已完成的设置其追踪任务
			{
				if (GetTaskState(uin, achievementInfo.taskDef->NextTrack) == TASK_UNCOMPLETE)
				{
					SetCurTrackTaskID(uin, achievementInfo.taskDef->NextTrack);
					notSetTrack = false;
					break;
				}
			}
		}

		//没找到合适的追踪任务，把追踪任务清零
		if (notSetTrack && msg.size() > 0)
			SetCurTrackTaskID(uin, 0);
	}

	return true;
}

bool TaskSubSystem::loadObjectiveTaskInfoByJson(int uin, std::string jsonStr)
{
	if (m_mapPlayerObjectiveTaskInfo.find(uin) == m_mapPlayerObjectiveTaskInfo.end())
		return false;

	jsonxx::Object obj;
	bool ret = obj.parse(jsonStr);
	if (!ret)
		return false;

	if (!obj.has<jsonxx::Array>("info"))
		return false;

	jsonxx::Array& msg = (jsonxx::Array&)obj.get<jsonxx::Array>("info");
	for (int i = 0; i < msg.size(); i++)
	{
		auto data = msg.get<jsonxx::Object>(i);

		int id = data.get<jsonxx::Number>("id");
		auto& playerAchie = m_mapPlayerObjectiveTaskInfo[uin];
		auto& achievementInfo = playerAchie[id];
		achievementInfo.rewardState = data.get<jsonxx::Number>("rs");
		//achievementInfo.objectiveDef = GetDefManagerProxy()->getSurviveObjectiveDef(id);
		m_mapPlayerObjectiveDirtyList[uin].insert(id);
	}
	
	return true;
}

//隔帧检测
void TaskSubSystem::InsertCheckDeque(long long objid, int type, int target1, int target2, int goalnum)
{
	TaskDeque taskDeque;
	taskDeque.objid = objid;
	taskDeque.type = type;
	taskDeque.target1 = target1;
	taskDeque.target2 = target2;
	taskDeque.goalnum = goalnum;
	m_checkTaskDeque.push_back(taskDeque);
}
bool TaskSubSystem::CheckDeque(long long objid, int type, int target1, int target2, int goalnum)
{
	if (!GetWorldManagerPtr()) return false;

	IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(objid);
	if (!iplayer) return false;

	//if (!isTestDeveloper && !ModPackMgr::GetInstancePtr()->HasMainTask())
	//{
	//	if (GetWorldManagerPtr()->getWorldId() == NEWBIEWORLDID ||
	//		GetWorldManagerPtr()->getGameMode() != OWTYPE_SINGLE)
	//	{
	//		return false;
	//	}
	//}
	if (type == TASKSYS_NULL || type >= TASKSYS_MAX)
		return false;
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return false;
	//检测指定类型
	int checkRet1 = CheckTask(objid, type, target1, target2, goalnum);
	int checkRet2 = 0;
	if (checkRet1 == 2)
	{
		//检测自动类型
		checkRet2 = CheckTask(objid, TASKSYS_AUTO_FINISH, 0, 0, 1);
	}
	return checkRet1 > 0 || checkRet2 > 0;
}

int	TaskSubSystem::CheckTask(long long objid, int type, int target1, int target2, int goalnum)
{
	if (!GetWorldManagerPtr()) return 0;
	IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(objid);
	if (!iplayer)
		return 0;
	ClientPlayer* player = iplayer->GetPlayer();
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return 0;
	int checkRet = 0;
	auto& taskInfos = m_mapPlayerTaskInfo[objid];
	for (auto iter = taskInfos.begin(); iter != taskInfos.end(); iter++)
	{
		if (iter->second.taskDef == nullptr)
		{
			continue;
		}
		if (TASK_UNCOMPLETE != iter->second.taskState)
		{
			continue;
		}
		if (iter->second.arryNum >= iter->second.taskDef->GoalNum)
		{
			continue;
		}
		if (iter->second.taskDef->Type == type)
		{
			//目标没有解锁
			if (!IsUnlockObjective(objid, iter->second.taskDef->ObjectiveID))
			{
				continue;
			}
			//前置没有完成
			if (!IsTaskFrontAccomplish(objid, *iter->second.taskDef))
			{
				continue;
			}
			bool isMeet = false;
			auto interactionInfo = iter->second.taskDef->GetInteraction();
			auto iter1 = interactionInfo.find(1);
			auto iter2 = interactionInfo.find(2);
			//2个条件都要满足
			if (iter1->second.size() > 0 && iter2->second.size() > 0)
			{
				if (IsInteraction(iter1->second, target1) && IsInteraction(iter2->second, target2))
				{
					isMeet = true;
				}
			}
			else if (iter1->second.size() > 0)
			{
				if (IsInteraction(iter1->second, target1))
				{
					isMeet = true;
				}
			}
			else if (iter2->second.size() > 0)
			{
				if (IsInteraction(iter2->second, target2))
				{
					isMeet = true;
				}
			}
			else
			{
				//不用任何条件
				isMeet = true;
			}
			if (isMeet)
			{
				int frontNum = iter->second.arryNum;
				iter->second.arryNum += goalnum;

				if (checkRet == 0)
					checkRet = 1;

				bool isTaskAccomplish = false;		//任务完成
				bool isObjectiveAccomplish = false;		//目标完成
				if (frontNum < iter->second.taskDef->GoalNum && iter->second.arryNum >= iter->second.taskDef->GoalNum)
				{
					//任务完成
					isTaskAccomplish = true;
					//标记
					checkRet = 2;

					time_t t = time(NULL);
					struct tm* stm = localtime(&t);
					if (stm)
					{
						iter->second.completeYear = stm->tm_year + 1900;
						iter->second.completeMonth = stm->tm_mon + 1;
						iter->second.completeDay = stm->tm_mday;
					}
					//设置成完成
					iter->second.taskState = TASK_COMPLETE;
					iter->second.rewardState = TASK_CAN_RECEIVE;
					iter->second.arryNum = iter->second.taskDef->GoalNum;
					int objectiveID = iter->second.taskDef->ObjectiveID;
					//激活
					auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
					for (int i = 0; i < taskVec.size(); i++)
					{
						if (Activate(objid, taskVec[i]))
						{
							m_mapPlayerDirtyList[objid].insert(taskVec[i]);
						}
					}
					//计算目标
					if (IsObjectiveAccomplish(objid, objectiveID))
					{
						auto& objectiveInfos = m_mapPlayerObjectiveTaskInfo[objid];
						auto tempIter = objectiveInfos.find(objectiveID);
						if (tempIter != objectiveInfos.end())
						{
							isObjectiveAccomplish = true;
							tempIter->second.rewardState = TASK_CAN_RECEIVE;
							m_mapPlayerObjectiveDirtyList[objid].insert(objectiveID);
						}
					}
				}
				if (player == g_pPlayerCtrl)
				{
					GetGameEventQue().postTaskUpdate(iter->second.taskDef->ID);
					if (isTaskAccomplish)
					{
						GetGameEventQue().postTaskComplete(iter->second.taskDef->ID);

						if (iter->second.taskDef->RewardDistributionType == TASK_REWARD_RECEIVE_AUTO)
						{
							GetTaskReward(objid, iter->second.taskDef->ID);
						}
					}
					//计算目标
					if (isObjectiveAccomplish)
					{
						GetGameEventQue().postObjectiveTaskComplete(iter->second.taskDef->ObjectiveID);
					}
				}
				else
				{
					m_mapPlayerDirtyList[objid].insert(iter->first);
				}
				StatisticsOnAchieve(iter->second.taskDef->ID);
			}
		}
	}

	if (checkRet == 0) {
		if (ModPackMgr::GetInstancePtr()->HasMainTask()) {
			if (type == TASKSYS_INTERACT_BLOCK || type == TASKSYS_GAIN_ITEM || type == TASKSYS_CRAFT_ITEM) {
				//这几个任务类型有可能插件需要处理
				jsonxx::Object modHandles;
				modHandles << "type" << type;
				modHandles << "targe1" << target1;
				modHandles << "targe2" << target2;
				modHandles << "playerid" << objid;
				modHandles << "num" << goalnum;
				ObserverEvent obevent;
				obevent.SetData_CustomStr(modHandles.json_nospace());
				GetObserverEventManager().OnTriggerEvent("MainTask.Check", &obevent);
			}
		}
	}

	return checkRet;
}
void TaskSubSystem::StatisticsOnAchieve(int id)
{
	//if (id >= 1032) return;

	//int curid = GetIWorldConfigProxy()->getStatistics("curachieve");
	//int bit = 1 << (id - 1000);

	//if ((curid & bit) == 0)
	//{
	//	curid |= bit;
	//	GetIWorldConfigProxy()->setStatistics("curachieve", curid, true);

	//	char evname[64];
	//	char wminute[64];
	//	sprintf(evname, "Achieve_%d", id);
	//	sprintf(wminute, "%d", GetWorldManagerPtr()->getWorldTime() / (20 * 60)); //当前玩了多少分钟

	//	STATISTICS_INTERFACE_EXEC(gameEvent(evname, "minute", wminute), 0);

	//	char achievementID[64];
	//	sprintf(achievementID, "%d", id);
	//	STATISTICS_INTERFACE_EXEC(gameEvent("AchievementFinish", "AchievementID", achievementID, "minute", wminute), 0);
	//}
}
int TaskSubSystem::GetObjectiveTaskRewardState(long long objid, int objectiveID)
{
	if (m_mapPlayerObjectiveTaskInfo.find(objid) == m_mapPlayerObjectiveTaskInfo.end())
		return -1;
	auto& achievementInfos = m_mapPlayerObjectiveTaskInfo[objid];
	if (achievementInfos.find(objectiveID) == achievementInfos.end())
		return -1;
	auto& target = achievementInfos[objectiveID];
	return target.rewardState;
}

bool	TaskSubSystem::IsDirty()
{
	return m_Dirty;
}
void	TaskSubSystem::SetDirty(bool b)
{
	m_Dirty = b;
}

void TaskSubSystem::GetTaskReward(long long objid, int id)
{
	if (!GetWorldManagerPtr()) return;
	IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(objid);
	if (iplayer == nullptr || iplayer->GetPlayerWorld() == nullptr)
		return;
	ClientPlayer* player = iplayer->GetPlayer();

	if (!player->getWorld()->isRemoteMode())
	{
		int verify = 0;
		if (m_mapPlayerTaskInfo.find(player->getObjId()) == m_mapPlayerTaskInfo.end())
			verify = -1;
		auto& achievementInfos = m_mapPlayerTaskInfo[player->getObjId()];
		if (achievementInfos.find(id) == achievementInfos.end())
			verify = -1;
		auto& target = achievementInfos[id];
		int state = GetTaskRewardState(player->getObjId(), id);
		if (state != TASK_CAN_RECEIVE)
			verify = -1;
		if (target.taskDef == nullptr)
			verify = -1;

		target.rewardRet++;

		static auto taskCallBack = [this](int id, bool ret, ClientPlayer* tempPlayer) -> void {
			//主机自己
			if (tempPlayer == g_pPlayerCtrl)
			{
				GetGameEventQue().postTaskReward(1, id, ret);
			}
			else
			{
				m_mapPlayerDirtyList[tempPlayer->getObjId()].insert(id);
			}
		};
		//失败
		if (verify == -1)
		{
			taskCallBack(id, false, player);
			return;
		}
		auto& itemIds = target.taskDef->RewardID;
		auto& nums = target.taskDef->RewardNum;
		bool isNeedModHandle = false;

		jsonxx::Array modHandleIDs;
		jsonxx::Array modHandleNums;
		for (int i = 0; i < itemIds.size(); ++i) {
			int itemId = itemIds[i];
			int	num = nums[i];

			if (num > 0) {
				if (target.taskDef->SpecialTag || itemId < 0)
				{
					isNeedModHandle = true;
					modHandleIDs << itemId;
					modHandleNums << num;
				}
				else if (itemId > 0) {
					player->socgainItems(itemId, num);
					player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TASKGET, itemId, num);
					//if (player->getBackPack())
					//{
					//	GridCopyData grid_copy_data;
					//	grid_copy_data.resid = itemId;
					//	grid_copy_data.num = num;
					//	int resultReal = player->getBackPack()->addItemWithPickUp_bySocGridCopyData(grid_copy_data);
					//}
				}
				else if (itemId == 0) {//星星
					player->AddStar(num);
				}
			}
		}

		if (isNeedModHandle) {
			jsonxx::Object modHandles;
			modHandles << "ids" << modHandleIDs;
			modHandles << "nums" << modHandleNums;
			modHandles << "playerid" << objid;
			modHandles << "taskid" << id;

			ObserverEvent obevent;
			obevent.SetData_CustomStr(modHandles.json_nospace());
			GetObserverEventManager().OnTriggerEvent("MainTask.Gain", &obevent);
		}

		//变成领取
		target.rewardState = TASK_RECEIVED;
		m_Dirty = true;
		taskCallBack(id, true, player);

		if (!HasDirtyTaskUin(objid))
			m_DirtyTaskUins.push_back(objid);
	}
	else
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", player->getObjId());
		context << "playerid" << objid_str;
		context << "awardid" << id;
		GetSandBoxManager().sendToHost("PB_TASK_REWARD_CH", context.bin(), context.binLen());
	}
}

void TaskSubSystem::GetObjectiveReward(long long objid, int id)
{
	if (!GetWorldManagerPtr()) return;

	IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(objid);
	if (iplayer == nullptr || iplayer->GetPlayerWorld() == nullptr)
		return;
	ClientPlayer* player = iplayer->GetPlayer();
	if (!player->getWorld()->isRemoteMode())
	{
		int verify = 0;
		if (m_mapPlayerObjectiveTaskInfo.find(player->getObjId()) == m_mapPlayerObjectiveTaskInfo.end())
			verify = -1;
		auto& achievementInfos = m_mapPlayerObjectiveTaskInfo[player->getObjId()];
		if (achievementInfos.find(id) == achievementInfos.end())
			verify = -1;
		auto& target = achievementInfos[id];
		int state = GetObjectiveTaskRewardState(player->getObjId(), id);
		if (state != TASK_CAN_RECEIVE)
			verify = -1;
		if (target.objectiveDef == nullptr)
			verify = -1;

		target.rewardRet++;

		static auto objectiveCallBack = [this](int id, bool ret, ClientPlayer* tempPlayer) -> void {
			//主机
			if (tempPlayer == g_pPlayerCtrl)
			{
				GetGameEventQue().postTaskReward(2, id, ret);
			}
			else
			{
				m_mapPlayerObjectiveDirtyList[tempPlayer->getObjId()].insert(id);
			}
		};
		if (verify == -1) //失败了
		{
			objectiveCallBack(id, false, player);
			return;
		}
		int itemId = target.objectiveDef->RewardID;
		int num = target.objectiveDef->RewardNum;
		// 道具
		if (target.objectiveDef->RewardType == 0)
		{
			if (itemId > 0 && num > 0)
			{
				player->socgainItems(itemId, num);
			}
		}
		else if (target.objectiveDef->RewardType == 1)
		{
			if (num > 0)
			{
				player->AddStar(num);
			}
		}
		//变成领取
		target.rewardState = TASK_RECEIVED;
		m_Dirty = true;
		objectiveCallBack(id, true, player);

		if (!HasDirtyObjectiveTaskUin(objid))
		{
			m_DirtyObjectiveTaskUins.push_back(objid);
		}
	}
	else
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", player->getObjId());
		context << "playerid" << objid_str;
		context << "awardid" << id;
		GetSandBoxManager().sendToHost("PB_TASK_OBJECTIVE_REWARD_CH", context.bin(), context.binLen());
	}
	return;
}

void TaskSubSystem::SetCurTrackTaskID(long long objid, int taskID)
{
	if (!GetWorldManagerPtr()) return;

	IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(objid);
	if (iplayer == nullptr || iplayer->GetPlayerWorld() == nullptr)
	{
		return;
	}
	ClientPlayer* player = iplayer->GetPlayer();
	auto iter = m_mapPlayerTrackInfo.find(objid);
	if (iter == m_mapPlayerTrackInfo.end())
		return;
	if (iter->second == taskID)
		return;

	m_mapPlayerTrackInfo[objid] = taskID;
	m_Dirty = true;
	if (player->getWorld()->isRemoteMode())
	{
		m_mapPlayerTrackDirtyList[objid].insert(taskID);
	}
	if (g_pPlayerCtrl == player)
	{
		GetGameEventQue().postTaskTrack(taskID);
	}
}

int TaskSubSystem::GetCurTrackTaskID(long long objid)
{
	if (m_mapPlayerTrackInfo.find(objid) == m_mapPlayerTrackInfo.end())
		return 0;
	return m_mapPlayerTrackInfo[objid];
}

bool	TaskSubSystem::IsTaskFrontAccomplish(long long objid, const SurviveTaskDef taskDef)
{
	//前置完成了
	int num = 0;
	int size = 0;
	auto frontIDs = taskDef.FrontIDs;
	int frontSize = GetDefManagerProxy()->getSurviveTaskFrontSize();
	for (int i = 0; i < frontSize; i++)
	{
		if (frontIDs[i] > 0)
			size++;
	}
	for (int i = 0; i < size; i++)
	{
		if (frontIDs[i] > 0)
		{
			int state = GetTaskState(objid, frontIDs[i]);
			if (state == TASK_COMPLETE)
			{
				num++;
			}
		}
	}
	return num >= size;
}

// 云服数据
bool TaskSubSystem::NeedSave()
{
#ifdef IWORLD_SERVER_BUILD
	// 此模式下只关心存在是否未保存的数据
	return m_uinTaskNeedSave.size() > 0;
#endif
	return false;
}

void TaskSubSystem::OnPlayerLeaveAch(long long uin)
{
#ifdef IWORLD_SERVER_BUILD
	//云服执行的逻辑
	if (NeedSave() && g_WorldMgr)
	{
		SaveCloudWorldATask(g_WorldMgr->getFromWorldID());
	}
	m_mapPlayerTaskInfo.erase(uin);
	m_mapPlayerObjectiveTaskInfo.erase(uin);
	m_mapPlayerTrackInfo.erase(uin);
	LOG_INFO("onPlayerLeaveAch uin=%d", uin);
#endif
}

bool TaskSubSystem::SaveCloudWorldATask(long long owid)
{
#ifdef IWORLD_SERVER_BUILD
	// 云服使用dataserver存取
	{
		if (!g_zmqMgr)
		{
			LOG_WARNING("SaveCloudWorldATask error zmq null");
			return false;
		}
		// 只对数据发生变动的玩家做全量存储
		for (auto dirty_iter = m_uinTaskNeedSave.begin(); dirty_iter != m_uinTaskNeedSave.end(); dirty_iter++)
		{
			auto uin = *dirty_iter;
			auto player = GetWorldManagerPtr()->getPlayerByUin(uin);
			if (!player || player->GetPlayer()->isSimPlayer())
				continue;

			flatbuffers::FlatBufferBuilder builder;
			std::vector<flatbuffers::Offset<FBSave::SurviveTask>> surviveTaskList;
			std::vector<flatbuffers::Offset<FBSave::SurviveObjective>> surviveObjectiveList;
			std::vector<flatbuffers::Offset<FBSave::WorldTrackItem>> trackList;

			auto surviveIter = m_mapPlayerTaskInfo.find(*dirty_iter);
			if (surviveIter != m_mapPlayerTaskInfo.end())
			{
				for (auto iter2 = surviveIter->second.begin(); iter2 != surviveIter->second.end(); iter2++)
				{
					if (iter2->second.arryNum == 0 && iter2->second.taskState == 0 && iter2->second.rewardState == 0)
					{
						continue;
					}
					surviveTaskList.push_back(FBSave::CreateSurviveTask(builder, iter2->second.taskDef->ID,
						iter2->second.arryNum, iter2->second.taskState, iter2->second.rewardState, iter2->second.completeYear,
						iter2->second.completeMonth, iter2->second.completeDay));
				}
			}

			auto objectiveIter = m_mapPlayerObjectiveTaskInfo.find(*dirty_iter);
			if (objectiveIter != m_mapPlayerObjectiveTaskInfo.end())
			{
				for (auto iter2 = objectiveIter->second.begin(); iter2 != objectiveIter->second.end(); iter2++)
				{
					if (iter2->second.rewardState == 0)
					{
						continue;
					}
					surviveObjectiveList.push_back(FBSave::CreateSurviveObjective(builder, iter2->second.objectiveDef->ID,
						iter2->second.rewardState, iter2->second.oldLock));
				}
			}

			auto trackIter = m_mapPlayerTrackInfo.find(*dirty_iter);
			if (trackIter != m_mapPlayerTrackInfo.end())
			{
				trackList.push_back(FBSave::CreateWorldTrackItem(builder, trackIter->second, trackIter->first));
			}

			auto wa = FBSave::CreateRoleWorldTaskSys(builder, builder.CreateVector(surviveTaskList), builder.CreateVector(trackList), builder.CreateVector(surviveObjectiveList));
			builder.Finish(wa);
			g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveTaskSys, builder.GetBufferPointer(), builder.GetSize(), owid, *dirty_iter);
			LOG_INFO("saveCloudWorldATask %lld %d", owid, *dirty_iter);
		}
		m_uinTaskNeedSave.clear();
		return true;
	}
	if (!g_zmqMgr)
	{
		LOG_WARNING("SaveCloudWorldATask error zmq null");
		return false;
	}
#endif
	return false;
}

void TaskSubSystem::OnSaveTaskByUinResp(bool r, long long uin, long long wid)
{
}
void TaskSubSystem::OnLoadRoleWorldTask(long long uin, long long wid, const void* data, int len)
{
	if (len > 0)
	{
		LOG_INFO("OnLoadRoleWorldTask uin = %d, wid = %lld, data len = %d", uin, wid, len);
		LoadRoleWorldTaskFromData(uin, data, len);
	}
}
bool TaskSubSystem::LoadRoleWorldTaskFromData(long long uin, const void* buf, int buflen)
{
	flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
	if (!FBSave::VerifyRoleWorldTaskSysBuffer(verifier))
	{
		LOG_WARNING("LoadRoleWorldTaskFromData verify failed");
		return false;
	}
	const FBSave::RoleWorldTaskSys* wa = FBSave::GetRoleWorldTaskSys(buf);
	if (wa == NULL)
	{
		LOG_WARNING("LoadRoleWorldTaskFromData got NULL");
		return false;
	}

	for (size_t i = 0; i < wa->task()->size(); i++)
	{
		auto a = wa->task()->Get(i);
		if (a)
		{
			const SurviveTaskDef* item_taskdef = GetDefManagerProxy()->getSurviveTaskDef(a->id());
			//任务表被删除过老存档要容错
			if (!item_taskdef) continue;
			auto& taskInfo = m_mapPlayerTaskInfo[uin][a->id()];
			taskInfo.taskState = a->state();
			taskInfo.arryNum = a->arrynum();
			taskInfo.rewardState = a->getaward();
			taskInfo.completeYear = a->completeyear();
			taskInfo.completeMonth = a->completemonth();
			taskInfo.completeDay = a->completeday();
			taskInfo.rewardRet = 0;
			taskInfo.taskDef = item_taskdef;
		}
	}

	for (size_t i = 0; i < wa->objective()->size(); i++)
	{
		auto a = wa->objective()->Get(i);
		if (a)
		{
			const SurviveObjectiveDef* ObjectiveDef = GetDefManagerProxy()->getSurviveObjectiveDef(a->id());
			//任务表被删除过老存档要容错
			if (!ObjectiveDef) continue;
			auto& taskInfo = m_mapPlayerObjectiveTaskInfo[uin][a->id()];
			taskInfo.rewardState = a->getaward();
			taskInfo.objectiveDef = ObjectiveDef;
			taskInfo.rewardRet = 0;
			taskInfo.oldLock = a->oldLock();
		}
	}

	for (size_t i = 0; i < wa->track()->size(); i++)
	{
		auto a = wa->track()->Get(i);
		if (a)
		{
			auto& trackid = m_mapPlayerTrackInfo[a->objid()];
			trackid = a->trackid();
		}
	}
	return true;
}

bool TaskSubSystem::IsObjectiveAccomplish(long long objid, int objectiveID)
{
	//计算目标
	const auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
	return GetObjectiveFinishNum(objid, objectiveID) == taskVec.size();
}

int TaskSubSystem::GetObjectiveFinishNum(long long objid, int objectiveID)
{
	int complishNum = 0;
	const auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
	for (int i = 0; i < taskVec.size(); i++)
	{
		int state = GetTaskState(objid, taskVec[i]);
		if (state == TASK_COMPLETE)
		{
			complishNum++;
		}
	}
	return complishNum;
}

int TaskSubSystem::GetObjectiveTaskMinID(long long objid, int objectiveID)
{
	int min = MAX_MAIN_TASK;
	const auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
	for (int i = 0; i < taskVec.size(); i++)
	{
		int taskID = taskVec[i];
		int state = GetTaskState(objid, taskID);
		if (state < TASK_COMPLETE && state > TASK_LOCK)
		{
			if (min > taskID) min = taskID;
		}
	}
	return min;
}

void TaskSubSystem::LastCheckToGetObjtiveTask(long long objid, int objectiveID)
{
	int min_taskID = MAX_MAIN_TASK;
	const auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
	for (int i = 0; i < taskVec.size(); i++)
	{
		int taskID = taskVec[i];
		int state = GetTaskState(objid, taskID);
		if (state < TASK_COMPLETE && state >= TASK_LOCK)
		{
			if (min_taskID > taskID) min_taskID = taskID;
		}
	}

	int taskState = GetTaskState(objid, min_taskID);
	if (taskState == TASK_COMPLETE)
	{
		m_mapPlayerTrackInfo[objid] = 0;
	}
	else
	{
		m_mapPlayerTrackInfo[objid] = min_taskID;
		if (taskState == TASK_LOCK) this->Activate(objid, min_taskID);
	}
}

bool TaskSubSystem::IsInteraction(std::vector<TaskInteraction> taskInteraction, int target)
{
	for (int i = 0; i < taskInteraction.size(); i++)
	{
		if (taskInteraction[i].type == TASK_MUTUAL_ITEM_G)
		{
			auto itemDef = GetDefManagerProxy()->getItemDef(target);
			if (itemDef && itemDef->ItemGroup == taskInteraction[i].iVal)
			{
				return true;
			}
		}
		else if (taskInteraction[i].iVal == target)
		{
			return true;
		}
	}
	return false;
}

//公共任务
void TaskSubSystem::CheckCommonSyncTask(int type, WCoord trackPos, int target1, int target2, int goalnum, bool disCheck/*=true*/)
{
	////云服主机判断
	//if (GetWorldManagerPtr() && GetWorldManagerPtr()->IsRentServerHost())
	//{
	//	return ;
	//}
	////普通房间判断
	/*if (GetGameNetManagerPtr())
		GetGameNetManagerPtr()->getHostUin();*/
		/*bool isTest = false;
		MINIW::ScriptVM::game()->callFunction("TestDeveloper", ">b", &isTest);
		if (!isTest)*/
	if (!GetWorldManagerPtr()) return;

	if (!isTestDeveloper)
	{
		if (GetWorldManagerPtr()->getWorldId() == NEWBIEWORLDID ||
			!GetWorldManagerPtr()->isSurviveMode() || GetWorldManagerPtr()->isFreeMode())
		{
			return;
		}
	}
	if (type == TASKSYS_NULL || type >= TASKSYS_MAX)
		return;
	if (g_WorldMgr && !g_WorldMgr->isRemote())
	{
		std::vector<IClientPlayer*> players;
		GetWorldManagerPtr()->getAllPlayers(players);
		for (int i = 0; i < players.size(); i++)
		{
			auto player = players[i]->GetPlayer();
			auto objid = player->getObjId();
			if (disCheck)
			{
				if (InTrackingDistance(trackPos, player->getPosition()))
				{
					InsertCheckDeque(objid, type, target1, target2, goalnum);
				}
			}
			else
			{
				InsertCheckDeque(objid, type, target1, target2, goalnum);
			}

		}
	}
}

//GM完成
void	TaskSubSystem::CMDTaskIfo(long long objid, int id)
{
	if (!GetWorldManagerPtr()) return;

	IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(objid);
	if (!iplayer)return;
	ClientPlayer* player = iplayer->GetPlayer();
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return;
	auto& taskInfos = m_mapPlayerTaskInfo[objid];
	auto iter = taskInfos.find(id);
	iter->second.arryNum = 99999;
	m_Dirty = true;
	bool isTaskAccomplish = true;//任务完成
	time_t t = time(NULL);
	struct tm* stm = localtime(&t);
	if (stm)
	{
		iter->second.completeYear = stm->tm_year + 1900;
		iter->second.completeMonth = stm->tm_mon + 1;
		iter->second.completeDay = stm->tm_mday;
	}
	//设置成完成
	iter->second.taskState = TASK_COMPLETE;
	iter->second.rewardState = TASK_CAN_RECEIVE;
	int objectiveID = iter->second.taskDef->ObjectiveID;
	//激活
	auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(objectiveID);
	for (int i = 0; i < taskVec.size(); i++)
	{
		if (Activate(objid, taskVec[i]))
		{
			m_mapPlayerDirtyList[objid].insert(taskVec[i]);
		}
	}
	if (player == g_pPlayerCtrl)
	{
		GetGameEventQue().postTaskUpdate(iter->second.taskDef->ID);
		if (isTaskAccomplish)
		{
			GetGameEventQue().postTaskComplete(iter->second.taskDef->ID);
			if (iter->second.taskDef->RewardDistributionType == TASK_REWARD_RECEIVE_AUTO)
			{
				GetTaskReward(objid, iter->second.taskDef->ID);
			}
		}

		//计算目标
		if (IsObjectiveAccomplish(objid, objectiveID))
		{
			m_mapPlayerObjectiveDirtyList[objid].insert(objectiveID);
			GetGameEventQue().postObjectiveTaskComplete(objectiveID);
		}
	}
	m_mapPlayerDirtyList[objid].insert(iter->first);
}

bool TaskSubSystem::Activate(long long objid, int taskId)
{
	//激活
	if (m_mapPlayerTaskInfo.find(objid) == m_mapPlayerTaskInfo.end())
		return false;
	auto& playerTaskInfo = m_mapPlayerTaskInfo[objid];
	auto iter = playerTaskInfo.find(taskId);
	if (iter == playerTaskInfo.end())
	{
		return false;
	}
	if (GetTaskState(objid, taskId) == TASK_LOCK)
	{
		if (IsTaskFrontAccomplish(objid, *iter->second.taskDef))
		{
			iter->second.taskState = TASK_UNCOMPLETE;
			return true;
		}
	}
	return false;
}

//视距
bool TaskSubSystem::InTrackingDistance(const WCoord& pos1, const WCoord& pos2)
{
	int dx = CoordDivBlock(pos1.x) - CoordDivBlock(pos2.x);
	if (dx < -m_TrackingDistance || dx > m_TrackingDistance)
	{
		return false;
	}
	int dz = CoordDivBlock(pos1.z) - CoordDivBlock(pos2.z);
	if (dz < -m_TrackingDistance || dz > m_TrackingDistance)
	{
		return false;
	}
	return true;
}

int TaskSubSystem::GetNextTrackTaskID(int taskID)
{
	if (g_pPlayerCtrl == nullptr)
		return 0;
	auto objid = g_pPlayerCtrl->getObjId();
	auto playerIter = m_mapPlayerTaskInfo.find(objid);
	if (playerIter == m_mapPlayerTaskInfo.end())
		return 0;

	if (playerIter->second.find(taskID) == playerIter->second.end())
		return 0;
	std::unordered_map<int, int> findTrackMap;//记录

	std::function<int(int)> nextCallBack = [&](int nextTrackId)->int {
		if (findTrackMap.find(nextTrackId) != findTrackMap.end())
		{
			return 0;
		}
		auto iter = playerIter->second.find(nextTrackId);
		if (iter != playerIter->second.end())
		{
			findTrackMap.insert(std::make_pair(nextTrackId, 1));
			auto taskDef = iter->second.taskDef;
			if (taskDef == nullptr)
				return 0;
			bool isFindNextGroup = false;
			if (IsShowObjective(taskDef->ObjectiveID))
			{
				if (GetTaskState(objid, nextTrackId) == TASK_UNCOMPLETE)
				{
					return nextTrackId;
				}
				else
				{
					//优先自己组里从头找一个作为追踪
					auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(taskDef->ObjectiveID);
					for (auto iter = taskVec.begin(); iter != taskVec.end(); iter++)
					{
						if (GetTaskState(objid, *iter) == TASK_UNCOMPLETE)
						{
							return *iter;
						}
					}
					isFindNextGroup = true;
				}
			}
			if (isFindNextGroup)
			{
				auto taskVec = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(taskDef->ObjectiveID);
				if (taskVec.size() > 0)
				{
					//取最后接下一个组
					auto nextIter = playerIter->second.find(taskVec[taskVec.size() - 1]);
					if (nextIter != playerIter->second.end())
					{
						return nextCallBack(nextIter->second.taskDef->NextTrack);
					}
				}
			}
		}
		return 0;
	};
	return nextCallBack(playerIter->second[taskID].taskDef->NextTrack);
}
void TaskSubSystem::OnLeaveWorld(int objid)
{
	auto pPermitsSubSystem = GET_SUB_SYSTEM(PermitsSubSystem);
	if (pPermitsSubSystem && pPermitsSubSystem->isHost(objid))
	{
		m_checkTaskDeque.clear();
	}
}

bool TaskSubSystem::TrySaveAllWorldTask(long long owid, ChunkIOMgr* iomgr)
{
#ifdef IWORLD_SERVER_BUILD
	if (NeedSave())
		return SaveCloudWorldATask(owid);
#else
	if (IsDirty() && iomgr)
		return SaveWorldATask(owid, iomgr);
#endif
	return true;
}

void TaskSubSystem::AddNeedSave(SInt64 uin)
{
	m_uinTaskNeedSave.insert(uin);
}

bool TaskSubSystem::HasDirtyTaskUin(int uin)
{
	for (size_t i = 0; i < m_DirtyTaskUins.size(); i++)
	{
		if (m_DirtyTaskUins[i] == uin)
			return true;
	}

	return false;
}

bool TaskSubSystem::HasDirtyObjectiveTaskUin(int uin)
{
	for (size_t i = 0; i < m_DirtyObjectiveTaskUins.size(); i++)
	{
		if (m_DirtyObjectiveTaskUins[i] == uin)
			return true;
	}

	return false;
}