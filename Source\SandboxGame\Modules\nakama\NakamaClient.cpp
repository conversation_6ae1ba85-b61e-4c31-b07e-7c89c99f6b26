﻿#include "NakamaClient.h"
#include "OgreScriptLuaVM.h"
#include "ScriptVM/LuaCall.h"

#include <ctime>
#include <cstdint>
#include <iomanip>
#include <chrono>

#define NRTERRORCALL(LUAERRORTYPE) [this](const Nakama::NRtError& error) {\
MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiSi",\
    this, (int)LUAERRORTYPE, (int)CallbackError, error.message.c_str(), error.message.length(),(int)error.code);\
    }

#define NERRORCALL(LUAERRORTYPE) [this](const Nakama::NError& error) {\
if (error.code == Nakama::ErrorCode::Unauthenticated)\
_Unauthenticated = true;\
MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiSi", this,\
    (int)LUAERRORTYPE, (int)CallbackError, error.message.c_str(), error.message.length(),(int)error.code);\
    }

#define SUCCESSCALL(LUACODE,MSG) MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",\
this, (int)LUACODE, (int)CallbackOK, MSG.json().c_str(), MSG.json().length());

#define SUCCESSCALLNOMSG(LUACODE)  MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii",\
this, (int)LUACODE, (int)CallbackOK);

#define SUCCESSLOGINCALL [this](Nakama::NSessionPtr Session) {\
    _Session = Session;\
    _Iat = GetIatTime(Session->getAuthToken());\
    std::chrono::milliseconds current_time = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());\
    _StartTime = current_time.count();\
    PrintSessionExpireTime(_StartTime / 1000);\
    LOG_WARNING("GetIatTime %d", _Iat);\
    PrintSessionExpireTime(Session->getExpireTime() / 1000);\
    PrintSessionExpireTime(_Iat / 1000);\
    MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii", this, (int)AuthentMsg, (int)CallbackOK);\
    CrateRTClient();\
}

static const signed char* decodingTable() {
    static const signed char table[256] = {
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63,
        52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1,
        -1,  0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14,
        15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1,
        -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
        41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1
    };
    return table;
}

static std::string base64DecodeUrlToString(const std::string& input) {
    if (input.empty()) {
        return "";
    }

    std::string decoded_str = input;

    // Step 1: 替换 base64url 中的特殊字符
    std::replace(decoded_str.begin(), decoded_str.end(), '-', '+');
    std::replace(decoded_str.begin(), decoded_str.end(), '_', '/');

    // Step 2: 补全为 4 字节对齐的长度，并添加 '=' 填充
    size_t remainder = decoded_str.size() % 4;
    if (remainder > 0) {
        decoded_str.append(4 - remainder, '=');
    }

    // Step 3: 初始化解码表
    const signed char* dec_table = decodingTable();

    // Step 4: 解码逻辑
    std::string result;
    int i = 0;
    unsigned char c[4] = { 0 };

    for (auto it = decoded_str.begin(); it != decoded_str.end(); ++it) {
        unsigned char ch = *it;

        if (ch == '=') {
            c[i++] = 0;
            continue;
        }

        int val = dec_table[ch];
        if (val == -1) {
            throw std::invalid_argument("包含非法 Base64 字符");
        }
        c[i++] = static_cast<unsigned char>(val);

        if (i == 4) {
            unsigned char out[3];
            out[0] = (c[0] << 2) | ((c[1] & 0x30) >> 4);
            out[1] = ((c[1] & 0x0F) << 4) | ((c[2] & 0x3C) >> 2);
            out[2] = ((c[2] & 0x03) << 6) | c[3];

            int output_bytes = 3;
            if (c[2] == 0 && c[3] == 0)
                output_bytes = 1;
            else if (c[3] == 0)
                output_bytes = 2;

            result.append(reinterpret_cast<char*>(out), output_bytes);
            i = 0;
        }
    }

    // 处理最后不足一组的情况
    if (i > 0) {
        while (i < 4) c[i++] = 0;

        unsigned char out[3];
        out[0] = (c[0] << 2) | ((c[1] & 0x30) >> 4);
        out[1] = ((c[1] & 0x0F) << 4) | ((c[2] & 0x3C) >> 2);
        out[2] = ((c[2] & 0x03) << 6) | c[3];

        int output_bytes = (c[2] == 0 ? (c[1] == 0 ? 0 : 1) : 2);
        result.append(reinterpret_cast<char*>(out), output_bytes);
    }

    return result;
}

static std::string jwtUnpack(const std::string &token)
{
    // Hack decode JSON payload from JWT
    // first we narrow down to the segment between the first two '.'
    size_t dotIndex1 = token.find('.');
    if (dotIndex1 != std::string::npos)
    {
        ++dotIndex1;
        size_t dotIndex2 = token.find('.', dotIndex1);

        if (dotIndex2 != std::string::npos)
        {
            std::string payload = token.substr(dotIndex1, dotIndex2 - dotIndex1);

            try
            {
                // the segment is base64 encoded, so decode it...
                std::string json = base64DecodeUrlToString(payload);
                return json;
            }
            catch (const std::exception&)
            {
                return "";
            }
        }
    }

    LOG_WARNING("Could not unpack JWT.");
    return "";
}

static uint64_t GetIatTime(const std::string &token)
{
    std::chrono::milliseconds current_time = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
    std::string json = jwtUnpack(token);
    if (json == "") return current_time.count();

    jsonxx::Object obj;
    obj.parse(json);

    if (!obj.has<jsonxx::Number>("iat")) return current_time.count();

    return ((uint64_t)obj.get<jsonxx::Number>("iat")) * 1000;
}

NakamaClient::NakamaClient(const std::string& serverKey, const std::string& host, int port, bool ssl):
    _Session(nullptr), _ReqSession(false), _Unauthenticated(false), _Iat(0), _StartTime(0)
{
    Nakama::NLogger::initWithConsoleSink(Nakama::NLogLevel::Warn);
    params.serverKey = serverKey;
    params.host = host;
    params.port = port;
    params.ssl = ssl;

    //LOG_WARNING("NakamaClient::NakamaClient %s %s %d %d", host.c_str(), serverKey.c_str(), port,(int)ssl);

    _client = Nakama::createDefaultClient(params);
}

NakamaClient::~NakamaClient()
{
    if (_client) {
        _client->disconnect();
    }
}

void NakamaClient::SetLuaCallback(const std::string& fun)
{
    _LuaCallback = fun;

    //MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]i", this, msgdata.bytes, msgdata.len);
}

void NakamaClient::AuthenticateSteam(const std::string& token, const std::string& username)
{
    _client->authenticateSteam(token, username, true ,{}, SUCCESSLOGINCALL,NERRORCALL(AuthentMsg));
}

void NakamaClient::AuthenticateEmail(const std::string email, const std::string& passwd, const std::string& username, bool create)
{
    _client->authenticateEmail(email, passwd, username, create, {}, SUCCESSLOGINCALL,NERRORCALL(AuthentMsg));
}

void NakamaClient::AuthenticateCustom(const std::string& id, const std::string& username, const std::string& vars)
{
    jsonxx::Object obj;
    obj.parse(vars);
    Nakama::NStringMap varsmap = {};

    //必填
    if (!obj.has<jsonxx::String>("imei"))
    {
        LOG_WARNING("AuthenticateCustom not imei");
        return;
    }
    varsmap["imei"] = obj.get<jsonxx::String>("imei");

    if (obj.has<jsonxx::String>("udid"))
    {
        varsmap["udid"] = obj.get<jsonxx::String>("udid");
    }

    if (obj.has<jsonxx::String>("tenementId"))
    {
        varsmap["tenementId"] = obj.get<jsonxx::String>("tenementId");
    }

    _client->authenticateCustom(id,username,true, varsmap, SUCCESSLOGINCALL, NERRORCALL(AuthentMsg));
}

void NakamaClient::AuthenticateDevice(const std::string& id, const std::string& username)
{
    _client->authenticateDevice(id, username, true, {}, SUCCESSLOGINCALL, NERRORCALL(AuthentMsg));
}

std::string NakamaClient::GetAuthToken()
{
    if (!_Session)
    {
        return "";
    }

    return _Session->getAuthToken();
}

void NakamaClient::CrateRTClient()
{
    _listener.setConnectCallback([this]() {
        LOG_WARNING("setConnectCallback  ");
        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii", 
            this, (int)CrateRTMsg, (int)CallbackOK);
    });

    _listener.setDisconnectCallback([this](const Nakama::NRtClientDisconnectInfo& error) {
        jsonxx::Object obj;
        obj << "code" << (int)error.code;
        obj << "reason" << error.reason;
        obj << "remote" << (int)error.remote;

        LOG_WARNING("Disconnect error code = %d", (int)error.code);
        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
            this, (int)RTDisconnectMsg, (int)CallbackError, obj.json().c_str(), obj.json().length());
    });

    _listener.setErrorCallback([this](const Nakama::NRtError& error) {
        LOG_WARNING("setErrorCallback error code = %d", (int)error.code);
        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS", this, (int)CrateRTMsg, (int)CallbackError, error.message.c_str(), error.message.length()); 
    });

    _listener.setNotificationsCallback([this](const Nakama::NNotificationList& notificationList) {
        jsonxx::Array arr;

        for (Nakama::NNotification n : notificationList.notifications)
        {
            arr.import(NNotification2JsonObj(n));
        }

        SUCCESSCALL(NotificationsMsg, arr);
    });

    _listener.setChannelMessageCallback([this](const Nakama::NChannelMessage& ChannelMessage) {

        jsonxx::Object obj;
        obj << "channelId" << ChannelMessage.channelId;
        obj << "messageId" << ChannelMessage.messageId;
        obj << "code" << ChannelMessage.code;
        obj << "senderId" << ChannelMessage.senderId;
        obj << "username" << ChannelMessage.username;
        obj << "content" << ChannelMessage.content;
        obj << "createTime" << (float)ChannelMessage.createTime;
        obj << "updateTime" << (float)ChannelMessage.updateTime;
        obj << "persistent" << ChannelMessage.persistent;
        obj << "roomName" << ChannelMessage.roomName;
        obj << "groupId" << ChannelMessage.groupId;
        obj << "userIdOne" << ChannelMessage.userIdOne;

        SUCCESSCALL(ChannelMessageMsg, obj);
    });

    _listener.setPartyCallback([this](const Nakama::NParty& Party) {
        jsonxx::Object obj;

        obj << "id" << Party.id;
        obj << "open" << Party.open;
        obj << "maxSize" << Party.maxSize;
        obj << "self" << NUserPresence2JsonObj(Party.self);
        obj << "leader" << NUserPresence2JsonObj(Party.leader);

        jsonxx::Array presences;
        for (const auto& it : Party.presences)
        {
            presences.import(NUserPresence2JsonObj(it));
        }

        obj << "presences" << presences;

        SUCCESSCALL(PartyCallbackMsg, obj);
    });
    _listener.setPartyCloseCallback([this](const Nakama::NPartyClose& PartyClose) {
        jsonxx::Object obj;
        obj << "id" << PartyClose.id;

        SUCCESSCALL(PartyCloseCallbackMsg, obj);
    });
    _listener.setPartyDataCallback([this](const Nakama::NPartyData& PartyData) {
        jsonxx::Object obj;

        obj << "partyId" << PartyData.partyId;
        obj << "presence" << NUserPresence2JsonObj(PartyData.presence);
        obj << "opCode" << PartyData.opCode;
        obj << "data" << PartyData.data;

        SUCCESSCALL(SendPartyDataMsg, obj);
    });
    _listener.setPartyJoinRequestCallback([this](const Nakama::NPartyJoinRequest& PartyJoinRequest) {
        jsonxx::Object obj;
        obj << "partyId" << PartyJoinRequest.partyId;

        jsonxx::Array presences;
        for (const auto& it : PartyJoinRequest.presences)
        {
            presences.import(NUserPresence2JsonObj(it));
        }

        obj << "presences" << presences;

        SUCCESSCALL(PartyJoinRequestCallbackMsg, obj);
    });
    _listener.setPartyLeaderCallback([this](const Nakama::NPartyLeader& PartyLeader) {
        jsonxx::Object obj;

        obj << "partyId" << PartyLeader.partyId;
        obj << "presence" << NUserPresence2JsonObj(PartyLeader.presence);

        SUCCESSCALL(PartyLeaderCallbackMsg, obj);
    });
    _listener.setPartyMatchmakerTicketCallback([this](const Nakama::NPartyMatchmakerTicket& PartyMatchmakerTicket) {
        jsonxx::Object obj;

        obj << "partyId" << PartyMatchmakerTicket.partyId;
        obj << "ticket" << PartyMatchmakerTicket.ticket;

        SUCCESSCALL(PartyMatchmakerTicketCallbackMsg, obj);
    });
    _listener.setPartyPresenceCallback([this](const Nakama::NPartyPresenceEvent& PartyPresenceEvent) {
        jsonxx::Object obj;

        obj << "partyId" << PartyPresenceEvent.partyId;

        jsonxx::Array joins;
        for (const auto& it : PartyPresenceEvent.joins)
        {
            joins.import(NUserPresence2JsonObj(it));
        }

        obj << "joins" << joins;

        jsonxx::Array leaves;
        for (const auto& it : PartyPresenceEvent.leaves)
        {
            leaves.import(NUserPresence2JsonObj(it));
        }

        obj << "leaves" << leaves;

        SUCCESSCALL(PartyPresenceCallbackMsg, obj);
    });

    _rtClient = _client->createRtClient();
    _rtClient->setListener(&_listener);
    _rtClient->connect(_Session, true, Nakama::NRtClientProtocol::Json);
}

void NakamaClient::ConnectRTClient()
{
    _rtClient->connect(_Session, true, Nakama::NRtClientProtocol::Json);
}

void NakamaClient::ListNotifications(int num)
{
    if (!_Session)
    {
        return;
    }

    _client->listNotifications(_Session, num, "", 
        [this](Nakama::NNotificationListPtr notificationList) {

            jsonxx::Array arr;

            for (const Nakama::NNotification& n : notificationList->notifications)
            {
                arr.import(NNotification2JsonObj(n));
            }

            SUCCESSCALL(ListNotificationMsg, arr);
        },
        NERRORCALL(ListNotificationMsg));
}

void NakamaClient::DeleteNotification(const std::string& notificationId)
{
    if (!_Session)
    {
        return;
    }

    std::vector<std::string> notificationIds = { notificationId };
    _client->deleteNotifications(_Session, notificationIds, [this]() {
        SUCCESSCALLNOMSG(DeleteNotificationMsg);
        },
        NERRORCALL(DeleteNotificationMsg));
}

void NakamaClient::GetAccount()
{
    if (!_Session)
    {
        return;
    }

    _client->getAccount(_Session, [this](const Nakama::NAccount& account) {

            jsonxx::Object obj;

            obj << "user" << NUser2JsonObj(account.user);
            obj << "wallet" << account.wallet;
            obj << "email" << account.email;
            //obj << "devices" << account.devices;
            obj << "customId" << account.customId;
            obj << "verifyTime" << account.verifyTime;
            obj << "disableTime" << account.disableTime;

            SUCCESSCALL(AccountMsg, obj);

        },
        NERRORCALL(AccountMsg));
}

void NakamaClient::Rpc(const std::string& id, const std::string& payload)
{
    if (!_rtClient) return;

    _rtClient->rpc(id, payload, [this](const Nakama::NRpc& rpc) {

            jsonxx::Object obj;
            obj << "id" << rpc.id;
            obj << "payload" << rpc.payload;
            obj << "httpKey" << rpc.httpKey;

            SUCCESSCALL(RPCMsg, obj);

        }, 
        NRTERRORCALL(RPCMsg));
}

void NakamaClient::AddFriend(const std::string& id)
{
    if (!_Session)
    {
        return;
    }

    std::vector<std::string> ids = { id };
    _client->addFriends(_Session, ids, {},
        [this]() {
            SUCCESSCALLNOMSG(AddFriendMsg);
        },
        NERRORCALL(AddFriendMsg));
}

void NakamaClient::DeleteFriend(const std::string& id)
{
    if (!_Session)
    {
        return;
    }

    std::vector<std::string> ids = { id };
    _client->deleteFriends(_Session, ids, {},
        [this]() {
            SUCCESSCALLNOMSG(DeleteFriendMsg);
        },
        NERRORCALL(DeleteFriendMsg));
}

void NakamaClient::BlockFriend(const std::string& id)
{
    if (!_Session)
    {
        return;
    }

    std::vector<std::string> ids = { id };
    _client->blockFriends(_Session, ids, {},
        [this]() {
            SUCCESSCALLNOMSG(BlockFriendMsg);
        },
        NERRORCALL(BlockFriendMsg));
}

void NakamaClient::ListFriend(int limit)
{
    if (!_Session)
    {
        return;
    }

    _client->listFriends(
        _Session,
        limit,
        Nakama::NFriend::State::FRIEND,
        "",
        [this](Nakama::NFriendListPtr list){

            jsonxx::Array arr;

            for (const Nakama::NFriend &it : list->friends) {
                jsonxx::Object obj;

                obj << "user" << NUser2JsonObj(it.user);
                obj << "state" << (int)it.state;
                obj << "updateTime" << it.updateTime;

                arr.import(obj);
            }

            SUCCESSCALL(ListFriendMsg, arr);
        },
        NERRORCALL(ListFriendMsg)
    );
}

void NakamaClient::ListFriendRequest(int limit)
{
    if (!_Session)
    {
        return;
    }

    _client->listFriends(
        _Session,
        limit,
        Nakama::NFriend::State::INVITE_RECEIVED,
        "",
        [this](Nakama::NFriendListPtr list) {

            jsonxx::Array arr;

            for (const Nakama::NFriend& it : list->friends) {
                jsonxx::Object obj;

                obj << "user" << NUser2JsonObj(it.user);
                obj << "state" << (int)it.state;
                obj << "updateTime" << it.updateTime;

                arr.import(obj);
            }

            SUCCESSCALL(ListFriendRequestMsg, arr);
        },
        NERRORCALL(ListFriendRequestMsg)
    );
}

void NakamaClient::ListBlockFriend(int limit)
{
    if (!_Session)
    {
        return;
    }

    _client->listFriends(
        _Session,
        limit,
        Nakama::NFriend::State::BLOCKED,
        "",
        [this](Nakama::NFriendListPtr list) {

            jsonxx::Array arr;

            for (const Nakama::NFriend& it : list->friends) {
                jsonxx::Object obj;

                obj << "user" << NUser2JsonObj(it.user);
                obj << "state" << (int)it.state;
                obj << "updateTime" << it.updateTime;

                arr.import(obj);
            }

            SUCCESSCALL(BlockListFriendMsg, arr);
        },
        NERRORCALL(BlockListFriendMsg)
    );
}

void NakamaClient::JoinRoom(const std::string& roomName)
{
    JoinChat(roomName, JoinRoomMsg);
}

void NakamaClient::JoinGroup(const std::string& groupId)
{
    JoinChat(groupId, JoinGroupMsg);
}

void NakamaClient::JoinDirectMessage(const std::string& userId)
{
    JoinChat(userId, JoinDirectMessageMsg);
}

void NakamaClient::WriteChatMessage(const std::string& channelId, const std::string& content)
{
    auto successCallback = [this](const Nakama::NChannelMessageAck& messageAck)
    {
        jsonxx::Object obj;
        obj << "channelId" << messageAck.channelId;
        obj << "messageId" << messageAck.messageId;
        obj << "username" << messageAck.username;
        obj << "code" << messageAck.code;
        obj << "createTime" << (float)messageAck.createTime;
        obj << "updateTime" << (float)messageAck.updateTime;
        obj << "persistent" << messageAck.persistent;
        obj << "roomName" << messageAck.roomName;
        obj << "groupId" << messageAck.groupId;
        obj << "userIdOne" << messageAck.userIdOne;
        obj << "userIdTwo" << messageAck.userIdTwo;

        SUCCESSCALL(WriteChatMessageMsg, obj);
    };

    jsonxx::Object j;
    j << "message" << content;

    _rtClient->writeChatMessage(channelId, j.json(), successCallback, NRTERRORCALL(WriteChatMessageMsg));
}

void NakamaClient::LeaveChat(const std::string& roomName)
{
    _rtClient->leaveChat(roomName);
}

void NakamaClient::ListChannelMessages(const std::string& groupId, int limit, const std::string& cursor, bool forward)
{
    if (!_Session)
    {
        return;
    }

    auto successCallback = [this](Nakama::NChannelMessageListPtr messageList)
    {
        jsonxx::Object obj;
        jsonxx::Array arr;

        for (const Nakama::NChannelMessage &m : messageList->messages)
        {
            jsonxx::Object obj;
            obj << "channelId" << m.channelId;
            obj << "messageId" << m.messageId;
            obj << "code" << m.code;
            obj << "senderId" << m.senderId;
            obj << "username" << m.username;
            obj << "content" << m.content;
            obj << "createTime" << (float)m.createTime;
            obj << "updateTime" << (float)m.updateTime;
            obj << "persistent" << m.persistent;
            obj << "roomName" << m.roomName;
            obj << "groupId" << m.groupId;
            obj << "userIdOne" << m.userIdOne;
            obj << "userIdTwo" << m.userIdTwo;

            arr.import(obj);
        }

        obj << "messages" << arr;
        obj << "nextCursor" << messageList->nextCursor;
        obj << "prevCursor" << messageList->prevCursor;

        SUCCESSCALL(ListChannelMessagesMsg, obj);
    };

    _client->listChannelMessages(_Session, groupId, limit, cursor,forward, successCallback, NERRORCALL(ListChannelMessagesMsg));
}

void NakamaClient::JoinChat(std::string id, int type)
{
    auto successCallback = [this, type](Nakama::NChannelPtr channel)
        {
            jsonxx::Object obj;
            obj << "id" << channel->id;

            jsonxx::Array arr;
            for (const auto& presence : channel->presences) {
                jsonxx::Object obj;
                obj << "userId" << presence.userId;
                obj << "sessionId" << presence.sessionId;
                obj << "username" << presence.username;
                obj << "persistence" << (int)presence.persistence;
                obj << "status" << presence.status;
                arr.import(obj);
            }

            obj << "presences" << arr;
            obj << "self" << channel->self;
            obj << "roomName" << channel->roomName;
            obj << "groupId" << channel->groupId;
            obj << "userIdOne" << channel->userIdOne;

            SUCCESSCALL(type, obj);
        };

    auto errorCallback = [this, id,type](const Nakama::NRtError& error)
        {
            jsonxx::Object obj;
            obj << "roomName" << id;
            obj << "error" << error.message;

            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, type, (int)CallbackError, obj.json().c_str(), obj.json().length());
        };

    Nakama::NChannelType ChannelType = Nakama::NChannelType::ROOM;
    if (type == (int)JoinDirectMessageMsg) {
        ChannelType = Nakama::NChannelType::DIRECT_MESSAGE;
    }

    _rtClient->joinChat(id, ChannelType, false, false, successCallback, errorCallback);
}

void NakamaClient::CreateParty(bool open, int maxPlayers)
{
    if (!_rtClient) return;
    _rtClient->createParty(open, maxPlayers, [this](const Nakama::NParty& Party){
        jsonxx::Object obj;
        obj << "id" << Party.id;
        obj << "open" << Party.open;
        obj << "maxSize" << Party.maxSize;
        obj << "self" << NUserPresence2JsonObj(Party.self);
        obj << "leader" << NUserPresence2JsonObj(Party.leader);
        
        jsonxx::Array presences;
        for (const auto& it : Party.presences)
        {
            presences.import(NUserPresence2JsonObj(it));
        }

        obj << "presences" << presences;

        SUCCESSCALL(CreatePartyMsg, presences);
    },NRTERRORCALL(CreatePartyMsg));
}

void NakamaClient::CloseParty(const std::string& partyId)
{
    if (!_rtClient) return;
    _rtClient->closeParty(partyId, [this]() {SUCCESSCALLNOMSG(ClosePartyMsg);}, NRTERRORCALL(ClosePartyMsg));
}

void NakamaClient::JoinParty(const std::string& partyId)
{
    if (!_rtClient) return;
    _rtClient->joinParty(partyId, [this]() {SUCCESSCALLNOMSG(JoinPartyMsg); }, NRTERRORCALL(JoinPartyMsg));
}

void NakamaClient::LeaveParty(const std::string& partyId)
{
    if (!_rtClient) return;
    _rtClient->leaveParty(partyId, [this]() {SUCCESSCALLNOMSG(LeavePartyMsg); }, NRTERRORCALL(LeavePartyMsg));
}

void NakamaClient::ListPartyJoinRequests(const std::string& partyId)
{
    if (!_rtClient) return;
    _rtClient->listPartyJoinRequests(partyId, [this](const Nakama::NPartyJoinRequest& PartyJoinRequest) {
        jsonxx::Object obj;
        obj << "partyId" << PartyJoinRequest.partyId;

        jsonxx::Array presences;
        for (const auto& it : PartyJoinRequest.presences)
        {
            presences.import(NUserPresence2JsonObj(it));
        }

        obj << "presences" << presences;
        SUCCESSCALL(ListPartyJoinRequestsMsg, presences);
    }, NRTERRORCALL(ListPartyJoinRequestsMsg));
}

void NakamaClient::RemovePartyMember(const std::string& partyId,
    const std::string& userid,
    const std::string& sessionid,
    const std::string& username,
    bool persistence,
    const std::string& status)
{
    if (!_rtClient) return;
    Nakama::NUserPresence arg;
    arg.userId = userid;
    arg.sessionId = sessionid;
    arg.username = username;
    arg.persistence = persistence;
    arg.status = status;

    _rtClient->removePartyMember(partyId, arg, [this]() {SUCCESSCALLNOMSG(RemovePartyMemberMsg); }, NRTERRORCALL(RemovePartyMemberMsg));
}

void NakamaClient::AcceptPartyMember(const std::string& partyId,
    const std::string& userid,
    const std::string& sessionid,
    const std::string& username,
    bool persistence,
    const std::string& status)
{
    if (!_rtClient) return;
    Nakama::NUserPresence arg;
    arg.userId = userid;
    arg.sessionId = sessionid;
    arg.username = username;
    arg.persistence = persistence;
    arg.status = status;

    _rtClient->acceptPartyMember(partyId, arg,[this]() { SUCCESSCALLNOMSG(AcceptPartyMemberMsg); }, NRTERRORCALL(AcceptPartyMemberMsg));
}

void NakamaClient::PromotePartyMember(const std::string& partyId,
    const std::string& userid,
    const std::string& sessionid,
    const std::string& username,
    bool persistence,
    const std::string& status)
{
    if (!_rtClient) return;
    Nakama::NUserPresence arg;
    arg.userId = userid;
    arg.sessionId = sessionid;
    arg.username = username;
    arg.persistence = persistence;
    arg.status = status;

    _rtClient->promotePartyMember(partyId, arg, [this]() { SUCCESSCALLNOMSG(PromotePartyMemberMsg); }, NRTERRORCALL(PromotePartyMemberMsg));
}

void NakamaClient::SendPartyData(const std::string& partyId, long opCode, const std::string& data)
{
    if (!_rtClient) return;
    std::string tem = data.c_str();
    _rtClient->sendPartyData(partyId, opCode, tem);
}

void NakamaClient::Processing()
{
    if (!_client) {
        return;
    }
    _client->tick();

    if (_rtClient) _rtClient->tick();

    std::chrono::milliseconds current_time = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
    if (_Session && (_Session->isExpired(_Iat + 300000 + current_time.count() - _StartTime) || _Unauthenticated) && !_ReqSession)
    {
        _ReqSession = true;
        auto refreshSuccessCallback = [this](Nakama::NSessionPtr session)
            {
                _ReqSession = false;
                _Unauthenticated = false;
                _Session = session;
                _Iat = GetIatTime(session->getAuthToken());
                std::chrono::milliseconds current_time = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
                _StartTime = current_time.count();
                PrintSessionExpireTime(_StartTime / 1000);
                PrintSessionExpireTime(session->getExpireTime() / 1000);
                PrintSessionExpireTime(_Iat / 1000);
                LOG_WARNING("refresh Success");
            };
        
        auto refreshErrorCallback = [this](const Nakama::NError& error)
            {
                _ReqSession = false;
                LOG_WARNING("refresh error");
                //StopProcess();
                //LOG_WARNING("StopProcess");

                MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiSi",
                    this, (int)RestoreSessionMsg, (int)CallbackError, error.message.c_str(), error.message.length(), (int)error.code);
            };

        PrintSessionExpireTime(current_time.count() / 1000);
        _client->authenticateRefresh(_Session, refreshSuccessCallback, refreshErrorCallback);
        LOG_WARNING("restoreSession -----");
        //_Session = Nakama::restoreSession(_Session->getAuthToken(), _Session->getRefreshToken());
    }
}

jsonxx::Object NakamaClient::NUser2JsonObj(const Nakama::NUser& user)
{
    jsonxx::Object ret;

    ret << "id" << user.id;
    ret << "username" << user.username;
    ret << "displayName" << user.displayName;
    ret << "avatarUrl" << user.avatarUrl;
    ret << "lang" << user.lang;
    ret << "location" << user.location;
    ret << "timeZone" << user.timeZone;
    ret << "metadata" << user.metadata;
    ret << "facebookId" << user.facebookId;
    ret << "googleId" << user.googleId;
    ret << "gameCenterId" << user.gameCenterId;
    ret << "appleId" << user.appleId;
    ret << "steamId" << user.steamId;
    ret << "online" << user.online;
    ret << "edgeCount" << user.edgeCount;
    ret << "createdAt" << user.createdAt;
    ret << "updatedAt" << user.updatedAt;

    return ret;
}

jsonxx::Object NakamaClient::NNotification2JsonObj(const Nakama::NNotification& n)
{
    jsonxx::Object itemobj;

    itemobj << "id" << n.id;
    itemobj << "subject" << n.subject;
    itemobj << "content" << n.content;
    itemobj << "code" << n.code;
    itemobj << "senderId" << n.senderId;
    itemobj << "createTime" << n.createTime;
    itemobj << "persistent" << n.persistent;

    return itemobj;
}

jsonxx::Object NakamaClient::NUserPresence2JsonObj(const Nakama::NUserPresence& userpresence)
{
    jsonxx::Object itemobj;

    itemobj << "userId" << userpresence.userId;
    itemobj << "sessionId" << userpresence.sessionId;
    itemobj << "username" << userpresence.username;
    itemobj << "persistence" << (int)userpresence.persistence;
    itemobj << "status" << userpresence.status;

    return itemobj;
}

void NakamaClient::PrintSessionExpireTime(uint64_t raw_time)
{
    struct tm* time_info = localtime((time_t*)&raw_time);

    // 使用 stringstream 构造格式化字符串
    std::ostringstream oss;
    oss << std::setfill('0') << std::setw(4) << (time_info->tm_year + 1900) << "-"
        << std::setfill('0') << std::setw(2) << (time_info->tm_mon + 1) << "-"
        << std::setfill('0') << std::setw(2) << time_info->tm_mday << " "
        << std::setfill('0') << std::setw(2) << time_info->tm_hour << ":"
        << std::setfill('0') << std::setw(2) << time_info->tm_min << ":"
        << std::setfill('0') << std::setw(2) << time_info->tm_sec;

    LOG_WARNING("time = %s ", oss.str().c_str());
}

void NakamaClient::UpdataName(const std::string& Name)
{
    if (!_Session)
        return;

    _client->getAccount(_Session, [this, Name](const Nakama::NAccount& account) {
        _client->updateAccount(_Session,
            account.user.username,
            Name,
            Nakama::opt::nullopt,
            Nakama::opt::nullopt,
            Nakama::opt::nullopt,
            Nakama::opt::nullopt);
        },
        [this](const Nakama::NError& error) {
            if (error.code == Nakama::ErrorCode::Unauthenticated)
                _Unauthenticated = true;
        });
}

bool NakamaClient::IsCreate()
{
    if (!_Session)
        return false;

    return _Session->isCreated();
}

void NakamaClient::StartProcess()
{
    GetSandBoxManager().SetTimer(0, 100, this);
}

void NakamaClient::StopProcess()
{
    GetSandBoxManager().KillTimer(0,this);
}

void NakamaClient::OnTimer(unsigned long dwTimerID)
{
    Processing();
}
