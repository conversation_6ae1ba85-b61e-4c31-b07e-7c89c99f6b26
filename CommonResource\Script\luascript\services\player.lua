if not class then return end
-- player
return class.define('Player', {

	-- 玩家属性枚举(跟开发者端保持一致)
	PLAYERATTR = {
		MAX_HP = 1, -- 最大生命值
		CUR_HP = 2, -- 当前生命值
		HP_RECOVER = 3, -- 生命恢复
		LIFE_NUM = 4, -- 生命数  nowiki
		MAX_HUNGER = 5, -- 最大饥饿值 nowiki
		CUR_HUNGER = 6, -- 当前饥饿值
		MAX_OXYGEN = 7, -- 最大氧气值 nowiki
		CUR_OXYGEN = 8, -- 当前氧气值
		RECOVER_OXYGEN = 9, -- 氧气恢复速度
		WALK_SPEED = 10, -- 移动速度 nowiki
		RUN_SPEED = 11, -- 奔跑速度
		SNEAK_SPEED = 12, -- 潜行速度
		SWIN_SPEED = 13, -- 游泳速度
		JUMP_POWER = 14, -- 跳跃力
		DODGE = 16, -- 闪避率 nowiki
		ATK_MELEE = 17, -- 近战攻击
		ATK_REMOTE = 18, -- 远程攻击
		DEF_MELEE = 19, -- 近战防御
		DEF_REMOTE = 20, -- 远程防御
		DIMENSION = 21, -- 大小
		SCORE = 22, -- 分数
		LEVEL = 23, -- (星星数)等级
	
		--{{ 被策划删减的条目，暂时可以不处理
		WEIGHT = 15, -- 重量
		DEF_CHAOS = 24, -- 混乱防御
		PACK_SIZE = 25, -- 背包空间
		--}}

		CUR_LEVELEXP = 26, --当前经验
		CUR_LEVEL = 27, --当前等级
		
		-- 设置属性
		ENABLE_MOVE = 1, -- 可移动
		ENABLE_PLACEBLOCK = 2, -- 可摆放方块
		ENABLE_OPERATEBLOCK = 4, -- 可操作方块
		ENABLE_DESTROYBLOCK = 8, -- 可破坏方块
		ENABLE_USEITEM = 16, -- 可使用道具
		ENABLE_ATTACK = 32, -- 可攻击
		ENABLE_BEATTACKED = 64, -- 可被攻击
		ENABLE_BEKILLED = 128, -- 可被杀死
		ENABLE_PICKUP = 256, -- 可拾取道具
		ENABLE_DEATHDROPITEM = 512, -- 死亡掉落
		ENABLE_VEHICLEAUTOFORWARD = 1024, -- 载具自动前行
		ENABLE_FORBIFIRE = 2097152, -- 禁止开火（新枪械）
		ENABLE_FORBIDDODGEANIM = 4194304, -- 闪避禁止播放闪避动画

		CUR_STRENGTH = 28, -- 当前体力值
		MAX_STRENGTH = 29,  -- 当前最大体力值
		STRENGTH_RECOVER = 30,  -- 当前体力值恢复速度
		ATK_PHYSICAL = 31, -- 物理攻击  
		ATK_MAGIC = 32, -- 元素攻击
		DEF_PHYSICAL = 33, -- 物理防御
		DEF_MAGIC = 34, -- 元素防御
		CUR_ARMOR = 35, -- 当前护盾值
		MAX_ARMOR = 36, -- 当前最护盾值
		DAMAGED_ZOMBIE = 37, -- 被僵尸攻击受到伤害降低（百分比）
		ATK_FIREARM = 38, -- 枪械攻击加成（百分比）
	},

    constructor = function(self)
        --{{{
        --}}}
		self.DevEventPermits = {}
		self.DevActionPermits = {}

		--注册回调
		SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.UGC.TRIGGER_OPEN_DEVELOPSTORE, function(content)
			if content then
				TriggerShowDeveloperStore(content.bOpen)
			end
		end)
		SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.UGC.TRIGGER_OPEN_DEVELOPSTORE_NEW, function(content)
			if content then
				ShowDeveloperStore(true)
			end
		end)
		SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.UGC.TRIGGER_OPEN_DEVELOPSTORE_TAB, function(content)
			if content then
				ShowDeveloperStoreTab(content.page, content.name)
			end
		end)
    end,
	
	--{{{ 内部函数
	checkDevFunPermits = function(self, eventname , actionid) --检查开发者广告等商业接口是否开启/关闭
		if not eventname then
			return true
		end
		if ns_version and ns_version.Disable_TriggerID and next(ns_version.Disable_TriggerID) and (not next(self.DevEventPermits) and not next(self.DevActionPermits) )then
			local tab = ns_version.Disable_TriggerID
			local relapiid = check_apiid_ver_conditions(tab)
			if relapiid  then
				local eventid = utils.split(tab.ID, ',')
				if eventid then
					for k, v in pairs(eventid) do
						local id = tonumber(v)
						if TriggerFactorToEvent[id] then
							local name = TriggerFactorToEvent[id]
							if type(name) == "table" then
								name = name[1]
							end
							self.DevEventPermits[name] = true
						else
							self.DevActionPermits[id] = true
						end
					end
				end
			end
		end

		if self.DevEventPermits[eventname] and self.DevActionPermits[actionid]  then
			return false
		end
		return true
	end,

	--{{{ 内部函数
	getPlayerByUin = function(self, objid)
		local player
		objid = tonumber(objid)
		if objid then
			if objid == 0 then
				player = CurMainPlayer
			elseif WorldMgr and WorldMgr.getPlayerByUin then
				player = WorldMgr:GetPlayerByUin(objid)
			elseif CurWorld then
				player = GetWorldActorMgr(CurWorld):findPlayerByUin(objid)
			end
		end
		if player then
			return ErrorCode.OK, player
		else
			return ErrorCode.FAILED
		end
	end,

	getStrengthSetting = function (self,player) -- nowiki--
		if not player then
			return false
		end
		local baseSetterMgr = WorldMgr and WorldMgr:getBaseSettingManager() or nil
		if baseSetterMgr then 
			local teamid = player:getTeam()
			return baseSetterMgr:getIsStrengthUsed(teamid,false)
		end

		-- 新冒险改版后就地图会默认转换为体力值所以默认返回true
		return true 
	end,
	
	--}}}

	-- 获取房主uin
	getHostUin = function(self)
		if ns_SRR and ns_SRR.cloud_mode == 1 and zmqMgr_ then
			if ClientCurGame and ClientCurGame.getGameLeaderUin then
				local uin = ClientCurGame:getGameLeaderUin()
				return ErrorCode.OK, uin
			end
		else
			if ClientCurGame and ClientCurGame.getHostUin then
				local uin = ClientCurGame:getHostUin()
				return ErrorCode.OK, uin
			end
		end
		return ErrorCode.FAILED
	end,

	getGameResults = function(self, objid)
		if SSMgrGameMode and SSMgrGameMode:isAdventureMode() then return ErrorCode.FAILED end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local ret = player:getGameResults();
			return ErrorCode.OK, ret; 
		end
		return ErrorCode.FAILED;
	end,		
	
	-- 移除玩家 result 为2 游戏结束
	setGameResults = function(self, objid, result)
		if isTypeError("number",result)  then return ErrorCode.FAILED end
		if SSMgrGameMode and SSMgrGameMode:isAdventureMode() then return ErrorCode.FAILED end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:setGameResults(result);
			-- 如果有队伍要同步队伍的result
			-- local teamid = player:getTeam()
			-- if teamid and teamid > 0 then
			--  	if ClientCurGame then 
            -- 		ClientCurGame:setTeamResults(teamid, result)
            -- 	end
           	-- end
			return ErrorCode.OK; 
		end
		return ErrorCode.FAILED;
	end,	
	
	getGameScore = function(self, objid)
		if SSMgrGameMode and SSMgrGameMode:isAdventureMode() then return ErrorCode.FAILED end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local score = player:getGameScore();
			return ErrorCode.OK, score; 
		end
		return ErrorCode.FAILED;
	end,
	setGameScore = function(self, objid, score)
		if isTypeError("number", score)  then return ErrorCode.FAILED end
		if SSMgrGameMode and SSMgrGameMode:isAdventureMode() then return ErrorCode.FAILED end
		local rt, player = self:getPlayerByUin(objid)
		if player and type(score) == "number" then
			player:setGameScore(score);
			return ErrorCode.OK; 
		end
		return ErrorCode.FAILED;
	end,

	getGameRanking = function(self, objid)
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local ret = player:getGameRanking();
			return ErrorCode.OK, ret; 
		end
		return ErrorCode.FAILED;
	end,
	
	setGameRanking = function(self, objid, rank)
		if isTypeError("number", rank)  then return ErrorCode.FAILED end
		local rt, player = self:getPlayerByUin(objid)
		if player and type(rank) == "number" then
			player:setGameRanking(rank);
			return ErrorCode.OK; 
		end
		return ErrorCode.FAILED;
	end,	

	-- 先前走distance格（distance为负表示向后）
	walkForward = function(self, objid, distance, exid)
		exid = exid or 0 -- 扩充ID
		if _G.isTypeError("number", distance, exid) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:walkForward(distance, exid)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,
	-- 先右走distance格（distance为负表示向左）
	walkStrafing = function(self, objid, distance, exid)
		exid = exid or 0 -- 扩充ID
		if _G.isTypeError("number", distance, exid) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:walkStrafing(distance, exid)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	-- 锁定镜头，先前走distance格（distance为负表示向后）
	walkUp = function(self, objid, distance, exid)
		exid = exid or 0 -- 扩充ID
		if _G.isTypeError("number", distance, exid) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:walkUp(distance, exid)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,
	-- 锁定镜头，先右走distance格（distance为负表示向左）
	walkRight = function(self, objid, distance, exid)
		exid = exid or 0 -- 扩充ID
		if _G.isTypeError("number", distance, exid) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:walkRight(distance, exid)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,
	-- 锁定镜头，沿Y轴向上走distance格（distance为负表示向下）
	walkAlongY = function(self, objid, distance, exid)
		exid = exid or 0 -- 扩充ID
		if _G.isTypeError("number", distance, exid) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			-- player:walkAlongY(distance, exid)
			return ErrorCode.OK
		end	
		return ErrorCode.FAILED
	end,
	gainItems = function(self, objid, itemid, num, prioritytype)

		itemid = tonumber(itemid)
		num = tonumber(num)
		prioritytype = tonumber(prioritytype)
		if _G.isTypeError("number",itemid,num,prioritytype) then
			return ErrorCode.FAILED
		end

        local def = DefMgr:getItemDef(itemid)
        if not def or (def.ScriptMasking and def.ScriptMasking == 1) then  return ErrorCode.FAILED end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local addnum = player:gainItems(itemid, num, prioritytype);
			if addnum >= 0 then
				return ErrorCode.OK, addnum
			end
		end
		return ErrorCode.FAILED;
	end,

	teleportHome = function(self, objid)
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:teleportHome();
			return ErrorCode.OK; 
		end
		return ErrorCode.FAILED;
	end,	

	getCurToolID = function(self, objid)
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local ret = player:getCurToolID();
			return ErrorCode.OK, ret; 
		end
		return ErrorCode.FAILED;
	end,

	getNickname = function(self, objid)
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local name = player:getNickname();
			return ErrorCode.OK, name; 
		end
		return ErrorCode.FAILED;
	end,
	
	removeBackpackItem = function(self, objid, itemid, num)
		if _G.isTypeError("number", itemid, num) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:removeBackpackItem(itemid, num);
			return ErrorCode.OK; 
		end
		return ErrorCode.FAILED;
	end,
	
	getDieTimes = function(self, objid)
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local ret = player:getDieTimes();
			return ErrorCode.OK, ret; 
		end
		return ErrorCode.FAILED;
	end,
	
	getLeftLifeNum = function(self, objid)
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local ret = player:getLeftLifeNum();
			return ErrorCode.OK, ret; 
		end
		return ErrorCode.FAILED;
	end,
	
	setTeam = function(self, objid, teamid,resetAttr)
		if _G.isTypeError("number", teamid) then
			return ErrorCode.FAILED
		end
		if SSMgrGameMode and SSMgrGameMode:isAdventureMode() then return ErrorCode.FAILED end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			if resetAttr == nil then  resetAttr = true end
			player:setTeam(teamid,resetAttr)
			return ErrorCode.OK
		end
		--[[if CurWorld then
			local player = GetWorldActorMgr(CurWorld):findPlayerByUin(objid);
			if player then
				player:setTeam(teamid)
				return ErrorCode.OK
			end
		elseif true then
			-- 游戏没有创建的时候，CurWorld不存在
			local player = GetGameInfo():GetHostPlayer()
			if player then
				player:setTeam(teamid)
				return ErrorCode.OK
			end
		end]]
		return ErrorCode.FAILED
	end,
	
	getTeam = function(self, objid)
		if SSMgrGameMode and SSMgrGameMode:isAdventureMode() then return ErrorCode.FAILED end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local ret = player:getTeam();
			return ErrorCode.OK, ret; 
		end
		return ErrorCode.FAILED;
	end,

	-- 是否是本地玩家
	isMainPlayer = function(self, objid)
		if CurMainPlayer ~= nil then
			if CurMainPlayer:getUin() == objid then
				return ErrorCode.OK
			end
		end
		return ErrorCode.FAILED
	end,

	-- 获取本地玩家的uin
	getMainPlayerUin = function()
		if CurMainPlayer ~= nil then
			local uin = CurMainPlayer:getUin()
			return ErrorCode.OK, uin
		end
		return ErrorCode.FAILED
	end,

	--for minicode
	setRoleInfoForEducationMapCalledUin = nil,
	setMainPlayerUin = function(self, uin, token, sign)
		if not isEducationalVersion then
			return ErrorCode.FAILED;
		end
		MiniCodeUtils:Log("zuoyou***, setMainPlayerUin begin", tostring(uin), tostring(token), tostring(sign))
		if SlsLog then 
			SlsLog:logStart("lite_loading", "lite_login", {uin = uin});
		end
		if MCodeWorldStatus then
			MCodeWorldStatus:playerBeginLogin();
		end
		if not ClientMgr or not ClientMgr:isEducationLiteGame() then
			LuaInterface:log("zuoyou***, setMainPlayerUin eror")
			return ErrorCode.FAILED;
		end
	
		local conn = _G.container.conn
		conn.authed = true
		conn.token = token
		conn.sign = sign
		reset_login_sign();
		get_login_sign();    -- 这个函数里会设置 g_login_sign, g_login_s2t, g_login_pure_s2t


		--资源中心总库初始化
		if not self.setRoleInfoForEducationMapCalledUin or self.setRoleInfoForEducationMapCalledUin ~= uin then
			self.setRoleInfoForEducationMapCalledUin = uin;
			-- setRoleInfoForEducationMap暂时为空方法，如果没问题就不需要了
			LuaInterface:setRoleInfoForEducationMap(1, '', 4, uin, AccountManager:getCurWorldId());
			ResourceCenterInitResLib();
		end
		threadpool:work(function()
				if _G.minicode_requestEnterGame2New_once or AccountManager:requestEnterGame2New() then  
					_G.minicode_requestEnterGame2New_once = true;
					local code, extraArcvNum = AccountManager:setAccountAuth_MiniCode(uin, token);
					if code ~= ErrorCode.OK then
						MiniCodeUtils:Log("zuoyou****setAccountAuth_MiniCode, login failed!!", code, extraArcvNum, Model, SkinID);
                        -- 登录失败直接提示退出
						MsgApi.loginFailed();
						MsgApi.mapLoadedFail({type = 2, msg = "账号验证失败，请重试", sceneInfo = MCodeManager:getSceneInfo()});
						return;
					end
					-- MiniCodeUtils:Log("zuoyou***, setMainPlayerUin begin2", code, extraArcvNum);
					-- 不管成功与否都用minicode获取的认证信息
					conn.authed = true
					conn.token = token
					conn.sign = sign
					reset_login_sign();
					get_login_sign();    -- 这个函数里会设置 g_login_sign, g_login_s2t, g_login_pure_s2t
					
					--pyf 因为调用了 LuaInterface:setRoleInfoForEducationMap 这里不用调了， 解决进入一个社区作品开枪，会直接秒掉自己的问题（初始化clientplayer用的 作品原作者的uin,
					--这里设置的是当前用户uin, 两个uin不相同，会造成ProjectileLocoMotion::projectilePickAll 取子弹目标时不能排除自己（clientplayer））。
					-- if CurMainPlayer ~= nil then
					-- 	CurMainPlayer:setUin(uin);
					-- end
					WWW_get_cf_info();  --拉取config文件列表，只拉取一次
					local uin = AccountManager:getUin();
					local nickName = AccountManager:getNickName();

					local Model = 1;
					local SkinID = 4;
					if MCodeManager._roleInfoWhenOnAccountDirty and next(MCodeManager._roleInfoWhenOnAccountDirty) then
						Model = MCodeManager._roleInfoWhenOnAccountDirty.Model;
						SkinID = MCodeManager._roleInfoWhenOnAccountDirty.RoleSkinID;
					end
					MiniCodeUtils:Log("pyf test setAccountAuth_MiniCode", code, extraArcvNum, Model, SkinID);
					LuaInterface:setRoleInfoForEducationMap(Model, nickName, SkinID, uin, AccountManager:getCurWorldId());


					if MCodeMgr then
						local tExtraArcvNumInfo = {0, uin, extraArcvNum};
						MCodeMgr:miniCodeCallBack(-17, JSON:encode(tExtraArcvNumInfo));
					end
					if SlsLog then 
						SlsLog:logSuccess("lite_loading", "lite_login", {loginCode = code, extraArcvNum= extraArcvNum});
					end
					if MCodeWorldStatus then
						MCodeWorldStatus:playerLoginSuccess();
					end
					-- ShopInit();
				end
			end
		);
		
		return ErrorCode.OK
	end,
	--for minicode
	setRoleInfoForEducationMap = function(self, modle, nickname, skinid, uin, wid)	
		LuaInterface:setRoleInfoForEducationMap(modle, nickname, skinid, uin, wid)
		return ErrorCode.OK
	end,

	--设置玩家饱食度
	getFoodLevel = function(self, objid)
		local rt, player = self:getPlayerByUin(objid)
		if player and player.getPlayerAttrib then
			local strength = self:getStrengthSetting(player)
			if strength then --地图设置为使用体力值则返回
				return ErrorCode.FAILED
			end
			local value = player:getPlayerAttrib():getFoodLevel();  
			return ErrorCode.OK, value; 
		end
		return ErrorCode.FAILED;
	end,
	--设置玩家饱食度
	setFoodLevel = function(self, objid, foodLevel)
		if _G.isTypeError("number", foodLevel) then
			return ErrorCode.FAILED
		end

		local rt, player = self:getPlayerByUin(objid)
		if player and player.getPlayerAttrib then
			local strength = self:getStrengthSetting(player)
			if strength then --地图设置为使用体力值则返回
				return ErrorCode.FAILED
			end
			player:getPlayerAttrib():setFoodLevel(foodLevel);  
			return ErrorCode.OK;
		end
		return ErrorCode.FAILED;
	end,
	--获取玩家当前快捷栏
	getCurShotcut = function(self, objid)
		local rt, player = self:getPlayerByUin(objid)
		if player and player.getPlayerAttrib then
			local scutIdx = player:getPlayerAttrib():getCurShotcut();  
			return ErrorCode.OK, scutIdx;
		end
		return ErrorCode.FAILED, 0;
	end,
	--设置玩家的道具消耗
	onCurToolUsed = function(self, objid, num)
		num = tonumber(num)
		if _G.isTypeError("number",num) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player and player.getPlayerAttrib then
			player:getPlayerAttrib():onCurToolUsed(num);  
			return ErrorCode.OK;
		end
		return ErrorCode.FAILED;
	end,
	--设置CD
	setSkillCD = function(self, objid, itemid, cd)
		if _G.isTypeError("number",itemid,cd) then
			return ErrorCode.FAILED
		end

		if not cd then
			-- 使用默认值
			local toolDef = DefMgr:getToolDef(itemid)
			if not toolDef then
				return ErrorCode.FAILED
			end

			cd = toolDef.SkillCD
		end

		local rt, player = self:getPlayerByUin(objid)
		if player then
			local mgr = nil
			if TriggerScriptMgr then
				local mgr = TriggerScriptMgr:GetSingletonPtr():GetGlobalDataMgr()
				if mgr then
					mgr:setItemCdForTrigger(objid, itemid, cd)
				end
			end
			return ErrorCode.OK;
		end
		return ErrorCode.FAILED;
	end,
	--复活玩家到指定点
	reviveToPos = function(self, objid, x, y, z)
		if _G.isTypeError("number", x, y, z) then
			return ErrorCode.FAILED
		end
		x = x * 100.0 + 50;
		y = y * 100.0
		z = z * 100.0 + 50;
		local rt, player = self:getPlayerByUin(objid)
		if player then
			if player:revive(0, x, y, z) then
				return ErrorCode.OK;
			else
				return ErrorCode.FAILED;
			end
		end
		return ErrorCode.FAILED;
	end,
	--改变玩家复活点位置
	setRevivePoint = function(self, objid, x, y, z)
		if _G.isTypeError("number", x, y, z) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:setRevivePoint(x, y, z)
			return ErrorCode.OK;
		end
		return ErrorCode.FAILED;
	end,

	--登上、脱离载具
	mountActor = function(self, playerid, objid, posindex,bctrl)
		if _G.isTypeError("number", objid, posindex) then
			return ErrorCode.FAILED
		end
		bctrl = bctrl or false;
		local rt, player = self:getPlayerByUin(playerid);
		if player then
			player:mountActorForTrigger(objid, posindex,bctrl);
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

	-- 播放动作
	-- param : objid : number : 对象ID
	-- param : actid : number : 动作ID
	-- return : ret : number : ErrorCode.OK
	playAct = function(self, objid, actid)
		if _G.isTypeError("number", actid) then
			return ErrorCode.FAILED
		end
		local def = DefMgr:getTriggerActDef(actid)
		if not def then
			return ErrorCode.FAILED
		end
		local tdata = {
			objid = objid,
			actid = actid,
		}
		return ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_PLAY_ACT, tdata)
	end,

	notifyGameInfo2Self = function(self, objid, info)
		local rt, player = self:getPlayerByUin(objid)
		if player and info and type(info) == 'string' then
			player:notifyGameInfo2Self(1, 0, 0, info)
			return ErrorCode.OK;
		end
		return ErrorCode.FAILED;
	end,

	useItem = function(self, objid, itemid, status, onshift)
		if _G.isTypeError("number", itemid) then
			return ErrorCode.FAILED
		end
		onshift = onshift or false
		local rt, player = self:getPlayerByUin(objid)
		if player then
			if not status then
				local def = DefMgr:getItemDef(itemid)
				if def then
					if def.UseTarget == ITEM_USE_PRESSFOOD then
						status = 1
					elseif def.UseTarget == ITEM_USE_GUN then
						status = 0
					end
				end
			end
			status = status or 0
			if player:useItem(itemid, status, onshift) then
				return ErrorCode.OK;
			end
		end
		return ErrorCode.FAILED;
	end,

	rotateCamera = function(self, objid, yaw, pitch)
		local rt, player = self:getPlayerByUin(objid)
		yaw = tonumber(yaw)
		pitch = tonumber(pitch)
		if player and yaw and pitch then
			player:rotateCamera(yaw, pitch)
			return ErrorCode.OK;
		end
		return ErrorCode.FAILED;
	end,

	rotateCameraToActor = function(self, objid, targetid)
		if CurWorld then  
			local rt, player = self:getPlayerByUin(objid)
			if player then
				local playerControl = tolua.cast(player,"PlayerControl")
				if playerControl then 
					local target = GetWorldActorMgr(CurWorld):findActorByWID(targetid)
					if target then 
						local pos = target:getPosition()
						playerControl:setCameraRotate(0, pos.x, pos.y, pos.z)
						return ErrorCode.OK
					end
				end
			end
		end
		return ErrorCode.FAILED
	end,	

	changeViewMode = function(self, objid, viewmodel, islock)
		if _G.isTypeError("number", viewmodel) or _G.isTypeError("boolean", islock) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			player:changeViewMode(viewmodel, islock)
			return ErrorCode.OK;
		end
		return ErrorCode.FAILED;
	end,

	setActionAttrState = function(self, objid, actionattr, switch)
		if _G.isTypeError("number", actionattr) or _G.isTypeError("boolean", switch) then
			return ErrorCode.FAILED
		end
		local rt, player = self:getPlayerByUin(objid)
		if player then
			local tdata ={}
			tdata[1] = 'actorattr'
			tdata[2] = {id = objid,attr = actionattr,switch = switch}
			local ret = ScriptSupportTask:sendTaskToPlayer(0, SSTASKID.TRIGGERSTATEATTR, tdata)
			if ret then
				return ErrorCode.OK
			end
		end
		return ErrorCode.FAILED;
	end,

	checkActionAttrState = function(self, objid, actionattr)
		if _G.isTypeError("number", actionattr) then
			return ErrorCode.FAILED
		end
		local ret, player = self:getPlayerByUin(objid)
		if player then
			local isTrue = false
			if ActorComponentCallModule then
				isTrue = ActorComponentCallModule(player,"ActionAttrStateComponent","checkActionAttrState",actionattr)
			else
				isTrue = player:checkActionAttrState(actionattr)
			end
			if isTrue then
				return ErrorCode.OK
			end
		end

		return ErrorCode.FAILED
	end,

	--玩家是否装备了某个装备
	isEquipByResID = function(self, objid, resid)
		if _G.isTypeError("number", resid) then
			return ErrorCode.FAILED
		end
		local ret, player = self:getPlayerByUin(objid)
		if player and player.isEquipByResID then
			if player:isEquipByResID(resid) then
				return ErrorCode.OK
			end
		end

		return ErrorCode.FAILED
	end,

	--获取玩家属性
	getAtt = function(self, objid, atttype)
		if _G.isTypeError("number",atttype) then
			return ErrorCode.FAILED
		end
		local ret, player = self:getPlayerByUin(objid)
		if ret == ErrorCode.OK and player then
			local att = player:getAttrib()

			if atttype == self.PLAYERATTR.MAX_HP then -- 最大生命值
				return ret, att:getBasicMaxHP()
			elseif atttype == self.PLAYERATTR.CUR_HP then -- 当前生命值
				return ret, att:getHP()
			elseif atttype == self.PLAYERATTR.HP_RECOVER then -- HP恢复
				return ret, att:getHPRecover()
				--[[
			elseif atttype == self.PLAYERATTR.MAX_HUNGER then -- 最大饥饿度
				local attmob = player:getMobAttrib()
				if attmob then
					return ret, attmob:getMaxFood()
				end
				]]
			elseif atttype == self.PLAYERATTR.CUR_HUNGER then -- 当前饥饿度
				local playerAtt = player:getPlayerAttrib()
                if playerAtt then
					
					local strength = self:getStrengthSetting(player)
					if strength then --地图设置为使用体力值则返回
						return ErrorCode.FAILED
					end

					return ret, playerAtt:getFoodLevel()
				end
				--[[
			elseif atttype == self.PLAYERATTR.MAX_OXYGEN then -- 最大氧气值
				local attliving = player:getLivingAttrib()
				if attliving then
					return ret, attliving:getMaxOxygen()
				end
				]]
			elseif atttype == self.PLAYERATTR.CUR_OXYGEN then-- 当前氧气值
				local livingAtt = player:getLivingAttrib()
				if livingAtt then
					return ret, livingAtt:getOxygen()
				end
			elseif atttype == self.PLAYERATTR.WALK_SPEED then -- 移动速度
				return ret, att:getSpeedAtt(Actor_Walk_Speed)
			elseif atttype == self.PLAYERATTR.RUN_SPEED then -- 奔跑速度
				return ret, att:getSpeedAtt(Actor_Run_Speed)
			elseif atttype == self.PLAYERATTR.SWIN_SPEED then -- 游泳速度
				return ret, att:getSpeedAtt(Actor_Swim_Speed)
			elseif atttype == self.PLAYERATTR.JUMP_POWER then -- 跳跃力
				local locomotion = tolua.cast(player:getLocoMotion(),"PlayerLocoMotion") 
				if locomotion then
					return ret,locomotion:getJumpHeight()
				end
				return ret, att:getSpeedAtt(Actor_Jump_Speed)
			elseif atttype == self.PLAYERATTR.SNEAK_SPEED then -- 潜行速度
				return ret, att:getSpeedAtt(Actor_Sneak_Speed)
			elseif atttype == self.PLAYERATTR.DODGE then -- 闪避率
				local val = player:getAiInvulnerableProb()
				return ret, val < 0 and 0 or val
			elseif atttype == self.PLAYERATTR.ATK_MELEE then -- 近战攻击
				local livingAtt = player:getLivingAttrib()
				if livingAtt then
					return ret, livingAtt:getAttackBaseLua(0)
				end
			elseif atttype == self.PLAYERATTR.ATK_REMOTE then -- 远程攻击
				local livingAtt = player:getLivingAttrib()
				if livingAtt then
					return ret, livingAtt:getAttackBaseLua(1)
				end
			elseif atttype == self.PLAYERATTR.DEF_MELEE then -- 近战防御
				local livingAtt = player:getLivingAttrib()
				if livingAtt then
					return ret, livingAtt:getArmorBaseLua(0)
				end
			elseif atttype == self.PLAYERATTR.DEF_REMOTE then -- 远程防御
				local livingAtt = player:getLivingAttrib()
				if livingAtt then
					return ret, livingAtt:getArmorBaseLua(1)
				end
			elseif atttype == self.PLAYERATTR.LEVEL then -- 星星数
				local playerAtt = player:getPlayerAttrib()
                if playerAtt then
					return ret, math.ceil(playerAtt:getExp()/100)
				end
			elseif atttype == self.PLAYERATTR.DIMENSION then -- 模型大小
            	return ret, player:getCustomScale()
            elseif atttype == self.PLAYERATTR.SCORE then
            	return self:getGameScore(objid)
			elseif atttype == self.PLAYERATTR.CUR_LEVELEXP then	--当前经验
            	local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	return ret, playerAtt:getCurLevelExp();
				end
			elseif atttype == self.PLAYERATTR.CUR_LEVEL then	--当前等级
            	local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	return ret, playerAtt:getCurLevel();
				end
			elseif atttype == self.PLAYERATTR.CUR_STRENGTH then	--当前体力值
            	local playerAtt = player:getPlayerAttrib()
                if playerAtt then
					local strength = self:getStrengthSetting(player)
					if not strength then --地图设置为使用体力值则返回
						return ErrorCode.FAILED
					end
                	return ret, playerAtt:getStrength();
				end
			elseif atttype == self.PLAYERATTR.MAX_STRENGTH then	--最大体力值
            	local playerAtt = player:getPlayerAttrib()
                if playerAtt then
					local strength = self:getStrengthSetting(player)
					if not strength then --地图设置为使用体力值则返回
						return ErrorCode.FAILED
					end
                	return ret, playerAtt:getBasicMaxStrength();
				end
			elseif atttype == self.PLAYERATTR.STRENGTH_RECOVER then	--体力值的恢复速度
            	local playerAtt = player:getPlayerAttrib()
                if playerAtt then
					local strength = self:getStrengthSetting(player)
					if not strength then --地图设置为使用体力值则返回
						return ErrorCode.FAILED
					end
                	return ret, playerAtt:getBasicStrengthRestore();
				end
			elseif atttype == self.PLAYERATTR.ATK_PHYSICAL then -- 物理攻击
				local playerAtt = player:getPlayerAttrib()
				if playerAtt then
					return ret, playerAtt:getPlayerBaseAttr(PATTR_ATTACKPHY)
				end
			elseif atttype == self.PLAYERATTR.ATK_MAGIC then -- 元素攻击
				local playerAtt = player:getPlayerAttrib()
				if playerAtt then
					return ret, playerAtt:getPlayerBaseAttr(PATTR_ATTACKELEM)
				end
			elseif atttype == self.PLAYERATTR.DEF_PHYSICAL then -- 物理防御
				local playerAtt = player:getPlayerAttrib()
				if playerAtt then
					return ret, playerAtt:getPlayerBaseAttr(PATTR_DEFPHY)
				end
			elseif atttype == self.PLAYERATTR.DEF_MAGIC then -- 元素防御
				local playerAtt = player:getPlayerAttrib()
				if playerAtt then
					return ret, playerAtt:getPlayerBaseAttr(PATTR_DEFELEM)
				end
			elseif atttype == self.PLAYERATTR.CUR_ARMOR then -- 护盾值
				return ret, att:getArmor()
			elseif atttype == self.PLAYERATTR.MAX_ARMOR then -- 最大护盾值
				return ret, att:getMaxArmor()
			elseif atttype == self.PLAYERATTR.DAMAGED_ZOMBIE then -- 受僵尸伤害降低
				local livingAtt = player:getLivingAttrib()
				if livingAtt then
					return ret, livingAtt:getModAttrib(MODATTRIB_TYPE.MOBATTR_DAMAGED_ZOMBIE)
				end
			elseif atttype == self.PLAYERATTR.ATK_FIREARM then -- 枪械伤害加成
				local livingAtt = player:getLivingAttrib()
				if livingAtt then
					return ret, att:getModAttrib(MODATTRIB_TYPE.MOBATTR_ATTACK_GUN)
				end
			end
		end

		return ErrorCode.FAILED
	end,

	--设置玩家属性
    setAtt = function(self, objid, val, atttype)
		if _G.isTypeError("number",atttype,val) then
			return ErrorCode.FAILED
		end
        local ret, player = self:getPlayerByUin(objid)
		if player then
            local att = player:getAttrib()
            if not att then return ErrorCode.FAILED end

			if atttype == self.PLAYERATTR.MAX_HP then -- 最大生命值
				val = math.max(val, 0)
                att:setBasicMaxHP(val)
				-- --此时如果当前血量大于最大血量时，设置成最最大生命值(先不改)
				-- if att:getHP() > val then
				-- 	att:setHpForTrigger(val)
				-- end
                return ret
			elseif atttype == self.PLAYERATTR.CUR_HP then -- 当前生命值
				val = math.max(val, 0)
				--att:setHP(val)
				att:setHpForTrigger(val);
                return ret
            elseif atttype == self.PLAYERATTR.HP_RECOVER then -- HP恢复
				val = math.max(val, 0)
                att:setHPRecover(val)
                return ret
                --[[
            elseif atttype == self.PLAYERATTR.MAX_HUNGER then -- 最大饥饿度
            	return ret
            	]]
            elseif atttype == self.PLAYERATTR.CUR_HUNGER then -- 当前饥饿度
				val = math.max(val, 0)
                local playerAtt = player:getPlayerAttrib()
                if playerAtt then
					local strength = self:getStrengthSetting(player)
					if strength then --地图设置为使用体力值则返回
						return ErrorCode.FAILED
					end
					playerAtt:setFoodLevel(val)
                    return ret
                end
                --[[
            elseif atttype == self.PLAYERATTR.MAX_OXYGEN then -- 最大氧气值
                return ret
                ]]
            elseif atttype == self.PLAYERATTR.CUR_OXYGEN then-- 当前氧气值
				val = math.max(val, 0)
                local livingAtt = player:getLivingAttrib()
                if livingAtt then
                    livingAtt:setOxygen(val)
                    return ret
                end
            elseif atttype == self.PLAYERATTR.WALK_SPEED then -- 移动速度
				--这个接口会改变玩家的基础移动速度，退出游戏再进还是这个速度
				-- if(val < 5) then
				-- 	MiniLog("val:", val)
				-- 	MiniLog("BryanTest traceback:", debug.traceback())
				-- end
                att:setSpeedAtt(Actor_Walk_Speed, val)
                return ret
            elseif atttype == self.PLAYERATTR.RUN_SPEED then -- 奔跑速度
            	att:setSpeedAtt(Actor_Run_Speed, val)
                return ret
            elseif atttype == self.PLAYERATTR.SWIN_SPEED then -- 游泳速度
                att:setSpeedAtt(Actor_Swim_Speed, val)
                return ret
            elseif atttype == self.PLAYERATTR.JUMP_POWER then -- 跳跃力
                att:setSpeedAtt(Actor_Jump_Speed, val)
                return ret
            elseif atttype == self.PLAYERATTR.SNEAK_SPEED then -- 潜行速度
                att:setSpeedAtt(Actor_Sneak_Speed, val)
                return ret
            elseif atttype == self.PLAYERATTR.DODGE then -- 闪避率
                player:setDodge(val)
                return ret
            elseif atttype == self.PLAYERATTR.ATK_MELEE then -- 近战攻击
            	local livingAtt = player:getLivingAttrib()
                if livingAtt then
                	livingAtt:setAttackBaseLua(0, val)
                	return ret
                end
            elseif atttype == self.PLAYERATTR.ATK_REMOTE then -- 远程攻击
            	local livingAtt = player:getLivingAttrib()
                if livingAtt then
                	livingAtt:setAttackBaseLua(1, val)
                	return ret
                end
            elseif atttype == self.PLAYERATTR.DEF_MELEE then -- 近战防御
            	local livingAtt = player:getLivingAttrib()
                if livingAtt then
                	livingAtt:setArmorBaseLua(0, val)
                	return ret
                end
            elseif atttype == self.PLAYERATTR.DEF_REMOTE then -- 远程防御
            	local livingAtt = player:getLivingAttrib()
                if livingAtt then
                	livingAtt:setArmorBaseLua(1, val)
                	return ret
                end
            elseif atttype == self.PLAYERATTR.LEVEL then -- 星星数
				if val < 0 then return ErrorCode.FAILED end
				local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	playerAtt:setExp(val*100)
					return ret
				end
			elseif atttype == self.PLAYERATTR.DIMENSION then -- 模型大小
				if val <= 0 then return ErrorCode.FAILED end
				player:setCustomScale(val)
            	player:syncCustomScale() --设置主客机同步
				return ret;
			elseif atttype == self.PLAYERATTR.SCORE then
				if val < 0 then return ErrorCode.FAILED end
            	self:setGameScore(objid, val)
            	return ret
            elseif atttype == self.PLAYERATTR.CUR_LEVELEXP then	--设置当前等级经验
				if val < 0 then return ErrorCode.FAILED end
            	local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	playerAtt:setCurLevelExp(val)
					return ret
				end
            elseif atttype == self.PLAYERATTR.CUR_LEVEL then	--设置当前等级
				if val < 0 then return ErrorCode.FAILED end
            	local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	playerAtt:setCurLevel(val)
					return ret
				end
			elseif atttype == self.PLAYERATTR.CUR_STRENGTH then	--当前体力值
				-- if val < 0 then return ErrorCode.FAILED end
				if val < 0 then val = 0 end
				local playerAtt = player:getPlayerAttrib()
				if playerAtt then
					local strength = self:getStrengthSetting(player)
					if not strength then --地图设置为使用体力值则返回
						return ErrorCode.FAILED
					end
					playerAtt:setStrength(val)
					return ret
				end
			elseif atttype == self.PLAYERATTR.MAX_STRENGTH then	--最大体力值
				-- if val < 0 then return ErrorCode.FAILED end
				if val < 0 then val = 1 end
				local playerAtt = player:getPlayerAttrib()
				if playerAtt then
					local strength = self:getStrengthSetting(player)
					if not strength then --地图设置为使用体力值则返回
						return ErrorCode.FAILED
					end
					playerAtt:setBasicMaxStrength(val)
					return ret
				end
			elseif atttype == self.PLAYERATTR.STRENGTH_RECOVER then	--体力值的恢复速度
				-- if val < 0 then return ErrorCode.FAILED end
				if val < 0 then val = 0 end
				local playerAtt = player:getPlayerAttrib()
				if playerAtt then
					local strength = self:getStrengthSetting(player)
					if not strength then --地图设置为使用体力值则返回
						return ErrorCode.FAILED
					end
					playerAtt:setBasicStrengthRestore(val)
					return ret
				end
			elseif atttype == self.PLAYERATTR.ATK_PHYSICAL then -- 物理攻击
				local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	playerAtt:setPlayerBaseAttr(PATTR_ATTACKPHY, val)
                	return ret
                end
			elseif atttype == self.PLAYERATTR.ATK_MAGIC then -- 元素攻击
				local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	playerAtt:setPlayerBaseAttr(PATTR_ATTACKELEM, val)
                	return ret
                end
			elseif atttype == self.PLAYERATTR.DEF_PHYSICAL then -- 物理防御
				local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	playerAtt:setPlayerBaseAttr(PATTR_DEFPHY, val)
                	return ret
                end
			elseif atttype == self.PLAYERATTR.DEF_MAGIC then -- 元素防御
				local playerAtt = player:getPlayerAttrib()
                if playerAtt then
                	playerAtt:setPlayerBaseAttr(PATTR_DEFELEM, val)
                	return ret
                end
			elseif atttype == self.PLAYERATTR.CUR_ARMOR then -- 护盾值
				att:setArmor(val)
				return ret
			elseif atttype == self.PLAYERATTR.MAX_ARMOR then -- 最大护盾值
				att:setMaxArmor(val)
				return ret        
			elseif atttype == self.PLAYERATTR.DAMAGED_ZOMBIE then -- 受僵尸伤害降低
				local livingAtt = player:getLivingAttrib()
                if livingAtt then
                	livingAtt:setModAttrib(MODATTRIB_TYPE.MOBATTR_DAMAGED_ZOMBIE, val)
                	return ret
                end
			elseif atttype == self.PLAYERATTR.ATK_FIREARM then -- 枪械伤害加成
				local livingAtt = player:getLivingAttrib()
                if livingAtt then
                	livingAtt:setModAttrib(MODATTRIB_TYPE.MOBATTR_ATTACK_GUN, val)
                	return ret
                end
            end
        end

        return ErrorCode.FAILED
    end,

    -- 设置Playre位置-带物理位置
	setPosition = function(self, objid, x, y, z)
		if _G.isTypeError("number", x, y, z) then
			return ErrorCode.FAILED
		end
		local ret, player = self:getPlayerByUin(objid)
		if not player then return ErrorCode.FAILED end

		x = x and x * 100.0
		y = y and y * 100.0
		z = z and z * 100.0
		if not x or not y or not z then
			local srcx, srcy, srcz = player:getPosition(0,0,0)
			x = x or srcx
			y = y or srcy
			z = z or srcz
		end

		if CurWorld and player.gotoPosEx then
			local world = CurWorld._cptr or CurWorld
			-- player:gotoPosEx(world, x, y, z)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	--获取准星位置
	getAimPos = function(self, objid) 
		local ret, player = self:getPlayerByUin(objid)
		if player then
			local ret, x, y, z = player:getAimPos(0, 0, 0);
			if ret then
				x, y, z = x * 0.01, y * 0.01, z * 0.01
				return ErrorCode.OK, x, y, z
			end
		end

		return ErrorCode.FAILED
	end,

	--设置玩家道具设置属性 PLAYERATTR.ITEM_DISABLE_THROW
	setItemAttAction = function(self, objid, itemid, atttype, switch)
		if _G.isTypeError("number", itemid, atttype) or _G.isTypeError("boolean", switch) then
			return ErrorCode.FAILED
		end

		local ret, player = self:getPlayerByUin(objid)
		if player then
			local playerAtt = player:getPlayerAttrib()
			if playerAtt then
				playerAtt:setItemAttAction(itemid, atttype, switch)
				return ErrorCode.OK
			end
		end

		return ErrorCode.FAILED
	end,

	-- 通过音乐ID获取路径名
    getMusicPathById = function(self, soundId)
		if type(soundId) == "string" then
			if string.sub(soundId, 1, 2) == "s_" then
				return ErrorCode.OK, soundId, 0
			else
				return ErrorCode.FAILED
			end
		end

        if DefMgr and DefMgr.getSoundDef then 
            local soundDef = DefMgr:getSoundDef(soundId);
            if soundDef then
                local path = soundDef.SoundPath
                local name = string.gsub(path, '%.', '/')
                return ErrorCode.OK, 'sounds/'..name..'.ogg'
            end
        end

		if soundId < 0 then
			local path = g_audio_root .. math.abs(soundId)
			return ErrorCode.OK, path
		end

        return ErrorCode.FAILED
    end,

	--播放指定玩家身上的指定音乐
	playMusic = function(self, objid, soundId, volume, pitch, isLoop)
		local ret, player = self:getPlayerByUin(objid)
		local ret, fpath = self:getMusicPathById(soundId)
		if player and fpath then
			volume = tonumber(volume) or 100
			pitch = pitch or 1
			isLoop = isLoop or false
			volume = volume *0.01
			if (type(soundId) == "string" and string.sub(soundId, 1, 2) == "s_") or soundId < 0 then
				if CurWorld and CurWorld.getEffectMgr then
					local longSound = LongSound()
					longSound.objid = objid;
					longSound.type = SOUND_TYPE_OGG
					longSound.worldId = CurWorld:getCurMapID()
					longSound.volume = volume
					longSound.isLoop = isLoop
					longSound.duration = 0
					longSound:setPath(fpath)
					CurWorld:getEffectMgr():playLongSound2D(longSound)
				else
					return ErrorCode.FAILED
				end
			else
				player:playMusicByTrigger(fpath, volume, pitch, isLoop)
			end
			return ErrorCode.OK
		end

		return ErrorCode.FAILED
	end,

	--停止播放指定玩家身上的音乐
	stopMusic = function(self, objid, soundId)
		local ret, player = self:getPlayerByUin(objid)
		local ret, fpath = self:getMusicPathById(soundId)
		if player and fpath and soundId then
			if (type(soundId) == "string" and string.sub(soundId, 1, 2) == "s_") or soundId < 0 then
				if CurWorld and CurWorld.getEffectMgr then
					CurWorld:getEffectMgr():stopLongSound2D(fpath, objid)
				else
					return ErrorCode.FAILED
				end
			else
				player:stopMusicByTrigger(fpath)
			end
			return ErrorCode.OK
		end

		return ErrorCode.FAILED
	end,

    --设置玩家胜利
    setGameWin = function(self, objid)
		if SSMgrGameMode and SSMgrGameMode:isAdventureMode() then return ErrorCode.FAILED end
		if WorldMgr then --同时结束游戏
			local GameMakerMgr = WorldMgr:getGameMakerManager()
		   	if GameMakerMgr and GameMakerMgr.doGameEnd ~= nil then
				local ret = GameMakerMgr:doGameEndWithWinner(objid)
				if ret then return ErrorCode.OK end
		   	end
		end
        return ErrorCode.FAILED
    end,

    --打开开发者商店
	openDevStore = function(self, objid,eventname)

		local triggerID = 3160002
	   	if not self:checkDevFunPermits(eventname,triggerID) then
			return ErrorCode.FAILED
   		end

		ScriptSupportFunc:checkRealOwnerIsDeveloperCallback(function(cobjid)
			-- 稍稍延迟再打开
			threadpool:work(function()
				threadpool:wait(0.1)

				local bOpen = false --默认屏蔽触发器打开开发者商城
				if eventname == 'UI.Button.TouchBegin' or eventname == 'UI.Button.Click' then--xyang20220215 UI按钮按下和松开时单独处理
					bOpen = true
				end
				
				local selfUin = 0;
				if GetGameInfo():GetRoomHostType() ~= ROOM_SERVER_RENT then--云服需要全部转发
					if WorldMgr then
						selfUin = WorldMgr:getWorldOwnerUin()
					end
				end
				if selfUin == cobjid then
					TriggerShowDeveloperStore(bOpen)
				else
					local param = {}
					param.bOpen = bOpen
					param.triggerID = triggerID
					SandboxLuaMsg.sendToClient(cobjid, SANDBOX_LUAMSG_NAME.UGC.TRIGGER_OPEN_DEVELOPSTORE, param)
				end

				return ErrorCode.OK
			end)
		end, objid)
		return ErrorCode.OK
	end,

	--打开开发者商店（新）--不受UI按钮按下松开影响
	openDevStoreNew = function(self, objid,eventname)

		local triggerID = 3160006
		if not self:checkDevFunPermits(eventname,triggerID) then
			return ErrorCode.FAILED
		end

		ScriptSupportFunc:checkRealOwnerIsDeveloperCallback(function(cobjid)
			-- 稍稍延迟再打开
			threadpool:work(function()
				threadpool:wait(0.1)

				local selfUin = 0;
				if GetClientInfo():getRoomHostType() ~= ROOM_SERVER_RENT then--云服需要全部转发
					if WorldMgr then
						selfUin = WorldMgr:getWorldOwnerUin()
					end
				end
				if selfUin == cobjid then
					ShowDeveloperStore(true)
				else
					local param = {}
					param.triggerID = triggerID
					SandboxLuaMsg.sendToClient(cobjid, SANDBOX_LUAMSG_NAME.UGC.TRIGGER_OPEN_DEVELOPSTORE_NEW, param)
				end

				return ErrorCode.OK
			end)
		end, objid)
		return ErrorCode.OK
	end,

	--打开开发者商店指定分类
	openDevStoreTab = function(self, objid, page, name)
		local triggerID = 3160007
		if not self:checkDevFunPermits(eventname,triggerID) then
			return ErrorCode.FAILED
		end

		ScriptSupportFunc:checkRealOwnerIsDeveloperCallback(function(cobjid)
			-- 稍稍延迟再打开
			threadpool:work(function()
				threadpool:wait(0.1)

				local selfUin = 0;
				if GetClientInfo():getRoomHostType() ~= ROOM_SERVER_RENT then--云服需要全部转发
					if WorldMgr then
						selfUin = WorldMgr:getWorldOwnerUin()
					end
				end
				if selfUin == cobjid then
					ShowDeveloperStoreTab(page, name)
				else
					local param = {}
					param.triggerID = triggerID
					param.page = page
					param.name = name
					SandboxLuaMsg.sendToClient(cobjid, SANDBOX_LUAMSG_NAME.UGC.TRIGGER_OPEN_DEVELOPSTORE_TAB, param)
				end

				return ErrorCode.OK
			end)
		end, objid)
		return ErrorCode.OK
	end,

	--玩家手持道具的类型
	getPropsType = function(self,objid)
		local ret, player = self:getPlayerByUin(objid)
		if player and player.getCurToolID then
			local itemid = player:getCurToolID()
			if itemid > 0 then
				return ErrorCode.OK, itemid
			end
		end
		return ErrorCode.FAILED
	end,

    -- 玩家播放广告(异步)
    -- objid : number : 玩家id
	-- adname : string : 广告名称
    -- callbackevent : table : 回调事件信息
        -- name : string : 事件名称
        -- params : table : 事件参数
	playAdvertising = function(self, objid, adname, callbackevent)
		-- if callbackevent.params.def.EventExeResult == false then return ErrorCode.OK end -- 点击取消之后的广告返回
		local eventname = callbackevent and callbackevent.name
		local eventparams = callbackevent and callbackevent.params
		if not objid or not eventname then
			return ErrorCode.FAILED
		end

		-- local worldDesc = AccountManager:getCurWorldDesc()
		-- -- 以仅供学习模板的新地图屏蔽广告播放
		-- if worldDesc and worldDesc.TempType and worldDesc.TempType == 1 and (worldDesc.gwid > 0 and worldDesc.pwid > 0) then
		-- 	self:notifyGameInfo2Self(objid,GetS(34434))
		-- 	return ErrorCode.FAILED
		-- end

	   	local eventstr = eventparams and eventparams.def and eventparams.def.msgStr
	   	local triggerID = 3160001
	   	if eventparams and eventparams.def and eventparams.def.triggerID then
			triggerID = eventparams.def.triggerID
		end
	   	if not self:checkDevFunPermits(eventstr,triggerID) then
			return ErrorCode.FAILED
	   	end

		ScriptSupportFunc:checkRealOwnerIsDeveloperCallback(function(objid, adname, callbackevent)
			-- 发送给客机播放
			local tdata = {
				adname = adname,
				triggerevent = callbackevent,
				triggerid = triggerID
			}
			ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_ADVERTISING, tdata)
		end, objid, adname, callbackevent)
		return ErrorCode.OK
	end,

    -- 玩家播放广告(异步)(回调任务)
    -- objid : number : 玩家id
	-- adname : string : 广告名称
    -- taskname : string : 任务名称
	playAdvertisingTask = function(self, objid, adname, taskname,tdata)
		if not objid or not adname or not taskname then
			return ErrorCode.FAILED
		end

		-- local worldDesc = AccountManager:getCurWorldDesc()
		-- -- 以仅供学习模板的新地图屏蔽广告播放
		-- if worldDesc and worldDesc.TempType and worldDesc.TempType == 1 and (worldDesc.gwid > 0 and worldDesc.pwid > 0) then
		-- 	self:notifyGameInfo2Self(objid,GetS(34434))
		-- 	return ErrorCode.FAILED
		-- end
	   
		local eventstr = tdata and tdata.msgStr
		if not self:checkDevFunPermits(eventstr,3160001) then
				return ErrorCode.FAILED
		end
	
		ScriptSupportFunc:checkRealOwnerIsDeveloperCallback(function(objid, adname, taskname)
			-- 发送给客机播放
			local tdata = {
				adname = adname,
				taskname = taskname,
				msgStr = eventstr
			}
			ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_ADVERTISING_CALLBACK, tdata)
		end, objid, adname, taskname)
		return ErrorCode.OK
	end,
	
	-- 给actor附加一个速度
	appendSpeed = function(self, objid, vx, vy, vz)
		if _G.isTypeError("number", vx, vy, vz) then
			return ErrorCode.FAILED
		end
		local ret, player = self:getPlayerByUin(objid)
		if player then
			-- 发送到客机执行
			local tdata = {
				vx = vx * 100,
				vy = vy * 100,
				vz = vz * 100,
			}
			player:addMotion(tdata.vx, tdata.vy, tdata.vz)
			if not CurMainPlayer or CurMainPlayer:getUin() ~= player:getUin() then
				local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_APPENDSPEED, tdata)
				if ret then
					return ErrorCode.OK
				end
			end
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	-- 设置检测掉落物半径
	setCheckBoxScale = function(self, objid,range)
		if _G.isTypeError("number", range) then
			return ErrorCode.FAILED
		end
		local ret, player = self:getPlayerByUin(objid)
		if player and player:setCheckBoxScale(range) then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	-- 打开坐标为箱子或熔炼炉的方块
	openBoxByPos = function(self, objid ,x,y,z)
		if _G.isTypeError("number", x, y, z) then
			return ErrorCode.FAILED
		end
		x = math.floor(x)
		z = math.floor(z)
		local ret, player = self:getPlayerByUin(objid)
		if player and player:openBoxByPos(x,y,z) then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,
	-- 强制打开某个工作台
	-- itemid 为固定参数 WOKESTAGE-> 800  824 833  工具箱  修理台 附魔台
	forceOpenBoxUI = function(self, objid,itemid)
		if _G.isTypeError("number", itemid) then
			return ErrorCode.FAILED
		end
		local ret, player = self:getPlayerByUin(objid)
		if player and player:forceOpenBoxUI(itemid) then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	-- 打开开发者商店商品购买弹框
	-- objid : number : 玩家id
	-- devGoodsId : number : 商品id
	-- customDesc : string : 自定义商品描述
	openDevGoodsBuyDialog = function(self, objid, devGoodsId, customDesc, tdata)
		if _G.isTypeError("number", devGoodsId) then
			return ErrorCode.FAILED
		end
		-- local worldDesc = AccountManager:getCurWorldDesc()
		-- if worldDesc and worldDesc.TempType and worldDesc.TempType == 1 and (worldDesc.gwid > 0 and worldDesc.pwid > 0) then
		-- 	-- self:notifyGameInfo2Self(objid,GetS(34434))
		-- 	return ErrorCode.FAILED
		-- end
		if nil == tdata then
			tdata = {}
		end

		local eventstr = tdata and tdata.msgStr
		if not self:checkDevFunPermits(eventstr,3160003) then
			return ErrorCode.FAILED
	   	end

		tdata.itemid = devGoodsId;
		tdata.customDesc = customDesc;
		local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_DEVGOODS, tdata)
		if ret then
			AddTempPlayerSSDevGoods(objid, devGoodsId)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	-- 打开开发者商店商品详情页
	-- objid : number : 玩家id
	-- devGoodsId : number : 商品id
	openDevGoodsBuyDetailedDialog = function(self, objid, devGoodsId, tdata)
		if _G.isTypeError("number", devGoodsId) then
			return ErrorCode.FAILED
		end
		if nil == tdata then
			tdata = {}
		end
		tdata.itemid = devGoodsId
		local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_DEVGOODS_DETAILS, tdata)
		if ret then
			AddTempPlayerSSDevGoods(objid, devGoodsId)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	-- 打开视频链接
    -- playerid : number : 玩家ID
    -- url : string : 视频url
    openVideoUrl = function(self, objid, url, tdata)
		--url只能在指定的url中
		if ns_version and ns_version.video_trigger_URL then--云服上没有video_trigger_URL 字段
			if not ns_version.video_trigger_URL.url then
				return ErrorCode.FAILED
			end
			local urlTab = StringSplit(ns_version.video_trigger_URL.url,",") or {}
			for i, _url in ipairs(urlTab) do
				if url == _url then
					tdata = tdata or {}
					tdata.url = url;
					local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_VIDEO_URL, tdata)
					if ret then
						return ErrorCode.OK
					end
					break
				end
			end
		else
			tdata = tdata or {}
			tdata.url = url;
			local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_VIDEO_URL, tdata)
			if ret then
				return ErrorCode.OK
			end
		end
		return ErrorCode.FAILED
	end,

	-- 打开一个UI界面
	openUIView = function(self, objid, uiname ,effectid,time)
		local ret, player = self:getPlayerByUin(objid)
		if player and uiname and type(uiname) == 'string' and  uiname ~= '' then
			local tdata = {}
			tdata[1] = uiname;
			tdata[2] = 1;
			tdata[3] = effectid;
			tdata[4] = time;
			local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_SHOWUI, tdata)
			if ret then
				return ErrorCode.OK
			end
		end
		return ErrorCode.FAILED
	end,

	-- 隐藏一个界面
	hideUIView = function(self, objid, uiname ,effectid,time)
		local ret, player = self:getPlayerByUin(objid)
		if player and uiname and type(uiname) == 'string' and  uiname ~= '' then
			local tdata = {}
			tdata[1] = uiname;
			tdata[2] = 0;
			tdata[3] = effectid;
			tdata[4] = time;
			local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_SHOWUI, tdata)
			if ret then
				return ErrorCode.OK
			end
		end
		return ErrorCode.FAILED
	end,


	-- 改变玩家移动方式
	changPlayerMoveType = function(self, objid, moveType)
		local ret, player = self:getPlayerByUin(objid)
		if player then
			local tdata = {}
			tdata.objid = objid;
			tdata.moveType = moveType;
			local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_CHANGE_MOVE_TYPE, tdata)
			if ret then
				-- 服务器设置状态，以便检测
				if player.isNewMoveSyncSwitchOn and player:isNewMoveSyncSwitchOn() then
					if player.changeMoveFlag then
						player:changeMoveFlag(0, moveType == MOVETYPE.FLY);
					end
				else
					if player.setFlyingAndSync then
						player:setFlyingAndSync(moveType == MOVETYPE.FLY)
					end
				end
				return ErrorCode.OK
			end	
		end
		return ErrorCode.FAILED
	end,

	-- 抖动玩家镜头
    -- objid : number : 玩家id
    -- power : number :强度 取值范围1-1000
    -- duration : number :持续时间
    shakeCamera = function(self, objid, duration, power)
		if _G.isTypeError("number",duration,power) then
			return ErrorCode.FAILED
		end
		power = power and math.floor(power) or 1
		if power <= 1 then
			power = 1
		elseif power >= 1000 then
			power = 1000
		end
		if not duration or duration <= 0 then
			duration = 1
		end
		local tdata = {}
		tdata.objid = objid;
		tdata.power = power;
		tdata.duration = duration;
		local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_CAMERA_SHAKE, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

    -- 停止抖动玩家镜头
    -- objid : number : 玩家id
    stopShakeCamera = function(self, objid)
        local tdata = {}
		tdata.objid = objid;
		local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_CAMERA_SHAKE_STOP, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

	--玩家播放qq音乐
    playQQMusic = function(self, objid, musicId, volume, isLoop)
		if _G.isTypeError("number", volume) then
			return ErrorCode.FAILED
		end

		--volume换算成0-1
		volume = volume / 100
		if volume < 0 then volume = 0 end
		if volume > 1 then volume = 1 end
		
		local tdata = {}
		tdata.objid = objid;
		tdata.volume = volume;
		tdata.isLoop = isLoop or false;

		if type(musicId) == "string" then--老触发器
			local tab = StringSplit(musicId, '|')
			if tab and tab[1] then
				tdata.musicId = tonumber(tab[1])
			else
				return ErrorCode.FAILED
			end
		elseif type(musicId) == "number" then--新触发器
			tdata.musicId = musicId
		else
			return ErrorCode.FAILED
		end

		

		local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_PLAY_QQMUSIC, tdata)
		if ret then
			--抛出音乐开始播放的事件
			GetInst("QQMusicTriggerManager"):PostTriggerEvent(QQMUSICE_PLAY_BEGIN, tdata.musicId, tdata.objid)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

    --暂停/恢复/停止玩家的QQ音乐
    operateQQMusic = function(self, operate, objid)
		if _G.isTypeError("number",objid,operate) then
			return ErrorCode.FAILED
		end

        local tdata = {}
		tdata.objid = objid;
		tdata.operate = operate;
		local ret = ScriptSupportTask:sendTaskToPlayer(objid, SSTASKID.PLAYER_OP_QQMUSIC, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

	--打开评价界面
	OpenAppraiseView = function(self,playerid)
		return self:OpenMapInteractionView(playerid,PlayerRelations.EvaluateMaps)
	end,
	--打开收藏界面
	OpenCollectionView = function(self,playerid)
		return self:OpenMapInteractionView(playerid,PlayerRelations.CollectMaps)
	end,

	--获取迷你会员等级
	IsMiniVip = function(self,playerid,newTaskName)
		playerid = tonumber(playerid) or 0
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		local tdata = {}
		table.insert(tdata,"getviplevel")
		table.insert(tdata,newTaskName)
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_OP_GAMEMSG, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,
	
    -- 玩家自定义数据上报埋点
    StandReportEvent = function (self,playerid,eventstr)
		if not playerid or playerid == 0 or not eventstr or eventstr == '' then
			return ErrorCode.FAILED
		end
		if GameVmReport then
			GameVmReport:CollectInfo(playerid,eventstr)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,


	SendFriendApply = function (self,playerid,playerid2)
		if _G.isTypeError("number",playerid,playerid2) then
			return ErrorCode.FAILED
		end
		if playerid == 0 or playerid2 == 0 or playerid == playerid2 then
			return ErrorCode.FAILED
		end

		local rt, player1 = self:getPlayerByUin(playerid)
		local rt, player2 = self:getPlayerByUin(playerid2)
		if not player1 or not player2  then
			return ErrorCode.FAILED
		end
		
		if playerid ~= playerid2 then
			local tdata = {}
			table.insert(tdata,'applyfriend')
			table.insert(tdata,playerid2)
			local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_OP_GAMEMSG, tdata)
			if ret then
				return ErrorCode.OK
			end
		end
		return ErrorCode.FAILED
	end,

	
	HasFriend = function(self,playerid,playerid2,newTaskName)
		if _G.isTypeError("number",playerid,playerid2) then
			return ErrorCode.FAILED
		end
		if playerid == 0 or playerid2 == 0 or playerid == playerid2 then
			return ErrorCode.FAILED
		end

		local rt, player1 = self:getPlayerByUin(playerid)
		local rt, player2 = self:getPlayerByUin(playerid2)
		if not player1 or not player2  then
			return ErrorCode.FAILED
		end
		
		local tdata = {}
		table.insert(tdata,"hasfriend")
		table.insert(tdata,newTaskName)
		table.insert(tdata,playerid2)
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_OP_GAMEMSG, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

    -- 手持道具播放动作
    PlayActInHand = function (self,playerid,animid,playmode)
		if _G.isTypeError("number",playerid,animid,playmode) then
			return ErrorCode.FAILED
		end
		if playerid == 0 then return ErrorCode.FAILED end
		local tdata = {}
		table.insert(tdata,'handitem')
		table.insert(tdata,playerid)
		table.insert(tdata,animid)
		table.insert(tdata,playmode)
		local ret = ScriptSupportTask:sendTaskToPlayer(0, SSTASKID.ITEM_PLAY_ANIM, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
	
	-- 打开内置排行榜
	OpenBuiltinRank = function (self,playerid,id)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if id then
			local tdata = {}
			table.insert(tdata,id)
			local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_SHOWRANK, tdata)
			if ret then
				return ErrorCode.OK
			end
		end
		return ErrorCode.FAILED
	end,
	-- 隐藏内置排行榜
	HideBuiltinRank = function (self,playerid)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		local tdata = {}
		table.insert(tdata,-1)
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_SHOWRANK, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	-- 玩家手机震动
	setMobileVibrate = function (self, playerid, time, amplitude)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		local tdata = {}
		tdata.time = time
		tdata.amplitude = amplitude
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_MOBILEVIBRATE, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	--玩家摄像机变换相对位置
    SetCameraPosTransformBy = function(self, playerid, vec, animid,time)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "table" ~= type(vec) then return ErrorCode.FAILED end
		if "number" ~= type(animid) then return ErrorCode.FAILED end
		if "number" ~= type(time) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "CameraPosBy"
		tdata[2] = vec
		tdata[3] = animid
		tdata[4] = time
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    
    --玩家摄像机变换到位置
    SetCameraPosTransformTo = function(self, playerid, vec, animid,time)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "table" ~= type(vec) then return ErrorCode.FAILED end
		if "number" ~= type(animid) then return ErrorCode.FAILED end
		if "number" ~= type(time) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "CameraPosTo"
		tdata[2] = vec
		tdata[3] = animid
		tdata[4] = time
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

    --玩家摄像机旋转相对角度
    SetCameraRotTransformBy = function(self, playerid, vec, animid,time)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "table" ~= type(vec) then return ErrorCode.FAILED end
		if "number" ~= type(animid) then return ErrorCode.FAILED end
		if "number" ~= type(time) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "CameraRotBy"
		tdata[2] = vec
		tdata[3] = animid
		tdata[4] = time
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    --玩家摄像机旋转到角度
    SetCameraRotTransformTo = function(self, playerid, vec, animid,time)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "table" ~= type(vec) then return ErrorCode.FAILED end
		if "number" ~= type(animid) then return ErrorCode.FAILED end
		if "number" ~= type(time) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "CameraRotTo"
		tdata[2] = vec
		tdata[3] = animid
		tdata[4] = time
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    --玩家摄像机Fvo变换相对值
    SetCameraFovTransformBy = function(self, playerid, value, animid,time)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "number" ~= type(value) then return ErrorCode.FAILED end
		if "number" ~= type(animid) then return ErrorCode.FAILED end
		if "number" ~= type(time) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "CameraFovBy"
		tdata[2] = value
		tdata[3] = animid
		tdata[4] = time
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    --玩家摄像机Fvo变换到值
    SetCameraFovTransformTo = function(self, playerid, value, animid,time)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "number" ~= type(value) then return ErrorCode.FAILED end
		if "number" ~= type(animid) then return ErrorCode.FAILED end
		if "number" ~= type(time) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "CameraFovTo"
		tdata[2] = value
		tdata[3] = animid
		tdata[4] = time
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    --设置玩家的摄像机设置开关
    SetCameraAttrState = function (self,playerid,attr,enable)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "number" ~= type(attr) then return ErrorCode.FAILED end
		if "boolean" ~= type(enable) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "AttrState"
		tdata[2] = attr
		tdata[3] = enable and 1 or 0
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    --设置玩家的摄像机旋转模式
    SetCameraRotMode = function (self,playerid,attr)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "number" ~= type(attr) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "RotMode"
		tdata[2] = attr
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    --设置玩家的摄像机挂载到对象
    SetCameraMountObj = function (self,playerid,objid)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "number" ~= type(objid) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "MountObj"
		tdata[2] = objid
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    --设置玩家的摄像机挂载到位置
    SetCameraMountPos = function (self,playerid,pos)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		if "table" ~= type(pos) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "MountPos"
		tdata[2] = pos
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    --重置玩家摄像机
    ResetCameraAttr = function (self,playerid)
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		local tdata = {}
		tdata[1] = "ResetCamera"
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_CAMERA, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

	--打开皮肤商品购买弹框<1.28+>
    openDevGoodsSkinBuyDialog = function (self,playerid,skin,desc)
		if type(playerid) ~= 'number' or playerid == 0 then return ErrorCode.FAILED end
		if type(skin) ~= 'string' or skin == "" then return ErrorCode.FAILED end
		local tdata = {
			skin = skin,
			desc = desc,
			itype = 1,
		}
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_DEVSKIN, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

    --打开皮肤商品详情页<1.28+>
    openDevGoodsSkinDetail = function (self,playerid,skin,eventstr)
		if type(playerid) ~= 'number' or playerid == 0 then return ErrorCode.FAILED end
		if type(skin) ~= 'string' or skin == "" then return ErrorCode.FAILED end
		local msg = ""
        if eventstr and TriggerEventToFactor then
            msg = TriggerEventToFactor(eventstr)
        end
        local bOpen = false
        if msg == '1120004' or msg == '1120001' or msg == '1120006' or msg == '1120007' then--'UI.Button.TouchBegin' or 'UI.Button.Click'--UI按钮按下和松开时单独处理
           bOpen = true
        end
        if not bOpen then
			if not isAbroadEvn() then -- 海外不限制只有点击事件触发
				return ErrorCode.FAILED
			end
		end
		local tdata = {
			skin = skin,
			msg = msg,
			itype = 2,
		}
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_DEVSKIN, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

    --打开皮肤商店<1.28+>
    openDevGoodsSkin = function (self,playerid,eventstr)
		if type(playerid) ~= 'number' or playerid == 0 then return ErrorCode.FAILED end

		local msg = ""
        if eventstr and TriggerEventToFactor then
            msg = TriggerEventToFactor(eventstr)
        end
        local bOpen = false
        if msg == '1120004' or msg == '1120001' or msg == '1120006' or msg == '1120007' then
           bOpen = true
        end
        if not bOpen then return ErrorCode.FAILED end

		local tdata = {
			itype = 3,
		}
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_DEVSKIN, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
	
	--打开收藏界面
	OpenInviteBulletBox = function(self,playerid)
		playerid = tonumber(playerid) or 0
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		local tdata = {}
		table.insert(tdata,"OpenInviteBulletBox")
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_OP_GAMEMSG, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
	end,

	--获取当前语言
    GetLanguageAndRegion = function (self,objid)
		if not objid then
			return ErrorCode.FAILED 
		end
		local ret, player = self:getPlayerByUin(objid)
		if  player then
			if player.hasUIControl and player:hasUIControl() then--主机
				local lang = get_game_lang_code and get_game_lang_code() or "cn" -- 获取到的语言
				local area = get_game_country and get_game_country() or "CN" -- 获取到的国家
				return ErrorCode.OK ,lang,area
			end
			local briefInfo = ClientCurGame.findPlayerInfoByUin and ClientCurGame:findPlayerInfoByUin(objid) or nil
			if briefInfo then
				local lang = briefInfo.lang or "0" -- 获取到的语言
				local area = briefInfo.country or "CN" -- 获取到的国家
				return ErrorCode.OK ,lang,area
			end
		end
		return ErrorCode.FAILED
	end,

	--获取玩家首次邀请人
    --playerid:number:玩家Uin
    --code:number:成功(ErrorCode.OK) , uin:number:玩家ID
    GetFirstInviter = function (self,callback,playerid)
		if type(playerid) ~= "number" or playerid <= 0 or type(callback) ~= "function" then
			return ErrorCode.FAILED
		end
		-- if ROOM_SERVER_RENT ~= GetGameInfo():GetRoomHostType() then
		-- 	return ErrorCode.FAILED
		-- end
		if GetInst("InviteFriendListInterface") then
			local mapid = WorldMgr and WorldMgr:getFromWorldID() or nil
			local uin = AccountManager:getUin()
			local function reqcallback (ret)
				if callback then
					if ret.code == 0 then
						return callback({ErrorCode.OK , ret.data})
					else
						return callback({ErrorCode.FAILED , ret.code})
					end
				end
			end
			GetInst("InviteFriendListInterface"):GetInvitePlayer(uin,mapid,playerid,reqcallback)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
	--获取玩家全部邀请的玩家
    --objid:number:玩家Uin
    --code:number:成功(ErrorCode.OK) ,uins:table:玩家ID的数组
    GetInviteGroupS = function (self,playerid)
		return ErrorCode.OK ,{}

    end,
	--获取玩家新邀请的玩家
    --objid:number:玩家Uin
    --code:number:成功(ErrorCode.OK) , uins:table:玩家ID的数组
    GetNewInviteGroup = function (self,callback,playerid,pageix,pagesize,retype)
		if type(playerid) ~= "number" or playerid <= 0 or type(callback) ~= "function" then
			return ErrorCode.FAILED
		end
		-- if ROOM_SERVER_RENT ~= GetGameInfo():GetRoomHostType() then
		-- 	return ErrorCode.FAILED
		-- end
		if GetInst("InviteFriendListInterface") then
			local mapid = WorldMgr and WorldMgr:getFromWorldID() or nil
			local uin = AccountManager:getUin()
			local function reqcallback (ret)
				if callback then
					if ret.code == 0 then
						return callback({ErrorCode.OK , ret.data})
					else
						return callback({ErrorCode.FAILED , ret.code})
					end
				end
			end
			if pageix <= 0 then pageix = 0 end
			local offset = pageix*200
			if retype ~= 0 and retype ~= 1 then retype = 1 end
			GetInst("InviteFriendListInterface"):GetInvitedPlayerList(uin,mapid,playerid,offset,retype,pagesize,reqcallback)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

	--打开地图交互窗口<1.33+>
    --playerid:number:玩家Uin,code:number:玩家关系属性枚举值(PlayerRelations)
    --code:number:成功(ErrorCode.OK) , uins:table:玩家ID的数组
    OpenMapInteractionView = function (self,playerid,code)
		playerid = tonumber(playerid) or 0
		if "number" ~= type(playerid) then return ErrorCode.FAILED end
		local tdata = {}
		if code == PlayerRelations.CollectMaps then -- 收藏界面
			table.insert(tdata,"OpenCollectionView")
		elseif code == PlayerRelations.EvaluateMaps then -- 评价界面
			table.insert(tdata,"OpenAppraiseView")
		elseif code == PlayerRelations.FollowTheAuthor then -- 关注作者界面
			table.insert(tdata,"OpenFollowAuthorView")
		end
		if next(tdata) then
			local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_OP_GAMEMSG, tdata)
			if ret then
				return ErrorCode.OK
			end
		end
		return ErrorCode.FAILED
    end,

    --获取玩家地图交互状态<1.33+>
    --objid:number:玩家Uin ,code:number:玩家关系属性枚举值(PlayerRelations)
    --code:number:成功(ErrorCode.OK) 
    GetPlayerRelations = function (self,playerid,code)
        return ErrorCode.FAILED
    end,

    --打开开发者商店页面<1.34+>
    OpenDevGoodsPage = function(self,playerid,pagetype,pagetitle)
		playerid = tonumber(playerid) or 0
		if "number" ~= type(playerid) or playerid == 0 then return ErrorCode.FAILED end
		if "number" ~= type(pagetype) then return ErrorCode.FAILED end
		local tdata = {}
		table.insert(tdata,"OpenDevGoodsPage")
		table.insert(tdata,pagetype)
		table.insert(tdata,pagetitle)
		local ret = ScriptSupportTask:sendTaskToPlayer(playerid, SSTASKID.PLAYER_OP_GAMEMSG, tdata)
		if ret then
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
	
	GetPlayerCostStatic = function(self,callback,playerid,tbegin,tend,costtype)
		if _G.isTypeError("number",playerid,tbegin,tend,costtype) or playerid <= 0 or type(callback) ~= "function" then
			return ErrorCode.FAILED
		end

		local owid = nil
        if WorldMgr and WorldMgr.getFromWorldID then
            owid= WorldMgr:getFromWorldID()
        end
        if not owid then
			return ErrorCode.FAILED
        end

		local tdata = {
			op_uin = playerid,
			stime = tbegin < tend and tbegin or tend,
			etime = tbegin < tend and tend or tbegin,
			map_id = owid,
		}
		if costtype == 1 then --迷你豆
			tdata.type = 9
		elseif costtype == 2 then --迷你币
			tdata.type = 8
		else
			return ErrorCode.FAILED
		end
		GameVmSeversList.TriggerHttp:ReqPlayerCostStatic(tdata,function(ret)
			if callback then
				if type(ret) == "table" and ret.code == 0 and ret.data and ret.data.cost then
					return callback({ErrorCode.OK , ret.data.cost})
				else
					return callback({ErrorCode.FAILED , -1})
				end
			end
		end)
		return ErrorCode.OK
	end,

    OpenShopTryOnView = function (self,playerid)
		local ret =  UGCGetInst("ScriptEnvMgr"):GetServices("Player"):OpenShopTryOnView(playerid)
        if ret  then
            return ErrorCode.OK
        end
        return ErrorCode.FAILED
    end,

    OpenShopSkinBuyDialog = function (self,playerid,objid)
		local ret =  UGCGetInst("ScriptEnvMgr"):GetServices("Player"):OpenShopSkinBuyDialog(playerid,objid)
        if ret then
            return ErrorCode.OK
        end
        return ErrorCode.FAILED
    end,


}, class.ServiceBase)