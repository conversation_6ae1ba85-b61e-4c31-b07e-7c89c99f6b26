--[[
    desc：特长系统帮助类
    date: 2022-07-04
    author: ch<PERSON><PERSON>
]]
local Genius_Helper = {}

local NEW_GENIUS_STATUS = {
    UNKNOWN = -1, --尚未获取
    VALID   = 0, -- 新特长系统生效
    INVALID = 1 -- 新特长系统未生效
}

function Genius_Helper:OnInit()
    self.playerGenius = {
        [1000031783] = 2
    }

    self.define = {
        ROOM_NET = {
            GENIUS_SYSTEM_SETGENIUS_C2H = "GENIUS_SYSTEM_SETGENIUS_C2H",
            GENIUS_SYSTEM_SETGENIUS_H2C = "GENIUS_SYSTEM_SETGENIUS_C2H"
        }
    }

    -- 玩家特长开启标记
    self.playerNewGeniusOpenFlag = {}

    --role_skill_config lua 配置数据
    self.mapRoleItemTrans = {}  --旧角色碎片兑换表
    self.mapGeniusDef = {}      --天赋特长表
    self.mapRoleID = {}    -- k 新皮肤ID， v 老角色ID

    -- 主机收到客机更新特长消息
    -- SandboxLuaMsg:SubscibeMsgHandle(self.define.ROOM_NET.GENIUS_SYSTEM_SETGENIUS_C2H, function(content)
    --     if not content then 
    --         return 
    --     end

    --     if content.isGeniusValid then
    --         GetInst("GeniusMgr"):ReqGenius(content.uin)
    --     end

    --     self.playerNewGeniusOpenFlag[content.uin] = content.isGeniusValid and true or false
    -- end )

    -- 收到主机特长信息，修正自己特长加成
    -- SandboxLuaMsg:SubscibeMsgHandle(self.define.ROOM_NET.GENIUS_SYSTEM_SETGENIUS_H2C, function(content)
    --     if not content then 
    --         return 
    --     end

    --     self:RefreshPlayerGenius(content.uin, content.geniusID)

    --     self.playerNewGeniusOpenFlag[content.uin] = content.isGeniusValid
    -- end )
end

function Genius_Helper:OrganizeRoleItemTrans()
    -- [1] = {
    --     NewID = 94001,
    --     Ratio = 50,
    --     OldID = 12950,
    --     Name = [[特长碎片]],
    -- }
    for _, v in pairs(ns_role_skill_config.RoleItemTrans or {}) do
        self.mapRoleItemTrans[v.OldID] = v
    end
end

function Genius_Helper:OrganizeGeniusDef()
    -- [1] = {
    --     ULConsume2Num = 0,
    --     ULConsume2ID = 10000,
    --     GeniusName = [[强壮]],
    --     GeniusValue2 = 0,
    --     GeniusValue3 = 0,
    --     ULConsume1ID = 10043,
    --     ULConsumeType = 1,
    --     ULConsume1Num = 0,
    --     GeniusValue1 = 0,
    --     GeniusLv = 0,
    --     ID = 1,
    --     GeniusDesc = [[增加基础伤害（未激活）]],
    --     GeniusType = 0,
    --     GeniusID = [[Genius_icon01]],
    -- } ,

    for _, v in pairs(ns_role_skill_config.GeniusDef or {}) do
        self.mapGeniusDef[v.GeniusType] = self.mapGeniusDef[v.GeniusType] or {}
        self.mapGeniusDef[v.GeniusType][v.GeniusLv] = v
    end
end

function Genius_Helper:OrganizeSkin2RoleID()
    if not ns_role_skill_config or not ns_role_skill_config.RoleSkin then
        return
    end
    for _, v in pairs(ns_role_skill_config.RoleSkin) do
        self.mapRoleID[v.Skinid] = v.ID
    end
end

function Genius_Helper:OrganizeRoleSkillConfigData()
    if not ns_role_skill_config then
        return
    end

    self:OrganizeRoleItemTrans()
    self:OrganizeGeniusDef()
    self:OrganizeSkin2RoleID()
end

function Genius_Helper:GetOriginRoleItemTrans() 
    return self.mapRoleItemTrans
end

function Genius_Helper:GetOriginRoleItemTransByItemID(itemID)
    return self.mapRoleItemTrans[itemID]
end

function Genius_Helper:GetOriginGeniusDef()
    return self.mapGeniusDef
end

--返回的是 该类型多个等级的天赋
function Genius_Helper:GetOriginGeniusDefByType(gType)
    return self.mapGeniusDef[gType]
end

function Genius_Helper:GetOriginGeniusDefByTypeAndLv(gType, gLv)
    if not gType or not self.mapGeniusDef[gType] then
        return nil
    end

    return self.mapGeniusDef[gType][gLv]
end

-- 获取天赋值
function Genius_Helper:GetGeniusVal(uin, geniusType, geniusIdx)
    if not ns_role_skill_config or not ns_role_skill_config.GeniusDef then
        return 0
    end

    local geniusID = self.playerGenius[uin] or 0
    local info = ns_role_skill_config.GeniusDef[geniusID]
    if info and geniusType == info.GeniusType then
        return info["GeniusValue" .. geniusIdx + 1] or 0
    end

    return 0
end

-- 获取天赋类型
function Genius_Helper:GetGeniusType(uin)
    if not ns_role_skill_config or not ns_role_skill_config.GeniusDef then
        return 0
    end

    local geniusID = self.playerGenius[uin] or 0
    local info = ns_role_skill_config.GeniusDef[geniusID]
    if info then
        return info.GeniusType
    end

    return 0
end

function Genius_Helper:SetPlayerGeniusID(uin, GeniusID)
    self.playerGenius = self.playerGenius or {}
    self.playerGenius[uin] = GeniusID
end

function Genius_Helper:GetPlayerGeniusID(uin)
    return self.playerGenius[uin]
end

function Genius_Helper:GetOldRoleID(skinID)
    if next(self.mapRoleID) then
        return self.mapRoleID[skinID] or 0
    else
        local jsonStr = getkv("RoleSkinTabJson")
        if jsonStr then
            local ok, tab = pcall(JSON.decode, JSON, jsonStr)
            if ok and type(tab) == "table" then

                for _, v in pairs(tab) do
                    if v.Skinid == skinID then
                        return v.ID
                    end
                end

            end
        end
    end
    return 0
end

function Genius_Helper:GetPlayerNewGeniusValidFlag(uin)
    if not uin then
        return NEW_GENIUS_STATUS.INVALID
    end
    
    -- 0 是开启新特长，1是未开启
    if uin == AccountManager:getUin() and (not GetClientInfo():isPureServer()) then
        return GetInst("GeniusMgr"):IsOpenGeniusSys() and NEW_GENIUS_STATUS.VALID or NEW_GENIUS_STATUS.INVALID
    else
        
        if self.playerNewGeniusOpenFlag[uin] == nil then
            return NEW_GENIUS_STATUS.UNKNOWN-- 还没从服务器取到值
        else
            return self.playerNewGeniusOpenFlag[uin] and NEW_GENIUS_STATUS.VALID or NEW_GENIUS_STATUS.INVALID -- 已经从服务器取到值
        end
    end
end

function Genius_Helper:OnEnterWorld()
    -- 先更新自己
    if not CurWorld then
        return 
    end

    self:ReqUpdatePlayerGenius(AccountManager:getUin())
end

function Genius_Helper:RefreshPlayerGenius(uin, geniusID)
    if not ns_role_skill_config then
        return
    end

    self:SetPlayerGeniusID(uin, geniusID)

    local info = ns_role_skill_config.GeniusDef[geniusID]
    if info then
        ActorGeniusMgr:changePlayerGenius(uin, info.GeniusType)
    else
        ActorGeniusMgr:changePlayerGenius(uin, GENIUS_NONE)
    end
end

function Genius_Helper:BroadcastPlayerGenius(uin, isNewGeniusOpen, geniusID)
    if not CurWorld or CurWorld:isRemoteMode() then
        return
    end

    SandboxLuaMsg.sendBroadCast(self.define.ROOM_NET.GENIUS_SYSTEM_SETGENIUS_H2C, {uin = uin, isGeniusValid = isNewGeniusOpen, geniusID = geniusID})
end

function Genius_Helper:ReqUpdatePlayerGenius(uin)
    if not CurWorld then
        return 
    end
    
    if not CurWorld:isRemoteMode() then
        -- 请求玩家特长特长信息
        GetInst("GeniusMgr"):ReqGenius(uin)
    else -- 客机告诉主机请求特长信息
        SandboxLuaMsg.sendToHost(self.define.ROOM_NET.GENIUS_SYSTEM_SETGENIUS_C2H, {uin = uin, isGeniusValid = GetInst("GeniusMgr"):IsOpenGeniusSys()})
    end
end

Genius_Helper:OnInit()

_G.Genius_Helper = Genius_Helper
