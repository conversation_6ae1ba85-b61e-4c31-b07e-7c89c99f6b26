-- UI x方向对应游戏X方向
-- UI Y方向和游戏Y方向相反
-- 游戏1个block对应UI1个像素
--声明
local pixelmapCtrl = Class("pixelmapCtrl",ClassList["UIBaseCtrl"])
pixelmapCtrl.__private = {};
local __private = pixelmapCtrl.__private;

local bed_tag = 2

--创建
function pixelmapCtrl:Create(param)
	return ClassList["pixelmapCtrl"].new(param)
end

--初始化
function pixelmapCtrl:Init(param)
	self.super:Init(param);
	self.m_firstShow = true;
	self.m_langConifg = {
		moreMarkerTip = GetS(80233),-- 标记点数量已达上限，请删除部分后再尝试添加
		coord = GetS(532),--坐标
		height = GetS(533),--高度
		addMarkTipTitle = GetS(80231),--如何添加标记点
		addMarkTipTitle2 = GetS(80209),--添加标记点
		addMarkTipContent = GetS(80232),--单击任意空白区域，即可弹出标记点放置界面，可以设置标记点的图案与名称。
		name = GetS(153),--名称
		icon = GetS(80218),--图案
		marked = GetS(80234),--已标记@1/@2
		sure = GetS(80222),--确定
		trace = GetS(80219),--追踪
		untrace = GetS(80221),--取消追踪
		delete = GetS(80220),--删除
	}
	self.m_Graphs = {}
end

function pixelmapCtrl:StartUpdata()
    local function _update()
        if not type(self.posints) == "table" then
            return
        end
		--CD刷新
        for i,v in ipairs(self.posints) do
			local itemdef = ItemDefCsv:get(v.itemid)
			--MiniLogWarning("pixelmapCtrl:StartUpdata v :"..v.time)

			while itemdef do
				local CD = tonumber(itemdef.para)
				if not CD then
					CD = 20
				end
				
				local tag = self.m_socpixmapLogic:GetTagByPosition(v.x,v.z,bed_tag)
				if not tag then
					break
				end

				local node = tag.node
				if not node then
					break
				end

				if v.time > CD then
					node:getController("cd"):setSelectedIndex(0)
					local dielist_item = self.view.bedlist:getChildAt(i - 1)
					if dielist_item then
						dielist_item:getController("cd"):setSelectedIndex(0)
					end
					break
				end
				--显示CD倒计时
				node:getController("cd"):setSelectedIndex(1)
				--node:getChild("cdtime"):setText(tostring(CD - v.time))
				node:getChild("cdtime"):setText(tostring(math.floor(CD - v.time)))

				local dielist_item = self.view.bedlist:getChildAt(i - 1)
				if dielist_item then
					dielist_item:getController("cd"):setSelectedIndex(1)
					--dielist_item:getChild("cdtime"):setText(tostring(CD - v.time))
					dielist_item:getChild("cdtime"):setText(tostring(math.floor(CD - v.time)))
				end

				v.time = v.time + 1
				break
			end
        end

		--长按列表
		self.delete_bedtag = {}
		for i,v in ipairs(self.touch_bedtag) do
			local tag = self.m_socpixmapLogic:GetTagByPosition(v.position.x,v.position.z,bed_tag)
			--找不到可能被删除了
			if not tag then
				table.insert(self.delete_bedtag, v)
			end

			if not tag.time then
				tag.time = 0
			end

			tag.time = tag.time + 1
			tag.node:getController("bar"):setSelectedIndex(1)
			tag.node:getChild("bar"):setFillAmount(tag.time / 3)
			MiniLogWarning("pixelmapCtrl:StartUpdata value "..(tag.time / 3))

			if tag.time > 3 then
				table.insert(self.delete_bedtag, v)
				--self.m_socpixmapLogic:RemoveTagByNode(tag.node)
				if CurMainPlayer then
					local SocRevivePointComponent = CurMainPlayer:GetComponentByName("SocRevivePointComponent")
					SocRevivePointComponent = tolua.cast(SocRevivePointComponent,"SocRevivePointComponent")
					SocRevivePointComponent:GivePosint(v.position.x,v.position.y,v.position.z)
					MiniLogWarning("pixelmapCtrl:StartUpdata give posint")
				end
			end
		end

		for i,v in ipairs(self.delete_bedtag) do
			for j,v2 in ipairs(self.touch_bedtag) do
				if v == v2 then
					table.remove(self.touch_bedtag, j)
					MiniLogWarning("pixelmapCtrl:StartUpdata remove touch bedtag")
					break
				end
			end
		end
    end
	MiniLogWarning("pixelmapCtrl:StartUpdata")
	self.m_timer = GetInst("MiniUIScheduler"):regPrivate(self.view.soc_maploader,_update,1,-1,nil,false)
end

function pixelmapCtrl:StopUpdata()
	MiniLogWarning("pixelmapCtrl:StopUpdata")
    GetInst("MiniUIScheduler"):unreg(self.m_timer)
    self.m_timer = nil
end

--启动
function pixelmapCtrl:Start()
	--MiniLogWarning("pixelmapCtrl:Start "..debug.traceback())

	__private.initMulLangForConstNode(self)
	__private.regClickListen(self)
	self.super:Start()
	if not self.m_pixmapLogic then
		if PixelMapInterface:IsEnterSocMap() then
			self.m_socpixmapLogic = ClassList["SocPixelMapLogic"].new({ctrl = self,model = self.model,view = self.view});
		else
			self.m_pixmapLogic = ClassList["PixelMapShowLogic"].new({ctrl = self,model = self.model,view = self.view});
			self.m_markerLogic = ClassList["PixelMapMarkerLogic"].new({ctrl = self,model = self.model,view = self.view});
		end
	end

	--小地图页面曝光埋点
	--self:StandbyReport('SCREEN_MAP_2D', '-', 'view', nil)
	MiniLogWarning("pixelmapCtrl:Start IsEnterSocMap")
	if PixelMapInterface:IsEnterSocMap() then
		self:InitSocUIEvent()
		self.posints = {}
		if self.view.bedlist then
			self.view.bedlist:setNumItems(0)
		end
		self.touch_bedtag = {}

		self.m_pckey_map = {
			[InputKeyCode.SDLK_c] = false,
			[InputKeyCode.SDLK_z] = false,
			[InputKeyCode.SDLK_x] = false,
		}

		self._events = {
			{name = "SocRevivePoint_AddPosition", callback = handler(self, self.SocRevivePoint_AddPosition)},
			{name = "SocRevivePoint_DelPosition", callback = handler(self, self.SocRevivePoint_DelPosition)},
			{name = "SocRevivePoint_RenameBed", callback = handler(self, self.SocRevivePoint_RenameBed)},
			{name = "SocRevivePoint_CheckPositionRet", callback = handler(self, self.SocRevivePoint_CheckPositionRet)},
			{name = "SocRevivePoint_ReqAllPosints", callback = handler(self, self.SocRevivePoint_ReqAllPosints)},
			{name = "SocReviveEvent_AirDrop", callback = handler(self, self.SocReviveEvent_AirDrop)},
			--{name = "AllSingleBuildData", callback = handler(self, self.AllSingleBuildData)},
			{name = "SocReviveSuccess", callback = handler(self, self.SocReviveSuccess)},
			{name = GameEventType.PCKeyEvent, callback = handler(self, self.PCKeyEvent)},
			{name = "SocMainTeamTags", callback = handler(self, self.SocMainTeamTags)},
			{name = "SocTeamPositions", callback = handler(self, self.SocTeamPositions)},
			{name = "AirDrop_getTime", callback = handler(self, self.AirDrop_getTime)},
		}
		self:SubscibeModuleEvents()
		self.m_socpixmapLogic:Start()
		self:StartUpdata()
	else
		self.m_pixmapLogic:Start();
		self.m_markerLogic:Start();

		--self._events = {
		--	{name = "SocReviveEvent_AirDrop", callback = handler(self, self.SocReviveEvent_AirDrop)},
		--}
		--self:SubscibeModuleEvents()
	end
end

function pixelmapCtrl:PCKeyEvent(param)
	--param.result
	--param.isKeyDown
	--InputKeyCode.SDLK_c
	--InputKeyCode.SDLK_z
	--InputKeyCode.SDLK_x
	--MiniLogWarning("pixelmapCtrl:PCKeyEvent ")
	if self.m_pckey_map[param.result] ~= nil then
		self.m_pckey_map[param.result] = param.isKeyDown
	end

	--MiniLogWarning("InputKeyCode.SDLK_c :"..tostring(self.m_pckey_map[InputKeyCode.SDLK_c]))
	--MiniLogWarning("InputKeyCode.SDLK_x :"..tostring(self.m_pckey_map[InputKeyCode.SDLK_x]))
	--MiniLogWarning("InputKeyCode.SDLK_z :"..tostring(self.m_pckey_map[InputKeyCode.SDLK_z]))

	--放大地图
	if self.m_pckey_map[InputKeyCode.SDLK_c] and self.m_pckey_map[InputKeyCode.SDLK_x] then
		--MiniLogWarning("pixelmapCtrl:PCKeyEvent MapScaleStep 1")
		self.m_socpixmapLogic:MapScaleStep(1)
		return
	end

	--缩小地图
	if self.m_pckey_map[InputKeyCode.SDLK_c] and self.m_pckey_map[InputKeyCode.SDLK_z] then
		--MiniLogWarning("pixelmapCtrl:PCKeyEvent MapScaleStep -1")
		self.m_socpixmapLogic:MapScaleStep(-1)
		return
	end
end

function pixelmapCtrl:AirDrop_getTime(param)
	MiniLogWarning("pixelmapCtrl:AirDrop_getTime "..param.countdown_time)
	self.m_socpixmapLogic:SetAirtime(param.countdown_time)
end

--function pixelmapCtrl:AllSingleBuildData(param)
--	MiniLogWarning("pixelmapCtrl:AllSingleBuildData")
--	if self.m_socpixmapLogic then
--		self.m_socpixmapLogic:InitBuildData(param.data)
--	end
--end

function pixelmapCtrl:SocReviveSuccess(param)
	MiniLogWarning("pixelmapCtrl:SocReviveSuccess")
	self:ShowMainMiniMap()
	--睡觉ui会恢复准星
	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:SetUISleep()
	end
end

function pixelmapCtrl:AddSocTeamPositions(TeamPositions)
	self.m_socpixmapLogic:AddSocTeamPositions(TeamPositions)
end

function pixelmapCtrl:SocTeamPositions(param)
	--MiniLogWarning("pixelmapCtrl:SocTeamPositions data = "..param.data)
	local TeamPositions = json2table(param.data)
	local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
	if ctrl then
		ctrl:AddSocTeamPositions(TeamPositions)
	end
end

function pixelmapCtrl:AddSocMainTeamTags(tags)
	self.m_socpixmapLogic:AddSocMainTeamMainTags(tags)
end

function pixelmapCtrl:SocMainTeamTags(param)
	MiniLogWarning("pixelmapCtrl:SocMainTeamTags data = "..param.data)
	local tags = json2table(param.data)
	local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
	if ctrl then
		ctrl:AddSocMainTeamTags(tags)
	end
end

function pixelmapCtrl:SocRevivePoint_CheckPositionRet(param)
	if param.ret ~= 0 then
		MiniLogWarning("SocRevivePoint_CheckPositionRet ret != 0 " .. param.ret.." msg:"..param.msg)
		ShowGameTips("不可用")
		self.select_data = nil
		return
	end

	if self.select_data then
		self.select_data.time = 0
		MiniLogWarning("pixelmapCtrl:SocRevivePoint_CheckPositionRet self.select_data.time :"..self.select_data.time)
	end
	self:Revive()
end

--events的函数好像访问的是镜像所以需要通过ctrl来访问
function pixelmapCtrl:AddPosint(posint)
	table.insert(self.posints, posint)
	if self.posints then
		self.view.bedlist:setNumItems(#self.posints)
	end
	self.m_socpixmapLogic:SocRevivePoint_Refresh(self.posints)

	if self.posints then
		self.view.bedlist:setNumItems(#self.posints)
		self.view.bedlist_c:setSelectedIndex(#self.posints == 0 and 0 or 1)
	else
		self.view.bedlist_c:setSelectedIndex(0)
	end
end

function pixelmapCtrl:SocRevivePoint_AddPosition(param)
	local posint = json2table(param.pos)
	local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
	if ctrl then
		ctrl:AddPosint(posint)
	end
end

function pixelmapCtrl:RemovePosintByIndex(index)
	table.remove(self.posints, index)
	if self.posints then
		self.view.bedlist:setNumItems(#self.posints)
	end
	self.m_socpixmapLogic:SocRevivePoint_Refresh(self.posints)

	if self.posints then
		self.view.bedlist:setNumItems(#self.posints)
		self.view.bedlist_c:setSelectedIndex(#self.posints == 0 and 0 or 1)
	else
		self.view.bedlist_c:setSelectedIndex(0)
	end
end

function pixelmapCtrl:SocRevivePoint_DelPosition(param)
	local posint = json2table(param.pos)
	
	for i,v in ipairs(self.posints) do
		if v.x == posint.x and v.y == posint.y and v.z == posint.z then
			local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
			if ctrl then
				ctrl:RemovePosintByIndex(i)
			end
			break
		end
	end
end

function pixelmapCtrl:SocRevivePoint_RenameBed(param)
	local posint = json2table(param.pos)
	--local newName = param.name or ""
	
	for i,v in ipairs(self.posints) do
		if v.x == posint.x and v.y == posint.y and v.z == posint.z then
			-- 更新床位名称
			v.name = posint.name
			-- 刷新显示
			self.m_socpixmapLogic:SocRevivePoint_Refresh(self.posints)
			MiniLogWarning("SocRevivePoint_RenameBed: Renamed bed at (" .. posint.x .. "," .. posint.y .. "," .. posint.z .. ") to '" .. newName .. "'")
			break
		end
	end

	if self.posints then
		self.view.bedlist:setNumItems(#self.posints)
		self.view.bedlist_c:setSelectedIndex(#self.posints == 0 and 0 or 1)
	else
		self.view.bedlist_c:setSelectedIndex(0)
	end
end

function pixelmapCtrl:TouchBegin_BedTag(tag_item)
	tag_item.time = 0
	table.insert(self.touch_bedtag, tag_item)
end

function pixelmapCtrl:TouchEnd_BedTag(tag_item)
	local tag = self.m_socpixmapLogic:GetTagByPosition(tag_item.position.x,tag_item.position.z,bed_tag)
	--找不到可能被删除了
	if not tag then
		return
	end
	tag.node:getController("bar"):setSelectedIndex(0)

	for i,v in ipairs(self.touch_bedtag) do
		if v == tag_item then
			table.remove(self.touch_bedtag, i)
			break
		end
	end
end

function pixelmapCtrl:SetPosints(posints)
	self.posints = posints
	if self.posints then
		self.view.bedlist:setNumItems(#self.posints)
	end
	self.m_socpixmapLogic:SocRevivePoint_Refresh(posints)

	if self.posints then
		self.view.bedlist:setNumItems(#self.posints)
		self.view.bedlist_c:setSelectedIndex(#self.posints == 0 and 0 or 1)
	else
		self.view.bedlist_c:setSelectedIndex(0)
	end
end

function pixelmapCtrl:SocRevivePoint_ReqAllPosints(param)
	MiniLogWarning("pixelmapCtrl:SocRevivePoint_ReqAllPosints "..param.posints)
	local posints = json2table(param.posints)
	
	local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
    if ctrl then
		ctrl:SetPosints(posints)
	end
end

function pixelmapCtrl:GetTagList()
	if self.m_socpixmapLogic then
		return self.m_socpixmapLogic:GetTagList()
	end

	return {}
end

function pixelmapCtrl:SocReviveEvent_AirDrop(param)
	id = param.eventid
	x = param.current_pos_x  
	z = param.current_pos_z  
	MiniLogWarning("pixelmapCtrl:SocReviveEvent_AirDrop "..id .. " " .. param.eventname .. " " .. param.eventtype .. " x=" .. x .. " z=" .. z)

	MiniLogWarning("drop param.drop_pos_x="..param.drop_pos_x.." param.drop_pos_z="..param.drop_pos_z)

	if param.eventtype == "start" then
		--MiniLogWarning("pixelmapCtrl:SocReviveEvent_AirDrop AIR_DROP_Start")
		local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
		if ctrl and ctrl.m_socpixmapLogic then
			ctrl.m_socpixmapLogic:AddAirdrop(id, {
				start_pos = {x = param.start_pos_x, z = param.start_pos_z},
				end_pos = {x = param.end_pos_x, z = param.end_pos_z},
				current_pos = {x = x, z = z},
				drop_pos = {x = param.drop_pos_x, z = param.drop_pos_z},
			})
		end

	elseif param.eventtype == "end" then
		--MiniLogWarning("pixelmapCtrl:SocReviveEvent_AirDrop AIR_DROP_End")
		local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
		--空投结束请求剩余时间
		if ClientCurGame and ClientCurGame.GetSocAirTime then
            ClientCurGame:GetSocAirTime(AccountManager.account.Account.Uin)
        end
		if ctrl and ctrl.m_socpixmapLogic then
			ctrl.m_socpixmapLogic:RemoveAirdrop(id)
		end
	else
		--MiniLogWarning("pixelmapCtrl:SocReviveEvent_AirDrop AIR_DROP_running")
		local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
		if ctrl and ctrl.m_socpixmapLogic then
			ctrl.m_socpixmapLogic:UpdateAirdrop(id, {
				start_pos = {x = param.start_pos_x, z = param.start_pos_z},
				end_pos = {x = param.end_pos_x, z = param.end_pos_z},
				current_pos = {x = x, z = z},
				drop_pos = {x = param.drop_pos_x, z = param.drop_pos_z},
			})
		end
	end
end

function pixelmapCtrl:SubscibeModuleEvents()
    self._events = self._events or {}

    for _, eventInfo in pairs(self._events) do
        if eventInfo.name and eventInfo.callback then
            SandboxLua.eventDispatcher:CreateEvent(nil, eventInfo.name)
            eventInfo.handler = SandboxLua.eventDispatcher:SubscribeEvent (nil, eventInfo.name, function(context)
                if eventInfo.callback then
                    local param = context:GetParamData()
                    
                    if not param then
                        print("techtreepageModel Error： param is nil")
                        return
                    end

                    return eventInfo.callback(param)
                end
            end)
        end
    end
end

function pixelmapCtrl:UnSubscibeModuleEvents()
	for _, eventInfo in pairs(self._events) do
        local eventName = eventInfo.name
        local handler = eventInfo.handler

        if not eventName or not handler then
            return
        end

		SandboxLua.eventDispatcher:UnsubscribeEvent(nil, eventName, handler)
		eventInfo.handler = nil
    end
end

function pixelmapCtrl:BedRevivePoint(x,y,z)
	local itemdata = nil
	for i,v in ipairs(self.posints) do
		if v.x == x and v.y == y and v.z == z then
			itemdata = v
		end
	end

	if not itemdata then
		return
	end

	self.select_data = itemdata

	local itemdef = ItemDefCsv:get(itemdata.itemid)
	if not itemdef then
		MiniLogWarning("pixelmapCtrl:BedRevivePoint itemdef is nil")
		ShowGameTips(GetS(10000745).." itemdef = null")
		return
	end

	local CD = tonumber(itemdef.para)
	if not CD then
		CD = 20
	end

	if itemdata.time < CD then
		ShowGameTips(GetS(10000745))
		return
	end

	if CurMainPlayer then
		local SocRevivePointComponent = CurMainPlayer:GetComponentByName("SocRevivePointComponent")
		SocRevivePointComponent = tolua.cast(SocRevivePointComponent,"SocRevivePointComponent")
		SocRevivePointComponent:SelectPosint(x,y,z)
		--self:Revive()
	end
end

function pixelmapCtrl:InitSocUIEvent()
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.revivebtn,UIEventType_Click,function(obj, context)
		--self:ReviveToNearestPoint()
		self:ReviveToNearestPoint()
	end)

	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.randombtn,UIEventType_Click,function(obj, context)
		self:Revive()
	end)

	GetInst("MiniUIComponents"):setCallback(self.view.bedlist, "GList.itemRenderer", function(comp, idx, obj)
		local item = self.posints[idx + 1]
		if not item then
			return
		end
		--obj:getChild("title"):setText("BAG"..(idx + 1))
		obj:getChild("title"):setText(item.name)
	end)
	
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.bedlist,UIEventType_ClickItem,function(obj, context)
		local item = context:getData()
		local itemindex = obj:getChildIndex(item)
		local itemdata = self.posints[itemindex + 1]
		if not itemdata then
			return
		end

		self:BedRevivePoint(itemdata.x,itemdata.y,itemdata.z)
	end)

	self.view.bedlist:setNumItems(0)
end

--刷新
function pixelmapCtrl:Refresh()
	self.super:Refresh()
	if self.m_pixmapLogic then
		self.m_pixmapLogic:Refresh()
	end

	if self.m_socpixmapLogic then
		self.m_socpixmapLogic:Refresh();
	end

	if CurMainPlayer then
		CurMainPlayer:setSightMode(false);
	end

	if self.view.soc_mainmap then
		self.view.soc_mainmap:setVisible(true)
	end
end

--刷新
function pixelmapCtrl:Reset()
	self.super:Reset()
	if self.m_pixmapLogic then
		self.m_pixmapLogic:Reset()
	end
	if self.m_markerLogic then
		self.m_markerLogic:Reset();
	end

	if self.m_socpixmapLogic then
		self.m_socpixmapLogic:Reset();
	end

	if CurMainPlayer then
		CurMainPlayer:setSightMode(true);
	end

	if self.view.soc_mainmap then
		self.view.soc_mainmap:setVisible(false)
	end
end

--消息处理
function pixelmapCtrl:FGUIHandleEvent(eventName)
	if eventName == "GIE_MINIMAP_CHANGE" then

	end
end

function pixelmapCtrl:InitMap()
	if self.m_socpixmapLogic then
		self.m_socpixmapLogic:InitMap()
	end
end

function pixelmapCtrl:GetCurrentSceneWH()
	local scence = GetInst("MiniUISceneMgr"):getCurrentSceneRootNode()
	return scence:getWidth(),scence:getHeight();
end

--界面关闭回调
function pixelmapCtrl:OnRemove()
	MiniLogWarning("pixelmapCtrl:OnRemove")
	if self.m_pixmapLogic then
		self.m_pixmapLogic:OnRemove();
	end
	if self.m_markerLogic then
		self.m_markerLogic:OnRemove();
	end

	if self.m_socpixmapLogic then
		self.m_socpixmapLogic:OnRemove();
	end

	if self._events then
		self:UnSubscibeModuleEvents()
	end

	if PixelMapInterface:IsEnterSocMap() then
		self:StopUpdata()

		local root = GetInst("MiniUISceneMgr"):getCurrentSceneRootNode()
		root:removeChild(self.view.soc_mainmap)

		self.m_pckey_map[InputKeyCode.SDLK_c] = false
		self.m_pckey_map[InputKeyCode.SDLK_z] = false
		self.m_pckey_map[InputKeyCode.SDLK_x] = false
	end

	self.m_Graphs = {}
end

function pixelmapCtrl:AddChildToMap(pNode,pBlockX,pBlockY,pOrder)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end

	self.m_pixmapLogic:AddChild(pNode,pBlockX,pBlockY,pOrder);
end

function pixelmapCtrl:MoveMapBlockPosToScreenCenterAction(pBlockX,pBlockZ,pCallBack)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	self.m_pixmapLogic:MoveMapBlockPosToScreenCenterAction(pBlockX,pBlockZ,pCallBack);
end

function pixelmapCtrl:BlockPos2ScreenPos(pX,pZ)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	return self.m_pixmapLogic:BlockPos2ScreenPos(pX,pZ);
end

function pixelmapCtrl:SceenPos2BlockPos(pX,pZ)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	return self.m_pixmapLogic:SceenPos2BlockPos(pX,pZ);
end

function pixelmapCtrl:OnMapChange(pType,pParam)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	self.m_markerLogic:OnMapChange(pType,pParam);
end

function pixelmapCtrl:OnESCCall()
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
    self.m_markerLogic:OnESCCall();
end
------------------------------------------------------button callback-------------------------------------------
function pixelmapCtrl:Btn_closeClick(obj, context)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	self:StandbyReport('SCREEN_MAP_2D', 'Close', 'click', nil)
	PixelMapInterface:HideMiniMap();
end

function pixelmapCtrl:Btn_3dMapClick(obj, context)  --打开3d地图
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	self:StandbyReport('SCREEN_MAP_2D', '3DButton', 'click', nil)
	CompassOpenMap_OnClick();
	PixelMapInterface:HideCompass()
end
------------------------------------------------------private--------------------------------------------------
function __private.regClickListen(self)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mm_Btn_3dMap, UIEventType_Click, function(obj, context)
        self:Btn_3dMapClick();
    end)
end

function pixelmapCtrl:StandbyReport(cID, oID, event, eventTb)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	local reportWorldParam = {}
	local luaTable = {}
	local statue,standby1,standby2,standby3 = userTaskReportGetWorldParam2(reportWorldParam,luaTable)
	local publictab = {
		game_session_id = get_game_session_id(),
		cid = reportWorldParam.worldid,
		ctype = reportWorldParam.ctype,
		standby1 = standby1,
		standby2 = standby2,
		standby3 = standby3
	}
	local endtab = {}
	if eventTb == nil then
		endtab = publictab
	elseif eventTb ~= nil then
		endtab = publictab
		endtab.standby4 = eventTb.standby4
		endtab.standby5 = eventTb.standby5
		endtab.standby6 = eventTb.standby6
	end
end

function __private.initMulLangForConstNode(self)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	self.view.root:getChildByPath("popups_question.title_01"):setText(self.m_langConifg.addMarkTipTitle);
	self.view.root:getChildByPath("popups_question.title_content"):setText(self.m_langConifg.addMarkTipContent);
	self.view.root:getChildByPath("popups_addpoint.title_name"):setText(self.m_langConifg.name);
	self.view.root:getChildByPath("popups_addpoint.title_pattern"):setText(self.m_langConifg.icon);

	--添加自定义标记
	self.view.root:getChildByPath("popups_addpoint.n42.title"):setText(self.m_langConifg.sure);
	self.view.root:getChildByPath("popups_addpoint.n40.title"):setText(self.m_langConifg.trace);
	self.view.root:getChildByPath("popups_addpoint.cancelTraceBtn.title"):setText(self.m_langConifg.untrace);
	self.view.root:getChildByPath("popups_addpoint.n41.title"):setText(self.m_langConifg.delete);

	--官方标记展示
	self.view.root:getChildByPath("popups_point.track.title"):setText(self.m_langConifg.trace);
	self.view.root:getChildByPath("popups_point.nottrack.title"):setText(self.m_langConifg.untrace);
end


function pixelmapCtrl:RefMapGraphs(infos)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	if infos then
		self.m_Graphs = infos
		for k, v in pairs(self.m_Graphs) do
			self.m_pixmapLogic:newShape(v.name,v.isshow,v.itype)
		end
	end
end

function pixelmapCtrl:updateCircle(shapeid, cx, cz, r)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	if self.m_Graphs[shapeid] then
		self.m_pixmapLogic:updateCircle(self.m_Graphs[shapeid].name,cx, cz, r,self.m_Graphs[shapeid].color)
	end
end

function pixelmapCtrl:updateLine(shapeid,sx, sz, ex, ez)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	if self.m_Graphs[shapeid] then
		self.m_pixmapLogic:updateLine(self.m_Graphs[shapeid].name,sx, sz, ex, ez,self.m_Graphs[shapeid].color)
	end
end

function pixelmapCtrl:updateRectangle(shapeid,sx, sz, w, h)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	if self.m_Graphs[shapeid] then
		self.m_pixmapLogic:updateRectangle(self.m_Graphs[shapeid].name,sx, sz, w, h,self.m_Graphs[shapeid].color)
	end
end

function pixelmapCtrl:showShape(shapeid, showflag)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	if self.m_Graphs[shapeid] then
		self.m_Graphs[shapeid].isshow = showflag
		self.m_pixmapLogic:showShape(self.m_Graphs[shapeid].name,showflag)
	end
end

function pixelmapCtrl:newShape(shapeid, type, isshow, r, g, b,a)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	if self.m_Graphs[shapeid] then
		return
	end
    local tdata = {
        itype = type,
        isshow = isshow,
        color = {r =r/255,g=g/255,b = b/255,a=a/255},
        id = shapeid,
		name = 'graph_'..shapeid,
    }
	self.m_Graphs[tdata.id] = tdata
	self.m_pixmapLogic:newShape(tdata.name,isshow,type)
    return tdata.id;
end


function pixelmapCtrl:deleteShape(shapeid)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end

	if self.m_Graphs[shapeid] then
		self.m_pixmapLogic:deleteShape(self.m_Graphs[shapeid].name)
		self.m_Graphs[shapeid] = nil
	end
end

--增加一个自定义节点
function pixelmapCtrl:addCustomMarker(iconName,name,x,z,level)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	local pMarkerData={}
	pMarkerData.iconName=iconName
	pMarkerData.name= name
	pMarkerData.posX=x
	pMarkerData.posZ=z
	pMarkerData.level=level
	pMarkerData.zoomMin=0.001
	pMarkerData.zoomMax=20
	pMarkerData.isCanUI = true;
	pMarkerData.type = "CustomMark";
	PixelMapInterface:FillMakerData(pMarkerData);
	local owid = CurWorld:getOWID();
    local mapId = CurWorld:getCurMapID();
    local path = "data/".."pixelMapMarker/w"..owid.."/".."m_"..mapId;
    self.m_markerLogic.m_localData = GetInst("LocalDataManager"):CreatePlayerData("root",path):SetEncryptFlag("xxtea_64");
    self.m_markerLogic.m_localDataChunk = self.m_markerLogic.m_localData:GetDataChunk();
	if not self.m_markerLogic.m_localDataChunk.markData then
        self.m_markerLogic.m_localDataChunk.markData = {};
    end
	for k,v in pairs(self.m_markerLogic.m_localDataChunk.markData) do
        if v.posX == pMarkerData.posX and v.posZ == pMarkerData.posZ and v.name == pMarkerData.name then
			return
        end
    end

	table.insert(self.m_markerLogic.m_localDataChunk.markData,pMarkerData);
	self.m_markerLogic.m_localData:NotifyDataChange(pMarkerData,"add");
	self.m_markerLogic.__private.createAndInitMarkerNode(self.m_markerLogic,pMarkerData);
	self.m_markerLogic.__private.refreshMarkerCounterNode(self.m_markerLogic);
	self.m_markerLogic.m_localData:Save(true);
end

--删除一个自定义节点
function pixelmapCtrl:delCustomMarker(iconName,name,x,z,level)
	if PixelMapInterface:IsEnterSocMap() then
		return
	end
	local pMarkerData={}
	pMarkerData.iconName=iconName
	pMarkerData.name=name
	pMarkerData.posX=x
	pMarkerData.posZ=z
	pMarkerData.level=level
	pMarkerData.zoomMin=0.001
	pMarkerData.zoomMax=20
	local owid = CurWorld:getOWID();
    local mapId = CurWorld:getCurMapID();
    local path = "data/".."pixelMapMarker/w"..owid.."/".."m_"..mapId;
    self.m_markerLogic.m_localData = GetInst("LocalDataManager"):CreatePlayerData("root",path):SetEncryptFlag("xxtea_64");
    self.m_markerLogic.m_localDataChunk = self.m_markerLogic.m_localData:GetDataChunk();

	for k,v in pairs(self.m_markerLogic.m_localDataChunk.markData) do
        if v.posX == pMarkerData.posX and v.posZ == pMarkerData.posZ and v.name == pMarkerData.name then
			self.m_markerLogic.__private.delOneMarkerAllByBindData(self.m_markerLogic,v);
			self.m_markerLogic.__private.refreshMarkerCounterNode(self.m_markerLogic);
			self.m_markerLogic.__private.hideMarkerInfoEdit(self.m_markerLogic);
            break;
        end
    end
	self.m_markerLogic.m_localData:Save(true);
end

function pixelmapCtrl:IsShowReviveFrame()
	if self.view.soc_pixelmap then
		return self.view.showrevive:getSelectedIndex() == 1
	end
end

function pixelmapCtrl:IsShowMainMap()
	if self.view.soc_pixelmap then
		return self.view.showrevive:getSelectedIndex() == 0
	end
end

function pixelmapCtrl:IsShowMiniMap()
	if self.view.soc_pixelmap then
		return self.view.showrevive:getSelectedIndex() == 2
	end
end

--似乎有重生bug没复现，留下log查看
local function printCaller()
    -- 获取调用者的栈信息
    local info = debug.getinfo(4, "Sl")
    if info then
        MiniLogWarning("Called from: " .. (info.source or "unknown") ..
              ", line " .. (info.currentline or "?"))
    else
        MiniLogWarning("Could not get caller info.")
    end
end

function pixelmapCtrl:ShowReviveFrame()
	printCaller()
	if self.view.showrevive:getSelectedIndex() == 1 then
		return
	end

	if self.m_socpixmapLogic then
		if self.view.showrevive:getSelectedIndex() == 2 then
			self.m_socpixmapLogic:SetMask(false)
		end
		ClientCurGame:setOperateUI(true)
		self.view.showrevive:setSelectedIndex(1)
		self.m_socpixmapLogic:UpdateDieTag()
	end
end

function pixelmapCtrl:ShowMainMap()
	printCaller()
	if self.view.showrevive:getSelectedIndex() == 0 then
		return
	end

	if self.m_socpixmapLogic then
		if self.view.showrevive:getSelectedIndex() == 2 then
			self.m_socpixmapLogic:SetMask(false)
		end
		ClientCurGame:setOperateUI(true)
		self.view.showrevive:setSelectedIndex(0)
	end
end

function pixelmapCtrl:ShowMainMiniMap()
	if CurMainPlayer and CurMainPlayer:isDead() then
		--self:ShowReviveFrame()
		return
	end

	printCaller()
	if self.view.showrevive:getSelectedIndex() == 2 then
		return
	end

	--MiniLogWarning("pixelmapCtrl:ShowMainMiniMap "..debug.traceback())
	if self.m_socpixmapLogic then
		self.m_socpixmapLogic:SetMask(true)
	end
	ClientCurGame:setOperateUI(false)
	self.view.showrevive:setSelectedIndex(2)
end

local PlayerAttributeType = {
	Life = 1,
	Food = 2,
	Thirst = 3,
	Temperature = 4,
	Oxygen = 5,
	Comfort = 6,
	Radiation = 7,
	Strength = 8,
}

local function get_player_name(playerid)
	local playernum = ClientCurGame and ClientCurGame.getNumPlayerBriefInfo and ClientCurGame:getNumPlayerBriefInfo() or 0;
    for i = 1, playernum do
        local briefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
        if briefInfo ~= nil then
            if tonumber(briefInfo.uin) ~= 1000 and playerid == tonumber(briefInfo.uin) then
                return briefInfo.nickname
            end
        end
    end

	return ""
end

function pixelmapCtrl:SetReviveData()
	if CurMainPlayer then
		local SocRevivePointComponent = CurMainPlayer:GetComponentByName("SocRevivePointComponent")
		SocRevivePointComponent = tolua.cast(SocRevivePointComponent,"SocRevivePointComponent")
		if self.posints then
			self.view.bedlist:setNumItems(#self.posints)
		end
	end

	if not self.view.soc_pixelmap then
		return
	end

	if self.posints then
		self.view.bedlist_c:setSelectedIndex(#self.posints == 0 and 0 or 1)
	else
		self.view.bedlist_c:setSelectedIndex(0)
	end

	local dieinfo = CurMainPlayer:getSocAttackInfo()

	local atktype = dieinfo.atktype
	local buffId = dieinfo.buffId
	local buffLevel = dieinfo.buffLevel
	local toolid = dieinfo.toolid
	local playerid = dieinfo.playerid
	local length = dieinfo.length
	local mobid = dieinfo.mobid
	local survival_time_str = dieinfo:GetSurvivalStr()

	MiniLogWarning("pixelmapCtrl:SetReviveData atktype "..atktype..
		" buffId "..buffId..
		" toolid "..toolid..
		" playerid "..playerid..
		" mobid "..mobid..
		" survival_time_str "..survival_time_str)

	local dietop = self.view.soc_pixelmap:getChild("dietop")
	local diedown = self.view.soc_pixelmap:getChild("diedown")

	local panel_mid = dietop:getChild("panel_mid")
	local panel_right = dietop:getChild("panel_right")

	local die_text = ""
	local weapon_text = ""
	local killlength_text = ""

	panel_mid:getChild("livetime"):setText(survival_time_str)

	--人导致的死亡
	if playerid ~= 0 then
		MiniLogWarning("pixelmapCtrl:SetReviveData playerid "..playerid.." toolid "..toolid)
		panel_right:setVisible(false)
		die_text = get_player_name(playerid)
		if die_text == "" and playerid == tonumber(AccountManager:getUin()) then
			die_text = AccountManager:getNickName()
		end

		--人使用工具杀的
		if toolid ~= 0 then
			panel_right:setVisible(true)
			local itemdef = ItemDefCsv:get(toolid)
			if itemdef then
				weapon_text = itemdef.Name
			else
				weapon_text = "未知工具"
			end

			UIUtils:SetItemIcon(panel_right:getChild("icon"), tonumber(toolid))
			panel_right:getChild("weaponname"):setText(weapon_text)
			panel_right:getChild("killlength"):setText(tostring(length).."m")
		end

		--todo 角色头像
		MiniLogWarning("pixelmapCtrl:SetReviveData die_text "..die_text)
		panel_mid:getChild("die"):setText(die_text)
		return
	end

	--野兽导致的死亡
	if mobid ~= 0 then
		MiniLogWarning("pixelmapCtrl:SetReviveData mobid "..mobid)
		panel_right:setVisible(false)

		local monsterdef = MonsterCsv:get(mobid)
		if monsterdef then
			die_text = monsterdef.Name
		else
			die_text = "未知野兽"
		end
		panel_mid:getChild("icon"):setURL("ui/roleicons/"..monsterdef.Icon..".png")
		panel_mid:getChild("die"):setText(die_text)
		return
	end

	--除了buff 人为 野兽 其他原因走type
	panel_right:setVisible(false)
	MiniLogWarning("pixelmapCtrl:SetReviveData atktype "..atktype)

	local dienum = DieInfoCsv:getNum()
	for i = 1, dienum do
		local dieinfo = DieInfoCsv:get(i-1)
		if dieinfo.ID == atktype then
			die_text = GetS(dieinfo.LanguageId)
			panel_mid:getChild("die"):setText(die_text)
			panel_mid:getChild("icon"):setURL("ui/itemexticons/"..dieinfo.Icon..".png")
			return
		end
	end
	
	die_text = "未知"
	panel_mid:getChild("die"):setText(die_text)
end

function pixelmapCtrl:Revive()
	if not ClientCurGame.getMainPlayer or ClientCurGame:getMainPlayer():revive(0) then
		--解决某些界面锁定了UI，重生时需要解开
		ClientCurGame:setOperateUI(false);
		ShowHomeMainUI()
		--@soc2024
		SetSocUI()
	end
	self.model:GetIncomingParam().disableOperateUI = true
end

function pixelmapCtrl:ReviveToNearestPoint()
	-- 获取玩家死亡位置
	local deathX, deathY, deathZ = 0, 0, 0
	if CurMainPlayer then
		deathX, deathY, deathZ = CurMainPlayer:getPosition(0, 0, 0)
		-- 转换为方块坐标
		deathX = math.floor(deathX / 100)
		deathY = math.floor(deathY / 100)
		deathZ = math.floor(deathZ / 100)
	end
	
	-- 查找可用的复活点
	local availablePoints = {}
	if self.posints then
		for i, v in ipairs(self.posints) do
			local itemdef = ItemDefCsv:get(v.itemid)
			if itemdef then
				local CD = tonumber(itemdef.para) or 20
				-- 检查CD是否完成
				if v.time >= CD then
					table.insert(availablePoints, v)
				end
			end
		end
	end
	
	-- 如果有可用复活点，选择距离最近的
	if #availablePoints > 0 then
		local nearestPoint = nil
		local nearestDistance = math.huge
		
		for _, point in ipairs(availablePoints) do
			-- 计算距离（使用欧几里得距离公式）
			local dx = point.x - deathX
			local dy = point.y - deathY
			local dz = point.z - deathZ
			local distance = math.sqrt(dx * dx + dy * dy + dz * dz)
			
			if distance < nearestDistance then
				nearestDistance = distance
				nearestPoint = point
			end
		end
		
		-- 选择最近的复活点
		if nearestPoint then
			MiniLogWarning("pixelmapCtrl:ReviveToNearestPoint - 选择最近复活点: " .. nearestPoint.name .. " 距离: " .. math.floor(nearestDistance))
			self:BedRevivePoint(nearestPoint.x, nearestPoint.y, nearestPoint.z)
			return
		end
	end
	
	-- 如果没有可用复活点，则随机复活
	MiniLogWarning("pixelmapCtrl:ReviveToNearestPoint - 无可用复活点，执行随机复活")
	self:Revive()
end

--debug
function pixelmapCtrl:SwitchMobile()
	self.m_socpixmapLogic.is_mobile = not self.m_socpixmapLogic.is_mobile
	self.view.soc_mainmap:getController("mobile"):setSelectedIndex(self.m_socpixmapLogic.is_mobile and 1 or 0)
	self.m_socpixmapLogic:SetMask(false)
	self.m_socpixmapLogic:SetMask(true)
end
