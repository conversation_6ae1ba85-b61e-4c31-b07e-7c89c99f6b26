#include "BlockWaterStorage.h"
#include "BlockMaterialMgr.h"
#include "Collision.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "Environment.h"
#include "ClientActorManager.h"
#include "IClientPlayer.h"
#include "ChunkGenerator.h"
//#include "GameEvent.h"
#include "DefManagerProxy.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
#include "ActorVillager.h"
#include "SandboxIdDef.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(BlockWaterStorage)
BlockWaterStorage::BlockWaterStorage()
{
}

int BlockWaterStorage::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* psection, const WCoord& blockpos, World* pworld)
{
	if (psection == NULL || idbuf == NULL || dirbuf == NULL)
	{
		return 0;
	}
	if (pworld == NULL || pworld->getContainerMgr() == NULL)
	{
		return 0;
	}
	containerWaterStorage* container = dynamic_cast<containerWaterStorage*>(pworld->getContainerMgr()->getContainer(psection->getOrigin() + blockpos));
	if (container == NULL) 
	{
		return 0;
	}
	int blockdata = psection->getBlock(blockpos).getData();
	idbuf[0] = 0;
	dirbuf[0] = blockdata & 3;
	return 1;
}

void BlockWaterStorage::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}

bool BlockWaterStorage::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (!player || !pworld)
	{
		return false;
	}

	if (pworld->isRemoteMode())
	{
		return true;
	}

	if (!isCoreBlock(pworld, blockpos))
	{
		WCoord corePos = getCoreBlockPos(pworld, blockpos);
		if (corePos.y < 0)
		{
			return false;
		}

		return onTrigger(pworld, corePos, face, player, colpoint);
	}

	containerWaterStorage* container = sureContainer(pworld, blockpos);
	if (container == NULL)
	{
		return false;
	}
	// 是否禁止多人操作
	if (GetBlockDef()->UserData[0] == 1 && container->isOpenning())
	{
		return false;
	}
	player->openContainer(container);
	return true;
}

void  BlockWaterStorage::init(int resid)
{
	ModelBlockMaterial::init(resid);
	SetToggle(BlockToggle_HasContainer, true);
	setMultiBlockSize(WCoord(m_Def->BlockSize[0] - 1, m_Def->BlockSize[1] - 1, m_Def->BlockSize[2] - 1));
	if (m_nWaterVolunmeMax == -1)
	{
		auto pitemdef = GetDefManagerProxy()->getItemDef(m_Def->ID);
		m_nWaterVolunmeMax = atoi(pitemdef->para.c_str());
	}
	if (BlockMaterial::m_LoadOnlyLogic) return;
	getDefaultMtl()->setItemMtlOpaque(true);
}

void BlockWaterStorage::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord origin = blockpos * BLOCK_SIZE;
	coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

containerWaterStorage* BlockWaterStorage::createContainer(World* pworld, const WCoord& blockpos)
{
	if (pworld->isRemoteMode())
	{
		return nullptr;
	}
	if (isCoreBlock(pworld, blockpos))
	{
		return SANDBOX_NEW(containerWaterStorage, blockpos, m_Def->ID);
	}
	else
	{
		return nullptr;
	}
}

void BlockWaterStorage::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto corePos = blockpos + data.m_SharedSectionData->getOrigin();
	if (!isCoreBlock(data.m_World, corePos)) corePos = getCoreBlockPos(data.m_World, corePos);
	Super::createBlockMesh(data, corePos - data.m_SharedSectionData->getOrigin(), poutmesh);
}

bool BlockWaterStorage::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}
	containerWaterStorage* container = dynamic_cast<containerWaterStorage*>(getCoreContainer(pworld, blockpos));
	if (container)
	{
		container->addHp(-damage);
		return true;
	}
	return false;
}

int BlockWaterStorage::getBlockHP(World* pworld, const WCoord& blockpos)
{
	containerWaterStorage* container = dynamic_cast<containerWaterStorage*>(getCoreContainer(pworld, blockpos));
	if (container)
	{
		// 获取容器的HP
		return container->getHp();
	}
	else
	{
		// 如果没有容器，返回默认值
		return 0;
	}
}

void BlockWaterStorage::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (pworld == NULL || player == NULL)
	{
		return;
	}
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;

	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	pworld->setBlockData(blockpos, placeDir);

	std::vector<WCoord> poslist;
	getMultiBlockRange(poslist, placeDir, blockpos, false, true);
	for (auto& pos : poslist)
	{
		auto block = pworld->getBlock(pos);
		if (block.getResID() != m_BlockResID || block.getData() != (4 | placeDir))
		{
			pworld->setBlockAll(pos, m_BlockResID, 4 | placeDir);
		}
	}
}

int BlockWaterStorage::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (playerTmp) 
		return playerTmp->getCurPlaceDir();

	return 0;
}

int BlockWaterStorage::getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	int placeDir = face;
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	return placeDir;
}

void BlockWaterStorage::onBlockAdded(World* pworld, const WCoord& blockpos)
{
}

void BlockWaterStorage::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if (pworld == NULL)
	{
		return;
	}

	int placeDir = blockdata & 3;
	if (blockdata & 4)  //普通的方块
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos, blockid, blockdata);
		if (basePos.y >= 0)
		{
			pworld->setBlockAir(basePos);
		}
	}
	else
	{
		ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
		//containerWaterStorage* canvasContainer = dynamic_cast<containerWaterStorage*>(pworld->getContainerMgr()->getContainer(blockpos));
		//if (canvasContainer)
		//{
		//	
		//}
		pworld->getContainerMgr()->destroyContainer(blockpos);
		clearNormalBlock(pworld, blockpos, blockdata);
	}
}

void BlockWaterStorage::clearNormalBlock(World* pworld, const WCoord& blockpos, int placeDir)
{
	std::vector<WCoord> poslist;
	getMultiBlockRange(poslist, placeDir, blockpos, false, true);
	for (auto& pos : poslist)
	{
		int blockid = pworld->getBlockID(pos);
		if (blockid == m_BlockResID)
		{
			pworld->setBlockAir(pos);
		}
	}
}

WorldContainer* BlockWaterStorage::getCoreContainer(World* pworld, const WCoord& blockpos)
{
	int blockid = pworld->getBlockID(blockpos);
	if (blockid != m_Def->ID)
	{
		return NULL;
	}
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	//auto orgcontainer = pworld->getContainerMgr()->getContainer(corePos);

	//containerWaterStorage* container = dynamic_cast<containerWaterStorage*>(pworld->getContainerMgr()->getContainer(corePos));
	////return container;
	return sureContainer(pworld, corePos);
}

void BlockWaterStorage::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	if (!isCoreBlock(pworld, blockpos))
	{
		WCoord corePos = getCoreBlockPos(pworld, blockpos);
		if (corePos.y < 0)
		{
			return;
		}
		return dropBlockAsItem(pworld, corePos, pworld->getBlockData(corePos), droptype, chance);
	}
	ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

int BlockWaterStorage::getCoreBlockData(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return 0;
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	return pworld->getBlockData(corePos);
}

containerWaterStorage* BlockWaterStorage::sureContainer(World* pworld, const WCoord& blockpos)
{
	containerWaterStorage* container = dynamic_cast<containerWaterStorage*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (!container)
	{
		container = SANDBOX_NEW(containerWaterStorage, blockpos, m_Def->ID);
		pworld->getContainerMgr()->spawnContainer(container);
	}
	return container;
}

bool BlockWaterStorage::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
	auto corepos = getCoreBlockPos(pworld, blockpos);
	if (corepos.y < 0) return false;
	auto placedir = pworld->getBlockData(corepos) & 3;
	getMultiBlockRange(blockList, placedir, corepos, includeSelf, true);
	return blockList.size() != 0;
}

int BlockWaterStorage::getCurWaterVolume(World* pworld, const WCoord& blockpos)
{
	auto pcontainer = getCoreContainer(pworld, blockpos);
	if (pcontainer)
	{
		containerWaterStorage* container = dynamic_cast<containerWaterStorage*>(pcontainer);
		if(container) return container->getCurWaterVolume();
	}
	return 0;
}

void BlockWaterStorage::addWaterVolume(World* pworld, const WCoord& blockpos, int value)
{
	auto pcontainer = getCoreContainer(pworld, blockpos);
	if (pcontainer)
	{
		containerWaterStorage* container = static_cast<containerWaterStorage*>(pcontainer);
		if (container)
		{
			container->addWater(value);
		}
	}
}

int BlockWaterStorage::getCurWaterVolumeForLua(const WCoord& blockpos)
{
	return getCurWaterVolume(g_pPlayerCtrl->getWorld(), blockpos);
}

void BlockWaterStorage::addWaterVolumeForLua(const WCoord& blockpos, int value)
{
	addWaterVolume(g_pPlayerCtrl->getWorld(), blockpos, value);
}


WorldContainer* BlockWaterStorage::repairContainer(const WCoord& blockpos, const int& data)
{
	if ((data & 4) == 0)
	{
		return SANDBOX_NEW(containerWaterStorage, blockpos, m_Def->ID);
	}
	return NULL;
}