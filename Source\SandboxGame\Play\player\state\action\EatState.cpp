#include "EatState.h"
#include "PlayerControl.h"
#include "InputInfo.h"
#include "PlayerStateController.h"
#include "DefManagerProxy.h"
#include "CameraModel.h"
#include "world.h"
#include "EffectManager.h"
#include "PlayerAnimation.h"
#include "OgreTimer.h"

#include "PlayerAttrib.h"
#include "SoundComponent.h"
#include "ClientActorFuncWrapper.h"
#include "SandboxIdDef.h"
#include "ClientInfoProxy.h"

EatState::EatState(PlayerControl* host) : PlayerState(host), EatStateAction(nullptr), m_EatStartMark(0), m_EatItemDuration(0), m_PlayEatTicks(0),
                                          m_EatMark(0),
                                          m_HasBurp(false)
{
	m_StateID = "Eat";
}

EatState::~EatState()
{

}

void EatState::doBeforeEntering()
{
	m_OperateTicks = 0;
	//LOG_INFO("EatState Enter");
	m_EatStartMark = Rainbow::Timer::getSystemTick();
	createMarkMD5(m_EatStartMark);
	m_CurToolID = m_Host->getCurToolID();
	m_CurShortcut = m_Host->getCurShortcut();
	const FoodDef *fooddef = GetDefManagerProxy()->getFoodDef(m_CurToolID);
	if (fooddef == NULL)
	{
		return;
	}

	//tick转换成毫秒
	m_EatItemDuration = fooddef->UseTime * 50; 
	m_Host->m_PlayerAnimation->performEat();
	if (!m_Host->getWorld()->isRemoteMode())
	{
		auto soundComp = m_Host->getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound("misc.eat", 1.0f, 1.0f);
		}
	}

	m_PlayEatTicks = 0;
	m_EatMark = m_EatItemDuration - 300;
	m_HasBurp = false;
	m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN);
}

std::string EatState::update(float dtime)
{
	auto functionWrapper = m_Host->getFuncWrapper();

	// 检查是否需要终止进食状态
	bool shouldTerminate = false;
	// 不要中止，触发后就需要完成吃一次
	// if (GetClientInfoProxy()->isMobile())
	// {
	// 	// 移动端：只检查useAction状态
	// 	shouldTerminate = !m_Host->m_InputInfo->useAction;
	// }
	// else
	// {
	// 	// PC端：使用原有的判断逻辑
	// 	shouldTerminate = IsUseActionEnd();
	// }

	if ((shouldTerminate || m_Host->getCurToolID()!=m_CurToolID || m_CurShortcut != m_Host->getCurShortcut() || m_Host->isDead()))
	{
		m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
		m_Host->setOperate(PLAYEROP_NULL);
		return "ToActionIdle";
	} 
	//新加禁食效果,生效后玩家不能进食且不能有动作反馈也不能有声音.结束进食状态 code by曹泽港
	if (m_Host->getPlayerAttrib() != NULL && !m_Host->getPlayerAttrib()->IsEnableEatFood())
	{
		return "ToActionIdle";
	}

	int curtick = Rainbow::Timer::getSystemTick();

	if(m_HasBurp && curtick-m_EatStartMark >= m_EatItemDuration+300)
	{
		m_HasBurp = false;
		m_EatStartMark = Rainbow::Timer::getSystemTick();
		createMarkMD5(m_EatStartMark);
		m_Host->m_PlayerAnimation->performEat();
		m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN);
		return "";
	}

	if(!m_HasBurp && curtick-m_EatStartMark>=m_EatItemDuration)
	{
		//2022318 烤牛排类型蓄力反作弊，校验m_EastStartMark是否被篡改 codeby:liushuxin
		if (!checkMarkMD5(m_EatStartMark))
		{
			m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
			m_Host->setOperate(PLAYEROP_NULL);
			return "ToActionIdle";
		}
		m_HasBurp = true;
		m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_END);
		// m_Host->m_PlayerAnimation->performIdle();
		m_OperateTicks = 0;
		//m_Host->setOperate(PLAYEROP_NULL);
		// 吃完就结束
		return "ToActionIdle";
	}

	// m_PlayEatTicks += Rainbow::TimeToTick(dtime);
	// if(m_PlayEatTicks >= 500)
	// {
	// 	if (!m_Host->getWorld()->isRemoteMode())
	// 	{
	// 		auto soundComp = m_Host->getSoundComponent();
	// 		if (soundComp)
	// 		{
	// 			if (m_CurToolID == ITEM_BANDAGE)
	// 				soundComp->playSound("misc.bandage", 1.0f, 1.0f);
	// 			else
	// 				soundComp->playSound("misc.eat", 1.0f, 1.0f);
	// 		}
	// 	}
	// 	m_PlayEatTicks = 0;
	// }

	// if (m_CurToolID == 12502)
	// {
	// 	m_Host->checkNewbieWorldProgress(19, "eatBread");
	// }

	return "";
}

void EatState::doBeforeLeaving()
{
	//LOG_INFO("EatState leaving");
	m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
	m_Host->m_PlayerAnimation->performIdle();
}

void EatState::OnTick(float elapse)
{
#if 1
	m_OperateTicks++;
#else
	return;
	m_OperateTicks++;
	if (mpCtrl->getCurToolID() != mpCtrl->getOperateData())
	{
		mpCtrl->setOperate(PLAYEROP_NULL);
		return;
	}

	const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(mpCtrl->getCurToolID());
	if (fooddef)
	{
		if ((m_OperateTicks % 10) == 1)
		{
			auto soundComp = mpCtrl->getSoundComponent();
			if (soundComp)
			{
				if (fooddef->ID == ITEM_BANDAGE)
				{
					soundComp->playSound("misc.bandage", 1.0f, 1.0f);
				}
				else
				{
					soundComp->playSound("misc.eat", 1.0f, 1.0f);
				}
			}
		}
	}
#endif
}
