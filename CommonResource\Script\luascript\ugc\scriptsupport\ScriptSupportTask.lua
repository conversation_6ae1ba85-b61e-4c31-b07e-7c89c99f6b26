-- 定义开发者同步操作(ID可修改)
_G.SSTASKID = {
    HOST_TRIGGEREVENT = 1, -- 触发器事件
    HOST_RECEIVETASK = 2, -- 接受任务

    HOST_MERGEMSGID = 3, -- 合并消息并消息重复过滤
    HOST_MSGPACK = 4, -- 合并消息
    HOST_BordCast = 5, -- 触发器广播消息
    HOST_UGC_REPORTHOST = 6, --UGC客机上报主机结果（mod接口）
    CLIENT_UGC_INITFINISH = 7, --主机通知客机初始化完成
    HOST_UGC_MERGEMSGID = 8, --主机通知客机初始化完成
    PLAYER_USER_VARLIB = 9000, -- 同步云变量到客机
    PLAYER_ADVERTISING = 10000 + 1, -- 播放广告
    PLAYER_APPENDSPEED = 10000 + 2, -- 附加速度
    PLAYER_ADVERTISING_CALLBACK = 10000 + 3, -- 播放广告(回调)
    PLAYER_PLAY_ACT = 10000 + 4, -- 播放动作
    PLAYER_SHOW_TIMERWND = 10000 + 5, -- 显示计时器窗口
    ACTOR_SHOW_NAME = 10000 + 6, -- 显示昵称
    ACTOR_SET_NAME = 10000 + 7, -- 设置昵称
    WORLD_SET_LIGHT = 10000 + 8, -- 设置光照
    TEAM_RESULTS = 10000 + 9, -- 设置队伍结果
    PLAYER_DEVGOODS = 10000 + 10, -- 快捷购买
    WORLD_SET_HOURS = 10000 + 11, -- 设置世界时间
    PLAYER_VIDEO_URL = 10000 + 12, -- 打开视频链接
    PLAYER_SHOWUI = 10000 + 13, -- 打开一个UI界面
    PLAYER_DEVGOODS_DETAILS = 10000 + 14, --打开开发者商品购买详情页

    CUSTOMUI_SETTEXT = 10000 + 15, --设置customui元素文字
    CUSTOMUI_SETTEXTURE = 10000 + 16, --设置customui元素纹理
    CUSTOMUI_SETSIZE = 10000 + 17, --设置customui元素大小
    CUSTOMUI_SETFONTSIZE = 10000 + 18, --设置customui元素文本大小
    CUSTOMUI_SETCOLOR = 10000 + 19, --设置customui元素文本颜色
    CUSTOMUI_SHOWELEMENT = 10000 + 20, --显示customui界面的元素
    CUSTOMUI_ROTATEELEMENT = 10000 + 22, --旋转customui界面的元素
    CUSTOMUI_SETELEMENTALPHA = 10000 + 23, --设置customui元素透明度
    CUSTOMUI_SETELEMENTSTATE = 10000 + 24, --设置customui元素状态
    CUSTOMUI_SETELEMENTPOSITION = 10000 + 25, --设置customui元素位置
    CUSTOMUI_SMOOTHANIM = 10000 + 26, --元件平滑动作
    CUSTOMUI_ANIMEFFECT = 10000 + 27, --元件动作特效
    CUSTOMUI_LOADERMODEL = 10000 + 28, --设置装载器的模型
    CUSTOMUI_LOADERMODELSCALE = 10000 + 29, --设置装载器的模型缩放
    CUSTOMUI_LOADERMODELDIR = 10000 + 30, --设置装载器的模型方向
    CUSTOMUI_LOADERMODELACT = 10000 + 31, --设置装载器的模型方向
    CUSTOMUI_SLIDEPOS = 10000 + 32, --滑动列表跳转到某位置
    CUSTOMUI_SLIDEDIR = 10000 + 33, --设置滑动列表的滑动方式
    CUSTOMUI_SLIDERBARIMG = 10000 + 34, --设置滑动条的图案
    CUSTOMUI_SETRELATIONPOSITION = 10000 + 35, --设置元件相对位置
    CUSTOMUI_SETRELATIONSIZE = 10000 + 36, --设置元件相对大小
    CUSTOMUI_CREATEELEMENT = 10000 + 37, --动态创建元件
    CUSTOMUI_CLONEELEMENT = 10000 + 38, --克隆元件
    CUSTOMUI_CHANGEPARENT = 10000 + 39, --修改元件父元件
    CUSTOMUI_SETSPINEANIMID = 10000 + 40, --设置动画文本ID
    CUSTOMUI_SETPROGRESSVAL = 10000 + 41, --设置玩家界面进度条的值
    CUSTOMUI_SETPROGRESSIMG = 10000 + 42, --设置玩家界面进度条的纹理
    CUSTOMUI_SMOOTHINCREASEPROGRESS= 10000 + 43, --进度条文本平滑计时

    PLAYER_CAMERA_SHAKE = 10000 + 100, --抖动玩家镜头
    PLAYER_CAMERA_SHAKE_STOP = 10000 + 101, --停止抖动玩家镜头
    PLAYER_CHANGE_MOVE_TYPE = 10000 + 102, --玩家改变移动方式

    PLAYER_PLAY_QQMUSIC = 10000 + 103, --玩家播放qq音乐
    PLAYER_OP_QQMUSIC = 10000 + 104, --暂停/恢复/停止玩家的QQ音乐

    PLAYER_OP_DISPLAYBOARD = 10000 + 105, --触发器同步显示板内容
    PLAYER_OP_GAMEMSG = 10000 + 106, --玩家操作内部函数消息

    ITEM_PLAY_ANIM = 10000 + 107, --道具掉落物投掷物播放动画
    ITEM_PLAY_MOTION = 10000 + 108, --道具掉落物播放粒子特效
    PLAYER_RANKING = 10000 + 109, --排行榜
    PLAYER_SHOWRANK = 10000 + 110, --排行榜
    WORLD_SKYBOX = 10000 + 111, --天空盒相关数据
    PLAYER_TRANSMIT = 10000 + 112, --玩家传送
    PLAYER_CAMERA = 10000 + 113, --摄像机接口
    PLAYER_DEVSKIN = 10000 + 114, --玩家打开开发者皮肤商店等
    
    DYNAMIC_API = 10000 + 200, --OGC_API 自定义指令
    
    PLAYER_MOBILEVIBRATE = 10000 + 201, --玩家手机震动

    TRIGGERSTATEATTR = 10000 + 202, --触发器权限设置

    REPORTTOHOST = 10000 + 203, --触发器权限设置
}
local DataJsonLenMax = 60*1024
--[[
    新配置需要合并发送给客机的消息包
    上面SSTASKID定义的消息如果需要打包发送就需要在MergeMsgID中定义
]]--
local MergeMsgID = {
    -- [SSTASKID.PLAYER_SHOWUI] = {1}, --打开一个UI界面 --屏蔽
    [SSTASKID.CUSTOMUI_SETTEXT] = {1}, --设置customui元素文字
    -- [SSTASKID.CUSTOMUI_SETTEXTURE] = {1}, --设置customui元素纹理
    [SSTASKID.CUSTOMUI_SETSIZE] = {1}, --设置customui元素大小
    [SSTASKID.CUSTOMUI_SETFONTSIZE] = {1}, --设置customui元素文本大小
    [SSTASKID.CUSTOMUI_SETCOLOR] =  {1}, --设置customui元素文本颜色
    -- [SSTASKID.CUSTOMUI_SHOWELEMENT] = {1}, --显示customui界面的元素
    [SSTASKID.CUSTOMUI_ROTATEELEMENT] = {1}, --旋转customui界面的元素
    [SSTASKID.CUSTOMUI_SETELEMENTALPHA] = {1}, --设置customui元素透明度
    -- [SSTASKID.CUSTOMUI_SETELEMENTSTATE] = {1}, --设置customui元素状态
    [SSTASKID.CUSTOMUI_SETELEMENTPOSITION] = {1}, --设置customui元素位置
}

--打包消息消息 --需要云服执行的全局类消息不能添加到打包消息内
local MsgPackID = {
    [SSTASKID.CUSTOMUI_SMOOTHANIM] = {1}, --元件播放动作
    [SSTASKID.CUSTOMUI_ANIMEFFECT] = {1}, --元件播放动作
    -- [SSTASKID.WORLD_SKYBOX] = {1}, --天空盒相关数据
    [SSTASKID.PLAYER_CAMERA] = {1}, --天空盒相关数据
    [SSTASKID.HOST_BordCast] = {1}, --mask相关消息
}

--云服主机需要处理的命令
local CloudRunTask = {
    [SSTASKID.ACTOR_SHOW_NAME] = true, --显示名称
    [SSTASKID.ACTOR_SET_NAME] = true, -- 设置名称
    [SSTASKID.ITEM_PLAY_MOTION] = true, -- 播放手持特效
    [SSTASKID.ITEM_PLAY_ANIM] = true, -- 播放动画
    [SSTASKID.WORLD_SET_HOURS] = true, --设置时间
    [SSTASKID.TEAM_RESULTS] = true, -- 设置队伍结果
    [SSTASKID.WORLD_SET_LIGHT] = true, -- 设置位置光照
    [SSTASKID.TRIGGERSTATEATTR] = true, -- 触发器动作权限
    [SSTASKID.WORLD_SKYBOX] = true, -- 天空盒相关
} 

-- 定义开发者任务，将开发者任务同步到客机
_G.ScriptSupportTask = {

    -- 任务处理注册
    TaskReg = {
        -- host
        [SSTASKID.HOST_TRIGGEREVENT] = 'Host_TriggerEvent',
        [SSTASKID.HOST_RECEIVETASK] = 'Host_ReceiveTask',
        -- player
        
        [SSTASKID.HOST_MERGEMSGID] = 'Host_MergeMsg',
        [SSTASKID.HOST_MSGPACK] = 'Host_MergeMsg',
        [SSTASKID.CLIENT_UGC_INITFINISH] = 'Client_Ugc_InitFinish',
        [SSTASKID.HOST_BordCast] = 'HOST_BordCast',
        
        [SSTASKID.PLAYER_USER_VARLIB] = 'Player_VarLib',
        [SSTASKID.PLAYER_ADVERTISING] = 'Player_PlayAD',
        [SSTASKID.PLAYER_APPENDSPEED] = 'Player_AppendSpeed',
        [SSTASKID.PLAYER_ADVERTISING_CALLBACK] = 'Player_PlayAD_Callback',
        [SSTASKID.PLAYER_PLAY_ACT] = 'Player_PlayAct',
        [SSTASKID.PLAYER_SHOW_TIMERWND] = 'Player_ShowTimerWnd',
        [SSTASKID.ACTOR_SHOW_NAME] = 'Actor_ShowNickName',
        [SSTASKID.ACTOR_SET_NAME] = 'Actor_SetNickName',
        [SSTASKID.WORLD_SET_LIGHT] = 'World_setLightByPos',
        [SSTASKID.TEAM_RESULTS] = 'Team_setGameResult',
        [SSTASKID.PLAYER_DEVGOODS] = 'Player_DevGoodsBuyDialog',
        [SSTASKID.PLAYER_DEVGOODS_DETAILS] = 'Player_DevGoodsDetailedBuyDialog',
        [SSTASKID.WORLD_SET_HOURS] = 'World_SETHOURS',
        [SSTASKID.PLAYER_VIDEO_URL] = 'Player_ViceoUrl',
        [SSTASKID.PLAYER_SHOWUI] = 'Player_showUI',

        --CUSTOMUI
        [SSTASKID.CUSTOMUI_SETTEXT] = 'CustomUI_setText',
        [SSTASKID.CUSTOMUI_SETTEXTURE] = 'CustomUI_setTexture',
        [SSTASKID.CUSTOMUI_SETSIZE] = 'CustomUI_setSize',
        [SSTASKID.CUSTOMUI_SETFONTSIZE] = 'CustomUI_setFontSize',
        [SSTASKID.CUSTOMUI_SETCOLOR] = 'CustomUI_setColor',
        [SSTASKID.CUSTOMUI_SHOWELEMENT] = 'CustomUI_showElement',
        [SSTASKID.CUSTOMUI_ROTATEELEMENT] = 'CustomUI_rotateElement',
        [SSTASKID.CUSTOMUI_SETELEMENTALPHA] = 'CustomUI_setAplha',
        [SSTASKID.CUSTOMUI_SETELEMENTSTATE] = 'CustomUI_setState',
        [SSTASKID.CUSTOMUI_SETELEMENTPOSITION] = 'CustomUI_setPosition',
        [SSTASKID.CUSTOMUI_SMOOTHANIM] = 'CustomUI_SmoothAnim',
        [SSTASKID.CUSTOMUI_ANIMEFFECT] = 'CustomUI_AnimEffect',
        [SSTASKID.CUSTOMUI_LOADERMODEL] = 'CustomUI_LoaderModel',
        [SSTASKID.CUSTOMUI_LOADERMODELSCALE] = 'CustomUI_LoaderModelScale',
        [SSTASKID.CUSTOMUI_LOADERMODELDIR] = 'CustomUI_LoaderModelDir',
        [SSTASKID.CUSTOMUI_LOADERMODELACT] = 'CustomUI_LoaderModelAct',
        [SSTASKID.CUSTOMUI_SLIDEPOS] = 'CustomUI_SliderToPos',
        [SSTASKID.CUSTOMUI_SLIDEDIR] = 'CustomUI_SliderDir',
        [SSTASKID.CUSTOMUI_SLIDERBARIMG] = 'CustomUI_SliderBarImg',
        [SSTASKID.CUSTOMUI_SETRELATIONPOSITION] = 'CustomUI_SetRelationPosition',
        [SSTASKID.CUSTOMUI_SETRELATIONSIZE] = 'CustomUI_SetRelationSize',
        [SSTASKID.CUSTOMUI_CREATEELEMENT] = 'CustomUI_CreateElement',
        [SSTASKID.CUSTOMUI_CLONEELEMENT] = 'CustomUI_CloneElement',
        [SSTASKID.CUSTOMUI_CHANGEPARENT] = 'CustomUI_ChangeParent',
        [SSTASKID.CUSTOMUI_SETSPINEANIMID] = 'CustomUI_SetSpineAnim',
        [SSTASKID.CUSTOMUI_SETPROGRESSVAL] = 'CustomUI_SetProgressBarValue',
        [SSTASKID.CUSTOMUI_SETPROGRESSIMG] = 'CustomUI_SetProgressBarResId',
        [SSTASKID.CUSTOMUI_SMOOTHINCREASEPROGRESS] = 'CUSTOMUI_SmoothIncreaseProgress',
        

        [SSTASKID.PLAYER_CAMERA_SHAKE] = 'Player_shakeCamera',
        [SSTASKID.PLAYER_CAMERA_SHAKE_STOP] = 'Player_stopShakeCamera',
        [SSTASKID.PLAYER_CHANGE_MOVE_TYPE] = 'Player_changeMoveType',
        [SSTASKID.PLAYER_PLAY_QQMUSIC] = 'Player_playQQMusic',
        [SSTASKID.PLAYER_OP_QQMUSIC] = 'Player_opQQMusic',
        [SSTASKID.PLAYER_OP_DISPLAYBOARD] = 'Player_DisplayBoard',
        [SSTASKID.PLAYER_OP_GAMEMSG] = 'Player_GameMsg',

        [SSTASKID.ITEM_PLAY_ANIM] = 'Item_Play_Anim',
        [SSTASKID.ITEM_PLAY_MOTION] = 'Item_Play_Motion',
        [SSTASKID.PLAYER_RANKING] = 'Player_Ranking',
        [SSTASKID.PLAYER_SHOWRANK] = 'Player_ShowRank',

        [SSTASKID.PLAYER_MOBILEVIBRATE] = 'Player_MobileVibrate',
        [SSTASKID.WORLD_SKYBOX] = 'WorldSkyBox',
        [SSTASKID.PLAYER_TRANSMIT] = 'TransmitPlayer',
        [SSTASKID.PLAYER_CAMERA] = 'PlayerCameraMsg',
        [SSTASKID.PLAYER_DEVSKIN] = 'PlayerOpenDevSkin',
        [SSTASKID.TRIGGERSTATEATTR] = 'TriggerStateAttr',
	--OGC_API_CMD
        [SSTASKID.DYNAMIC_API] = 'Dynamic_Api',
        

        [SSTASKID.REPORTTOHOST] = 'Report2Host',
        
    },

    constructor = function(self)
		self.DevEventPermits = {}
		self.DevActionPermits = {}
        self.m_PkgMsg = {}
        self.m_lastSyncData = {}
        self.m_UinList = {}
        self.taskBatchSendCount = 0
        self.taskBatchSendCountMax = 1
        self.m_customuiitemingroe = {}
    end,

    getAllPlayersUin = function(self)
        if not next(self.m_UinList) then
            if ClientCurGame then
                local size = ClientCurGame:requireArrayOfAllPlayers()
                for i = 1, size do
                    local player = ClientCurGame:getIthPlayerInArray(i-1)
                    if player then
                        self:InitSyncData(player:getUin())
                    end
                end
            end
        end
        return self.m_UinList
    end,

    _tojson = function (self,tab)
        return ScriptSupportCtrl:tableToJson(tab)
    end,

    _totab = function (self,json)
        return ScriptSupportCtrl:jsonToTable(json)
    end,

    OnTick = function (self)
        --不需要同步时，不进行任何操作
        if not self:batchTableHasAny() then
            return
        end
    
        self.taskBatchSendCount = self.taskBatchSendCount + 1
        --每隔一段时间，同步一次客机的task
        if self.taskBatchSendCount >= self.taskBatchSendCountMax then
            --按用户传输需要同步的内容
            for uin, tdTable in pairs(self.m_PkgMsg) do
                local maxIdx = tdTable.idx
                local msgList = tdTable.msg
                local tabs = {}
                for i = 1, maxIdx do
                    if msgList[i] then
                        table.insert(tabs, msgList[i])
                    end
                end
                -- 编码
                if #tabs > 0 then
                    local paramjson = self:_tojson(tabs) or ''
                    -- 同步
                    if string.len(paramjson) <= DataJsonLenMax then
                        if WorldMgr and WorldMgr.syncSSTaskToPlayer then
                            WorldMgr:syncSSTaskToPlayer(uin, SSTASKID.HOST_MERGEMSGID, paramjson)
                        end
                    else --极限情况分包发送
                        --[[
                            tabs = {msg,msg,msg}
                        ]]
                        local len = 0
                        local curmsg ={}
                        local count = 0
                        for i = 1, #tabs do
                            local curjson = self:_tojson(tabs[i]) or ''
                            local tlen = string.len(curjson)
                            if len + tlen > DataJsonLenMax then --
                                curjson = self:_tojson(curmsg) or ''
                                if WorldMgr and WorldMgr.syncSSTaskToPlayer and curjson ~= '' then
                                    WorldMgr:syncSSTaskToPlayer(uin, SSTASKID.HOST_MERGEMSGID, curjson)
                                end
                                curmsg = {}
                                len = 0
                                count = 0
                            end
                            count = count + 1
                            curmsg[count] = tabs[i]
                            len = len + tlen
                        end
                        if #curmsg > 0 then
                            local curjson = self:_tojson(curmsg) or ''
                            if WorldMgr and WorldMgr.syncSSTaskToPlayer then
                                WorldMgr:syncSSTaskToPlayer(uin, SSTASKID.HOST_MERGEMSGID, curjson)
                            end
                            curmsg = {}
                        end
                    end
                end
            end
            self.m_PkgMsg = {}
            self.taskBatchSendCount = 0

            --这里需要标记一下idx 表示上一个tick的数据
            for uin, tdTable in pairs(self.m_lastSyncData) do
                for taskid, tab in pairs(tdTable) do
                    for key, _ in pairs(tab) do
                        self.m_lastSyncData[uin][taskid][key].idx = -1
                    end
                end
            end
            
        end
    end,

    batchTableHasAny = function(self)
        if next(self.m_PkgMsg) then
            return true
        end
        return false
    end,

    ClearSyncData = function (self,playerid)
        UGCGetInst("DataSycMgr"):ClearSyncData(playerid)
        if playerid then
            self.m_lastSyncData[playerid] = nil
            for i, uin in ipairs(self.m_UinList) do
                if uin == playerid then
                    table.remove(self.m_UinList, i)
                    break
                end
            end
        else
            self.m_lastSyncData = {}
            self.m_UinList = {}
            self.m_customuiitemingroe = {}
        end
    end,

    InitSyncData = function (self,playerid)
        for _, uin in ipairs(self.m_UinList) do
            if uin == playerid then
                return
            end
        end
        table.insert(self.m_UinList, playerid)
    end,

    pushOneUinMsg = function(self,uin,taskid,param,key)
        if not self.m_lastSyncData[uin] then self.m_lastSyncData[uin] = {} end
        if not self.m_lastSyncData[uin][taskid] then self.m_lastSyncData[uin][taskid] = {} end
        if not self.m_PkgMsg[uin] then self.m_PkgMsg[uin] = {idx = 0, msg = {}} end
    
        local oldParamTab = self.m_lastSyncData[uin][taskid][key]
        if not oldParamTab then
            local idx = self.m_PkgMsg[uin].idx + 1 --递增索引
            self.m_PkgMsg[uin].idx = idx
            self.m_PkgMsg[uin].msg[idx] = {p = param, i = taskid}

            self.m_lastSyncData[uin][taskid][key] = {param = param, idx = idx}
        else
            local oldIdx = oldParamTab.idx --旧值数组中的索引
            if oldIdx == -1 then
                --上一个tick有发这个数据，需要判断是否一样
                if table.equal(oldParamTab.param, param) then
                    --啥都不干
                else
                    self.m_PkgMsg[uin].msg[oldIdx] = nil --旧值置空
                    local idx = self.m_PkgMsg[uin].idx + 1 --递增索引
                    self.m_PkgMsg[uin].idx = idx
                    self.m_PkgMsg[uin].msg[idx] = {p = param, i = taskid} --赋值新索引

                    oldParamTab.param = param
                    oldParamTab.idx = idx
                end
            else
                self.m_PkgMsg[uin].msg[oldIdx] = nil --旧值置空
                local idx = self.m_PkgMsg[uin].idx + 1 --递增索引
                self.m_PkgMsg[uin].idx = idx
                self.m_PkgMsg[uin].msg[idx] = {p = param, i = taskid} --赋值新索引

                oldParamTab.param = param
                oldParamTab.idx = idx
            end
        end
    end,

    pushMsg = function(self,playerid,taskid,param)
        local paramix = MergeMsgID[taskid]
        local key = ''
        for _, v in pairs(paramix) do
            local ix1 = paramix[v]
            if ix1 and param[ix1]  then
                key = tostring(param[ix1])
            end
        end

        if playerid == 0 then
            local array = self:getAllPlayersUin()
            for i = 1, #array do
                self:pushOneUinMsg(array[i],taskid,param,key)
            end
        else
            self:pushOneUinMsg(playerid,taskid,param,key)
        end
    end,
    
    Host_MergeMsg = function (self,playerid, tdata)
        --客户端兼容 新老版本数据打包
        local key = next(tdata)
        if type(key) == "number" then
            for i = 1, #tdata do
                local taskid = tdata[i].i
                -- 执行
                local funcname = self.TaskReg[taskid]
                local func = funcname and self[funcname]
                if func then
                    func(self, playerid, tdata[i].p)
                end
            end
        else
            for k, v in pairs(tdata) do
                k = tonumber(k)
                -- 如果后面开发者相关接口打包发送了 需要检测开发者权限
                -- 执行
                local funcname = self.TaskReg[k]
                local func = funcname and self[funcname]
                if func then
                    for i = 1, #v, 1 do
                        func(self, playerid, v[i])
                    end
                end
            end
        end
    end,

    HOST_BordCast = function (self,playerid, tdata)
        if tdata and #tdata == 3 then
            local sev = tdata[1]
            local met = tdata[2]
            if _G.GameVmSeversList[sev][met] then
                return _G.GameVmSeversList[sev][met](_G.GameVmSeversList[sev],unpack(tdata[3]))
            end
        end
    end,

    -- 执行回调同步回主机
    -- taskid : number : 同步操作类型(参考：SSTASKID)
    -- tdata : table or nil : 操作相关参数
    -- [return] : boolean : 成功/失败
    reportTaskToHost = function(self, taskid, tdata)
        if not self.TaskReg[taskid] then
            -- assert(false)
            return false
        end

        -- 编码
        local paramjson = self:_tojson(tdata) or ''
        if string.len(paramjson) > DataJsonLenMax then
            -- assert(false)
            return false
        end

        -- 同步
        if WorldMgr and WorldMgr.syncSSTaskToHost then
            local ret = WorldMgr:syncSSTaskToHost(taskid, paramjson)
            if ret then
                return true
            end
        end
        --assert(false)
        return false
    end,


    Report2Host = function (self,playerid, tdata)
        if tdata then
            if tdata.event == 'invatefriend' then
                local param = {
                    eventobjid = playerid,
                    toobjid = tdata.destUin
                }
                ScriptSupport_TriggerEvent("Player.InvateFriend", param)
            end
        end
    end,
    -- 主机处理同步执行任务
    -- playerid : number : 上报的玩家uin
    -- taskid : number : 同步操作类型(参考：SSTASKID)
    -- paramjson : string : 操作相关参数
    receiveTaskForHost = function(self, playerid, taskid, paramjson)
        if not playerid then
            return
        end

        -- 解码
        local tdata = self:_totab(paramjson) or {}

        -- 执行
        local funcname = self.TaskReg[taskid]
        local func = funcname and self[funcname]
        if func then
            func(self, playerid, tdata)
        end
    end,

    -- 同步任务给客机执行
    -- playerid : number : 玩家uin(nil or 0 表示全部玩家)
    -- taskid : number : 同步操作类型(参考：SSTASKID)
    -- tdata : table or nil : 操作相关参数
    -- [return] : boolean : 成功/失败
    sendTaskToPlayer = function(self, playerid, taskid, tdata)
        playerid = playerid or 0 -- nil表示全部广播
        if taskid == nil or tdata == nil  then
            return false
        end
        if not self.TaskReg[taskid] then
            -- assert(false)
            return false
        end
        if MergeMsgID[taskid] and not self:_ignoreMergeMsg(taskid, tdata)  then --数据打包发送
            self:pushMsg(playerid, taskid, tdata)
            return true
        elseif MsgPackID[taskid] then
            self:PackMessage(playerid, taskid, tdata)
            return true
        end

        -- 编码
        local paramjson = self:_tojson(tdata) or ''
        if string.len(paramjson) > DataJsonLenMax then
            -- assert(false)
            return false
        end

        -- 同步
        if WorldMgr and WorldMgr.syncSSTaskToPlayer then
            local ret = WorldMgr:syncSSTaskToPlayer(playerid, taskid, paramjson)
            if ret then
                return true
            end
        end
        --assert(false)
        return false
    end,

    -- 客机处理同步执行任务
    -- taskid : number : 同步操作类型(参考：SSTASKID)
    -- paramjson : string : 操作相关参数
    receiveTaskForPlayer = function(self, taskid, paramjson)
        local playerid = 0
        if ns_SRR and ns_SRR.cloud_mode == 1 then
            if not CloudRunTask[taskid] then
                return
            end
        else
            playerid = CurMainPlayer and CurMainPlayer:getUin()
            if not playerid then
                return
            end
        end
        

        -- 解码
        local tdata = self:_totab(paramjson) or {}

        -- 开关
        if tdata and tdata.triggerevent and tdata.triggerevent.params and tdata.triggerevent.params.def and tdata.triggerevent.params.def.msgStr then
            --3160001-玩家播放广告(异步)
            --3160002-打开开发者商店
            --3160003-打开开发者商店商品购买弹框
            local triggerID = 3160001
            if tdata.triggerid then
                triggerID = tdata.triggerid
            end
            if not self:checkDevFunPermits(tdata.triggerevent.params.def.msgStr,triggerID) then
	    		return
	       end
        end

        -- 执行
        local funcname = self.TaskReg[taskid]
        local func = funcname and self[funcname]
        if func then
            func(self, playerid, tdata)
        end
    end,
    
    --检查开发者广告等商业接口是否开启/关闭
    --xyang 20220111 这个方法本来是在sendtask那边的，但是由于云服不拉取ns_version配置导致无法实现功能，只能在接收端再做一次
    -- eventname : 广告名称
    -- actionid : 协议id
	checkDevFunPermits = function(self, eventname , actionid)
		if not eventname then
			return true
		end
		if ns_version and ns_version.Disable_TriggerID and next(ns_version.Disable_TriggerID) and (not next(self.DevEventPermits) and not next(self.DevActionPermits) )then
			local tab = ns_version.Disable_TriggerID
			local relapiid = check_apiid_ver_conditions(tab)
			if relapiid  then
				local eventid = utils.split(tab.ID, ',')
				if eventid then
					for k, v in pairs(eventid) do
						local id = tonumber(v)
						if TriggerFactorToEvent[id] then
							local name = TriggerFactorToEvent[id]
							if type(name) == "table" then
								name = name[1]
							end
							self.DevEventPermits[name] = true
						else
							self.DevActionPermits[id] = true
						end
					end
				end
			end
		end
		if self.DevEventPermits[eventname] and self.DevActionPermits[actionid]  then
			return false
		end
		return true
	end,

    -- 同步任务给全体客机执行
    -- taskid : number : 同步操作类型(参考：SSTASKID)
    -- tdata : table or nil : 操作相关参数
    -- [return] : boolean : 成功/失败
    sendTaskToAll = function(self, taskid, tdata)
        return self:sendTaskToPlayer(0, taskid, tdata)
    end,

    -- 事件
    Host_TriggerEvent = function(self, playerid, tdata)
        local eventname = tdata.name
        local eventparams = tdata.params or {}
        if not eventname then
            return
        end

        -- 触发事件的对象改为目标
        eventparams.eventobjid = playerid

        ScriptSupport_TriggerEvent(eventname, eventparams)
    end,

    -- 接收任务
    Host_ReceiveTask = function(self, playerid, tdata)
        local taskname = tdata and tdata[1]
        if not taskname then
            return
        end
        GameVM.Task:receive(taskname, tdata and tdata[2])
    end,


    Player_VarLib = function(self,playerid,tdata)
        local curuin = nil
        if AccountManager then
            curuin = AccountManager:getUin()
        end
        if curuin and tdata[tostring(curuin)] then
            if type(tdata[tostring(curuin)]) == 'table' then
                local data = tdata[tostring(curuin)]
                local player = GetWorldActorMgr(CurWorld):findPlayerByUin(playerid)
                if player then
                    for i = 1, 2, 1 do
                        if data[i]  then
                            for k, v in pairs(data[i]) do
                                if i == 1 then
                                    player:setUserData(k,v)
                                else
                                    player:removeUserData(v)
                                end
                            end
                        end
                    end
                end
            else
                ArchiveMgr:setClientVarData(playerid,tdata[tostring(curuin)])
            end
        end
    end,

    -- 播放广告
    Player_PlayAD = function(self, playerid, tdata)
        local adname = tdata.adname
        local callbackevent = tdata.triggerevent
        if not adname or not callbackevent then
            return
        end
        local transtr = nil
        if tdata.transtr and tdata.transtr ~= "" then
            transtr = WorldStringTranslateMgr:getTransByMultiplekeys(14, tdata.transtr)
        end
        adname = (transtr and transtr ~= '') and transtr or adname
        ScriptSupportFunc:playAdvertisingForSelf(adname, callbackevent)
    end,

    -- 播放广告(回调)
    Player_PlayAD_Callback = function(self, playerid, tdata)
        local adname = tdata.adname
        local taskname = tdata.taskname
        local eventname = tdata.msgStr
        if not adname or not taskname then
            return
        end

        ScriptSupportFunc:playAdvertisingTaskForSelf(adname, taskname,eventname)
    end,

    -- 附加速度
    Player_AppendSpeed = function(self, playerid, tdata)
		if CurMainPlayer then
			CurMainPlayer:setMotionChange(tdata.vx, tdata.vy, tdata.vz, true)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

    -- 播放玩家动作
    Player_PlayAct = function(self, playerid, tdata)
        local objid = tdata.objid
        local actid = tdata.actid
        if not objid or not actid then
            return ErrorCode.FAILED
        end

        if CurWorld then
            local actor = GetWorldActorMgr(CurWorld):findActorByWID(objid)
            if actor and actor.playActForTrigger then
                if actor:playActForTrigger(actid) then
                    return ErrorCode.OK
                end
            end
        end
        return ErrorCode.FAILED
    end,

    -- 显示计时器窗口
    Player_ShowTimerWnd = function(self, playerid, tdata)
        local time = tdata.time
        local title = tdata.title
        local wndrunmode = tdata.wndrunmode
        if SetSSTimerUI then
            local transtr = title
            if tdata.translate and tdata.translate ~= "" then
                transtr = WorldStringTranslateMgr:getTransByMultiplekeys(14, tdata.translate)
            end
            transtr = transtr or title
            MiniLog("Player_ShowTimerWnd", ScriptSupportCtrl:tableToJson(tdata))
            SetSSTimerUI(time, transtr, wndrunmode)
        end
    end,

    -- 显示昵称
    Actor_ShowNickName = function(self, playerid, tdata)
        local objid = tdata.targetid
        local show = tdata.show
        if CurWorld then
            local actor = GetWorldActorMgr(CurWorld):findActorByWID(objid)
            if actor and actor.showNickName  then
                actor:showNickName(show)
            end
        end
    end,

    -- 设置昵称
    Actor_SetNickName = function(self, playerid, tdata)
        local objid = tdata.targetid
        local name = tdata.name
        if CurWorld then
            local actor = GetWorldActorMgr(CurWorld):findActorByWID(objid)
            if actor and actor.setNickName then
                if DefMgr:checkFilterString(name) then
                    name = "****"
                end
                actor:setNickName(name)
            end
        end
    end,

    World_setLightByPos = function(self, playerid, tdata)
        if CurWorld and CurWorld.setBlockLightEx then
            CurWorld:setBlockLightEx(tdata.x,tdata.y,tdata.z,tdata.lv)
        end
    end,

    Team_setGameResult = function (self, playerid, tdata)
        local teamid = tdata.teamid
        local result = tdata.result
        if ClientCurGame.setTeamResults then 
            ClientCurGame:setTeamResults(teamid,result)
        end
    end,

    -- 同步快捷购买触发器参数
    Player_DevGoodsBuyDialog = function(self, playerid, tdata)
        local msg = ""
        if tdata and tdata.msgStr and TriggerEventToFactor then
            msg = TriggerEventToFactor(tdata.msgStr)
        end
        if OpenDevGoodsBuyDialog then

            local transtr = nil
            if tdata.customDesc and tdata.customDesc ~= "" then
                transtr = WorldStringTranslateMgr:getTransByMultiplekeys(14, tdata.customDesc)
            end
            transtr = (transtr and transtr ~= '') and transtr or tdata.customDesc

            OpenDevGoodsBuyDialog(tdata.itemid, transtr, msg)
        end
    end,

    -- 同步触发器商店商品详情页参数
    Player_DevGoodsDetailedBuyDialog = function(self, playerid, tdata)
        local msg = ""
        if tdata and tdata.msgStr and TriggerEventToFactor then
            msg = TriggerEventToFactor(tdata.msgStr)
        end

        if OpenDevGoodsBuyDetailedDialog then
            OpenDevGoodsBuyDetailedDialog(tdata.itemid, msg)
        end
    end,
    
    -- 同步设置事件
    World_SETHOURS = function(self, playerid, tdata)
        local timehour = math.floor(tdata.time)
        if timehour < 0 then
            timehour = 0
        elseif timehour > 24 then
            timehour = 24
        end
        if CurWorld and CurWorld.setHours then
            CurWorld:setHours(timehour)
            return ErrorCode.OK
        end
    end,

    -- 同步客户端打开视频url
    Player_ViceoUrl = function(self, playerid, tdata)
        print("Player_ViceoUrl", tdata)
        if tdata and tdata.url then
            if ns_version and ns_version.video_trigger_URL and ns_version.video_trigger_URL.url then
                local urlTab = StringSplit(ns_version.video_trigger_URL.url,",") or {}
                for i, _url in ipairs(urlTab) do
                    if tdata.url == _url then
                        --"http://"
                        if string.sub(tdata.url,1, 4) ~= "http" then
                            tdata.url = "http://" .. tdata.url
                        end
                        open_http_link(tdata.url)
                        -- if tdata.msgStr and TriggerEventToFactor then
                        --     local msg = TriggerEventToFactor(tdata.msgStr)
                        -- end
                        break
                    end
                end
            end
        end
    end,
    
    -- 打开一个UI界面
    Player_showUI = function(self, playerid, tdata)
        MiniLog("Player_showUI :", tdata)
        if tdata and tdata[1] then
            if tdata[2] == 1 then
                if tdata[3] and type(tdata[3]) == "table" then
                    LuaGameEventTb.event(tdata[1],tdata[3])
                else
                    threadpool:work(function()
                        if UIEditorDef then
                            UIEditorDef:ui_editor_show_ui(tdata[1]) -- 打开自定义界面
                            if type(tdata[3]) == "number" and tdata[3] ~= 0 then
                                UIEditorDef:setElementAnimEffect(tdata[1],tdata[3],tdata[4],2) --旋转元素
                            end
                        end
                    end)
                end
            else
                if UIEditorDef then
                    if type(tdata[3]) == "number" and tdata[3] ~= 0 then
                        UIEditorDef:setElementAnimEffect(tdata[1],tdata[3],tdata[4],2) --旋转元素    
                    else
                        UIEditorDef:ui_editor_close_ui(tdata[1]) -- 隐藏自定义界面
                    end
                end
            end
        end
    end,

    CustomUI_setText = function(self, playerid, tdata)
        if UIEditorDef then
            local transtr = tdata[2]
            if tdata[6] and tdata[6] ~= "" then
                transtr = WorldStringTranslateMgr:getTransByMultiplekeys(14, tdata[6])
            end
            
            transtr = transtr or tdata[2] 
            transtr = DefMgr:filterString(transtr,false)
            UIEditorDef:setElementText(tdata[1], transtr) --设置元素文本
        end
    end,

    CustomUI_setTexture = function(self, playerid, tdata)
        if UIEditorDef then
            UIEditorDef:setElementTexture(unpack(tdata)) --设置元素纹理
        end
    end,

    CustomUI_setSize = function(self, playerid, tdata)
        if UIEditorDef then
            UIEditorDef:setElementSize(unpack(tdata)) --设置元素大小
        end
    end,

    CustomUI_setFontSize = function(self, playerid, tdata)
        if UIEditorDef then
            UIEditorDef:setElementFontSize(unpack(tdata)) --设置元素文本大小
        end
    end,

    CustomUI_setColor = function(self, playerid, tdata)
        if UIEditorDef then
            local transtr = ""
            if tdata[2] and type(tdata[2]) == "string" and tdata[2] ~= "" then
                transtr = WorldStringTranslateMgr:getTransByMultiplekeys(14, tdata[2])
            end
            tdata[2] = (transtr and transtr ~= '') and transtr or tdata[2]

            UIEditorDef:setElementColor(unpack(tdata)) --设置元素文本颜色
        end
    end,

    CustomUI_showElement = function(self, playerid, tdata)
        if UIEditorDef then
            local elementid,visible = unpack(tdata)
            if visible == 1 then
                UIEditorDef:setElementShow(elementid) -- 显示customui的元素
            else
                UIEditorDef:setElementHide(elementid) -- 隐藏customui的元素
            end
        end
    end,

    CustomUI_rotateElement = function(self, playerid, tdata)
        if UIEditorDef then
            UIEditorDef:setElementRotation(unpack(tdata)) --旋转元素
        end
    end,

    CustomUI_setAplha = function(self, playerid, tdata)
        if UIEditorDef then
            UIEditorDef:setElementAlpha(unpack(tdata)) --透明度
        end
    end,

    CustomUI_setState = function(self, playerid, tdata)
        if UIEditorDef then
            UIEditorDef:setElementState(unpack(tdata)) --页面状态
        end
    end,

    CustomUI_setPosition = function(self, playerid, tdata)
        if UIEditorDef then
            UIEditorDef:setElementPosition(unpack(tdata)) --位置
        end
    end,

    CustomUI_SetRelationPosition = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:setRelationPosition(unpack(tdata)) --相对位置
        end
    end,
    CustomUI_SetRelationSize = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:setRelationSize(unpack(tdata)) --相对大小
        end
    end,
    CustomUI_CreateElement = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:createElement(unpack(tdata)) --动态创建元件
        end
    end,
    CustomUI_CloneElement = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:cloneElement(unpack(tdata)) --克隆元件
        end
    end,
    CustomUI_ChangeParent = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:changeParent(unpack(tdata)) --修改元件父元件
        end
    end,

    CustomUI_SetSpineAnim = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:setSpineAnimId(unpack(tdata)) --修改元件父元件
        end
    end,

    CustomUI_SetProgressBarValue = function (self,playerid,tdata)
        if UIEditorDef then
            local elementid,itype,value = unpack(tdata)
            if elementid and itype and value then
                if itype == 4 then
                    UIEditorDef:setProgressBarValue(elementid,1,value) --最大值
                    UIEditorDef:setProgressBarValue(elementid,2,value) --当前值
                else
                    UIEditorDef:setProgressBarValue(elementid,itype - 1 ,value) --设置进度条的值
                end
            end
        end
    end,

    CustomUI_SetProgressBarResId = function (self,playerid,tdata)
        local elementid,itype,url = unpack(tdata)
        if elementid and elementid ~= '' and itype and itype >= 1 and itype <= 2 and url then
            if UIEditorDef then
                UIEditorDef:setProgressBarResId(elementid,itype - 1 ,url)
            end
        end
    end,

    CUSTOMUI_SmoothIncreaseProgress = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:SmoothIncreaseProgress(unpack(tdata)) --修改元件父元件
        end
    end,


    -- 抖动镜头
    Player_shakeCamera = function(self, playerid, tdata)
        local power = tdata.power
        local duration = tdata.duration
        if not power or not duration then
            return ErrorCode.FAILED
        end
		if CurMainPlayer then
			CurMainPlayer:setShakeCamera(true, power, duration)
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,

    -- 停止抖动镜头
    Player_stopShakeCamera = function(self, playerid, tdata)
		if CurMainPlayer then
            CurMainPlayer:setShakeCamera(false);
			return ErrorCode.OK
		end
		return ErrorCode.FAILED
    end,
    
    -- 改变玩家移动方式
    Player_changeMoveType = function(self, playerid, tdata)
        local objid = tdata.objid
        local moveType = tdata.moveType
        if not objid or not moveType then
            return ErrorCode.FAILED
        end
        if CurWorld then
            local player = GetWorldActorMgr(CurWorld):findPlayerByUin(objid)
            if player.isNewMoveSyncSwitchOn and player:isNewMoveSyncSwitchOn() then
                if player and player.changeMoveFlag then
                    player:changeMoveFlag(0, moveType == MOVETYPE.FLY);
                    return ErrorCode.OK
                end
            else
                if player and player.setFlying then
                    player:setFlying(moveType == MOVETYPE.FLY)
                    return ErrorCode.OK
                end
            end
        end
        return ErrorCode.FAILED
    end,


    -- 播放qq音乐
    Player_playQQMusic = function(self, playerid, tdata)
		if tdata and tdata.musicId and tdata.volume and tdata.isLoop ~= nil then
            GetInst("QQMusicTriggerManager"):PlayQQMusic4Trigger(tdata.musicId, tdata.volume, tdata.isLoop)
        end
		return ErrorCode.FAILED
    end,

    --暂停恢复停止qq音乐
    Player_opQQMusic = function(self, playerid, tdata)
        print("Player_opQQMusic", tdata)
		if tdata and tdata.operate then
            GetInst("QQMusicTriggerManager"):OperateQQMusic4Trigger(tdata.operate)
        end
		return ErrorCode.FAILED
    end,

    Player_DisplayBoard = function (self,playerid,tdata)
        if  tdata[1] == "IQiYiEvent" then
            local param = {}
            param.eventobjid = playerid
            param.content = tdata[2].content
            ScriptSupport_TriggerEvent("mapservice.notify", param)
        else
            local boardmgr = TriggerObjLibMgr:getDisplayBoardMgr()
            if boardmgr and boardmgr[tdata[1]] and type(tdata[2]) == "table" then
                local fun = boardmgr[tdata[1]]
                local param = tdata[2]
                -- if tdata[1] == "UpdateDisplayBoard4Trigger" and param[#param] == 2  then -- UITOOL3DTYPE.UITOOL3DTYPE_VIDEO = 2
                --     if ns_version and ns_version.video_trigger_URL and ns_version.video_trigger_URL.url then
                --         local urlTab = StringSplit(ns_version.video_trigger_URL.url,",") or {}
                --         for i, _url in ipairs(urlTab) do
                --             if param[2] == _url then
                --                 fun(boardmgr,unpack(param) )
                --                 return ErrorCode.OK
                --             end
                --         end
                --     end
                -- else
                    fun(boardmgr,unpack(param))
                    return ErrorCode.OK
                -- end
            elseif tdata[1] == "ReceiveDisplayBoardForToHost" then
                GameVmInterface[tdata[1]](GameVmInterface,tdata)
            end
        end
        return ErrorCode.FAILED
    end,

    Player_GameMsg = function (self,playerid,tdata)
        local opercode = tdata[1]
        if opercode == "OpenAppraiseView" then -- 打开评价界面
            GetInst("TriggerMapInteractiveInterFace"):ShowThumbup()
        elseif opercode == "OpenCollectionView" then --打开收藏界面
            GetInst("TriggerMapInteractiveInterFace"):ShowCollect()
        elseif  opercode == "OpenFollowAuthorView" then -- 关注作者界面
            --todo
        elseif opercode == "getviplevel" then -- 是否是迷你会员
            local viplevel =  GetInst("NewDeveloperStoreInterface"):GetVipLevel()-- 获取vip等级 viplevel < 0 则不是vip 0 会员 1一个月 3 三个月及以上
            local taskname =  tdata[2]
            if taskname then
                local param = {
                    [1] = viplevel,
                    [2] = taskname,
                }
                local data ={}
                table.insert(data,taskname)
                table.insert(data,param)
				self:reportTaskToHost(SSTASKID.HOST_RECEIVETASK,data)
            end
        elseif opercode == "minivip" then -- miniVip事件
            local param = {}
            param.eventobjid = playerid
            local level = tdata[2]
            if level then
                ScriptSupport_TriggerEvent("MiNiVip_"..level, param)
            end
        elseif opercode == "DeveloperBuItem" then -- 玩家购买开发者商店商品
            if not GetClientInfo():isPureServer() then --云服这里不触发
                local itemid =  tdata[2]
                local param = {}
                param.eventobjid = playerid --事件中的玩家
                param.itemid = itemid --事件中的道具
                ScriptSupport_TriggerEvent("Developer.BuyItem", param)
            end
        elseif opercode == "hasfriend" then
            local taskname =  tdata[2]
            local uin =  tdata[3]
            local fridData = GetFriendDataByUin(uin)
            if taskname then
                local param = {
                    [1] = fridData and 1 or 0,
                    [2] = taskname,
                }
                local data ={}
                table.insert(data,taskname)
                table.insert(data,param)
				self:reportTaskToHost(SSTASKID.HOST_RECEIVETASK,data)
            end
        elseif  opercode == "applyfriend" then --好友请求
            local paramUin = tdata[2]
            if GetFriendDataByUin(paramUin) ~=nil then
                ShowGameTips(GetS(38), 3)
            else
                GetInst("BattleEndFriendListGData"):AddFriend(paramUin)
            end
        elseif  opercode == "OpenInviteBulletBox" then --好友请求
            GetInst("MiniUIManager"):OpenUI(
                "InviteFriendlist", 
                "miniui/miniworld/roomInviteFriend", 
                "InviteFriendlistAutoGen",
                {disableOperateUI = true,standtriggerevent = 1}
            )
        elseif opercode == "OpenDevGoodsPage" then
            GameVmInterface:OpenDevGoodsPage(tdata[2],tdata[3])
        elseif opercode == "c2hevent" then
            local param = {
                eventobjid = tdata[3],
                customdata = tdata[4]
            }
            local obevent = observer_event_parse(param)
            if obevent then
                scriptsupport.ss_trigger_event(tdata[2], obevent)
                return ErrorCode.OK
            end
        end
        return ErrorCode.FAILED
    end,

    --OGC_API 动态验证指令
    Dynamic_Api = function (self, playerid, tdata)
        if OGC_DYNAMIC_API_CALL then
            OGC_DYNAMIC_API_CALL(tdata)
            return ErrorCode.OK
        end
        return ErrorCode.FAILED
    end,

    Item_Play_Anim = function (self,playerid,tdata)
        if not CurWorld then return  end
        local checkmodel = function (animid,mode)
            local anims ={ -- 对应entity/animmap.csv
                [100100] = 0,
                [100101] = 0,
                [100102] = 2,
                [100103] = 0,
                [100104] = 0,
                [100105] = 1,
                [100106] = 2,
                [100107] = 1,
                [100108] = 1,
                [100109] = 1,
            }
            if mode >= 3 then
                local def = DefMgr:getTriggerActDef(animid)
                if def then
                    mode = anims[def.ActID] or 0
                else
                    mode = 0
                end
            end
            return mode
        end

        local itype = tdata[1]
        if itype == 'dropitem' then
            local _,objid,animid,playmode = unpack(tdata)
            playmode = checkmodel(animid,playmode)
            local actor = GetWorldActorMgr(CurWorld):findActorByWID(objid)
            if actor then
                local actortype = actor:getObjType()
                if  actortype == 2 then -- OBJ_TYPE_DROPITE
                    local itemobj = tolua.cast(actor, "ClientItem")
                    if itemobj then
                        itemobj:playAct(animid,playmode)
                    end
                elseif actortype == 16  then -- OBJ_TYPE_THROWABLE
                    local itemobj = tolua.cast(actor, "ClientActorProjectile")
                    if itemobj then
                        itemobj:playAct(animid,playmode)
                    end
                end
            end
        elseif itype == 'block' then
            local _,x, y, z,animid,playmode = unpack(tdata)
            playmode = checkmodel(animid,playmode)
            GameVmInterface:BlockPlayAct(x, y, z,animid,playmode)
        elseif itype == 'handitem' then
            local _,objid,animid,playmode = unpack(tdata)
            playmode = checkmodel(animid,playmode)
            if CurMainPlayer and objid == CurMainPlayer:getUin() then
                threadpool:work(function ()
                    threadpool:wait(0.2)
                    GameVmInterface:PlayHandAct(objid,animid,playmode)
                end)
            else
                GameVmInterface:PlayHandAct(objid,animid,playmode)
            end
        elseif itype == 'blockanims' then
            local data = json2table(tdata[2])
            if next(data) then
                local mgr = nil
                if TriggerScriptMgr then
                    mgr = TriggerScriptMgr:GetSingletonPtr():GetGlobalDataMgr()
                end
                if mgr then
                    for k, v in pairs(data) do
                        mgr:SetBlockAnimData(v.x,v.y,v.z,v.id,v.mode)
                    end
                end
            end
        end
    end,

    Item_Play_Motion = function (self,playerid,tdata)
        local itype = tdata[1]
        if itype == 'SetPlayerShortcutIxEffect' then
            local _,objid,effectname,scale = unpack(tdata)
            GameVmInterface:SetPlayerShortcutIxEffect(objid,effectname,scale)
        elseif itype == 'StopPlayerShortcutIxEffect' then
            local _,objid,effectname = unpack(tdata)
            GameVmInterface:StopPlayerShortcutIxEffect(objid,effectname)
        elseif itype == 'SetPlayerShortcutItemEffect' then
            local _,playerid,itemid,effectname,scale = unpack(tdata)
            GameVmInterface:SetPlayerShortcutItemEffect(playerid,itemid,effectname,scale)
        elseif itype == 'StopPlayerShortcutItemEffect' then
            local _,playerid,itemid,effectname = unpack(tdata)
            GameVmInterface:StopPlayerShortcutItemEffect(playerid,itemid,effectname)
        elseif itype == 'handeffs'  then
            GameVmInterface:SetPlayerHandData(tdata[2])
        end
    end,

    Player_Ranking = function (self,playerid,tdata)
        local rankingMgr = UGCGetInst("RankingMgr")
        if rankingMgr then
            rankingMgr:ReceiveData(tdata)
        end
    end,
    
    Player_ShowRank = function (self,playerid,tdata)
        if tdata and tdata[1] then
            local id = tdata[1]
            if (type(id) == "number" and id > 0) or type(id) == "string" then
                local ctrl =  GetInst("MiniUIManager"):GetCtrl("Rank")
                if not ctrl then
                    GetInst("MiniUIManager"):OpenUI("rankMain", "miniui/miniworld/ugc_rank", "RankAutoGen", {disableOperateUI = false})
                    ctrl =  GetInst("MiniUIManager"):GetCtrl("Rank")
                end
                if type(id) == "string" or (type(id) == "number" and id > 10000) then
                    id = RankSetData:GetRankIndex(id)
                    ctrl:OpenRank(id)
                else
                    ctrl:OpenRank(id)
                end
            else                
                local ctrl =  GetInst("MiniUIManager"):GetCtrl("Rank")
                if ctrl then
                    ctrl:CloseRank()
                end
            end
        end
    end,

    -- 玩家手机震动
    Player_MobileVibrate = function (self, playerid, tdata)
        -- ShowGameTips("Task: Player_MobileVibrate___".."playerid_"..tostring(playerid).."time_"..tostring(tdata.time).."amplitude_"..tostring(tdata.amplitude), 3)
        if type(tdata) == "table" then
            GameVmInterface:SetMobileVibrateWithTimeAndAmplitude(tdata.time, tdata.amplitude)
        end
    end,

    PackMessage = function (self,playerid,taskid,param)
        local paramix = MsgPackID[taskid]
        local key = ''
        for _, v in pairs(paramix) do
            local ix1 = paramix[v]
            if ix1 and param[ix1]  then
                key = tostring(param[ix1])
            end
        end
        local ResetID = nil
        if taskid == SSTASKID.CUSTOMUI_SMOOTHANIM then
            local itype = {
                [50001] = SSTASKID.CUSTOMUI_SETELEMENTPOSITION,
                [50002] = SSTASKID.CUSTOMUI_SETELEMENTPOSITION,
                [50003] = SSTASKID.CUSTOMUI_SETSIZE,
                [50004] = SSTASKID.CUSTOMUI_SETSIZE,
                [50005] = SSTASKID.CUSTOMUI_ROTATEELEMENT,
                [50006] = SSTASKID.CUSTOMUI_ROTATEELEMENT,
            }
            ResetID = itype[param[2]]
        elseif taskid == SSTASKID.CUSTOMUI_ANIMEFFECT then
            local itype = {
                [40001] = SSTASKID.CUSTOMUI_SETTEXT,
            }
            ResetID = itype[param[3]]
        end
        if playerid == 0 then
            local array = self:getAllPlayersUin()
            for i = 1, #array do
                local uin = array[i]
                if not self.m_PkgMsg[uin] then self.m_PkgMsg[uin] = {idx = 0, msg = {}} end
                local idx = self.m_PkgMsg[uin].idx + 1 --递增索引
                self.m_PkgMsg[uin].idx = idx
                self.m_PkgMsg[uin].msg[idx] = {p = param, i = taskid}
                if ResetID and self.m_lastSyncData[uin] and self.m_lastSyncData[uin][ResetID] and self.m_lastSyncData[uin][ResetID][key] then
                    self.m_lastSyncData[uin][ResetID][key] = nil
                end
            end
        else
            if not self.m_PkgMsg[playerid] then self.m_PkgMsg[playerid] = {idx = 0, msg = {}} end
            local idx = self.m_PkgMsg[playerid].idx + 1 --递增索引
            self.m_PkgMsg[playerid].idx = idx
            self.m_PkgMsg[playerid].msg[idx] = {p = param, i = taskid}
            if ResetID and self.m_lastSyncData[playerid] and self.m_lastSyncData[playerid][ResetID] and self.m_lastSyncData[playerid][ResetID][key] then
                self.m_lastSyncData[playerid][ResetID][key] = nil
            end
        end
    end,

    CustomUI_SmoothAnim = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:setElementSmoothAnim(unpack(tdata)) --旋转元素
        end
    end,
    CustomUI_AnimEffect = function (self,playerid,tdata)
        if UIEditorDef then
            if #tdata == 4 then
                UIEditorDef:setElementAnimEffect(unpack(tdata)) --旋转元素
            else
                local elementid,text,animid,time,mode,transtr = unpack(tdata)
                if transtr and transtr ~= "" then
                    transtr = WorldStringTranslateMgr:getTransByMultiplekeys(14, transtr)
                    if not transtr and transtr == '' then
                        transtr = text
                    end
                    text = transtr or text
                end
                if type(animid) == 'number' then
                    local def = GetInst("CsvConfig"):GetOneLineById("DevUIAnimation",animid)
                    if def then
                        if def.Type == 4 then -- 文本特效
                            text = DefMgr:filterString(text,false)
                            UIEditorDef:setElementAnimEffect(elementid,animid,time,mode, text) --旋转元素
                        end
                    else
                        UIEditorDef:setElementState(elementid,text,animid,time) --页面状态动画
                    end
                end
            end
        end
    end,
    CustomUI_LoaderModel = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:SetLoaderModel(unpack(tdata)) --旋转元素
        end
    end,
    CustomUI_LoaderModelScale = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:SetLoaderModelScale(unpack(tdata)) --旋转元素
        end
    end,
    CustomUI_LoaderModelDir = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:SetLoaderModelDir(unpack(tdata)) --旋转元素
        end
    end,
    CustomUI_LoaderModelAct = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:SetLoaderModelAct(unpack(tdata)) --旋转元素
        end
    end,
    
    CustomUI_SliderToPos = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:TurnSliderToPos(unpack(tdata)) --旋转元素
        end
    end,
    CustomUI_SliderDir = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:SetSliderDir(unpack(tdata)) --旋转元素
        end
    end,
    CustomUI_SliderBarImg = function (self,playerid,tdata)
        if UIEditorDef then
            UIEditorDef:SetSliderBarImg(unpack(tdata)) --旋转元素
        end
    end,

    _ignoreMergeMsg = function (self,taskid,tdata)
        if taskid == SSTASKID.CUSTOMUI_SETTEXT then
            local itemid = tdata[1]
            if itemid and self.m_customuiitemingroe[itemid] == nil then
                local data = utils.split(itemid,'_')
                local uiid = data[1]
                local type = nil
                local project = UIProjectLibMgr:GetProjcet(UILIBTYPE_MAP, uiid)
                if project then
                    local pDevNode = project:FindNodeById(itemid)
                    if pDevNode then
                        type = pDevNode:GetControlType()
                    end
                end
                if type then
                    self.m_customuiitemingroe[itemid] = type == 7
                    return self.m_customuiitemingroe[itemid]
                end
            else
                return self.m_customuiitemingroe[itemid]
            end
        end
        return false
    end,

    -- 清理合并消息状态
    _ResetMergeMsg = function (self,uin,taskid,param)
        uin = uin or 0 -- nil表示全部广播
        local tkey = ''
        if MergeMsgID[taskid] then
            local paramix = MergeMsgID[taskid]
            for _, v in pairs(paramix) do
                local ix1 = paramix[v]
                if ix1 and param[ix1]  then
                    tkey = tostring(param[ix1])
                end
            end
        elseif  MsgPackID[taskid] then
            local paramix = MsgPackID[taskid]
            for _, v in pairs(paramix) do
                local ix1 = paramix[v]
                if ix1 and param[ix1]  then
                    tkey = tostring(param[ix1])
                end
            end
        end
        if tkey and tkey ~= '' then
            local len = string.len(tkey)
            if uin == 0  then
                for uin, data in pairs(self.m_lastSyncData) do
                    for _taskid , v in pairs(data) do
                        if _taskid ~= taskid then
                            for k, vv in pairs(v) do
                                -- if type(k) == 'string' and string.find(k,tkey) then
                                if type(k) == 'string' and string.sub(k,1,len) == tkey then
                                    v[k] = nil
                                end
                            end
                        end
                    end
                end
            else
                if self.m_lastSyncData[uin] then
                    for _taskid , v in pairs(self.m_lastSyncData[uin]) do
                        if _taskid ~= taskid then
                            for k, vv in pairs(v) do
                                if type(k) == 'string' and string.sub(k,1,len) == tkey then
                                -- if type(k) == 'string' and string.find(k,tkey) then
                                    v[k] = nil
                                end
                            end
                        end
                    end
                end
            end
        end
    end,

    TransmitPlayer = function (self,playerid,tdata)
        if GameVmInterface then
            GameVmInterface:TransmitPlayer(tdata)
        end
    end,
    
    --天空盒消息
    WorldSkyBox = function (self,playerid,tdata)
        if not tdata then
            return
        end
        local envInterface = GetInst("UGCEnvInterface")
        if envInterface then
            envInterface:OnTriggerTask(playerid, tdata)
        end
    end,

    PlayerCameraMsg = function (self,playerid,tdata)
        if type(tdata) ~= "table" then
            return
        end

        local camInterface = GetInst("UGCCamInterface")
        if not camInterface then
            return;
        end
        camInterface:OnTriggerTask(playerid, tdata)        
    end,

    PlayerOpenDevSkin = function (self,playerid,tdata)
        GameVmInterface:OpenDevSkin(tdata)
    end,

    TriggerStateAttr = function (self,playerid,tdata)
        GameVmInterface:TriggerStateAttr(tdata)
    end,

    Client_Ugc_InitFinish = function (self,playerid,tdata)
        UGCGetInst("SaveDataMgr"):SetClientInitFinish(tdata.code)
    end,
    
}
ScriptSupportTask:constructor() -- 构造


-- 处理开发者任务(主机收集)
_G.SSTaskReceiveForHost = function(uin, taskid, paramjson)
    -- if not ScriptSupportTask.TaskReg[taskid] then
    --     UGCGetInst("DataSycMgr"):ReciveMsgForHost(taskid,uin,paramjson)
    -- else
    --     ScriptSupportTask:receiveTaskForHost(uin, taskid, paramjson)
    -- end
end

-- 处理开发者任务(客机处理)
_G.SSTaskReceiveForPlayer = function(taskid, paramjson)
    if not ScriptSupportTask.TaskReg[taskid] then
        UGCGetInst("DataSycMgr"):ReciveMsgForClient(taskid,paramjson)
    else
        ScriptSupportTask:receiveTaskForPlayer(taskid, paramjson)
    end
end
