﻿#ifndef __GameAnalytics_H__
#define __GameAnalytics_H__

#include "SandboxEngine.h"

#include <map>
#include <string>
#include <type_traits>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>

// #if !defined(IWORLD_SERVER_BUILD) && (defined(_WIN32) || defined(_WIN64)) || defined(ANDROID) || defined(__ANDROID__) || defined(__APPLE__)
// // #if !defined(IWORLD_SERVER_BUILD) && (defined(_WIN32) || defined(_WIN64))
// #define USE_THINKINGDATA 1
// #endif  
//
// #ifdef USE_THINKINGDATA
// #include "ta_analytics_sdk.h" 
// #endif

class EXPORT_SANDBOXENGINE GameAnalytics {
public:
    GameAnalytics();
    ~GameAnalytics();
    
    struct Value
    {
        enum class Type {
            Int,
            Int64,
            Float,
            String,
            Bool
        };

        Type type;

        union {
            int int_value;
            int64_t int64_value;
            float float_value;
            bool bool_value;
        };
        std::string string_value; // string需要单独处理，不能放在union中

        // 构造函数
        Value() {}
        Value(int value) : type(Type::Int), int_value(value) {}
        Value(int64_t value) : type(Type::Int64), int64_value(value) {}
        Value(float value) : type(Type::Float), float_value(value) {}
        Value(bool value) : type(Type::Bool), bool_value(value) {}
        Value(const std::string& value) : type(Type::String), string_value(value) {}
        Value(const char* value) : type(Type::String), string_value(value) {}

        // 复制构造函数
        Value(const Value& other) : type(other.type) {
            switch (type) {
            case Type::Int: int_value = other.int_value; break;
            case Type::Int64: int64_value = other.int64_value; break;
            case Type::Float: float_value = other.float_value; break;
            case Type::Bool: bool_value = other.bool_value; break;
            case Type::String: string_value = other.string_value; break;
            }
        }

        // 赋值操作符
        Value& operator=(const Value& other) {
            if (this != &other) {
                type = other.type;
                switch (type) {
                case Type::Int: int_value = other.int_value; break;
                case Type::Int64: int64_value = other.int64_value; break;
                case Type::Float: float_value = other.float_value; break;
                case Type::Bool: bool_value = other.bool_value; break;
                case Type::String: string_value = other.string_value; break;
                }
            }
            return *this;
        }

        ~Value() { }
    };

    struct CommonProperties {

        // 会话ID
        std::string session_id;
        // 是否是第一次会话
        bool is_first_session;
        // 渠道ID
        int channel_id;
        // 应用版本
        std::string app_version;

        // IP地址
        std::string ip;
        // 操作系统类型
        int os_type;
        // 操作系统版本
        std::string os_version;
        // 系统语言
        std::string os_language;
        // app语言
        std::string app_language;
        // 时区
        int timezone_offset;
        // 网络类型
        int network_type; //"0=其他 1 = wifi  2 = 4G  3 = 5G"
        // 国家
        std::string country;
        // 国家代码
        std::string country_code;
        
        // 环境
        int env;
        // 游戏会话ID
        std::string game_id;
        // fps
        int fps;
        // ping
        int ping;

        // uin，作为account_id
        std::string uin;
        // 设备ID，作为distinct_id
        std::string device_id;        

        ////////////服务端通用参数///////////////////
        std::string roomid;
        std::string mapid;
        std::string host; //ip:port
        std::string mapver;
        std::string area;
    };

    static GameAnalytics* GetInstance();
    // 初始化
    static bool Init(int env);
    static void SetCommonProps(CommonProperties& properties);
    static const CommonProperties& GetCommonProps() { return s_commonProps; }

    // 设置会话相关信息
    static void SetSessionInfo(const std::string& session_id);
    // 设置游戏会话信息
    static void SetGameSessionInfo(const std::string& game_session_id);
    static void SetGameServerInfo(CommonProperties& properties);

    // 设置设备信息
    static void SetDeviceInfo(const std::string& ip_address, const std::string& os_type, int apn);

    // 客户端选择服务器后设置
    static void SetCurrentServerSetting(const std::string& server_setting);

    static std::string GetDistinctID();

    static void SetAccountId(const std::string& account_id);
    static void SetDistinctId(const std::string& distinct_id);

    // 登录/登出
    static void Login(const std::string& login_id);
    static void Logout();

    // 通用事件（用于自定义事件） 
    static void TrackEvent(const std::string& event_name, std::map<std::string, Value> data, bool server = false, int uin = 0);

    // 通用事件（用于自定义事件，json数据） 
    static void TrackEvent(const std::string& event_name, const std::string& json = "", bool server = false, int uin = 0);

    static void TrackEventImmediate(const std::string& event_name, std::map<std::string, Value> data, bool server = false, int uin = 0);
    static void TrackEventImmediate(const std::string& event_name, const std::string& json = "", bool server = false, int uin = 0);

    void Tick(double deltaTime);

    // 模板重载，支持直接传递不同类型的值
    template<typename T>
    static void SetUserProfile(const std::string& property_name, const T& value);

    static std::string genLogid();
    static int getSessionDuration();
    static int getGameSessionDuration();
   
    private:
        static bool m_initialized;
        // 存储公共参数
        static CommonProperties s_commonProps;

        //定时器相关
        double m_LastTick;
        double m_StartTick;
        
        // 异步处理相关
        struct EventItem {
            std::string event_name;
            std::string json_data;
            std::map<std::string, Value> map_data;
            bool is_json_type;
            bool is_immediate;
            bool is_server;
            int uin;
            EventItem(const std::string& name, const std::string& json, bool immediate = false, bool server = false, int uin = 0) 
                : event_name(name), json_data(json), is_json_type(true), is_immediate(immediate), is_server(server), uin(uin) {}
            EventItem(const std::string& name, const std::map<std::string, Value>& data, bool immediate = false, bool server = false, int uin = 0) 
                : event_name(name), map_data(data), is_json_type(false), is_immediate(immediate), is_server(server), uin(uin) {}
        };
        
        static std::queue<EventItem> s_eventQueue;
        static std::mutex s_queueMutex;
        static std::condition_variable s_queueCV;
        static std::thread s_workerThread;
        static std::atomic<bool> s_shouldStop;
        static std::atomic<bool> s_threadStarted;

        // 当前服务器类型
        static std::string s_current_server_setting;
        
        static void StartWorkerThread();
        static void StopWorkerThread();
        static void WorkerThreadFunc();
        static void ProcessEventInThread(const EventItem& item);

#ifdef USE_THINKINGDATA
        // 创建带公共属性的 TDJSONObject
        static thinkingdata::TDJSONObject createCommonProperties();
#endif  
};
 
#endif // __GameAnalytics_H__