﻿// #if defined(IWORLD_SERVER_BUILD) || (defined(ANDROID) || defined(__ANDROID__))

#include "GameAnalytics.h"

#include "TDAnalytics.h"
#include "TDLoggerConsumer.h"
#include "TDDebugConsumer.h"
#include "TDBatchConsumer.h"

#include "Utilities/Logs/LogAssert.h"
#include "RapidJSON/RapidJSONConfig.h"
#include "RapidJSON/document.h"

#include "Utils/ClientInfoProxy.h"
#include "Core/worldData/WorldManager.h"
#include "Core/actors/helper/IClientPlayer.h"
#include "proto_common.pb.h"

using namespace thinkingDataAnalytics;
 
// 全局变量定义
GameAnalytics* g_pGameAnalytics = nullptr;
GameAnalytics::CommonProperties GameAnalytics::s_commonProps;
bool GameAnalytics::m_initialized = false;
static TDAnalytics* te = nullptr;
static TDBatchConsumer* consumer = nullptr;

// 异步处理相关静态成员定义
std::queue<GameAnalytics::EventItem> GameAnalytics::s_eventQueue;
std::mutex GameAnalytics::s_queueMutex;
std::condition_variable GameAnalytics::s_queueCV;
std::thread GameAnalytics::s_workerThread;
std::atomic<bool> GameAnalytics::s_shouldStop{false};
std::atomic<bool> GameAnalytics::s_threadStarted{false};

std::string GameAnalytics::s_current_server_setting = "";

GameAnalytics::GameAnalytics() {
    m_StartTick = 0;
    m_LastTick = 0;
}

GameAnalytics::~GameAnalytics() {

    StopWorkerThread();
    
    if (te) {
        te->close();
        delete te;
        te = nullptr;
    }

    if (consumer) {
        delete consumer;
        consumer = nullptr;
    }
}

GameAnalytics* GameAnalytics::GetInstance()
{
    return g_pGameAnalytics;
}

bool GameAnalytics::Init(int env) {
    if (g_pGameAnalytics != nullptr) {
        return true;
    }
    g_pGameAnalytics = new GameAnalytics();

    std::string appId = "a12c62532cf54941ba8cb3cb63784b07";
    if (env == 1) {
        appId = "199b3326948e4dde9dd338bcc3634c6e"; //测试
    }
    s_commonProps.env = env;

    std::string serverUrl = "https://tga.mini1.cn"; 
    #ifdef IWORLD_SERVER_BUILD
    consumer = new TDBatchConsumer(appId, serverUrl, 20, false, "");  // 服务器设置为20条批量上报
    #else
    consumer = new TDBatchConsumer(appId, serverUrl, 3, false, "");  // 客户端设置为3条批量上报
    #endif

    te = new TDAnalytics(*consumer, false);

    m_initialized = true;
    
    // 启动异步处理线程
    StartWorkerThread();
    
    return true; 
}

void GameAnalytics::SetCommonProps(CommonProperties& properties) {
	
    // KEY: distinct_id
    s_commonProps.device_id = properties.device_id;
    // KEY: account_id
	s_commonProps.uin = properties.uin;

	s_commonProps.session_id = properties.session_id;
	s_commonProps.game_id = properties.game_id;
	s_commonProps.env = properties.env;

#ifdef IWORLD_SERVER_BUILD
    s_commonProps.roomid = properties.roomid;
    s_commonProps.mapid = properties.mapid;
    s_commonProps.host = properties.host; //ip:port
    s_commonProps.mapver = properties.mapver;
    s_commonProps.area = properties.area;
#else
    s_commonProps.is_first_session = properties.is_first_session;
    s_commonProps.ip = properties.ip;
    s_commonProps.app_version = properties.app_version;
    s_commonProps.channel_id = properties.channel_id;
    s_commonProps.os_type = properties.os_type;
    s_commonProps.os_version = properties.os_version;
    s_commonProps.os_language = properties.os_language;
    s_commonProps.network_type = properties.network_type;
    s_commonProps.app_language = properties.app_language;
    s_commonProps.timezone_offset = properties.timezone_offset;
    s_commonProps.country = properties.country;
    s_commonProps.country_code = properties.country_code;
    s_commonProps.ping = properties.ping;
    s_commonProps.fps = properties.fps;

    if (te) {
		TDPropertiesNode commProps;
		commProps.SetString("session_id", s_commonProps.session_id);
		commProps.SetString("game_id", s_commonProps.game_id);
		commProps.SetNumber("env", s_commonProps.env);
        commProps.SetBool("is_first_session", s_commonProps.is_first_session);
		commProps.SetString("ip", s_commonProps.ip);
		commProps.SetString("app_version", s_commonProps.app_version);
		commProps.SetNumber("channel_id", s_commonProps.channel_id);
		commProps.SetNumber("os_type", s_commonProps.os_type);
        commProps.SetString("os_version", s_commonProps.os_version);
        commProps.SetString("os_language", s_commonProps.os_language);
        commProps.SetString("app_language", s_commonProps.os_language);
        commProps.SetNumber("timezone_offset", s_commonProps.timezone_offset);
        commProps.SetNumber("network_type", s_commonProps.network_type);
        commProps.SetString("country", s_commonProps.country);
        commProps.SetString("country_code", s_commonProps.country_code);

        te->setSupperProperties(commProps);
    }
#endif
}

void GameAnalytics::SetGameServerInfo(CommonProperties& properties) {
   
}

std::string GameAnalytics::GetDistinctID()
{
    return s_commonProps.device_id;
}

// 服务端通用事件（用于自定义事件） 
void GameAnalytics::TrackEvent(const std::string& event_name, std::map<std::string, Value> data, bool server, int uin) {
    if (!m_initialized) {
        return;
    }

    // 将事件加入队列，异步处理
    {
        std::lock_guard<std::mutex> lock(s_queueMutex);
        s_eventQueue.emplace(event_name, data, false, server, uin);
        s_queueCV.notify_one();
    }
};
// 服务端通用事件（用于json上报） 
void GameAnalytics::TrackEvent(const std::string& event_name, const std::string& json_data, bool server, int uin) {
    if (!m_initialized) {
        //WarningString("[GameAnalytics] Not initialized");
        return;
    }
    
    if (event_name.empty()) {
        return;
    }

    // 将事件加入队列，异步处理
    {
        std::lock_guard<std::mutex> lock(s_queueMutex);
        s_eventQueue.emplace(event_name, json_data, true, server, uin);
        s_queueCV.notify_one();
    }
};

void GameAnalytics::TrackEventImmediate(const std::string& event_name, std::map<std::string, Value> data, bool server, int uin) {
    if (!m_initialized) {
        return;
    }

    {
        std::lock_guard<std::mutex> lock(s_queueMutex);
        s_eventQueue.emplace(event_name, data, false, server, uin);
        s_queueCV.notify_one();
    }
}

void GameAnalytics::TrackEventImmediate(const std::string& event_name, const std::string& json_data, bool server, int uin) {
    if (!m_initialized) {
        return;
    }
    
    if (event_name.empty()) {
        return;
    }

    {
        std::lock_guard<std::mutex> lock(s_queueMutex);
        s_eventQueue.emplace(event_name, json_data, true, server, uin);
        s_queueCV.notify_one();
    }
}
 
void GameAnalytics::Tick(double deltaTime) {
    //unsigned long long currentTick = Rainbow::Timer::getSystemTick();
    if (m_StartTick == 0) { // First call
        m_StartTick = m_LastTick = deltaTime;
    }
    m_LastTick += deltaTime;

    double totalElapsed = m_LastTick - m_StartTick;

    if (totalElapsed >= 5) { //5s
        if (te) {
            te->flush();
            LOG_INFO("GameAnalytics::flush success");
        }
        m_StartTick = m_LastTick;
    }
}

void GameAnalytics::SetGameSessionInfo(const std::string& game_session_id) {
    s_commonProps.game_id = game_session_id;

    SetCommonProps(s_commonProps);
}

void GameAnalytics::SetAccountId(const std::string& account_id) {
    s_commonProps.uin = account_id;
}

void GameAnalytics::SetDistinctId(const std::string& distinct_id) {
    s_commonProps.device_id = distinct_id;
}

void GameAnalytics::Login(const std::string& login_id) 
{
    s_commonProps.uin = login_id;
}

void GameAnalytics::Logout()
{
    s_commonProps.uin = "";
}

void GameAnalytics::SetSessionInfo(const std::string& session_id) {
    s_commonProps.session_id = session_id;

    SetCommonProps(s_commonProps);
}

std::string GameAnalytics::genLogid() { return ""; }
int GameAnalytics::getSessionDuration() { return 0; }
int GameAnalytics::getGameSessionDuration() { return 0; }

// 异步处理方法实现
void GameAnalytics::StartWorkerThread() {
    if (s_threadStarted.exchange(true)) {
        return; // 已经启动
    }
    
    s_shouldStop = false;
    s_workerThread = std::thread(WorkerThreadFunc);
}

void GameAnalytics::StopWorkerThread() {
    if (!s_threadStarted.exchange(false)) {
        return; // 未启动或已停止
    }
    
    // 通知工作线程停止
    s_shouldStop = true;
    s_queueCV.notify_all();
    
    // 等待线程结束
    if (s_workerThread.joinable()) {
        s_workerThread.join();
    }
    
    // 处理剩余事件
    std::lock_guard<std::mutex> lock(s_queueMutex);
    while (!s_eventQueue.empty()) {
        ProcessEventInThread(s_eventQueue.front());
        s_eventQueue.pop();
    }
}

void GameAnalytics::WorkerThreadFunc() {
    while (!s_shouldStop) {
        std::unique_lock<std::mutex> lock(s_queueMutex);
        
        // 等待事件或停止信号
        s_queueCV.wait(lock, []{ 
            return !s_eventQueue.empty() || s_shouldStop; 
        });
        
        // 处理队列中的所有事件
        while (!s_eventQueue.empty() && !s_shouldStop) {
            EventItem item = s_eventQueue.front();
            s_eventQueue.pop();
            lock.unlock();
            
            // 在后台线程中处理事件
            ProcessEventInThread(item);
            
            lock.lock();
        }
    }
}

void GameAnalytics::ProcessEventInThread(const EventItem& item) {
    if (!te) {
        return;
    }
    
    TDPropertiesNode properties;
    
    if (item.is_json_type) {
        // 处理JSON类型事件
        if (!item.json_data.empty()) {
            rapidjson::Document root;
            root.Parse(item.json_data.c_str());
            
            if (root.IsObject()) {
                for (auto it = root.MemberBegin(); it != root.MemberEnd(); ++it) {
                    const rapidjson::Value& value = it->value;
                    if (value.IsString()) {
                        properties.SetString(it->name.GetString(), value.GetString());
                    } else if (value.IsInt()) {
                        properties.SetNumber(it->name.GetString(), value.GetInt());
                    } else if (value.IsDouble()) {
                        properties.SetNumber(it->name.GetString(), static_cast<float>(value.GetDouble()));
                    } else if (value.IsBool()) {
                        properties.SetBool(it->name.GetString(), value.GetBool());
                    }
                }
            }
        }
    } else {
        // 处理Map类型事件
        for (const auto& pair : item.map_data) {
            const std::string& key = pair.first;
            const Value& value = pair.second;
            
            switch (value.type) {
            case Value::Type::String:
                properties.SetString(key, value.string_value);
                break;
            case Value::Type::Int:
                properties.SetNumber(key, value.int_value);
                break;
            case Value::Type::Float:
                properties.SetNumber(key, value.float_value);
                break;
            case Value::Type::Bool:
                properties.SetBool(key, value.bool_value);
                break;
            }
        }
    }
    
#ifndef IWORLD_SERVER_BUILD
    if (s_current_server_setting.size() > 0) {
        properties.SetString("current_server_setting", s_current_server_setting);
    }
#endif

    // 调用te接口上报事件
#if defined(IWORLD_SERVER_BUILD)
    if (item.is_server) {
		std::string uin = std::to_string(item.uin);
        std::string distinct = "";
        IClientPlayer* player = GetWorldManagerPtr()->getPlayerByUin(item.uin);
        if (player && player->getCommonProps()) {
            distinct = player->getCommonProps()->device_id();
            properties.SetString("game_id", player->getCommonProps()->game_id());
            properties.SetString("device_id", player->getCommonProps()->game_id());
            properties.SetNumber("channel_id", player->getCommonProps()->channel_id());
            properties.SetNumber("env", player->getCommonProps()->env());
            properties.SetNumber("timezone_offset", player->getCommonProps()->timezone_offset());
            properties.SetNumber("os_type", player->getCommonProps()->os_type());
            properties.SetString("app_version", player->getCommonProps()->app_version());
            properties.SetString("os_language", player->getCommonProps()->os_language());
            properties.SetString("country", player->getCommonProps()->country());
        }

		te->track(uin, distinct, item.event_name, properties);
    } else {
        te->track(s_commonProps.roomid, s_commonProps.host, item.event_name, properties);
    }

#else
    if (s_commonProps.device_id.size() == 0) {
        s_commonProps.device_id = GetClientInfoProxy()->getDeviceID();
    }
    te->track(s_commonProps.uin, s_commonProps.device_id, item.event_name, properties);
#endif
    
    if (item.is_immediate) {
        te->flush();
    }
}

// 模板函数实现
template<typename T>
void GameAnalytics::SetUserProfile(const std::string& property_name, const T& value) {
}

void GameAnalytics::SetCurrentServerSetting(const std::string& server_setting) {
    s_current_server_setting = server_setting;
}


// #endif
