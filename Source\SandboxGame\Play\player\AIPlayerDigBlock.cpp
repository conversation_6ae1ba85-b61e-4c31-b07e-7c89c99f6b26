
#include "AIPlayerDigBlock.h"
#include "ClientMob.h"
#include "AINpc.h"
#include "world.h"
#include "EffectManager.h"
#include "container_backpack.h"
#include "PathEntity.h"
#include "special_blockid.h"
#include "PlayerAttrib.h"
#include "ActorLocoMotion.h"
#include "FindComponent.h"
#include "SoundComponent.h"
#include "ClientActorHelper.h"
#include "base/SandboxGlobalNotifyCore.h"
#include "ActorBody.h"
#include "backpack.h"
#include "ToAttackTargetComponent.h"

using namespace Rainbow;
using namespace MNSandbox;

AIPlayerDigBlock::AIPlayerDigBlock(AINpcPlayer *pActor, int prob, int dist, int blockid, float speed, int digdist) : AIBase(pActor), m_iProb(prob), m_iDist(dist),
	m_iBlockID(blockid), m_fSpeed(speed), m_iDigDist(digdist), m_iDigTicks(-1), m_iTimer(0), m_TargetPos(WCoord(0, -1, 0))
{
	setMutexBits(7);
	m_DigBlockIds = pActor->getDef()->DigBlocks;
	m_iDist = pActor->getDef()->DigSearchRange * 100; //方块搜索范围，单位 block
	m_iDigDist = pActor->getDef()->DigDistance *100; //挖掘距离，单位 block
	
}

AIPlayerDigBlock::~AIPlayerDigBlock()
{
	//if (m_PathEntity) m_PathEntity->release();
}

bool AIPlayerDigBlock::findDigBlock(WCoord& targetPos)
{ 
	float dist = (float)m_iDist / BLOCK_SIZE;
	int chunkRange = (int)ceil(dist/16);
 
	char cExtend[16];
	memset(cExtend, 0, sizeof(cExtend));
	sprintf(cExtend, "%d", m_iDigDist);

	for (size_t i = 0; i < m_DigBlockIds.size(); i++)
	{
		int blockId = m_DigBlockIds[i];
	 
		auto findComponent = m_pAINpcPlayer->getFindComponent();
		if (findComponent && findComponent->findNearestBlock(targetPos, blockId, (int)dist, AIPlayerDigBlock::canDigBlock, cExtend,true))
		{
			m_iBlockID = blockId;
			return true;
		}
	}
	return false;
}
 
bool AIPlayerDigBlock::willRun()
{
	if (!m_pAINpcPlayer)
		return false;

	auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
	if (targetComponent)
	{
		if (targetComponent->getTarget()) {
			return false;
		}
	}

	IClientActor* item = searchForOtherItemsNearby();
	if (item) {
		m_TargetPos = (item->getPosition() + (BLOCK_SIZE / 2));
		m_pAINpcPlayer->faceWorldPos(m_TargetPos, 180.0f, 180.0f);
		m_iDigTicks = 0;
		return true;
	}

	auto *blockDef = GetDefManagerProxy()->getBlockDef(m_iBlockID);
	if (!blockDef)
		return false;

	/*BackPack* backpack = m_pAINpcPlayer->getBackPack();
	if (backpack == NULL )
		return false;

	PackContainer* bags =  backpack->getPack(SHORTCUT_START_INDEX); // 获取快捷栏容器（8格） 选择武器
	BackPackGrid* pweapon = livingAttr->getEquipGrid(EQUIP_WEAPON);
	if (!pweapon)
		return false;
	*/

	auto livingAttr = m_pAINpcPlayer->getLivingAttrib();
	if (!livingAttr)
		return false;

	WCoord tmpPos;
	 
	if (findDigBlock(tmpPos))
	{
		if (m_pAINpcPlayer->getBody())
		{
			livingAttr->applyEquips(m_pAINpcPlayer->getBody(), EQUIP_WEAPON);
		}

		//int toolId = 0;
		//int ticks = m_pAINpcPlayer->getMineBlockTicks(toolId, m_iBlockID, m_pAINpcPlayer->getWorld()->getBlockData(tmpPos), &m_MineType);

		m_TargetPos = BlockCenterCoord(tmpPos);
		
		return true;
	}

	return false;
}
void AIPlayerDigBlock::start()
{
	m_pAINpcPlayer->faceWorldPos(m_TargetPos, 180.0f, 180.0f);
	m_pAINpcPlayer->getNavigator()->tryMoveTo(m_TargetPos.x, m_TargetPos.y, m_TargetPos.z, m_fSpeed);
	m_pAINpcPlayer->playAnim(SEQ_WALK);

	m_iDigTicks = 0;
	m_iLastPlaySoundTick = 0;
	m_iDigTotalTicks = 5 * 20;  //大概每个方块挖5秒，
	
	m_iTimer = GenRandomInt(300, 400);
	m_TraceTimer = 0;
}

bool AIPlayerDigBlock::continueRun()
{
	if (!m_pAINpcPlayer)
		return false;
	if (m_iDigTicks < 0)
		return false;
	if (m_iTimer < 0)
		return false;

	auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
	if (targetComponent)
	{
		if (targetComponent->getTarget()) {
			return false;
		}
	}

	WCoord vec = m_pAINpcPlayer->getLocoMotion()->getPosition() - m_TargetPos;
	float dist = vec.length();
	 
	if (dist <= m_iDigDist)
	{
		return true;
	}

	if (m_pAINpcPlayer->getNavigator()->noPath())
	{
		return false;
	}

	return true;
}


void AIPlayerDigBlock::reset()
{
	m_pAINpcPlayer->getNavigator()->clearPathEntity();

	WCoord blockpos = CoordDivBlock(m_TargetPos);
	int blockid = m_pAINpcPlayer->getWorld()->getBlockID(blockpos);
	if (blockid == m_iBlockID)
	{
		m_pAINpcPlayer->getWorld()->getEffectMgr()->playBlockCrackEffect(blockpos, -1, m_pAINpcPlayer->getObjId());
	}
	if (blockid  > 0 && m_iDigTicks > 0 && m_iDigTicks != m_iDigTotalTicks)
	{
		ObserverEvent_ActorBlock obevent(m_pAINpcPlayer->getObjId(), m_iBlockID, blockpos.x, blockpos.y, blockpos.z);
		if (ClientActorHelper::isTriggerCreatureType(m_pAINpcPlayer->getObjType()))
		{
			obevent.SetData_Actor(m_pAINpcPlayer->getDefID());
		}
		GetObserverEventManager().OnTriggerEvent("Block.Dig.Cancel", &obevent); // 生物采集方块完成
		MNSandbox::GlobalNotifyCore::GetInstance().NotifyBlockDigCancel(m_pAINpcPlayer->getWorld(), m_iBlockID, blockpos, m_pAINpcPlayer->StaticToCast<MNSandbox::SandboxNode>());
	}
	m_TargetPos = WCoord(0, -1, 0);
	m_iDigTicks = -1;
}

void AIPlayerDigBlock::update()
{
	m_iTimer--;
	if (!m_pAINpcPlayer) return;

	/*if (--m_TraceTimer <= 0 && m_pAINpcPlayer->getAttrib()->getMoveSpeed() * m_fSpeed != 0)
	{
		m_TraceTimer = 4 + GenRandomInt(0, 6);
		m_pAINpcPlayer->getNavigator()->tryMoveTo(m_TargetPos.x, m_TargetPos.y, m_TargetPos.z, m_fSpeed);
	}
	*/
	WCoord vec = m_pAINpcPlayer->getLocoMotion()->getPosition() - m_TargetPos;
	float dist = vec.length();
	if (dist <= m_iDigDist)
	{
		WCoord blockpos = CoordDivBlock(m_TargetPos);
		int blockid = m_pAINpcPlayer->getWorld()->getBlockID(blockpos);
		if (blockid == m_iBlockID)
		{
			const BlockDef *def = GetDefManagerProxy()->getBlockDef(m_iBlockID);

			if (!m_pAINpcPlayer->getNavigator()->noPath())
			{
				m_pAINpcPlayer->getNavigator()->clearPathEntity();
			}
			m_iDigTicks++;
			if ((m_iDigTicks % 10) == 1)
			{
				// 观察者事件接口
				if (m_iDigTicks == 1)
				{
					ObserverEvent_ActorBlock obevent(m_pAINpcPlayer->getObjId(), m_iBlockID, blockpos.x, blockpos.y, blockpos.z);
					if (ClientActorHelper::isTriggerCreatureType(m_pAINpcPlayer->getObjType()))
					{
						obevent.SetData_Actor(m_pAINpcPlayer->getDefID());
					}
					GetObserverEventManager().OnTriggerEvent("Block.Dig.Begin", &obevent);
					MNSandbox::GlobalNotifyCore::GetInstance().NotifyBlockDigBegin(m_pAINpcPlayer->getWorld(), m_iBlockID, blockpos, m_pAINpcPlayer->StaticToCast<SandboxNode>());
				}
				m_pAINpcPlayer->playAnim(SEQ_STAND);
				m_pAINpcPlayer->playAnim(SEQ_ATTACK);
			}
			if (m_iDigTicks >= m_iDigTotalTicks)
			{
				m_pAINpcPlayer->getWorld()->getEffectMgr()->playBlockDestroyEffect(0, blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2), DIR_NEG_X, 40);
				m_pAINpcPlayer->getWorld()->destroyBlock(blockpos, m_MineType, m_pAINpcPlayer->getLivingAttrib()->getDigProbEnchant());


				if (!def->DigSound.empty()) m_pAINpcPlayer->getWorld()->getEffectMgr()->playSound(m_TargetPos, def->DigSound.c_str(), GSOUND_DESTROY);

				auto mobattrib = m_pAINpcPlayer->getPlayerAttrib();
				if (mobattrib)
					mobattrib->onCurToolUsed(-1);

				// 观察者事件接口
				if (m_iDigTicks == m_iDigTotalTicks)
				{
					ObserverEvent_ActorBlock obevent(m_pAINpcPlayer->getObjId(), m_iBlockID, blockpos.x, blockpos.y, blockpos.z);
					if (ClientActorHelper::isTriggerCreatureType(m_pAINpcPlayer->getObjType()))
					{
						obevent.SetData_Actor(m_pAINpcPlayer->getDefID());
					}
					GetObserverEventManager().OnTriggerEvent("Block.Dig.End", &obevent); // 生物采集方块完成
					GetObserverEventManager().OnTriggerEvent("Block.DestroyBy", &obevent);   // 生物采集方块完成也触发方块被破坏
					MNSandbox::GlobalNotifyCore::GetInstance().NotifyBlockDigFinish(m_pAINpcPlayer->getWorld(), m_iBlockID, blockpos, m_pAINpcPlayer->StaticToCast<MNSandbox::SandboxNode>());
				}
				m_iDigTicks = -1;
			}
			else
			{
				//播放挖掘的声音
				if (((m_iDigTicks + 1) % 5) == 0)
				{
					if (m_iDigTicks != m_iLastPlaySoundTick)
					{
						m_iLastPlaySoundTick = m_iDigTicks;

						if(def)
							m_pAINpcPlayer->getWorld()->getEffectMgr()->playSound(m_TargetPos, !def->WalkSound.empty() ? def->WalkSound.c_str() : "blocks.grass", GSOUND_DIG);

					}
				}

				//更新裂纹，有10个阶段
				int stage = m_iDigTicks * 10 / m_iDigTotalTicks;
				m_pAINpcPlayer->getWorld()->getEffectMgr()->playBlockCrackEffect(blockpos, stage, m_pAINpcPlayer->getObjId());
			}
		}
		else
			m_iDigTicks = -1;
	}	
}

static int checkDigOffsetBlcokPos[6][3] = {
	{-1, 0, 0}, {1, 0, 0}, {0, -1, 0},{0, 1, 0}, {0, 0, -1}, {0, 0, 1}
};

bool AIPlayerDigBlock::canDigBlock(ClientActor *actor, WCoord &blockpos, int itemid, std::string extend/* ="" */)
{
	if (!actor)
		return false;

	bool edgeBlock = false;
	for (int i = 0; i <= 5; i++)
	{
		WCoord pos = blockpos + WCoord(checkDigOffsetBlcokPos[i][0], checkDigOffsetBlcokPos[i][1], checkDigOffsetBlcokPos[i][2]);
		if (IsAirBlockID(actor->getWorld()->getBlockID(pos)))
		{ 
			edgeBlock = true;
			break;
		}
	}

	if (!edgeBlock)
		return false;

	if (IsCropsBlock(itemid) && actor->getWorld()->getBlockData(blockpos.x, blockpos.y, blockpos.z) < 7) //农作物未成熟
		return false;

	bool canDig = false;
	WCoord pos = BlockCenterCoord(blockpos);
	if (actor->getNavigator()->tryMoveTo(pos.x, pos.y, pos.z, 0.1f))
	{
		auto *path = actor->getNavigator()->getPath();
		if (path)
		{
			int size = path->getCurrentPathLength();
			PathPoint *pathPoint = path->getPathPointFromIndex(size - 1);

			WCoord dist1 = pos - pathPoint->block*BLOCK_SIZE;

			float dist2 = (float)atoi(extend.c_str());
			if (dist2 <= 0)
				dist2 = 200;
			if (dist1.length() <= dist2)
				canDig = true;
		}
	}
	

	actor->getNavigator()->clearPathEntity();

	return canDig;
}


IClientActor* AIPlayerDigBlock::searchForOtherItemsNearby()
{
	World* m_pWorld = g_WorldMgr->getWorld(0);
	if (m_pWorld == nullptr)
		return nullptr;

	std::vector<IClientActor*>actors;
	
	CollideAABB box;
	m_pAINpcPlayer->getCollideBox(box);
	box.expand(BLOCK_SIZE *50 , 0, BLOCK_SIZE * 50);

	m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_DROPITEM,-1);

	for (size_t i = 0; i < actors.size(); i++)
	{
		IClientActor* item = actors[i];
		if (item) {
			return item;
		}
		 
	}
	return nullptr;
}
