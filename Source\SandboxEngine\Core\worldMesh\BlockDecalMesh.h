#ifndef __DECALBLOCK_H__
#define __DECALBLOCK_H__

#include "GameScene/MovableObject.h"
#include "blocks/ShareRenderMaterial.h"
#include "SectionMesh.h"
#include "Core/GameObject.h"
#include "Utilities/dynamic_array.h"
#include "SectionRenderObject.h"
class BlockTexElement;
class BlockGeomTemplate;
//class BlockDecalRenderObject;
class BlockDecalMesh;
class BlockDecalRenderObject : public Rainbow::SectionRenderObject
{
public:
	BlockDecalRenderObject(BlockDecalMesh* obj);


	Rainbow::AABB CalculateWorldBounds() const override;

	virtual bool IsEnabled() const override;

	virtual bool PrepareRender(const Rainbow::PrimitiveFrameNode& node) override;

	virtual void ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator) override;

private:
	BlockDecalMesh* m_Object;
};



struct BlockDecalMaterialInfo 
{
	BlockDecalMaterialInfo()
		: m_IsPacked(false)
		, m_MatPacked(nullptr)
		, m_Mat(nullptr)
		, m_RenderBlockMtl(nullptr)
	{}
	~BlockDecalMaterialInfo() 
	{
		ENG_RELEASE(m_Mat);
		ENG_RELEASE(m_MatPacked);
		m_RenderBlockMtl = nullptr;
	}
	bool m_IsPacked;
	Rainbow::MaterialInstance* m_MatPacked;
	Rainbow::MaterialInstance* m_Mat;
	RenderBlockMaterial* m_RenderBlockMtl;
};


class BlockDecalSectionMesh : public SectionMesh 
{
public:
	BlockDecalSectionMesh(RenderBlockMaterial* mtl);
	virtual ~BlockDecalSectionMesh();
	virtual SectionSubMesh* getSubMesh(RenderBlockMaterial* mtl, bool foritem = false, int lodLevel = 0) override;
	//virtual SectionSubMesh* GetOrCreateSubMesh(RenderBlockMaterial* mtl, int materialId) override;
	Rainbow::MaterialInstance* GetRenderMaterial(size_t index);
	void SetCustomMaterial();
	void SetIsSelectHightLight();
	bool IsSelectHight();
	void UpdateOverlayColor(const Rainbow::ColorRGBAf& color);
	void SetCurBlockId(int id);

	void UpdateVertexAnimationEnable();
	void UpdateOverrideColor(const Rainbow::ColorRGBAf& color);
private:
	void AppendMaterialInfo();
	size_t GetMaterialInfoIndex(RenderBlockMaterial* mtl);
private:
	dynamic_array<BlockDecalMaterialInfo*> m_MaterialList;
	SectionSubMesh* m_SubMesh;
	RenderBlockMaterial* m_Mtl;
	//Rainbow::MaterialInstance* m_MatPacked;
	//Rainbow::MaterialInstance* m_Mat;
	Rainbow::MaterialInstance* m_CustomMat;
	//bool m_IsPacked;
	int m_CurBlockResId;
	bool m_IsRebuildMesh;

};



class EXPORT_SANDBOXENGINE BlockDecalMesh : public Rainbow::MovableObject //tolua_exports
{ //tolua_exports
	DECLARE_CLASS(BlockDecalMesh);
public:
	DECLARE_CREATE_DESTORY(BlockDecalMesh)

	//tolua_begin
	BlockDecalMesh(const char *texname, int textype);
	~BlockDecalMesh();
	BlockDecalSectionMesh* getSectionMesh();
	//SectionSubMesh *getSubMesh();
	
	//virtual void updateWorldCache();
	//virtual void render(MINIW::SceneRenderer* pRenderer, const MINIW::ShaderEnvData &envdata);
	
	//tolua_end
	void Show(bool b) override;
	bool IsShow() { return isShow;  };

	void setBlock(World* pworld, const WCoord& grid, int stage); //stage:0-10
	WCoord GetBlock() const { return m_CurBlock;  };

	//virtual SectionSubMesh* getSubMesh(RenderBlockMaterial* mtl, bool foritem = false) override;

	Rainbow::MaterialInstance* GetRenderMaterial(size_t idx);

	void SetIsSelectHightLight();

	void SetOverlayColor(const Rainbow::ColorRGBAf& color);
	void SetOverrideColor(const Rainbow::ColorRGBAf& color);

	void UpdateVertexAnimationEnable();

	void SetCustomMaterial();
	
	// 方块描边功能
	void SetOutline(UInt32 value);
	UInt32 GetOutline() const;

protected:
	virtual void UpdateWorldBounds(const Rainbow::Matrix4x4f& localToWorld) override;

protected:
	virtual void OnAddToScene(Rainbow::GameScene* scene) override;
	virtual void OnRemoveFromScene(Rainbow::GameScene* scene) override;

	void UpdateOverlayColorInternal();
	void UpdateOverrideColorInternal();

	BlockDecalSectionMesh* m_DecalSectionMesh;

	RenderBlockMaterial * m_Mtl;
	//SectionSubMesh *m_SubMesh;
	//Rainbow::MaterialInstance* m_MatPacked;
	//Rainbow::MaterialInstance* m_Mat;
	//SectionMesh* m_SectionMesh;
	WCoord m_CurBlock;
	int m_CacheBlockData;

	Rainbow::ColorRGBAf m_OverlayColor;
	Rainbow::ColorRGBAf m_OverrideColor;
	int m_LastStage;
	bool isShow;
	//bool m_IsPacked;
	BlockDecalRenderObject* m_RenderObject;
	
	// 方块描边相关成员变量
	UInt32 m_OutlineValue;

}; //tolua_exports

#endif
