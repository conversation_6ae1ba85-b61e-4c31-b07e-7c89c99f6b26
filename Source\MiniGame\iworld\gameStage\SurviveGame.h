#ifndef __SURVIVE_GAME_H__
#define __SURVIVE_GAME_H__

#include "ClientGame.h"
#include "GameSuviveLoadHandler.h"
#include "SandboxEngine.h"
#include "SandboxAutoRef.h"
#include "ClientAccountInfo.h"
#include "world_types.h"

class PlayerControl;
class ClientPlayer;
class ActorBody;
//class ClientManager;
struct GameEvent;
class WorldContainerMgr;
class ClientActorMgr;
class WorldManager;
class EffectManager;
class EffectParticle;
class ClientActor;
class ChunkViewer;
struct WorldDesc;
class ClientGameNetHandler;
class WCoord;

//2021115  玩家房间行为，用于云服房主选择 codeby:liushuxin
enum ROOM_ACTION
{
	ROOM_ACTION_INIT = 0,
	ROOM_ACTION_ENTER = 1,
	ROOM_ACTION_LEAVE = 2,
};

//******** 选新房主条件 codeby:liushuxin
enum ROOM_SELECT_LEADER
{
	ROOM_SELECT_LEADER_DEFAULT = 0,      //不更换房主
	ROOM_SELECT_LEADER_NEW = 1,            //玩家加入地图当前没有房主
	ROOM_SELECT_LEADER_LEAVE = 2,          //房主离开地图
	ROOM_SELECT_LEADER_ORGANIZER = 3, //发起者加入地图没当过房主且其不为房主
};

///**
// * @brief 用作返回值, 返回一个队伍的信息
// * 
// */
//struct TeamInfo
//{
//	int m_TeamID; // 队伍id
//	unsigned m_Cap; // 队伍人数上限
//	std::vector<int> m_Members;  // 队伍玩家成员信息
//	TeamInfo(int teamid, unsigned cap, const std::vector<int> &members)
//		: m_TeamID(teamid), m_Cap(cap), m_Members(members)
//	{}
//};

class SurviveGame : public ClientGame{ //tolua_exports
	friend GameSuviveLoadHandler;
	typedef ClientGame Super;
public:
	virtual void setupPlayerAndVM();
	virtual void createNetHandler();
	virtual void createLoadHandler();
	virtual void createUiHandler();
	virtual bool initSurviveGame();

public:

	SurviveGame();
	virtual ~SurviveGame();
	//tolua_begin
	virtual const char *getName();
	virtual const char *getTypeName();
	virtual int getDebugInfo(char *buffer, int buflen);
	virtual bool load();
	virtual void unload(GAME_RELOAD_TYPE reloadtype=NO_RELOAD);
	virtual int getGameStage() override;
	virtual bool isInGame() override;
	virtual void prepareTick();
	virtual void tick();
	virtual void update(float dtime);

	//virtual void onGameEvent(GameEvent *ge);
	virtual int OnInputEvent(const Rainbow::InputEvent& event) override;
	
	virtual void renderUI(bool isHide=false);
	virtual void beginGame();
	virtual void endGame(bool isreload=false);

	virtual void pauseGame();

	int getCurOpenContanierIndex();
	virtual void setOperateUI(bool b); //设置操作ui或者场景
	virtual bool isOperateUI();
	virtual int getOperateUICount();
	bool getOperateUIState();//showOperateUI的get

	// 服务器的房间id，客户端需要保存，用于上报数据
	void setServerRoomId(const std::string& roomId) { m_serverRoomId = roomId; }
	std::string getServerRoomId() const { return m_serverRoomId; }

	void ResetOperateState();
	virtual void sendChat(const char *content, int type=0, int targetuin=0,int language=1, const char* extend = "");
	virtual void sendChatToSelf(const char* content, int type = 0, int language = 1) override;
	bool SetSigns(const char *content, int x, int y, int z);
	virtual void summonAccountHorse(int uin, int horseid, bool isshapeshift) {};
	
	virtual PlayerControl *getMainPlayer();
	void enableMinimap(bool b);

	int getGameTimeHour(bool curworld=false);
	int getGameTimeMinute(bool curworld=false);

	void playEffect(int x, int y, int z, const char *ent, int index=0);
	void playEffectVisibleBlock(int x, int y, int z, const char *ent, int index=0, bool sync2client=true,int visibledistblock = 16);
	void playEffectInRoleFront(const char *ent);
	void stopEffect(int index);
	void stopEffect();

	void addmob(int mobid, const char *effect=NULL);
	virtual int getNumPlayerBriefInfo()
	{
		return 0;
	}
	virtual PlayerBriefInfo *getPlayerBriefInfo(int i);
	virtual PlayerBriefInfo *findPlayerInfoByUin(int i) {return NULL;}

	void setTeamScore(int teamid, int s);
	int getTeamScore(int teamid);
	void setTeamResults(int teamid, int r);
	virtual int getTeamResults(int teamid);
	virtual int getNumTeam();
	virtual ClientPlayer *getRandomPlayer(int teamid=-1, int alive=-1); //<0: 不限制，  teamid>=0:需要等于这个id,  alive=0:死亡， alive=1:活的
	virtual int getNumPlayers(int teamid=-1, int alive=-1);
	virtual void setPlayerVisibleDispayName(bool b);
	void setPlayersResults(int teamid, int r);

	int requireArrayOfPlayers(int teamid=-1, int alive=-1);
	ClientPlayer *getIthPlayerInArray(int i);

	void GetSocAirTime(int uin);

	virtual ClientPlayer *getPlayerByUin(int uin);
	virtual bool changePlayerTeam(int uin, int teamid,bool bResetAttr = true);
	virtual bool RentChangePlayerTeam(int uin, int teamid);
	//soc 目前都是客户端上报的队伍,没有服务器校验的可能
	virtual bool SocRentChangePlayerTeam(const std::vector<int> uins, int teamid);
	virtual void hostStartGame()
	{

	}
	void resetGameRuleData();
	float getRuleOptionVal(int ruleid);
	int getPersonalRentLeftSeconds();
	bool isLockViewMode();
	void showOperateUI(bool state);
	virtual void HideTouchControlUi(bool bHide);

	virtual bool isHost(int uin) { return true; }
	virtual int getHostUin();
	void InviteJoinRoom(int uin, char* RoomState, char* PassWorld);
	void world2RadarPos(int &x, int &z);
	int world2RadarDist(int d);
	void setCameraDepth(float n, float f);
	bool getHaveJudge() { return m_bHaveJudge;}
	virtual int getJudgeUin() { return 0;};
	void initNewShortcut(ClientPlayer *player);

	void transferToTargetMap(WCoord& pos, int mapid, bool isRocketTypeTeleport = false);
	virtual int getGameLeaderUin();
	int requireArrayOfAllPlayers();
	virtual void initRoleInfo();//zuoyou, 星工场试玩时切换到编辑模式需要重新设置背包
	//找距离某点最近的玩家(方块坐标单位)
	int findNearestPlayerByPos(float posx, float posy, float posz, int worldmapid);
	//tolua_end
	virtual void checkGameLeader(int playerUin, ROOM_ACTION roomAction);
	void clearScript();
	bool GetCoreUiIsHideUiCursor();
	virtual void OnLoaded() override;
	virtual int updateLoad() override;  //0: 继续load,  1: load完成，  -1网络错误
	virtual bool getEnableTeamsInfo(std::vector<TeamInfo>& teams);
	virtual unsigned long long getGameTick() const {
		return m_TickCount;
	}
	virtual bool IsSurviveGame() override { return true; }

	// 加载并放置蓝图
	virtual bool loadAndPlaceBluePrint(const std::string& blueprintName, const WCoord& position) override;

protected:
	void updateMinimap();
	void updateRadarmap();
	//virtual void setupPlayerAndVM();

	void roleInit(WORLD_ID worldId, ClientPlayer *player);
	virtual MNSandbox::AutoRef<PlayerControl> createPlayerControl();
	virtual void applayGameSetData(bool viewchange = false);
	virtual void applyScreenBright();

	void getPlayers(std::vector<ClientPlayer *>&players, int teamid, int alive);

private:
	void suviveGameUnload(GAME_RELOAD_TYPE reloadtype);

public:
	//tolua_begin

	std::map<int, EffectParticle *> m_Effects;

	bool m_OpenCmd;

	//tolua_end
protected:

	std::vector<ClientPlayer *>m_TmpPlayers; //为脚本临时取玩家的数据暂存
	bool m_bHaveJudge;
	long long m_updateLoadDelayFlag = 0; // 加载等待标记
	unsigned long long m_TickCount;  // 每执行一次tick时加1

	std::string m_serverRoomId;
	int64_t m_BeginGameTs;

}; //tolua_exports

#endif
