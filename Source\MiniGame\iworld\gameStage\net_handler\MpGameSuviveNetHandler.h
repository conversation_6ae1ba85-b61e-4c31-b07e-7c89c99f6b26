
#ifndef __MP_GAME_SUVIVE_NET_HANDLER_H__
#define __MP_GAME_SUVIVE_NET_HANDLER_H__

#include "GameSuviveNetHandler.h"
#include <functional>
#include "proto_common.h"
#include "proto_define.h"
#include "RoomClientHandler.h"
#include "EventHandleEx.h"
//#include "SandboxEngine.h"


class ChunkIOCmd;
class ClientActor;
class SurviveGame;
class WorldManager;
class PlayerControl;
class ClientPlayer;
class GameEvent;




class MpGameSurviveNetHandler:public GameSuviveNetHandler
{
private:
#define REGIS_CLIENT_HANDLER(MSGCODE, FUNCNAME) registerClientHandler(MSGCODE, std::bind(&MpGameSurviveNetHandler::FUNCNAME, this, std::placeholders::_1));
#define REGIS_HOST_HANDLER(MSGCODE, FUNCNAME) registerHostHandler(MSGCODE, std::bind(&MpGameSurviveNetHandler::FUNCNAME, this, std::placeholders::_1, std::placeholders::_2));
public:
	MpGameSurviveNetHandler();
	virtual ~MpGameSurviveNetHandler();
public:
	virtual void load();
	virtual void registerNetHandler();
	virtual void registerHostNetHandler();
	virtual void registerClientNetHandler();

	//客机请求进入房间
	virtual void sendMsgClientEnterHostWorld();
	//心跳
	virtual void keepGame();
	//读取区块缓存
	virtual void loadCacheChunkData(const ChunkIOCmd &cmd);
	virtual void saveCacheChunkData(const ChunkIOCmd &cmd);
	virtual void tick();
	virtual void onPlayerLeave(int uin);
	virtual void kickoff(int uin);
	virtual void applyPermits(int uin);
	virtual void replyApplyPermits(int uin, int ret);
	//virtual int handleSyncMsg(void* obj) {}
	
	//void sendError2Client(int uin, int errorCode);
	//ClientPlayer *uin2Player(int uin);
	ClientPlayer *checkPlayerByMsg2Host(int uin, bool ignoreDead = false);
	ClientPlayer *checkDownedPlayerByMsg2Host(int uin);
	void onPlayerTeleport(ClientPlayer *player, int targetmap, const WCoord targetpos);
	void terminateMPGame(int errcode = 1);
	void clientLogin(long long objId, const PB_OWGlobal &worldGlobal, const PB_RoleData &roleData);

	virtual void sendChat(const char *content, int type = 0, int targetuin = 0, int language = 1, const char* extend = "");

	void RoleEnterWorld2HostLast(int uin, ClientPlayer *player);
	ClientActor* getActorAndInitData(long long objId, long long objIdRoot);
private:
	std::string m_lifeToken;
public:
	void sendChatCH(const char* content, int type = 0, int targetuin = 0, int language = 1, const char* extend = "", const std::string& tmpTranslate = "");
	void afterhandleChat2Host(const int fromUin, const int targetUin, const std::string& contentStr, int language, const std::string& translate, const std::string& extend);
	void afterhandleNpcChat2Host(const string& npc, const int fromUin, const std::string& contentStr, const std::string& reply, int language, const std::string& translate, const std::string& extend);
	virtual void handleErrorMsg2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncChunkData2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleBlockUpdate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRoleEnterWorld2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorEnterAoi2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorLeaveAoi2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorMove2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorMoveV22Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorMoveV32Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorModelChgClient(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleFullrotActorMove2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleTrainMove2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncSaveWorld(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorRevive2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorTeleport2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorMotion2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleMechaMotion2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorMotionV22Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerSkinning2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleActorAttrChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorBuffChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerAttrChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleGameLeaderSwitch2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleGeneralEnterAoi2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleBackPackGridUpdate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleBackPackGridEquipWeapon2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorEquipItem2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleOpenContainer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleNeedContainerPassword2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCloseContainer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCloseDialogue2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleUpdateTask2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleUpdateContainer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleEnchantSuccess2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRuneOperateSuccess2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRepairSuccess2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleOpenHomeNpc2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleUpdatePotContainer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleAchievementUpdate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleAchievementInit2Client(const PB_PACKDATA_CLIENT &pkg);


	virtual void handleActorAnim2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorStopAnim2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleChat2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleWGlobalUpdate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerInfoUpdate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerLeave2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncTeamScore2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSetTeamID2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSetPlayerGameInfo2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCGameStage2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerPermits2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleGameTips2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayEffect2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayEffect2Client_V2(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleEffectScale2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handlePlayWeaponEffectClient(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayWeaponEffectHost(int uin, const PB_PACKDATA& pkg);

	virtual void handleScriptVars2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleAccountHorse2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleYMVoice2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSkillCD2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleHorseSkillCD2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVaCantBossStateClient(const PB_PACKDATA_CLIENT&pkg);


	virtual void handleBlockInteract2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleBlockPunch2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleBlockExploit2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleItemUse2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSetHook2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleItemSkillUse2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSpecialItemUse2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleLeaveRoomInfo2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorInteract2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRClickUpInteract2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handlePlayerWakeUp2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handlePlayerMount2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual	void handleActorMount2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual	void handleActorReverseClient(const PB_PACKDATA_CLIENT &pkg);
	virtual	void handleActorBindClient(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerSleep2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleMobBodyChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleOpenWindow2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleReplyApplyPermits2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleGunDoReload2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncGridUserData2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncTriggerBlock2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncTotemPoint2Client(const PB_PACKDATA_CLIENT &pkg);


	virtual void handleHeartBeat2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleInviteJoinRoomClient(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleYMChangeRoleClient(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleSpectatorMode2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSpectatorType2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSetSpetatorPlayer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSetPlayerModelAni2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSetMyViewModeToSpectator2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSetBobblingToSpectator2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleBallOperate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleBasketBallOperator2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePushSnowBallOperate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleResetRound2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRocketAttrChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleWorldTimes2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleStatistic2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleHorsreFlyState2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleOpenDialogue2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncTaskEnterWorld2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCompleteTask2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleAttractAttrChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorBodyTexture2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerAddAvartar2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerChangeModel2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerTransformSkinModel2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerAvartarColor2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayAct2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlaySkinAct2Client(const PB_PACKDATA_CLIENT &pkg) ;
	virtual void handlePlayerSkin2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleStopSkinAct2Client(const PB_PACKDATA_CLIENT &pkg) ; //20210915 codeby:chenwei 新增停止装扮互动客机事件监听

	virtual void handleMeasureDistance2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleBluePrintPreBlock2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleGravityOperate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerBodyColor2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCustomModel2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCustomModelPre2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCustomModelPre2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleActorInvite2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCustomItemIDs2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerSpawnPoint2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCustomModelClass2Client(const PB_PACKDATA_CLIENT &pkg);
	// 传送点更新 h->c 
	virtual void handleTransfer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleTransferAddDel2Client(const PB_PACKDATA_CLIENT &pkg);
	// 传送点更新 h->c 
	virtual void handleTransferStatus2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleTransferData2Client(const PB_PACKDATA_CLIENT &pkg);
	// 传送块 主机通知客机打开传送ui（ActorCollided不支持客机判断）
	virtual void handleTransferUI2Client(const PB_PACKDATA_CLIENT &pkg);
	// 物理机械预览 h->c
	virtual void handleVehiclePreBlock2Client(const PB_PACKDATA_CLIENT &pkg);
	// 物理机械同步itemid
	virtual void handleVehicleAllItemid2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVehicleOneItemid2Client(const PB_PACKDATA_CLIENT &pkg);
	//npc shop h->c
	virtual void handleNpcShopRespGetInfo2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleNpcShopNotifyBuySku2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleOpenEditActorModel2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCloseEditActorModel2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleCustomActorModelData2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleSyncLoveAmbassadorIcon2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncLoveAmbassadorIcon2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handlePlayerCameraRotate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerChangeViewMode2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerCanMove2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerCanControl2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handlePlayAltmanMusic2Client(const PB_PACKDATA_CLIENT &pkg);

	/**
	@brief 当前HP/体力由 PB_PlayerAttrChangeHC 和 PB_PlayerBriefInfo 同步
	*/
	virtual void handlePlayerSetAttr2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerFreezing2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerLevelMode2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActionAttrState2Client(const PB_PACKDATA_CLIENT &pkg);

	//制作队列
	virtual void handleCraftingQueueUpdate2Client(const PB_PACKDATA_CLIENT& pkg);

	//载具同步
	virtual void handleVehicleMove2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVehicleAttribChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVehicleAttribChange2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleWorkshopItemInfo2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleWorkshopBuild2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVehicleAssembleBlockUpdate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVehicleAssembleBlockAll2Client(const PB_PACKDATA_CLIENT &pkg);
	//virtual void handleVehicleAttrChange2Client(const PB_PACKDATA_CLIENT &pkg);
	//同步一个计时器数据到客机
	virtual void handleTriggerTimer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleGameRule2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerAttrScale2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleTriggerMusic2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleTriggerOpenStore2Client(const PB_PACKDATA_CLIENT &pkg);
	// 通知客机自动寻路
	virtual void handlePlayerNavigate2Client(const PB_PACKDATA_CLIENT &pkg);
	// 通知客机player转向
	virtual void handlePlayerFaceYaw2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleOpenEditFullyCustomModel2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCloseEditFullyCustomModel2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRespDownLoadRes2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePreOpenEditFCMUI2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerJumpOnce2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVehicleAssembleLine2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVehicleAssembleLineOperate2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVehicleBindActor2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCloudServerPlayerPermit2Client(const PB_PACKDATA_CLIENT &pkg);
	// 下发 超管、管理员名单和对应操作权限
	virtual void handleCloudServerAuthority2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSSTask2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleUsePackingFCMItem2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCreatePackingCM2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePackingFCMData2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handlePlayerCloudRoomStatusTime2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCarryActor2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVillagerBodyChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleTameActor2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleVillagerCloth2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorHeadDisplayIcon2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleActorPlayAnimById2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleSyncMove2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleUIDisplayHorse2Client(const PB_PACKDATA_CLIENT& pkg);
	
	virtual void handleSyncChunkData2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleRoleEnterWorld2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleRoleLeaveWorld2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleRoleMove2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleActorRevive2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleActorGetAccountItem2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleRoleGotoPos2Host(int uin, const PB_PACKDATA &pkg);
	//player try to do
	virtual void handleBlockInteract2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleAttackBlock2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBlockPunch2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBlockExploit2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleItemUse2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSetHook2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleItemSkillUse2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerSpecialSkill2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handlePlayerAltarLuckyDraw2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSpecialItemUse2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleActorInteract2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleActorStopAnim2Host(int uin, const PB_PACKDATA & pkg);
	virtual void handleActorAnim2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerSkinning2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleRClickUpInteract2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handlePCMouseEvent2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleCraftQueueAddTask2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleCraftQueueRemoveTask2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleCraftQueueSwapTask2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handlePlayerWakeUp2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleAccountHorse2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleBackPackGridSwap2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBackPackGridDiscard2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBackPackGridEquipWeapon2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleBackPackSort2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleStorageBoxSort2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBackPackSetItem2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBackPackSetItemWithoutLimit2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBackPackMoveItem2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBackPackRemoveItem2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleGetAccountItems2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleCraftItem2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleEnchantItem2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleEnchantItemRandom2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleRuneOperate2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleRepairItem2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleLootItem2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleStoreItem2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleCloseContainer2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleNeedContainerPassword2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleCloseDialogue2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleAnswerTask2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleCompleteTask2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleChat2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleActorInvite2Host(int uin, const PB_PACKDATA &pkg);//20210914装扮互动 cody-by: wangyu
	virtual void handleYMVoice2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handlePlayerMount2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerMoveInput2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerSleep2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleNpcTrade2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleApplyPermits2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleHeartBeat2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleClientCommonInfo2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleGunSpread2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSyncSetInfo2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleGunDoReload2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSyncGridUserData2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleTrainMove2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleYMChangeRole2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleSpectatorMode2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSpectatorType2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSetSpetatorPlayer2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleActorTeleport2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSetPlayerModelAni2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSetMyViewModeToSpectator2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSetBobblingToSpectator2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBallOperate2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBasketBallOperator2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handlePushSnowBallOperate2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleRocketTeleport2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayAct2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlaySkinAct2Host(int uin, const PB_PACKDATA &pkg) ;
	virtual void handleStopSkinAct2Host(int uin, const PB_PACKDATA &pkg) ; //20211008 codeby:chenwei 停止互动动画

	virtual void handlePlayerSkin2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleCreateBlueprint2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleBluePrintPreBlock2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleGravityOperate2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleMakeCustomModel2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSelectMobSpawnBlock2Host(int uin, const PB_PACKDATA &pkg);
	// 传送点更新 c->h
	virtual void handleTransfer2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleTransferStatus2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleTransferTarget2Host(int uin, const PB_PACKDATA &pkg);

	//npc shop c->h
	virtual void handleNpcShopGetInfo2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleNpcShopBuySku2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleCloseEditActorModel2Host(int uin, const PB_PACKDATA &pkg);

	//pack gift c->h
	virtual void handlePackGiftItemChg2Host(int uin, const PB_PACKDATA &pkg);

	// 物理机械预览
	virtual void handleVehiclePreBlock2Host(int uin, const PB_PACKDATA &pkg);
	//物理机械对地使用
	virtual void handleVehicleItemUse2Host(int uin, const PB_PACKDATA &pkg);
	// 物理机械载入
	virtual void handleVehicleStartBlock2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleWorkshopItemInfo2Host(int uin, const PB_PACKDATA &pkg);
	// 主机收到客机物理机械操作数据
	virtual void handlePlayerVehicleMoveInput2Host(int uin, const PB_PACKDATA &pkg);
	// 客机收到主机物理机械操作数据
	virtual void handlePlayerVehicleMoveInput2Client(const PB_PACKDATA_CLIENT &pkg);
	// 物理机械重置
	virtual void handlePlayerResetVehicle2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handlePlayerMotionStateChange2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerClick2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerSelectShortcut2Host(int uin, const PB_PACKDATA &pkg);
	// 玩家属性变化 触发器
	virtual void handleTriggerPlayerAttri2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerAttrScale2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayTriggerSound2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerJumpOnce2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleReqDownloadResUrl2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleCloseFullyCustomModelUI2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handlePlayerNavFinished2Host(int uin, const PB_PACKDATA &pkg);
	// 物理机械连线
	virtual void handleVehicleAssembleLine2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleVehicleAssembleLineOperate2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleUpdateActionerData2Host(int uin, const PB_PACKDATA &pkg);
	// 云服 玩家权限
	virtual void handleCloudServerPlayerPermit2Host(int uin, const PB_PACKDATA &pkg);
	// 云服 操作权限
	virtual void handleCloudServerAuthority2Host(int uin, const PB_PACKDATA &pkg);
	// 开发者
	virtual void handleSSTask2Host(int uin, const PB_PACKDATA &pkg);
	// 物理机械车间连线
	virtual void handleVehicleWorkshopLine2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleCloudServerChangePlayerTeam2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleCloudServerChangeState2Client(const PB_PACKDATA_CLIENT &pkg);
	// 云服 自动禁言功能
	virtual void handleCloudServerAutoMute2Host(int uin, const PB_PACKDATA &pkg);
	// 给主机发消息计算一下车间的连通然后同步过去
	virtual void handleWorkshopLineUpdate2Host(int uin, const PB_PACKDATA &pkg);
	void handleMapEditHandle2Host(int uin, const PB_PACKDATA &pkg);
	void handleMapEditRevoke2Host(int uin, const PB_PACKDATA &pkg);

	void handleCloudRoomOnwerStartGame2Host(int uin, const PB_PACKDATA &pkg);
	void handleCloudRoomKickOff2Host(int uin, const PB_PACKDATA &pkg);

	void handleUsePackingFCMItem2Host(int uin, const PB_PACKDATA &pkg);
	void handleCreatePackingCM2Host(int uin, const PB_PACKDATA &pkg);

	void handlePlayerInputKeys2Host(int uin, const PB_PACKDATA &pkg);
	void handlePlayerInputContent2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleSensorContainerData2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleSensorContainerData2Client(const PB_PACKDATA_CLIENT &pkg);

	//村庄图腾交互
	virtual void handleVillageTotemTip2Client(const PB_PACKDATA_CLIENT &pkg);
	//村庄图腾激活
	virtual void handleVillageTotemActive2Client(const PB_PACKDATA_CLIENT &pkg);
	//外部导入模型
	virtual void handleImportModel2Client(const PB_PACKDATA_CLIENT &pkg);
	//玩家存档数据
	virtual void handlePlayerArch2Client(const PB_PACKDATA_CLIENT &pkg);

	//更新门刚体
	virtual void handleDoorData2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleCarryActor2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleVillagerModifyName2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleSaveTombStone2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleDeformationSkin2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleResetDeformation2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleRestoreDefomation2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleLightning2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleInteractMobPack2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleMoveMobBackpack2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleUpdateMobBackpack2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleMobInteractItem2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleRolePushArch2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleChangeGraphics2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleSFActivityClient(const PB_PACKDATA_CLIENT &pkg);//春节活动
	virtual void handleOpenDevGoodsBuyDialogClient(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleGodTempleCreate2Client(const PB_PACKDATA_CLIENT &pkg);
	//家园祈福
	virtual void handlePrayInfoClient(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePrayTreeStageClient(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePrayTimeClient(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePrayReqClient(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePrayTimeHost(int uin, const PB_PACKDATA &pkg);

	virtual void handleOpenHomeCloset2Client(const PB_PACKDATA_CLIENT &pkg);
	/*
		同步养殖场信息给客机
	*/
	virtual void handleHomelandRanchInfo2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleHomelandRanchFooderStateClient(const PB_PACKDATA_CLIENT& pkg);
	/*
		通知主机刷新养殖场生物信息状态
	*/
	virtual void handleHomelandRanchAnimalState2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleHomelandRanchFooderInfo2Host(int uin, const PB_PACKDATA &pkg);
	//召唤宠物
	virtual void handleSummonPet2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleShapeAdditionAnim2Client(const PB_PACKDATA_CLIENT &pkg);

	//在家园里使用道具
	virtual void handleUseItemByHomeLand2Client(const PB_PACKDATA_CLIENT &pkg);
	//基础设置面板设置Avatar
	virtual void handleCustomBaseModel2Client(const PB_PACKDATA_CLIENT &pkg);
	//改变Actor模型
	virtual void handleChangeActorModel2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRequestAvtarModel2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleNotifiyAvtarModel2Client(const PB_PACKDATA_CLIENT &pkg);

	//举报语音
	virtual void handleVoiceInform2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleVoiceInform2Client(const PB_PACKDATA_CLIENT &pkg);

	//设置熔炉温度
	virtual void handleFurnaceTemperature2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handlePotStartMake2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleTakePotItem2Host(int uin, const PB_PACKDATA& pkg);

	//复活点相关
	virtual void handlePlayerRevivePoint2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handlePlayerRevivePoint2Client(const PB_PACKDATA_CLIENT &pkg);

	//星站相关
	virtual void handleNotifyStarStationAdded2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleNotifyStarStationRemoved2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleStarStationChangeNameStatus2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleNotifyStarStationChangeNameStatus2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleNotifyEnterStarStationCabin2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleLeaveStarStationCabin2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleNotifyLeaveStarStationCabin2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleNotifyUpgradeStarStationCabin2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleUpdateStarStationCabinStatus2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleNotifyUpdateStarStationCabinStatus2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleUpdateStarStationStatusEnd2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleNotifyUpdateStarStationStatusEnd2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleAddStarStationTransferDesc2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleNotifyUpdateStarStationCabinAdded2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleNotifyUpdateStarStationCabinRemoved2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleAddUnfinishedTransferRecord2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleNotifyAddUnfinishedTransferRecord2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleNotifyUpdateUnfinishedTransferRecordStatus2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleRemoveUnfinishedTransferRecord2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleNotifyRemoveUnfinishedTransferRecord2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleStarStationData2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleStarStationTransferTarget2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleNotifyStarStationTransfer2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleNotifyPlayerTransfer2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleNotifyModBlockChangeColor2Client(const PB_PACKDATA_CLIENT& pkg);
	
	virtual void handleNotifyActivateStarStation2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleNotifyUpdateStarStationSignInfo2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleStarStationRequest2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleNotifyStarStationResult2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleGainItemsToBackPack2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleGainItemsUserDataStrToBackPack2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleUseMusicYuPu2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleDanceByPlaying2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleStopDanceByPlaying2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleStartPlayingPiano2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleStopPlayingPiano2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleShowTopBrand2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleNotifyUpdateToolModelTexture2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleNotifyUpdateToolModelTexture2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handleUpdateAchievement2Host(int uin, const PB_PACKDATA &pkg);

	virtual void handleCoustomUi2Host(int uin, const PB_PACKDATA &pkg);

	// 客机通知主机添加/扣除星星币
	virtual void handleNewAdNpcAddExp2Host(int uin, const PB_PACKDATA &pkg);
	// 主机通知客机添加/星星币扣除成功
	virtual void handleNewAdNpcAddExpResult2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRoomExtra2Client(const PB_PACKDATA_CLIENT &pkg);
	// 20210824:客机通知主机交换道具 codeby：wangyu
	virtual void handleExchangeItemsToBackPack2Host(int uin, const PB_PACKDATA& pkg) ;
	// // 20210824:主机通知客机交换道具结果 codeby：wangyu
	virtual void handleExchangeItemsToBackPackResult2Client(const PB_PACKDATA_CLIENT &pkg) ;

	virtual void handleBattlePassEvent2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleHorseFlag2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handldUseHearth2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleCustomMsg2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleCustomMsg2Host(int uin, const PB_PACKDATA &pkg);

	virtual void onClientConnectSuccess();
	virtual void onClientConnectFailed(int errcode);
	virtual void onClientAuthenFailed();
	virtual void onClientAddPartner(int uin);
	virtual void onClientConnectionLost();

	virtual void onHostAddPartner(int uin);
	virtual void onHostConnectFailed();
	virtual void onHostConnectionLost(int clientUin);

	

	//virtual int getCurOpenContanierIndex();
	//virtual void summonAccountHorse(int uin, int horseid, bool isshapeshift);

	
	//virtual int getMaxPlayerNum();

	//virtual void onGameEvent(GameEvent *ge);
	//virtual int getJudgeUin();
	//********:修改菜谱购买主客机同步。codeby hyy
	virtual void handleHomeLandMenuBuySuccess2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleHomeLandMenuBuySuccess2Host(int uin, const PB_PACKDATA &pkg);
	virtual void handleHomeLandShopCell2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleHomeLandShopCell2Host(int uin, const PB_PACKDATA &pkg);

	/*
		********:特惠家具购买同步刷新状态 codeby：yangzhenyu
	*/
	virtual void handleHomeLandSpecialFurnitureBuySuccess2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleHomeLandSpecialFurnitureBuySuccess2Host(int uin, const PB_PACKDATA &pkg);


	//916冒险 2021/08/18 codeby:wudeshen
	virtual void handleAnswerLanternBird2Host(int uin, const PB_PACKDATA &pkg) ;
	virtual void handleSetTiangou2Client(const PB_PACKDATA_CLIENT &pkg) ;
	virtual void handlePlayerOpenUI2Client(const PB_PACKDATA_CLIENT &pkg) ;
	virtual void handlePlayerCloseUI2Host(int uin, const PB_PACKDATA &pkg) ;

	// 20210910：坐骑隐身技能  codeby： keguanqiang
	virtual void handleRideInvisible2Client(const PB_PACKDATA_CLIENT &pkg) ;

	/*
		20210823:QQ音乐播放器 codeby：邹龙进
	*/
	virtual void handleChangeQQMusicPlayer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleChangeQQMusicPlayer2Host(int uin, const PB_PACKDATA &pkg);



	//20210926:MiniCLub音乐 codeby：wangshuai
	virtual void handleMiniClubPlayer2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleMiniClubPlayer2Host(int uin, const PB_PACKDATA &pkg);

	//20211020 喷漆 codeby:柯冠强 begin
	virtual void handleAddPaintedInfo2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleRemovePaintedInfo2Client(const PB_PACKDATA_CLIENT &pkg);
	//20211020 喷漆 codeby:柯冠强 end

	//20211020 喷漆 codeby:柯冠强
	virtual void handleSprayPaintInfo2Host(int uin, const PB_PACKDATA& pkg) ;
	//20211101 手持物品 codeby:luoshuai
	void handlePlayerEquipWeapon2Client(const PB_PACKDATA_CLIENT &pkg);
	void handlePlayerEquipWeapon2Host(int uin, const PB_PACKDATA &pkg);
	// 436138 wangshuai
	virtual void handlePlayEffect2Host(int uin, const PB_PACKDATA &pkg);
	//20210915 音乐方块 codeby:huangxin
	virtual void handleChangeQQMusicClub2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleChangeQQMusicClub2Host(int uin, const PB_PACKDATA &pkg);
	// 20211027 广告商人购买 codeby:liusijia
	virtual void handleAdShopBuy2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleAdShopBuy2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleSyncPlayerPos2Client(const PB_PACKDATA_CLIENT &pkg);
	// 成就奖励获取
	virtual void handleAchievementAward2Host(int uin, const PB_PACKDATA& pkg);

	// 客户端上报actionlog 作弊等
	virtual void handleClientActionLog2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleCheatCheck2Host(int uin, const PB_PACKDATA& pkg);
	// 客户端上报检测信息
	virtual void handleCheckInfo2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleGetAdShopExtraAward2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleExtraStoreItem2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleShowTopBrand2Client(const PB_PACKDATA_CLIENT &pkg);

	//20220705同步武器熟练度信息 codeby:汪宇
	virtual void handleWeaponPoint2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual void handlePvpActivityConfig2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleExposePos2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleSetClientPlayerInfo2Host(int uin, const PB_PACKDATA& pkg);

	//20221020 闪电链 codeby shitengkai
	virtual void handleAddLightningChain2Client(const PB_PACKDATA_CLIENT &pkg);
	//20221118 钓鱼 codeby shitengkai
	virtual void handleStartFishing2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleEndFishing2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleQuitFishing2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleChangeFishingState2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleFishingBeginFlash2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handleStartFishing2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleEndFishing2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleQuitFishing2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleEndPlayFish2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleBindItemToActor2Client(const PB_PACKDATA_CLIENT &pkg);
	//20230309 黑板文字同步 codeby:huangxin
	virtual void handleChangeBlockData2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleSyncMove2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleRespSyncMove2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleActorShoot2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleActorFirework2Host(int uin, const PB_PACKDATA& pkg);

	//20220809 联机房间 地图模式改变通知（创造/冒险互转） codeby:huangrulin
	virtual void handleGameModeChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePushSnowBallSizeChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayEffectShader2Client(const PB_PACKDATA_CLIENT &pkg);

	//同步播放动画
	virtual void handlePlayAnimation2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handlePlayAnimation2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleActorAttack2Host(int uin, const PB_PACKDATA& pkg);

	// 同步生物防御状态
	virtual void handleActorDefanceState2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleResetRoleFlags2Client(const PB_PACKDATA_CLIENT &pkg);

	virtual bool handleBindPlayerToPhysicsPlat2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleBindPlayerToPhysicsPlat2Host(int uin, const PB_PACKDATA& pkg);
	virtual bool handleUnBindPlayerToPhysicsPlat2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleUnBindPlayerToPhysicsPlat2Host(int uin, const PB_PACKDATA& pkg);
	virtual bool handlePhysicsComUpdate2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual bool handlePhysicsComPlatLocPos2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual bool handleEffctComUpdate2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual bool handleSoundComUpdate2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleMeteorShower2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleNewRepairItem2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleActorPlaySound2Host(int uin, const PB_PACKDATA& pkg);
	virtual bool handleObjActorMsg2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleAddBullethole2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handlePlayerCanFire2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleActorPlayAnimByIdNew2Client(const PB_PACKDATA_CLIENT& pkg);

	// 玩家倒地状态改变 h->c
	virtual void handlePlayerDownedStateChange2Client(const PB_PACKDATA_CLIENT &pkg);
	virtual void handlePlayerDownedHealthUpdate2Client(const PB_PACKDATA_CLIENT &pkg);
	//救援 c -> h
	virtual void handlePlayerReviveRequest2host(int uin, const PB_PACKDATA& pkg);

	// 科技树
	virtual void handleOpenWorkbench2Host(int uin,  const PB_PACKDATA& pkg);
	virtual void handleOpenWorkbench2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleUnlockTechNode2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleUnlockTechNode2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handlePlayerCustom2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handlePlayerCustom2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleDecomposition2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleDecomposition2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleAllSingleBuildData2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleAllSingleBuildData2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleAirDropEvent2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleGetAirDropChest2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleGetAirDropChest2Client(const PB_PACKDATA_CLIENT& pkg);
	virtual void handleDelAirDropChest2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleResearch2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleResearch2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleTechBlueprint2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleTechBlueprint2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleBlockTrigger2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handlePlayerKillme(int uin, const PB_PACKDATA& pkg);

	virtual void handlePlayerSocMoveItem(int uin, const PB_PACKDATA& pkg);

	virtual void handleDrinkWater2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleFillWater2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleProcessBuildBlock2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleMachineSourceActive2Host(int uin, const PB_PACKDATA& pkg);
	
	virtual void handleSocWorkbench2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleSocWorkbench2Host(int uin, const PB_PACKDATA& pkg);

	virtual void handleSocTeamPositions2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleSocTeamTagData2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleSocTeamTagData2Client(const PB_PACKDATA_CLIENT& pkg);

	virtual void handleSocTeamReqTag2Host(int uin, const PB_PACKDATA& pkg);
	virtual void handleSocTeamUpData2Host(int uin, const PB_PACKDATA& pkg);

	void handleGetAirDropTime2Host(int uin, const PB_PACKDATA& pkg);
	void handleGetAirDropTime2Client(const PB_PACKDATA_CLIENT & pkg);
 
};
#endif
