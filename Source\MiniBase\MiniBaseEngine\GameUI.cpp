#include "GameUI.h"
#include "ui_common.h"
#include "ui_frame.h"
#include "ui_framemgr.h"
#include "ui_framemgreditor.h"
//#include "Common/OgreShared.h"
#include "xml_uimgr.h"
#include "OgreTimer.h"
#include "ui_texture.h"
#include "ui_cursor.h"
#include "base/CCDirector.h"
//#include "StringDefCsv.h"
#include "UIPackage.h"
#include "GameStatic.h"
#include "UIRenderer.h"
#include "Compress/CompressSystem.h"
#include "ModuleInterface/IClientInfo.h"
#if MINI_NEW_UI
#include "FairyGUI/Cocos2dx/base/CCDirector.h"
#include "UIScene.h"
#include "UI3DRenderer.h"
#endif
#include "Input/InputManager.h"
#include "UIDrawStateManager.h"
#include "Graphics/ScreenManager.h"
#include "Threads/ThreadChecks.h"
#include "TextRendering/FontAtlasCache.h"
#include "AssetPipeline/AssetManager.h"
#include "Debug/DebugMgr.h"
#include "Platforms/PlatformInterface.h"
#include "Core/Engine.h"
#include "Optick/optick.h"
#if PLATFORM_WIN
#include "ui/UIEditBox/UIEditBoxImpl-win32.h"
#endif
#include "ActorBodySafeHandle.h"

using namespace MINIW;
using namespace Rainbow;
using namespace Rainbow::UILib;
#define FAIRYGUI_W 1920.0f
#define FAIRYGUI_H 1080.0f
#if (OGRE_PLATFORM == OGRE_PLATFORM_WIN32) && defined(IWORLD_DEV_BUILD)
#define ENABLE_UILIB_EDITOR 1
#else
#define ENABLE_UILIB_EDITOR 0
#endif

#ifndef PLATFORM_MOBILE
#define PLATFORM_MOBILE (PLATFORM_ANDROID || PLATFORM_OHOS || PLATFORM_IOS)
#endif
#ifndef PLATFORM_DESKTOP
#define PLATFORM_DESKTOP (!PLATFORM_MOBILE)
#endif


Rainbow::UILib::FrameManager *g_pFrameMgr = nullptr;
static bool g_enableReloadTest = false;

typedef std::pair<std::string, std::string> pairString;

#if  EXPORT_LEGACYUI
std::string GameUI::s_luaFileData = "";//用于导出xml中的lua数据
tinyxml2::XMLDocument GameUI::s_templateXml;//用于导出xml中的模板
#endif //  EXPORT_LEGACYUI

EXPORT_LEGACYMODULE void GCMemory()
{
	LogString("------------------GCMemory----------------------");
	GetActorBodySafeHandle()->ClearUnattachedBody();

	GetUIDrawStateManager().CollectUIActiveGarbage(true);
	Rainbow::GetAssetManager().GCResouces();

	WarningString("----------------After GCMemory-----------------------");
	GetDebugMgr().PrintMemoryInfo();
}


static void OnTextureDestruct(Rainbow::Texture* tex)
{
	ASSERT_RUNNING_ON_MAIN_THREAD
	GetGameUIPtr()->UnRegistTex(tex);
}

//-----------------------------------------------------------------------------
//  客户端可调用UI的函数接口
//-----------------------------------------------------------------------------
namespace Rainbow {
	namespace UILib {
		extern void ShowUIPanel(const char* frameName);
		extern void HideUIPanel(const char* frameName);
	}
}

//导出给外部使用的全局函数，给lua使用保证贴图有效性
void RegistSafeTex(Rainbow::Texture* tex)
{
	GetGameUIPtr()->RegistTexSafeHandle(tex);
}

bool IsSafeTexVaild(Rainbow::Texture* tex)
{
	return GetGameUIPtr()->IsTexVaild(tex);
}

//const char* getstr(int id)
//{
//	return StringDefCsv::getInstance()->get(id);
//}

bool IsSafeActorBodyVaild(const Rainbow::IActorBody* actorBody)
{
	Rainbow::IActorBody* body = const_cast<Rainbow::IActorBody*>(actorBody);
	return GetGameUIPtr()->IsActorBodyVaild(body);
}

GameUI::GameUI()
{
	m_bDestoryed = false;

#ifndef IWORLD_SERVER_BUILD
	g_pFrameMgr = &Rainbow::UILib::FrameManager::GetInstance();
	m_bReceiveMsg = true;
	m_HideUI = false;
	m_pXmlmgr = ENG_NEW_LABEL(Rainbow::UILib::XMLManager,kMemUI)();
	UIRenderer::GetInstance();
#if ENABLE_UILIB_EDITOR
	FrameManagerEditor::GetInstance();
#endif
	GlobalCallbacks::Get().preFrameTick.Register<GameUI>(&GameUI::OnPreTick, this);
	GlobalCallbacks::Get().frameTick.Register<GameUI>(&GameUI::OnTick, this);
	GlobalCallbacks::Get().m_PreRenderCallbacks.Register<GameUI>(&GameUI::OnRender, this);

#endif
	GlobalCallbacks::Get().preFrameTick.Register(UIThreadTask::checkQueue);
}

GameUI::~GameUI()
{
	Destory();
}

bool GameUI::IsExportLeagcyUI()
{
#if EXPORT_LEGACYUI
	return true;
#else
	return false;
#endif
}

bool GameUI::IsUseAutoLoadLegacyUI()
{
#if LEGACYUI_AUTLOAD
	return true;
#else
	return false;
#endif
}

void GameUI::RegistTexSafeHandle(Rainbow::Texture* tex)
{
	if (!tex)return;
	m_TexSafeHandles[tex] = (UInt64)(tex->GetTextureID().m_ID);
	tex->SetTextureDestructCallback(OnTextureDestruct);
}

bool GameUI::IsTexVaild(Rainbow::Texture* tex)
{
	if (!tex) return false;
	auto iter = m_TexSafeHandles.find(tex);
	if (iter == m_TexSafeHandles.end())
		return false;
	return (iter->second == (UInt64)(tex->GetTextureID().m_ID));
}

void GameUI::UnRegistTex(Rainbow::Texture* tex)
{
	if (!tex) return;
	m_TexSafeHandles.erase(tex);
}

void GameUI::Destory()
{
	if (m_bDestoryed)return;
	m_bDestoryed = true;
	GetUIDrawStateManager().CollectUIActiveGarbage(true);
#if ENABLE_UILIB_EDITOR
	FrameManagerEditor::FreeInstance();
#endif // ENABLE_UILIB_EDITOR
#ifndef IWORLD_SERVER_BUILD
	GlobalCallbacks::Get().preFrameTick.Unregister<GameUI>(&GameUI::OnPreTick, this);
	GlobalCallbacks::Get().frameTick.Unregister<GameUI>(&GameUI::OnTick, this);
	GlobalCallbacks::Get().m_PreRenderCallbacks.Unregister<GameUI>(&GameUI::OnRender, this);

	GlobalCallbacks::Get().preFrameTick.Unregister(UIThreadTask::checkQueue);
	ENG_DELETE_LABEL(m_pXmlmgr, kMemUI);
	ENG_DELETE_LABEL(g_pFrameMgr, kMemUI);
#endif
	//	g_pUIScriptVM->setUserTypePointer("UIFrameMgr", "FrameManager", NULL);

#if MINI_NEW_UI
	cocos2d::Director::getInstance()->end();
	cocos2d::Director::getInstance()->mainLoop();
#endif

	{
		auto iter = m_TexSafeHandles.begin();
		for (; iter != m_TexSafeHandles.end(); ++iter)
		{
			//跑这里的贴图可能有泄露!
			iter->first->SetTextureDestructCallback(nullptr);
		}
		m_TexSafeHandles.clear_dealloc();
	}
	
	SAFE_HANDLE_CLEAN(GLoader);

	ReleaseActorBodySafeHandle();
}

bool GameUI::Create( const char *uires, int default_width, int default_height, int screenw, int screenh, UI_PLATFORM platform, UI_LANG_VER lang_ver)
{

	m_pXmlmgr->setPlatform(platform);
	m_pXmlmgr->setLanguage(lang_ver);

	m_DefaultUIWidth = default_width;
	m_DefaultUIHeight = default_height;
	if(uires)
	{
		GetUIRenderManager().SetDesignResolutionSize(screenw, screenh, EResolutionPolicy::SHOW_ALL);
		//g_pDisplay = UIRenderer::GetInstancePtr();
		ScriptVM* pscriptvm = ScriptVM::game();
		g_pUIScriptVM = pscriptvm;

		pscriptvm->setUserTypePointer("UIFrameMgr", "FrameManager", g_pFrameMgr);
		g_pFrameMgr->m_ObjectTable.clear();
		g_pFrameMgr->m_TemplateTable.clear();
		g_pFrameMgr->m_RootFrames.clear();
#if ENABLE_UILIB_EDITOR
		g_pFrameMgr->m_CurrXMLTemplateTable.clear();
#endif // ENABLE_UILIB_EDITOR
		g_pFrameMgr->InitRootFrames();
		bool ret = ScriptVM::game()->callFile(uires);
		g_pFrameMgr->m_TocFileName = uires;

		//bool ret = NewXMLFile(uires);
		resetScreenSize(screenw, screenh);
		return ret;
	}
	
	return false;
}

void GameUI::resetScreenSize(int width, int height)
{
	
	if (g_pFrameMgr) {
		g_pFrameMgr->setScreenParam(width, height);
		g_pFrameMgr->ForceUpdateFramePos();
	}
	if (m_OldUIEnable) {
		if (g_pUIScriptVM) {
			g_pUIScriptVM->callFunction("ResetScreenSize", "ii", width, height);
		}
	}
	
}


void GameUI::InitNewMiniUI()
{
#ifndef IWORLD_SERVER_BUILD
#if PLATFORM_WIN
	cocos2d::ui::EditBoxImplWin::lazyInit();
#endif
	const int width = GetWidth();
	const int height = GetHeight();
	const Rectf& rect = GetScreenManager().GetRect();
	WarningStringMsg("screen size=%dx%d, padding=%d. rect=(%f,%f - %f,%f)", width, height, g_pFrameMgr->GetScreenEdge(),
			rect.GetLeft(), rect.GetTop(), rect.getWidth(), rect.getHeight());
	OnWindowSizeChanged(width, height);

	auto director = cocos2d::Director::getInstance();
	auto* glview = director->getGLView();
	if (!glview)
	{
		glview = cocos2d::GLViewImpl::createWithRect("fairygui", cocos2d::Rect(0, 0, rect.GetWidth(), rect.GetHeight()));
		//glview->setViewPortInPoints(0, 0, width, height);
		//glview->setFrameSize(width, height);
		director->setOpenGLView(glview);
		// set FPS. the default value is 1.0/60 if you don't call this
		//director->setAnimationInterval(1.0f / 60);
		// Set the design resolution
		glview->setRawDesignResolutionSize(cocos2d::Size(FAIRYGUI_W, FAIRYGUI_H));
		if (rect.GetWidth() / FAIRYGUI_W > rect.GetHeight() / FAIRYGUI_H)
		{
			glview->setDesignResolutionSize(FAIRYGUI_W, FAIRYGUI_H, ResolutionPolicy::FIXED_HEIGHT);
		}
		else
		{
			glview->setDesignResolutionSize(FAIRYGUI_W, FAIRYGUI_H, ResolutionPolicy::FIXED_WIDTH);
		}
		director->setClearColor(cocos2d::Color4F(cocos2d::Color4B(0x36, 0x3B, 0x44, 0xFF)));
		
		//showing how to regsiter a ttf font
		//UIConfig::registerFont(UIConfig::defaultFont, Rainbow::GetRainbowDefaultFont());
	}

	g_pFrameMgr->setScreenParam(width, height);
#endif
}

bool GameUI::IsActorBodyVaild(Rainbow::IActorBody* actor)
{
	return GetActorBodySafeHandle()->IsVaild(actor);
}

void GameUI::OnPreTick()
{
	OPTICK_EVENT();
	g_pFrameMgr->PreTick();
	
}


void GameUI::OnTick( float deltatime )
{
	OPTICK_EVENT();
	#if ENABLE_UILIB_EDITOR
			if (g_pFrameMgr->getFuncF4Enable())
			{
				FrameManagerEditor::GetInstance().Update(deltatime);
				return;
			}
	#endif
			g_pFrameMgr->Tick(deltatime);
#if MINI_NEW_UI && CC_DRAW_LEVEL
	cocos2d::Director::getInstance()->updateSceneDeltaTime(deltatime);
#endif

#if 0
	m_uiPackageClearTime += deltatime;
	if (m_uiPackageClearTime >= m_uiPackageClearInterval)
	{
		UIPackage::removeUnreferencedPackages();
		m_uiPackageClearTime = 0.f;
	}
#endif
}

void GameUI::OnRender()
{
	OPTICK_EVENT();
	static int frameCount = 0;
	static float lastTime = GetTimeSinceStartup();

	++frameCount;
	float currentTime = GetTimeSinceStartup();
	float dt = currentTime - lastTime;
	if(dt >= 1.0f)
	{
		framePerSecond = frameCount / dt;
		lastTime = currentTime;
		frameCount = 0;
	}

	UIRenderer::GetInstance().doRender();
}

void GameUI::Render()
{
	OPTICK_EVENT();
	g_pFrameMgr->Render();

#if ENABLE_UILIB_EDITOR
	if(g_pFrameMgr->getFuncF4Enable())
		FrameManagerEditor::GetInstance().Render();
#endif
}

void FindTextureFileName(const Frame* const pFrame, std::vector<pairString>& fileList)
{
	if (pFrame->m_bShow)
	{
		for (size_t i = 0; i < pFrame->m_DrawRegions.size(); ++i)
		{
			LayoutFrame* pLayout = pFrame->m_DrawRegions[i].pregion;
			if (pFrame->m_DrawRegions[i].pregion->m_bShow && strcmp(pFrame->m_DrawRegions[i].pregion->GetTypeName(), "UITexture") == 0)
			{
				::UITexture* pTexture = static_cast< ::UITexture *>(pFrame->m_DrawRegions[i].pregion);
				pairString pair;
				if (pTexture->m_UVName != NULL && pTexture->m_FilePath != NULL) {
					pair.first = pTexture->m_UVName;
					pair.second = pTexture->m_FilePath;
					std::vector<pairString>::iterator it = std::find(fileList.begin(), fileList.end(), pair);
					if (it == fileList.end())
						fileList.push_back(pair);
				}
			}

		}

		for (size_t i = 0; i < pFrame->m_Children.size(); ++i)
		{
			Frame* frame = pFrame->m_Children[i];
			if (frame->m_bShow)
			{
				for (size_t j = 0; j < frame->m_DrawRegions.size(); ++j)
				{
					if (frame->m_DrawRegions[j].pregion->m_bShow && strcmp(frame->m_DrawRegions[j].pregion->GetTypeName(), "UITexture") == 0)
					{
						::UITexture* pTexture = static_cast< ::UITexture *>(frame->m_DrawRegions[j].pregion);
						pairString pair2;
						if (pTexture->m_UVName != NULL&&pTexture->m_FilePath != NULL) {
							pair2.first = pTexture->m_UVName;
							pair2.second = pTexture->m_FilePath;
							std::vector<pairString>::iterator it = std::find(fileList.begin(), fileList.end(), pair2);
							if (it == fileList.end())
								fileList.push_back(pair2);
						}

					}
				}
				FindTextureFileName(frame, fileList);
			}
		}

	}
}


Rainbow::UILib::XMLManager& GameUI::GetXMLManager()
{
	assert(m_pXmlmgr);
	return *m_pXmlmgr;
}

void GameUI::saveUsedTextureInGame() const
{
	OPTICK_EVENT();
	static std::vector<pairString>  m_usedTextureInGame;
	m_usedTextureInGame.clear();
	for (size_t i = 0; i < g_pFrameMgr->m_RootFrames.size(); ++i)
	{
		FindTextureFileName(g_pFrameMgr->m_RootFrames[i], m_usedTextureInGame);
	}
	tinyxml2::XMLDocument doc;

	tinyxml2::XMLDeclaration* decl = doc.NewDeclaration();
	doc.LinkEndChild(decl);

	for (size_t j = 0; j < m_usedTextureInGame.size(); ++j)
	{
		tinyxml2::XMLElement* texture = doc.NewElement("Texture");
		doc.LinkEndChild(texture);
		texture->SetAttribute("Name", m_usedTextureInGame[j].first.c_str());
		texture->SetAttribute("Path", m_usedTextureInGame[j].second.c_str());
	}
	doc.SaveFile("filelist.xml");
}

bool GameUI::isGameUITouched(const float& x, const float& y , bool Include3DUI) const
{
	GObject* fairyObject = UIScene::pickObject(x, y);
	if (fairyObject)
		return true;

	Frame* frame = FrameManager::GetInstance().FindUIObjectOnPoint(x, y);
	if (frame)
		return true;
	if (Include3DUI && UI3DRenderer::getInstance()->getUI3DScene()&& UI3DRenderer::getInstance()->getUI3DRoot())
	{
		GObject* obj =UI3DRenderer::getInstance()->raycast3DUI(Vector2f(x, y),true);
		if (obj)
		{
			return true;
		}
	}
	return false;
}

int GameUI::hitPoint(const float& x, const float& y) const
{
	GObject* fairyObject = UIScene::pickObject(x, y);
	Frame* frame = FrameManager::GetInstance().FindUIObjectOnPoint(x, y);
	if (fairyObject)
	{
		if(frame)
		{
			// 新、老UI叠在一起，靠层级关系决定点击到了谁。
			if(fairyObject->GetFrameStrata() > frame->GetFrameStrata())
				return INPUTMSG_HANDLED_NEWUI;
			else if(fairyObject->GetFrameStrata() < frame->GetFrameStrata())
				return INPUTMSG_HANDLED;
			else  // fairyObject->GetFrameStrata() == frame->GetFrameStrata()
			{
				if(fairyObject->GetDrawLevel() > frame->GetDrawLevel())
					return INPUTMSG_HANDLED_NEWUI;
				else if(fairyObject->GetDrawLevel() < frame->GetDrawLevel())
					return INPUTMSG_HANDLED;
				else  // fairyObject->GetDrawLevel() == frame->GetDrawLevel()
				{
#if 0
					if(GetDebugMgr().LuaShowError())  // 偷懒写法，与 Lua 共用了一个开关
					{
						std::string newUIName = fairyObject->getName();

						const char* oldUIName = frame->GetName();
						const char* title = "UI level error";
						std::ostringstream oss;
						oss << "FairyGUI '" << newUIName << "', id='" << fairyObject->id << "' and MiniUI '" << oldUIName
							<< "' have the same level (FrameStrata=" << fairyObject->GetFrameStrata()
							<< ", drawLevel=" << std::hex << fairyObject->GetDrawLevel() <<
							"), did you forget to set level value for FairyGUI widget?";
						std::string message = oss.str();
						MINIW::PopMessageBox(message.c_str(), title);
					}
					assert(false);  // 新、老UI有相同层级，需要调整资源让一个胜出。
#endif
					return INPUTMSG_PASS;
				}
			}
		}
		else
			return INPUTMSG_HANDLED_NEWUI;
	}
	else
	{
		if(frame)
			return INPUTMSG_HANDLED;
		else
		{
			if (UI3DRenderer::getInstance()->raycast3DUI(Vector2f(x, y),true))
			{//场景内的fairygui 3DUI
				return INPUTMSG_HANDLED_NEWUI;
			}
			return INPUTMSG_PASS;  // 新老UI都没有点到，大概率是点在游戏场景里。
		}
			
	}
}

static bool isMouseEvent(const Rainbow::InputEvent& event)
{
	using Rainbow::InputEvent;
	return event.type == InputEvent::kMouseDown ||
			event.type == InputEvent::kMouseUp ||
			event.type == InputEvent::kMouseMove ||
			event.type == InputEvent::kMouseDrag ||
			event.type == InputEvent::kScrollWheel;
}

int GameUI::OnInputEvent(const Rainbow::InputEvent& event)
{
	using Rainbow::InputEvent;
	const Vector2f& position = event.mousePosition;

#if ENABLE_UILIB_EDITOR
	{
		int eventHandled = FrameManagerEditor::GetInstance().OnInputMessage(event);
		if(eventHandled != INPUTMSG_PASS)
			return eventHandled;
	}
#endif // ENABLE_UILIB_EDITOR

	// 调试相关的快捷键代码丢这里
	if (event.type == InputEvent::kKeyDown)
	{
		if (event.keycode == SDLK_F4)
		{
			if((event.modifiers & InputEvent::kControl) != 0)  //Ctrl + F4:
				g_pUIScriptVM->callFunction("ShowTemplateDisplayBtn", "d", 10);
		}
		//else if(event.keycode == SDLK_F6)
		//{
		//	saveUsedTextureInGame();
		//}
#if DEBUG_MODE || PROFILE_MODE //Debug模式下 shift+f12 刷新DebugConfig配置
		else if (event.keycode == SDLK_F11 &&( event.modifiers & InputEvent::kShift)!=0)
		{
			GetDebugMgr().loadDebugCfg();
		}
#endif //
	}

	if (g_pFrameMgr && m_bReceiveMsg)
	{
		Director* director = cocos2d::Director::getInstance();
		if(isMouseEvent(event))
			g_pFrameMgr->updateCursor(event);  // TODO: Cursor 不应属于老UI，不然点到新UI拖拽不会更新坐标，出现物品与光标分离。

		const int& index = event.button;

		// 在新、老UI之间，已经确定谁触发 DOWN 的，后续的 DRAG、UP 事件就由谁来消费。
		// 因为可能出现新UI按下，滑动鼠标，在老UI释放的案例，此时不能简单根据层级关系消费事件。	
		if (event.type == InputEvent::kMouseDrag || event.type == InputEvent::kMouseUp)
		{
			auto it = mouseDownEventsHandled.find(index);
			int downEventHandled = it != mouseDownEventsHandled.end() ? it->second : INPUTMSG_PASS;
			if (event.type == InputEvent::kMouseUp && it != mouseDownEventsHandled.end())
				mouseDownEventsHandled.erase(it);

			int upEventHandled = INPUTMSG_PASS;
			if (downEventHandled == INPUTMSG_HANDLED_NEWUI)
				upEventHandled = director->onInputEvent(event);
			else if (downEventHandled == INPUTMSG_HANDLED)
				upEventHandled = g_pFrameMgr->OnInputMessage(event);

			// 这里直接返回 downEventHandled，不关心 upEventHandled 是否消费了。
			return upEventHandled;
		}

		// DOWN、SCROLL 事件走下来，靠层级关系来决定是新UI，还是老UI消费。
		if(event.type == InputEvent::kMouseDown || event.type == InputEvent::kScrollWheel)
		{
			int category = hitPoint(position.x, position.y);
#ifdef  BUILD_MINI_EDITOR_APP
			category = INPUTMSG_HANDLED_NEWUI;
#endif
			int eventHandled = INPUTMSG_PASS;
			if(category == INPUTMSG_HANDLED_NEWUI)
				eventHandled = director->onInputEvent(event);
			else if(category == INPUTMSG_HANDLED)
				eventHandled = g_pFrameMgr->OnInputMessage(event);

			if(event.type == InputEvent::kMouseDown)
			{
#if PLATFORM_MOBILE
				// 关于触摸屏点击的水滴涟漪效果，看代码只有移动端才有的效果
				MINIW::ScriptVM::game()->callFunction("AddClickPos", "ii", int(position.x), int(position.y));
#endif
				if(category != INPUTMSG_PASS)
					mouseDownEventsHandled.emplace(index, category);
#if !PLATFORM_MOBILE
				else if(event.button == InputEvent::kLeftButton)  // 未点击到新、老UI，限制在鼠标左键丢物品
					g_pFrameMgr->m_pCurCursor->EndDrag("MousePickItem");
#endif
			}
			return eventHandled;
		}
		else
		{
			if(event.type == InputEvent::kWindowSize)
			{
				int newWidth = (int)event.delta.x;
				int newHeight = (int)event.delta.y;
				bool minimizeWindow = newWidth <= 0 || newHeight <= 0;
				GetIClientInfo().setPause(minimizeWindow);
				if(minimizeWindow)
					return INPUTMSG_HANDLED;
				
				// 不能缓存，开启了会有些问题，荣耀 HONOR 20 YAL-AL00，进入星舞动，横竖屏切换场景。
//				static int oldWidth = 0;
//				static int oldHeight = 0;
//				bool windowSizeChanged = oldWidth != newWidth || oldHeight != newHeight;
//				if(!windowSizeChanged)
// 					return INPUTMSG_HANDLED;
				
				OnWindowSizeChanged(newWidth, newHeight);
//				oldWidth = newWidth;
//				oldHeight = newHeight;
			}

			// 一个界面，同时存在新、老UI。所以对于 Key、Mouse MOVE 事件，新、老UI都要处理。
			int newEventHandled = director->onInputEvent(event);
			int oldEventHandled = g_pFrameMgr->OnInputMessage(event);
			if(newEventHandled == INPUTMSG_HANDLED_NEWUI)
				return INPUTMSG_HANDLED_NEWUI;
			return oldEventHandled;
		}
	}
	else
	{
		// LOG_INFO("GameUI::onInputEvent(): INPUTMSG_PASS");
		return INPUTMSG_PASS;
	}
}

void GameUI::OnWindowSizeChanged(int width, int height)
{
#if 0 // 打开后，可以在 Windows 平台模拟刘海屏、挖孔屏等异形屏，方便调试移动平台。
	const int notch1 = 120, notch2 = 60;  // portrait 模式下的上、下刘海的高度，数值可以不一样。
	RectInt rect(notch1, 0, width - notch1, height);  // landscape 模式下，刘海在左。
//	RectInt rect(0, 0, width - notch2, height);  // landscape 模式下，刘海在右。
//	RectInt rect(notch1, 0, width - notch1 - notch2, height);  // landscape 模式下，左右都有刘海。占很小的市场比例，比如夏普 AQUOS R3。
//	RectInt rect(0, 0, width, height); rect.inset(notch1, notch2);  // 四边都有刘海屏。目前还没有这样的手机问世。
//	RectInt rect(0, 0, width, height);  // 没有刘海
#else
	const RectInt& rect = RectfToRectInt(GetScreenManager().GetSafeArea());
#endif
	AppScreenOrientation orientation = GetScreenManager().GetScreenOrientation();
	//ErrorStringMsg("%s orientation=%d, ScreenManager SafeArea=(%d,%d - %d,%d)", __func__, (int)orientation,
	//		rect.GetLeft(), rect.GetTop(), rect.GetRight(), rect.GetBottom());
	GameUI::SetSafeArea(rect);

	//ErrorStringMsg("%s, GameUI width=%d, height=%d, SafeArea=(%d,%d - %d,%d)", __func__, width, height,
	//		safeArea.GetLeft(), safeArea.GetTop(), safeArea.GetRight(), safeArea.GetBottom());
}

void GameUI::SendEvent(const char *event)
{
	if (event)
	{
		g_pFrameMgr->SendEvent(event);
	}
}

void GameUI::SetCurrentCursor(const char *state)
{
	g_pFrameMgr->setCursor(state);
}

void GameUI::ShowCursor(bool bShow)
{
	if (bShow)
	{
		g_pFrameMgr->m_pCurCursor->ShowCursor();
	}
	else
	{
		g_pFrameMgr->m_pCurCursor->HideCursor();
	}
}

bool GameUI::isCursorDrag()
{
	return g_pFrameMgr->m_pCurCursor->IsInDragState();
}

const char *GameUI::getCurrentCursor()
{
	return g_pFrameMgr->m_pCurCursor->getCursor();
}

void GameUI::ShowUIPanel(const char *frameName)
{
	if (frameName)
	{
		Rainbow::UILib::ShowUIPanel(frameName);
	}
}

void GameUI::HideUIPanel(const char *frameName)
{
	if (frameName)
	{
		Rainbow::UILib::HideUIPanel(frameName);
	}
}

void GameUI::HideAllRootFrame()
{
	for(size_t i=0; i<g_pFrameMgr->m_RootFrames.size(); i++)
	{
		g_pFrameMgr->m_RootFrames[i]->Hide();
	}
}

void GameUI::UIReceiveMessage(bool bReceive)
{
	m_bReceiveMsg = bReceive;
}

void GameUI::ForceUpdateUIEditFramePos()
{
	g_pFrameMgr->ForceUpdateFramePos();
}

void GameUI::InitUIEditRootFrames()
{
	g_pFrameMgr->InitRootFrames();
}

void GameUI::SetXMLReload(bool enable)
{
	g_enableReloadTest = enable;
}

bool GameUI::IsXMLReload()
{
	return g_enableReloadTest;
}

Rainbow::UILib::FrameManager* GameUI::GetFrameManagerPtr()
{
	return g_pFrameMgr;
}

void GameUI::SetEditMode(bool mode)
{
	g_pFrameMgr->SetEditModeEnabled(mode);
}

void GameUI::SetEditingFrame(const char *framename)
{
	g_pFrameMgr->SetEditFrame(framename);
}

bool GameUI::GetEditMode() const
{
	return g_pFrameMgr->getFuncF4Enable();
}

void GameUI::SetLegacyUIXmlCfg(std::string xmlPath)
{
	size_t pointIndex = xmlPath.find(".xml");
	size_t lastTag = xmlPath.find_last_of("/");
	std::string filename = xmlPath.substr(lastTag + 1, pointIndex - (lastTag + 1));
	g_pFrameMgr->m_ObjectXmlRefenceMap[filename.c_str()] =xmlPath.c_str();
}

#if EXPORT_LEGACYUI
std::string slidePlaneName = "";
void GameUI::DumpXmlNode(tinyxml2::XMLElement* tmpnode, tinyxml2::XMLElement* root, bool isVirutal, std::string parentNodeName, std::string folderPath)
{

	std::string childFramePath = "";
	while (tmpnode)
	{
		bool isAloneFrame = false;
		std::string nodeType = tmpnode->Name();
		std::string nodeName = "";
		int index = nodeType.find("Frame");
		bool isAloneNode = false;
		bool beginDumpSliderPlane = false;
		if (tmpnode->Attribute("slideplane"))
		{
			slidePlaneName = tmpnode->Attribute("slideplane");
			beginDumpSliderPlane = true;
		}

		//独立节点名,节点名没有设置parent的
		if (tmpnode->Attribute("name"))
		{
			std::string name = tmpnode->Attribute("name");


			if (name.find("$parent") == std::string::npos)
			{
				isAloneNode = true;
			}
		}

		if ((index !=std::string::npos&&index==(nodeType.size()-5)|| isAloneNode)&&isVirutal == false&& (slidePlaneName.empty()|| beginDumpSliderPlane))//节点以frame结尾或没有&parent命名的孤立节点，且不是模板
		{
			beginDumpSliderPlane = false;
			//将Frame单独拆出来成为一个文件

			std::string parentType = "";
			tinyxml2::XMLElement* parentElment;
			std::string layerLevel = "";
			if (tmpnode->Parent())
			{
				parentElment = static_cast<tinyxml2::XMLElement*>(tmpnode->Parent());
				parentType = parentElment->Name();
				
			}
			if (parentType == "Layer")
			{
				if (parentElment->Attribute("level"))
				{
					layerLevel = parentElment->Attribute("level");
				}
			}
			isAloneFrame = true;
			tinyxml2::XMLDocument frameDoc;
			tinyxml2::XMLDeclaration* decl = frameDoc.NewDeclaration();
			frameDoc.LinkEndChild(decl);
			tinyxml2::XMLElement* frameRoot = frameDoc.NewElement("Ui");
			frameDoc.LinkEndChild(frameRoot);

			tinyxml2::XMLElement* frameElement = frameRoot->GetDocument()->NewElement(tmpnode->Name());
			for (const tinyxml2::XMLAttribute* a = tmpnode->FirstAttribute(); a; a = a->Next()) {
				frameElement->SetAttribute(a->Name(), a->Value());	
			}
			frameElement->SetAttribute("parentNode", parentNodeName.c_str());
			if (!layerLevel.empty())
			{
				frameElement->SetAttribute("layerLevel", layerLevel.c_str());
			}
			frameRoot->LinkEndChild(frameElement);
			if (tmpnode->FirstChild())
			{
				if (tmpnode->Attribute("name"))
				{
					nodeName = tmpnode->Attribute("name");
					if (nodeName.find("$parent") != std::string::npos)
					{
						nodeName.replace(0, 7, parentNodeName);
					}
				}
				else {
					nodeName = parentNodeName;
				}
				DumpXmlNode(tmpnode->FirstChildElement(), frameElement, isVirutal, nodeName, folderPath);
			}
			std::string curNodeName = tmpnode->Attribute("name");
			if (curNodeName.find("$parent") != std::string::npos)
			{
				curNodeName.replace(0, 7, parentNodeName);
			}
			std::string path = Format("%s/%s.xml", folderPath.c_str(), curNodeName.c_str());
			childFramePath = path;
			std::string abspath = Rainbow::GetFileManager().ToWriteableFullPath(path);
			frameDoc.SaveFile(abspath.c_str());

		}

		tinyxml2::XMLElement* element = root->GetDocument()->NewElement(tmpnode->Name());
		for (const tinyxml2::XMLAttribute* a = tmpnode->FirstAttribute(); a; a = a->Next()) {
			element->SetAttribute(a->Name(), a->Value());					// fixme: this will always allocate memory. Intern?
		}

		if (tmpnode->GetText())
		{
			element->SetText(tmpnode->GetText());
		}

		root->LinkEndChild(element);

		if (isAloneFrame)
		{//有子Frame 把拆出来的Frame文件依赖关系写进去
			slidePlaneName = "";
			element->SetAttribute("nodeFile", childFramePath.c_str());
			tmpnode = tmpnode->NextSiblingElement();//获取同级别的下一个兄弟元素
			isAloneFrame = false;
			continue;
		}
		if (tmpnode->Attribute("name"))
		{
			nodeName = tmpnode->Attribute("name");
			if (nodeName.find("$parent") != std::string::npos)
			{
				nodeName.replace(0, 7, parentNodeName);
			}
		}
		else {
			nodeName = parentNodeName;
		}
		DumpXmlNode(tmpnode->FirstChildElement(), element, isVirutal, nodeName, folderPath);//递归调用,打印子节点所有属性和文本信息

		tmpnode = tmpnode->NextSiblingElement();//获取同级别的下一个兄弟元素
	}
}
#endif

bool GameUI::LoadXmlForLua(const char* path)
{

#if EXPORT_LEGACYUI
	GameUI::s_luaFileData = "";
	s_templateXml.Clear();
	tinyxml2::XMLDeclaration* decl = s_templateXml.NewDeclaration();
	s_templateXml.LinkEndChild(decl);
	tinyxml2::XMLElement* templateRoot = s_templateXml.NewElement("Ui");
	s_templateXml.LinkEndChild(templateRoot);
#else 
#if LEGACYUI_AUTLOAD //开启老UI xml按需加载
	std::string xmlPath = path;
	size_t pointIndex = xmlPath.find(".xml");
	size_t lastTag = xmlPath.find_last_of("/");
	std::string filename = xmlPath.substr(lastTag + 1, pointIndex - (lastTag + 1));
	std::string folderpath = Format("legacyui/%s", filename.c_str());

	std::string luapath = Format("%s/%s_init.lua", folderpath.c_str(), filename.c_str());
	bool ret = g_pUIScriptVM->callFile(luapath.c_str());
#else
	bool ret = m_pXmlmgr->LoadUIFromXml(path);
#endif
#endif
#if EXPORT_LEGACYUI
		//bool ret = m_pXmlmgr->LoadUIFromXml(path);

	std::string xmlPath = path;
	size_t pointIndex = xmlPath.find(".xml");
	size_t lastTag = xmlPath.find_last_of("/");
	std::string filename = xmlPath.substr(lastTag + 1, pointIndex - (lastTag + 1));
	std::string folderpath = Format("legacyui/%s", filename.c_str());
	Rainbow::GetFileManager().CreateWritePathDir(folderpath.c_str());

	bool ret = false;

	if (!path)
	{
		return false;
	}
	XMLNode pRootNode;
	XMLData xmldata;
	if (!xmldata.loadFile(path))
	{
		return false;
	}
	pRootNode = xmldata.getRootNode();
	XMLNode pChildNode = pRootNode.iterateChild(nullptr);

	while (!pChildNode.isNull())
	{
		const char* name = pChildNode.getName();
		//lang_ver 需要连续的 这里检测上一个加载的控件如果同名不在加载避免重复加载
		const char* tempFrameName = pChildNode.attribToString("name");
		const char* tempNodeType = pChildNode.getName();
		name = tempNodeType;

		if (stricmp(name, "Script") == 0)
		{
			std::string strLuaFileName = pChildNode.attribToString("file");
			std::string repairName = strLuaFileName;
			std::string resultName = "";
			std::string tempStr = "";
			repairName = repairName.substr(0, repairName.size() - 4);//去除文件后缀
			for (int i = 0; i < repairName.size(); i++)
			{
				tempStr = repairName.at(i);
				if (tempStr == "/" || tempStr == "\\")
				{
					resultName += ".";
				}
				else {
					resultName += (repairName.at(i));
				}
			}
			GameUI::s_luaFileData.append(Format("require('%s')", resultName.c_str()));
			GameUI::s_luaFileData.append("\n");

		}
		if (stricmp(name, "Cursor") == 0 || stricmp(name, "font") == 0 || stricmp(name, "Script") == 0 || stricmp(name, "Accel") == 0)
		{
			pChildNode = pRootNode.iterateChild(pChildNode);

			continue;
		}
		const char* uiObjName =pChildNode.attribToString("name");
		bool isVirtual = false;
		if (pChildNode.attribToBool("virtual")==true)
		{ //template
			isVirtual = true;


			std::string nodeType = pChildNode.getName();
			//if (nodeType == "Frame")//处理模版中的frame
			//{
				tinyxml2::XMLDocument frameDoc;
				tinyxml2::XMLDeclaration* decl = frameDoc.NewDeclaration();
				frameDoc.LinkEndChild(decl);
				tinyxml2::XMLElement* frameRoot = frameDoc.NewElement("Ui");
				frameDoc.LinkEndChild(frameRoot);

				tinyxml2::XMLElement* element = frameRoot->GetDocument()->NewElement(pChildNode.getName());
				for (const tinyxml2::XMLAttribute* a = pChildNode.m_pElement->FirstAttribute(); a; a = a->Next()) {
					element->SetAttribute(a->Name(), a->Value());
				}
				frameRoot->LinkEndChild(element);
				if (!pChildNode.firstChild().isNull())
				{
					DumpXmlNode(pChildNode.m_pElement->FirstChildElement(), element, isVirtual, uiObjName, folderpath);
				}
				std::string path = Format("%s/%s.xml", folderpath.c_str(), uiObjName);
				std::string abspath = Rainbow::GetFileManager().ToWriteableFullPath(path);
				frameDoc.SaveFile(abspath.c_str());
			//}
			//else 
			//{
			// 	tinyxml2::XMLElement* element = templateRoot->GetDocument()->NewElement(pChildNode.getName());
			//	for (const tinyxml2::XMLAttribute* a = pChildNode.m_pElement->FirstAttribute(); a; a = a->Next()) {
			//		element->SetAttribute(a->Name(), a->Value());
			//	}
			//	templateRoot->LinkEndChild(element);
			//	if (!pChildNode.firstChild().isNull())
			//	{
			//		DumpXmlNode(pChildNode.m_pElement->FirstChildElement(), element, isVirtual, uiObjName, folderpath);
			//	}
			//}
		}
		else {//root frame
			isVirtual = false;
			tinyxml2::XMLDocument frameDoc;
			tinyxml2::XMLDeclaration* decl = frameDoc.NewDeclaration();
			frameDoc.LinkEndChild(decl);
			tinyxml2::XMLElement* frameRoot = frameDoc.NewElement("Ui");
			frameDoc.LinkEndChild(frameRoot);

			tinyxml2::XMLElement* element = frameRoot->GetDocument()->NewElement(pChildNode.getName());
			for (const tinyxml2::XMLAttribute* a = pChildNode.m_pElement->FirstAttribute(); a; a = a->Next()) {
				element->SetAttribute(a->Name(), a->Value());	
			}
			frameRoot->LinkEndChild(element);
			if (!pChildNode.firstChild().isNull())
			{
				DumpXmlNode(pChildNode.m_pElement->FirstChildElement(), element, isVirtual, uiObjName, folderpath);
			}

			std::string path = Format("%s/%s.xml", folderpath.c_str(), uiObjName);
			std::string abspath = Rainbow::GetFileManager().ToWriteableFullPath(path);
			frameDoc.SaveFile(abspath.c_str());
		}
		pChildNode = pRootNode.iterateChild(pChildNode);
	}


	if (!GameUI::s_templateXml.FirstChildElement("Ui")->NoChildren())
	{
		std::string templatePath = Format("%s/%s_template.xml", folderpath.c_str(), filename.c_str());
		std::string abspath = Rainbow::GetFileManager().ToWriteableFullPath(templatePath);
		GameUI::s_templateXml.SaveFile(abspath.c_str());
		//将模板添加到初始化文件中
		GameUI::s_luaFileData.append(Format("\nGameUI:ParseUIInXml('%s')", templatePath.c_str()));
	}

	std::string luapath = Format("%s/%s_init.lua", folderpath.c_str(), filename.c_str());
	std::string luaabspath = Rainbow::GetFileManager().ToWriteableFullPath(luapath);
	Rainbow::GetFileManager().SaveToWritePath(luaabspath, GameUI::s_luaFileData.c_str(), GameUI::s_luaFileData.size());

#endif

	g_pFrameMgr->ForceUpdateFramePos();
	//g_pFrameMgr->InitRootFrames();

#if EXPORT_LEGACYUI
	GameUI::s_luaFileData = "";
	GameUI::s_templateXml.Clear();
#endif
	return ret;
}

bool GameUI::NewXMLFile(const char *uires)
{
	LOG_INFO( "GameUI NewXMLFile file name=[%s]", uires );

	g_pFrameMgr->m_ObjectTable.clear();
	g_pFrameMgr->m_TemplateTable.clear();
	g_pFrameMgr->m_RootFrames.clear();
#if ENABLE_UILIB_EDITOR
	g_pFrameMgr->m_CurrXMLTemplateTable.clear();
#endif // ENABLE_UILIB_EDITOR


	const char *pdot = strrchr(uires, '.');
	assert(pdot);
	using Rainbow::Timer;
	unsigned int t1 = Timer::getSystemTick();

	bool ret = false;
	if(stricmp(pdot, ".toc") == 0) ret = m_pXmlmgr->LoadTOCFile(uires);
	else if(stricmp(pdot, ".xml") == 0) ret = m_pXmlmgr->LoadUIFromXml(uires);

	if(!ret)
	{
		return false;
	}
	unsigned int t2 = Timer::getSystemTick();
	LogStringMsg( "GameUI LoadTOCFile file  [%s] Costtime:[%d]",uires, t2-t1 );

	g_pFrameMgr->m_TocFileName = uires;

	g_pFrameMgr->InitRootFrames();

	unsigned int t3 = Timer::getSystemTick();
	LogStringMsg( "GameUI InitRootFrames file [%s] Costtime:[%d]",uires, t3-t2 );

	return true;
}


bool GameUI::AppendTOCFile(const char *uires)
{
	LOG_INFO( "AppendTOCFile file name=[%s]", uires );
	using Rainbow::Timer;
	unsigned int t1 = Timer::getSystemTick();
	if (!m_pXmlmgr->LoadTOCFile(uires))
	{
		return false;
	}
	unsigned int t2 = Timer::getSystemTick();
	LOG_INFO( "LoadTOCFile2 file time=[%d]", t2-t1 );

	g_pFrameMgr->ForceUpdateFramePos();
	g_pFrameMgr->InitRootFrames();
	unsigned int t3 = Timer::getSystemTick();
	LOG_INFO( "InitRootFrames2 file time=[%d]", t3-t2 );
	return true;
}


int GameUI::readTOCList(const char *uires)
{
	m_tocFileList.clear();
	return m_pXmlmgr->getTOCList(uires, m_tocFileList);
}


bool GameUI::parseSingleTOCFile(int index, const std::string& replacepath/* ="" */)
{
	bool ret = false;
	if (replacepath.empty())
	{
		size_t i = size_t(index);
		assert(i < m_tocFileList.size());
		ret = m_pXmlmgr->parseSingleTOCFile(m_tocFileList[i]);
	}
	else
	{
		ret = m_pXmlmgr->parseSingleTOCFile(replacepath);
	}

	if (index == m_tocFileList.size() - 1)
	{
		//加载正好完成
		g_pFrameMgr->ForceUpdateFramePos();
		g_pFrameMgr->InitRootFrames();
	}

	return ret;
}

bool GameUI::LoadUIFile(const char *path)
{
	if (!path)
	{
		return false;
	}
	std::string strPath = path;
	const std::string strPostfixXml = ".xml";
	const std::string strPostfixFui = ".fui";
	const std::string strPostfixMiniUI = ".miniui";

	if (strPath.rfind(strPostfixXml) == strPath.length() - strPostfixXml.length())
	{
		return LoadXMLFile(strPath);
	}

	if (strPath.rfind(strPostfixFui) == strPath.length() - strPostfixFui.length())
	{
		strPath = strPath.substr(0, strPath.length() - strPostfixFui.length());
	}
	else if (strPath.rfind(strPostfixMiniUI) == strPath.length() - strPostfixMiniUI.length())
	{
		strPath = strPath.substr(0, strPath.length() - strPostfixMiniUI.length());
	}
	fairygui::UIPackage* pkg = fairygui::UIPackage::addPackage(strPath);
	if (pkg != NULL)
	{
		return true;
	}

	return false;
}

bool GameUI::LoadXMLFile(const std::string& path)
{
	return m_pXmlmgr->parseLuaFileInXml(path);
}

bool GameUI::LoadXMLString(const char *str)
{
	if(str)
	{
		g_pFrameMgr->m_RootFramesReload.clear();

		if (!m_pXmlmgr->LoadUIFromXml(str, true))
		{
			return false;
		}

		g_pFrameMgr->InitRootFramesReload();
		return true;
	}

	return false;
}

bool GameUI::SaveXMLFile(const char *path, bool isNew/*=false*/)
{
	if (path)
	{
		if (!m_pXmlmgr->SaveUIToXml(path,isNew))
		{
			return false;
		}

		return true;
	}

	return false;
}

void GameUI::setApiId(const int apiId)
{
	if (!g_pFrameMgr)return;
	g_pFrameMgr->setApiId(apiId);
}


void GameUI::setUIHide(bool b, bool isShowCursor)
{
	m_HideUI = b;
	
	//g_playCtrl->m_Body->setVisibleDispayName(!b);
	//ClientGameManager::getInstance()->getCurGame()->setPlayerVisibleDispayName(!b);

	/*if(m_HideUI)
	{
		if(isShowCursor)
		{
			RenderSystem::getSingleton().showCursor(true);
		}
		else
		{
			RenderSystem::getSingleton().showCursor(false);
		}
	}
	else
	{
		RenderSystem::getSingleton().showCursor(true);
	}*/

}

std::string GameUI::getFilePathByToc(int index)
{
	if (0 <= index && (unsigned int)index < m_tocFileList.size())
	{
		return m_tocFileList[index];
	}
	return "";
}

int GameUI::getTocListSize()
{
	return (int)m_tocFileList.size();
}

void GameUI::addLuaTocFileList(const std::string& luapath)
{
	m_tocFileList.push_back(luapath);
}

void GameUI::ParseUIInXml(const std::string& path)
{

#if  EXPORT_LEGACYUI
	GameUI::s_luaFileData = "";
	s_templateXml.Clear();
	tinyxml2::XMLDeclaration* decl = s_templateXml.NewDeclaration();
	s_templateXml.LinkEndChild(decl);
	tinyxml2::XMLElement* root = s_templateXml.NewElement("Ui");
	s_templateXml.LinkEndChild(root);
#endif //  EXPORT_LEGACYUI

	m_pXmlmgr->parseUIInXml(path);
#if EXPORT_LEGACYUI
	std::string xmlPath = path;
	size_t pointIndex = xmlPath.find(".xml");
	size_t lastTag = xmlPath.find_last_of("/");
	std::string filename = xmlPath.substr(lastTag + 1, pointIndex - (lastTag + 1));
	std::string folderpath = Format("legacyui/%s", filename.c_str());
	Rainbow::GetFileManager().CreateWritePathDir(folderpath.c_str());
	std::string luapath = Format("%s/%s_init.lua", folderpath.c_str(), filename.c_str());
	std::string abspath = Rainbow::GetFileManager().ToWriteableFullPath(luapath);
	Rainbow::GetFileManager().SaveToWritePath(abspath, GameUI::s_luaFileData.c_str(), GameUI::s_luaFileData.size());
#endif
	g_pFrameMgr->ForceUpdateFramePos();



	//g_pFrameMgr->InitRootFrames();

#if  EXPORT_LEGACYUI
	GameUI::s_luaFileData = "";
	GameUI::s_templateXml.Clear();
#endif //  EXPORT_LEGACYUI
}

bool GameUI::GetOrCreateLegacyUIObjectFromXml(const std::string& xmlPath, const std::string& uiObjectName)
{

	return m_pXmlmgr->GetOrCreateLegacyUIObjectFromXml(xmlPath, uiObjectName);
}

bool GameUI::isUIHide() const 
{ 
	return m_HideUI; 
}

void  GameUI::ShowUIPanel(const std::string& frameName) 
{ 
	ShowUIPanel(frameName.c_str()); 
}

void GameUI::SetSafeArea(const RectInt& rect)
{
	assert(rect.GetWidth() > 0 && rect.GetHeight() > 0);
	this->safeArea = rect;
	AppScreenOrientation orientation = GetScreenManager().GetScreenOrientation();
	const int width = GetWidth();
	const int height = GetHeight();
	int marginHorizontal;

	// landscape 模式下左右对称留白；portrait 模式下，仅刘海屏那一侧留白。
	// m_UIScreenPadding 先保留，后面逐渐退役去掉。
	// TODO: 引擎返回的 orientation 不正确。先用 width > height 来判定是横屏，如果遇到正方形的刘海屏就要修正了。
//	if(orientation == kPortrait || orientation == kPortraitUpsideDown)
	if(width < height)
	{
//		int marginVertical = std::max(rect.GetTop(), height - rect.GetBottom());
		marginHorizontal = 0;  // m_UIScreenPadding 好像用作左右的边距，对于竖屏的星工场，没有左右边距。
	}
	else //if(orientation == kLandscapeLeft || orientation == kLandscapeRight)
	{
		// 游戏在 landscape 模式，不让上下留白，即使引擎返回的 safeArea 上下有非安全区域。
		marginHorizontal = std::max(rect.GetLeft(), width - rect.GetRight());
		LOG_WARNING("marginHorizontal = %d", marginHorizontal);
		this->safeArea.Set(marginHorizontal, 0, width - 2 * marginHorizontal, height);
	}

	LOG_WARNING("marginHorizontal = %d", marginHorizontal);
	g_pFrameMgr->setScreenEdge(marginHorizontal);
}

int GameUI::GetWidth() const
{
	return GetScreenManager().GetWidth();
}

int GameUI::GetHeight() const
{
	return GetScreenManager().GetHeight();
}

void  GameUI::HideUIPanel(const std::string& frameName) 
{ 
	HideUIPanel(frameName.c_str()); 
}

//延后释放
GameStatic<GameUI> s_GameUI(kInitManual);
GameUI* GetGameUIPtr() {
	return s_GameUI.EnsureInitialized();
}

void SafelyDestroyGameUI()
{
	s_GameUI.SafelyDestroy();
}

