--此文件用来与C++交互

--开启开发者模式，允许进入开发者UI页面
EnableDeveloper = true 

--枚举类型ID
DeveloperEnumTypeID = 999999

--素材库类型ID
DeveloperLibraryTypeID = 999998

--变量库类型ID
DeveloperVariableTypeID = 999997

--对象库类型ID
DeveloperObjTypeID = 999996

--触发器类型ID
DeveloperTriggerTypeID = 999995

--'导入脚本'类型ID
DeveloperScriptTypeID = 999994

-- 开发者商品映射ID
DeveloperDevGoodsFactorID = 999993

--触发器事件传参/条件或动作中的可选项
TriggerEventAllParams = {250001,250002,300001,300002,240001,260002,270001,280001,290001,310001,340001,400001,430001,440001,440002,450001,460001,470001,480001}

--定义触发器组/组内触发器数量
DeveloperTriggerDefines = {}
DeveloperTriggerDefines['GroupCount'] = 40
DeveloperTriggerDefines['ChildCount'] = 100

DeveloperParamMaxCount = 10 --参数最大个数

LangEnum = {
	--开发中工具中队伍名字多语言前缀
	MapRuleTeamNamePrefix = "Map_Team_Name",
	--开发者工具中开场介绍前缀
	MapRuleStartIntroducePrefix = "startIntroduceKey",
	--开发者工具中触发器多语言前缀
	DeveloperEditTriggerLangPrefix = "triggerLangPrefix",
}

--获取多语言显示的字符串
function GetEditorLangShowText(oldText, key)
	local wid = WorldMgr:getWorldId()
	local fwid = WorldMgr:getFromWorldID();
	local wdesc = AccountManager:findWorldDesc(wid)
	local supportlang = 0;
	if wdesc then 
		supportlang = wdesc.translate_supportlang; 
	end
	local other = supportlang - math.pow(2, get_game_lang())
	if supportlang == 0 then
		other = 0
	end
	--说明不支持多语言 直接return了 
	if not other or other <= 0 then
		return oldText
	end
	if not key then return oldText end;
	local lang = EditorLang:getLangByKey(wid, fwid, key);
	if not lang or lang.val == "" then
		return oldText
	end
	local lang_now = get_game_lang();
	local showText = SignManagerGetInstance():parseTextFromLanguageJson(lang.val, lang_now);
	return showText;
end

function DeveloperGetLangKey(dataIndex, optionIndex)
	if not dataIndex or not optionIndex then return end

	if not dataIndex.triggerid then dataIndex.triggerids = "" end
	if not dataIndex.itemIndex then dataIndex.itemIndex = "" end
	if not dataIndex.typeIndex then dataIndex.typeIndex = "" end
	if not dataIndex.childIndex then dataIndex.childIndex = "" end
	if not dataIndex.modpacketid then dataIndex.modpacketid = "" end

	local langkey = LangEnum.DeveloperEditTriggerLangPrefix
					.."_"..dataIndex.triggerid
					.."_"..dataIndex.itemIndex
					.."_"..dataIndex.typeIndex
					.."_"..dataIndex.childIndex
					.."_"..optionIndex.."_"
					..dataIndex.modpacketid;
	return langkey;
end

function OpenDeveloperTriggerView()
	if UGCModeMgr and UGCModeMgr:IsUGCMode() then
		GetInst("MiniUIManager"):HideUI("DebugFrameAutoGen")

		CloseAllDeveloperWindow();
		GetInst("MiniUIManager"):HideUI("DevToolsAutoGen")

		local param = {
			fromId = 1,
			actorType = VisualCodeDef.ActorType.Global,
			callback = function()
				local devCtrl = GetInst("MiniUIManager"):GetCtrl("DevTools")
				if devCtrl then
					GetInst("MiniUIManager"):ShowUI("DevToolsAutoGen")
				else
					GetInst('MiniUIManager'):AddPackage({'miniui/miniworld/commonTexture'},"DevToolsAutoGen")
					GetInst("MiniUIManager"):OpenUI("devToolsMain","miniui/miniworld/ugc_devTools","DevToolsAutoGen")
					devCtrl = GetInst("MiniUIManager"):GetCtrl("DevTools")
				end

				devCtrl:BackOldSelect()
			end,
		}
		GetInst("VisualCodeInterface"):OpenVisualCode(param);
	else
		OpenDeveloperEditTriggerWindow()
		CloseDeveloperEditScriptWindow()
		CloseDeveloperQuickEntryWindow()
		if HasUIFrame("DeveloperEditTrigger") and getglobal("DeveloperEditTrigger"):IsShown() then
			local devCtrl = GetInst("MiniUIManager"):GetCtrl("DevTools")
			if devCtrl then
				devCtrl:SetVarLibBtnVisible(true)
			end
		end
	end
end

function OpenUIDeveloperTriggerView(uid)
	if UGCModeMgr and UGCModeMgr:IsUGCMode() then
		GetInst("MiniUIManager"):HideUI("DebugFrameAutoGen")
		CloseAllDeveloperWindow()

		GetInst("MiniUIManager"):CloseUI("DevToolsAutoGen")

		local name = "UI"
		local mode = GetInst("UIEditorModelManager"):getModsAndMapModel(uid)
		if mode then
			name = mode:getName()
		end

		local isShowResource = GetInst("MiniUIManager"):IsShown("SceneEditorResourceMainFrameAutoGen")

		local isNewActor = false;
		local param = {
			fromId = 2,
			actorType = VisualCodeDef.ActorType.UI,
			uiid = uid,
			actorName = name,
			callback = function()
				if uid and UIEditorDef and UIEditorDef.ui_editor_open_ui_project then
					UIEditorDef:ui_editor_open_ui_project({ uiid = uid })
				end

				if isShowResource then
					GetInst("MiniUIManager"):ShowUI("SceneEditorResourceMainFrameAutoGen")
				end
			end,
			runCallback = function()
				GetInst("ResourceLibDataManager"):LoadUIProject()--xyang重新加载数据，保证UI库数据的刷新问题
			end,
			-- isNewActorCB = function()
			-- 	isNewActor = true;
			-- end,
		}

		GetInst("VisualCodeInterface"):OpenVisualCode(param);
		
		-- if isNewActor then
		-- 	-- 在积木界面生成初始的触发器积木
		-- 	local PrefabcfgTab = PrefabBlockCfg_StringToTable(PrefabBlockCfg_UiEditInit);
        --     GetInst("VisualCodeMsgHandler"):dispatcher(VisualCodeDef.msg.CREATE_PREFAB_BLOCKS, PrefabcfgTab);
		-- end
	else
		local handleType = ScriptSupportCtrl:makeSSType('coustomui',uid,2,nil,false)
		local mapId = PluginDeveloperGetMapID()
		local param = {}
		param.curType = handleType
		param.curMapId = mapId
		OpenDeveloperEditTriggerWindow(param)
		CloseDeveloperEditScriptWindow()
		CloseDeveloperQuickEntryWindow()
		if HasUIFrame("DeveloperEditTrigger") and getglobal("DeveloperEditTrigger"):IsShown() then
			local devCtrl = GetInst("MiniUIManager"):GetCtrl("DevTools")
			if devCtrl then
				devCtrl:SetVarLibBtnVisible(true)
			end
		end
	end
end

function OpenDeveloperLocalTriggerView(moduuid)
	if UGCModeMgr and UGCModeMgr:IsUGCMode() then
		GetInst('SingleEditorFrameManager'):CloseUI()

		local showPkgPluginEdit = false
		if GetInst("MiniUIManager"):IsShown("ModsLibPkgAutoGen") then
			GetInst("MiniUIManager"):HideUI("ModsLibPkgAutoGen")
			showPkgPluginEdit = true
		end
		
		local showModsLib = false
		if ModsLib_IsShown() then
			GetInst("MiniUIManager"):HideUI("ModsLibAutoGen")
			showModsLib = true
		end
		local param = {
			fromId = 3,
			actorClass = CurEditorClass,
			actorDefId = CurrentEditDef.ID,
			actorDefType = CurrentEditDef.Type,
			actorName = CurrentEditDef.Name,
			packageUUID = moduuid or "",
			callback = function(operateObjParam)
				if operateObjParam then
					operateObjParam.fromTrigger = nil
					GetInst('SceneEditorMsgHandler'):dispatcher(SceneEditorUIDef.objectOperate.obj_operate_edit, operateObjParam)
				end
				-- singleEditorFrame:Show();
				local ctrl = GetInst("SingleEditorFrameManager"):GetCtrl()
				if ctrl then
					ctrl:ChangeSingleEditor2Tab(1)
				end

				if showPkgPluginEdit then
					GetInst("MiniUIManager"):ShowUI("ModsLibPkgAutoGen")
				end

				if showModsLib then
					GetInst("MiniUIManager"):ShowUI("ModsLibAutoGen")
				end
				-- ClientCurGame:setOperateUI(true)
			end,
			runCallback = function()
				SingleEditorFrameSaveBtn_OnClick()

				if showPkgPluginEdit then
					GetInst("MiniUIManager"):CloseUI("ModsLibPkgAutoGen")
				end

				if showModsLib then
					GetInst("MiniUIManager"):CloseUI("ModsLibAutoGen")
				end
			end,
		}
		if moduuid and moduuid ~= '' then
			local path = "data/w".. WorldMgr:getWorldId()..'/modpkg/user_1_'..moduuid..'/visualcode'
			if not GetInst("UgcFileSystem"):IsDirExisted(path) then 
				GetInst("Triggers2BlocksMgr"):ConvertImportRes(WorldMgr:getWorldId(),moduuid)
			end
		end

		-- ClientCurGame:setOperateUI(false)
		GetInst('SceneEditorMsgHandler'):dispatcher(SceneEditorUIDef.common.hide_resource_shortcut)
		GetInst("VisualCodeInterface"):OpenVisualCode(param);
	else
		local handleType = PluginDeveloperGetHandleType(nil,nil,moduuid)
		handleType.name = handleType.name.."local" -- 组合局部触发器命名
		local mapId = PluginDeveloperGetMapID()
		local param = {}
		param.curType = handleType
		param.curMapId = mapId
		param.Itemtype = CurrentEditDef.Type or -1 --辅助参数（插件添加后未从新进入地图 datamgr获取不了道具类型）
		if type(CurrentEditDef.Type) == "string" then
			param.Itemtype = -1
		end
		OpenDeveloperEditTriggerWindow(param)
		CloseDeveloperEditScriptWindow()
		CloseDeveloperQuickEntryWindow()

		if EnableDeveloper then 
			GetInst("SingleEditorFrameManager"):ResetSingleEditorBtn(true) 
		end
		GetInst("SingleEditorFrameManager"):setWidgetsVisible('singleEditorFrameRightTopTitle', false)
	end
end

--切换开发者页签
function DeveloperSwitchTab(tabType,tabIndex,moduuid,uiinfo)

	local debugFrameCtrl = GetInst("MiniUIManager"):GetCtrl("DebugFrame")
	if not debugFrameCtrl then
		GetInst("MiniUIManager"):OpenUI("debugFrame", "miniui/miniworld/ugc_devTools", "DebugFrameAutoGen", {disableOperateUI = true})
		GetInst("MiniUIManager"):HideUI("DebugFrameAutoGen")
	end

	if tabType == 1 then 
		--全局页签
		--全局页签
		local devCtrl = GetInst("MiniUIManager"):GetCtrl("DevTools")
		if devCtrl then
			devCtrl:SetVarLibBtnVisible(false)
		end
		GetInst("MiniUIManager"):ShowUI("DebugFrameAutoGen")
		if tabIndex == 1 then
			GetInst("MiniUIManager"):HideUI("DebugFrameAutoGen")

			CloseAllDeveloperWindow()
		elseif tabIndex == 2 then
			OpenDeveloperTriggerView()
		elseif tabIndex == 3 then 
			OpenDeveloperEditScriptWindow()
			CloseDeveloperEditTriggerWindow()
			CloseDeveloperQuickEntryWindow()
		elseif tabIndex == 4 then
			GetInst("MiniUIManager"):HideUI("DebugFrameAutoGen")

			CloseDeveloperEditScriptWindow()
			CloseDeveloperEditTriggerWindow()
			OpenDeveloperQuickEntryWindow()
		end
	elseif tabType > 1 and tabType < 10 then -- 插件全局
		if tabIndex == 1 then
			CloseAllDeveloperWindow()
			GetInst("SingleEditorFrameManager"):setWidgetsVisible('singleEditorFrameRightTopTitle', true)
			GetInst("SingleEditorFrameManager"):ResetSingleEditorBtn(false)
		elseif tabIndex == 2 then 
			local handleType = PluginDeveloperGetHandleType()
			local mapId = PluginDeveloperGetMapID()
			local param = {}
			param.curType = handleType
			param.curMapId = mapId
			
			OpenDeveloperEditTriggerWindow(param)
			CloseDeveloperEditScriptWindow()
			CloseDeveloperQuickEntryWindow()

			if EnableDeveloper then GetInst("SingleEditorFrameManager"):ResetSingleEditorBtn(true) end
			GetInst("SingleEditorFrameManager"):setWidgetsVisible('singleEditorFrameRightTopTitle', false)
		elseif tabIndex == 3 then 
			local handleType = PluginDeveloperGetHandleType()
			local mapId = PluginDeveloperGetMapID()
			local param = {}
			param.curType = handleType
			param.curMapId = mapId
			OpenDeveloperEditScriptWindow(param)
			CloseDeveloperEditTriggerWindow()
			CloseDeveloperQuickEntryWindow()

			GetInst("SingleEditorFrameManager"):ResetSingleEditorBtn(false)
			GetInst("SingleEditorFrameManager"):setWidgetsVisible('singleEditorFrameRightTopTitle', false)
		end
		GetInst("SingleEditorFrameManager"):setWidgetsVisible('singleEditorFrameVarLibBtn', false)
		if HasUIFrame("DeveloperEditTrigger") and getglobal("DeveloperEditTrigger"):IsShown() then
			GetInst("SingleEditorFrameManager"):setWidgetsVisible('singleEditorFrameVarLibBtn', true)
		end
	elseif tabType == 100 then -- 插件局部
		if tabIndex == 1 then
			CloseAllDeveloperWindow()
			GetInst("SingleEditorFrameManager"):setWidgetsVisible('singleEditorFrameRightTopTitle', true)
			GetInst("SingleEditorFrameManager"):ResetSingleEditorBtn(false)
		elseif tabIndex == 2 then
			OpenDeveloperLocalTriggerView(moduuid)
		elseif tabIndex == 3 then 
			local handleType = PluginDeveloperGetHandleType(nil,nil,moduuid)
			handleType.name = handleType.name.."local"  -- 组合局部脚本命名
			local mapId = PluginDeveloperGetMapID()
			local param = {}
			param.curType = handleType
			param.curMapId = mapId
			OpenDeveloperEditScriptWindow(param)
			CloseDeveloperEditTriggerWindow()
			CloseDeveloperQuickEntryWindow()

			GetInst("SingleEditorFrameManager"):ResetSingleEditorBtn(false)
			GetInst("SingleEditorFrameManager"):setWidgetsVisible('singleEditorFrameRightTopTitle', false)
		end
		GetInst("SingleEditorFrameManager"):setWidgetsVisible("singleEditorFrameVarLibBtn", false)
		if HasUIFrame("DeveloperEditTrigger") and getglobal("DeveloperEditTrigger"):IsShown() then
			GetInst("SingleEditorFrameManager"):setWidgetsVisible("singleEditorFrameVarLibBtn", true)
		end
		
	elseif tabType == 200 then
		local devCtrl = GetInst("MiniUIManager"):GetCtrl("DevTools")
		if devCtrl then
			devCtrl:SetVarLibBtnVisible(true)
		end

		GetInst("MiniUIManager"):ShowUI("DebugFrameAutoGen")
		if tabIndex == 1 then
			GetInst("MiniUIManager"):HideUI("DebugFrameAutoGen")
			CloseAllDeveloperWindow()
		elseif tabIndex == 2 then
			OpenUIDeveloperTriggerView(uiinfo.uid)
		elseif tabIndex == 3 then 
			local handleType = ScriptSupportCtrl:makeSSType('coustomui',uiinfo.uid,1,nil,false)
			local mapId = PluginDeveloperGetMapID()
			local param = {}
			param.curType = handleType
			param.curMapId = mapId
			OpenDeveloperEditScriptWindow(param)
			CloseDeveloperEditTriggerWindow()
			CloseDeveloperQuickEntryWindow()
		end
	end
end

--关闭全部
function CloseAllDeveloperWindow()
	CloseDeveloperEditScriptWindow()
	CloseDeveloperEditTriggerWindow()
	CloseDeveloperQuickEntryWindow()
end

--开发者模式脚本编辑面板
function OpenDeveloperEditScriptWindow(param)
	SceneEditorUIInterface:OpenUI("EditScript","miniui/miniworld/DeveloperEditScript","EditScriptAutoGen", param)
end

function CloseDeveloperEditScriptWindow()
	GetInst("MiniUIManager"):CloseUI("EditScriptAutoGen")
end

--开发者模式触发器编辑面板
function OpenDeveloperEditTriggerWindow(param)
	GetInst("UIManager"):Open("DeveloperEditTrigger",param)
end

function CloseDeveloperEditTriggerWindow()
	GetInst("UIManager"):Close("DeveloperEditTrigger")
end

--开发者模式快捷入口面板
function OpenDeveloperQuickEntryWindow()
	local param = nil;
	if UGCModeMgr and UGCModeMgr:IsUGCMode() then
		param = {disableOperateUI = true}
	end
	GetInst("UIManager"):Open("DeveloperQuickEntry", param)
end

function CloseDeveloperQuickEntryWindow()
	local param = nil;
	if UGCModeMgr and UGCModeMgr:IsUGCMode() then
		param = {disableOperateUI = true}
	end
	GetInst("UIManager"):Close("DeveloperQuickEntry", param)
end

--开发者模式生物的编辑面板
function OpenDeveloperWindowByRole(isLeft,objType,objId,defId,teamId)
	local monsterDef
	local atktype = 0
	local atkmode = 0
	local showAttack = true
	-- 获取插件怪物或者直接用csv文件数据
	for i = 1, ModMgr:getMapModCount() do
		local mod = ModMgr:getMapModByIndex(i-1);
		monsterDef = mod:getMonsterDef(defId)
		-- 走插件的逻辑
		if monsterDef then
			if monsterDef.AttackType == 0 then
				atktype = 0
				if mod:getMonsterAIString(defId,"attack") == "" then
					showAttack = false
				end
			end
			if mod:getMonsterAIString(defId,"target_hurtee") ~= "" then
				atkmode = 0 
			elseif mod:getMonsterAIString(defId,"target_nearest") ~= "" then
				atkmode = 1
			end
		else
			-- 走csv的逻辑
			monsterDef = MonsterCsv:get(defId)
		end
	end
	if ModMgr:getMapModCount() == 0 then
		monsterDef = MonsterCsv:get(defId)
	end
	atktype = monsterDef.AttackType
	local param = {
		objType = objType,
		objId = objId,
		defId = defId,
		hp = monsterDef.Life,
		teamId = teamId,
		name = monsterDef.Name,
		monsterdef = monsterDef,	
		showattack = showAttack,		-- 是否显示 攻击
		attacktype = atktype,			-- 攻击类型：近战、远程、爆炸
		attackmode = atkmode			-- 攻击模式：被动攻击、主动攻击
	}
	-- 改成右键打开面板
	if EnableDeveloper then
		--if CurWorld and CurWorld.isGameMakerMode and CurWorld:isGameMakerMode() and not isLeft then
			SceneEditorUIInterface:OpenUI("ObjInfoEdit", "miniui/miniworld/ugc_objInfoEdit", "ObjInfoEditAutoGen", param)
			--GetInst("UIManager"):Open("DeveloperObjEdit",param)
			--StatisticsUseDeveloperObjEdit()
		--end
	end
end

function OpenBTreeForTest(btreeid)
	GetInst("UIManager"):Open("BTDebug", {disableOperateUI = true, btreeid = btreeid})
end

--开发者模式方块的编辑面板
function OpenDeveloperWindowByBlock(blockDef,posX,posY,posZ)
	local param =
	{
		objType = -1,
		posX = posX,
		posY = posY,
		posZ = posZ,
		objId = blockDef.ID,
		name = blockDef.Name,
        blockDef = blockDef
	}
	-- 改成右键打开面板
	if EnableDeveloper then
		--if CurWorld and CurWorld.isGameMakerMode and CurWorld:isGameMakerMode() then
			SceneEditorUIInterface:OpenUI("ObjInfoEdit", "miniui/miniworld/ugc_objInfoEdit", "ObjInfoEditAutoGen", param)
			--GetInst("UIManager"):Open("DeveloperObjEdit",param)
			--StatisticsUseDeveloperObjEdit()
		--end
	end
end

--[[使用手持编辑器 上报]]
function StatisticsUseDeveloperObjEdit()
	local staTime = getkv("last_use_developerObjEdit_timeStatistics") or 0
	local nowTime = os.date("%d", AccountManager:getSvrTime())
	if nowTime ~= staTime then
		-- statisticsGameEventNew(52016)
		setkv("last_use_developerObjEdit_timeStatistics", nowTime)
	end
end

-- 触发器红点数据 数据完整性及参数可用性
function GetGroupsTriggerCompleted(owid, curType)
	local groupList = ScriptSupportCtrl:getScriptList(owid, curType)
	if not groupList or not next(groupList) then return true end

	local groupCount = #groupList
	for groupIndex = 1, groupCount do 
		local groupData = groupList[groupIndex]
		for itemIndex = 1, #groupData do
			local itemData = groupData[itemIndex]
			local itemPrams = GetTriggerItemParams(itemData.event)
			--1/数据完整性->红点 2/参数可用性->叹号
			for childIdx = 1, #itemData.event do 
				local optionData = itemData.event[childIdx]
				if not GetTriggerCompleted(optionData,curType.modpacketid and curType.modpacketid or "") then return false end
			end
			for childIdx = 1, #itemData.condition do 
				local optionData = itemData.condition[childIdx]
				if not GetTriggerParamCanUse(optionData, itemPrams) then return false end
				if not GetTriggerCompleted(optionData,curType.modpacketid and curType.modpacketid or "") then return false end
			end
			for childIdx = 1, #itemData.action do 
				local optionData = itemData.action[childIdx]
				if not GetTriggerParamCanUse(optionData, itemPrams) then return false end
				if not GetTriggerCompleted(optionData,curType.modpacketid and curType.modpacketid or "") then return false end
			end
		end 
	end
	return true
end

-- 单条触发器的数据完整性
function GetTriggerCompleted(data,uuid)
	local isComplete = false
	local modpacketid = uuid and uuid or ""
	local function checkParam(param)
		local completeCount = 0
		for i = 1,#param do 
			local aParam = param[i]
			if type(aParam) ~= "table" then 
				completeCount = completeCount + 1
			else
				if aParam.factor == DeveloperEnumTypeID or aParam.factor == DeveloperVariableTypeID 
				or aParam.factor == DeveloperObjTypeID or aParam.factor == DeveloperTriggerTypeID 
				or aParam.factor == DeveloperScriptTypeID or aParam.factor == DeveloperDevGoodsFactorID then 
					completeCount = completeCount + 1
				elseif aParam.factor == DeveloperLibraryTypeID then -- 资源库查找资源是否找到
					local selecttype = aParam.param.factor -- 选择类型
					local selectID = aParam.param.param -- 选择ID
					if selecttype == 12 then --model
						--model
						if ScriptSupportFunc:IsModelDataComplete(selectID) then
							completeCount = completeCount + 1
						end
					else
						local data = nil
						local configName = ""
						local typename = ""
						if selecttype == 1 then
							configName = "itemdef"
							typename = "block"
						elseif selecttype == 9 or selecttype == 2 then -- 选择方块 / 装备 / 道具资源库
							configName = "itemdef"
							typename = "item"
						elseif selecttype == 4 then -- 选择生物库
							configName = "monster"
							typename = "actor"	
						elseif selecttype == 5 then -- 选择状态库
							configName = "buffdef"
							typename = "status"
							selectID = ScriptSupportFunc:Buff2Csvid(selectID) or selectID
						else
							if selectID > 0 then completeCount = completeCount + 1 end  --声音 特效 如果id存在直接+1
						end
						if modpacketid ~= "" then
							local PluginAllocate = SSMgrResource:GetPluginAllocate()
							if PluginAllocate then
								selectID = PluginAllocate:TransfomOrigin2Current(modpacketid,typename, selectID,true)
							end
							data = {IsGetModDef = true }
						end
						local itemdef = ScriptSupportFunc:GetCsvConfig(selectID,configName,data)
						if itemdef then completeCount = completeCount + 1 end
					end
				else
					local childParam = aParam.param
					if childParam and type(childParam) == "table" then
						if #childParam > 0 then
							local childComplete = checkParam(childParam)
							if childComplete then 
								completeCount = completeCount + 1 
							end 
						else
							--空参数，要看它是否真的没有参数，如果确实没有参数，才认为已经完成编辑
							local triggerFunctionDef = ScriptSupportFunc:getTriggerFunctionDef(aParam.factor)
							local hasParam = false
							if triggerFunctionDef then
								for i = 1, DeveloperParamMaxCount do 
									if triggerFunctionDef["Param" .. i] ~= nil and triggerFunctionDef["Param" .. i] ~= "" then 
										hasParam = true 
										break 
									end 
								end
							end
							if not hasParam then 
								completeCount = completeCount + 1
							end 
						end 
					end 
				end 
			end 
		end 
		if completeCount == #param then 
			return true 
		else
			return false 
		end 
	end 
	if data and data.factor and data.param then 
		if data.param and type(data.param) ~= "table" then 
			--是完整数据
			isComplete = true 
		else
			local triggerItemDef = ScriptSupportFunc:getTriggerItemDef(data.factor)
			local paramCount = 0
			if triggerItemDef ~= nil then
				for i = 1, DeveloperParamMaxCount do 
					local aParam = triggerItemDef["Param" .. i]
					if aParam and aParam ~= "" then 
						paramCount = paramCount + 1
					end 
				end
			end
			if #data.param < paramCount then 
				isComplete = false 
			else
				isComplete = checkParam(data.param)
			end  
		end 
	end 
	return isComplete
end

-- 插件包内插件参数 reqType=> {type="actor", ID=200}
function GetPluginHandleType(reqType, modUUID, scriptType)
	if not reqType then return end

	local typeId = reqType.ID
	local typeName = reqType.type
	if not typeId or not typeName then return end
	
	if not scriptType then scriptType = 2 end

	return ScriptSupportCtrl:makeSSType(typeName, typeId, scriptType, modUUID, true)
end 

-- 单条触发器的参数可用性
function GetTriggerParamCanUse(data, itemParams)
	-- 1、数据不存在,参数有问题,返回false
	if not data or not data.factor then return false end
	
	-- 2、填写参数全部为非事件参数则返回true
	local params = {} --触发器所用参数
	local allParams = TriggerEventAllParams
	local function getParamId(cParam)
		if type(cParam)~='table' then return end
		if type(cParam.param)~='table' then
			for idx = 1, #allParams do
				if cParam.param==allParams[idx] then
					--params[#params+1] = cParam.param
					return cParam.param
				end
			end
		else
			for idx = 1, #cParam.param do
				local subParam = cParam.param[idx]
				local paramVal = getParamId(subParam)
				if paramVal then params[#params+1] = paramVal end
			end
		end
	end
	local paramVal = getParamId(data)
	if paramVal then params[#params+1] = paramVal end
	if #params==0 then return true end

	-- 3、没有事件参数则返回false
	if not itemParams or #itemParams==0 then return false end

	-- 4、存在事件参数,进行匹配
	for idx = 1, #params do
		local isFind = false
		for jdx = 1, #itemParams do
			if params[idx]==itemParams[jdx] then
				isFind = true
			end
		end
		-- 在Event参数中找不到对应参数
		if not isFind then return false end
	end

	return true
end

-- 获取触发器的事件参数
function GetTriggerItemParams(itemEvents)
	-- 做个安全保护
	if type(itemEvents)~='table' then return {} end

	local result = {}
	-- 1、当前触发器存在触发事件
	for idx = 1, #itemEvents do
		local toggle = itemEvents[idx].toggle
		local factor = itemEvents[idx].factor
		-- 玩家启用该事件且事件对应的参数表存在
		if (toggle==nil or toggle==true) and factor then
			local itemDef = ScriptSupportFunc:getTriggerItemDef(factor)
			if itemDef and itemDef['Type']==1 then
				local strParams = itemDef['TriggerEventParams']
				local eventParams = StringSplit(strParams, "|")
				if type(eventParams)=='table' then --事件传参可能不存在
					for jdx = 1, #eventParams do --遍历获取事件中的传参
						local param = tonumber(eventParams[jdx])
						if param and param~=0 then table.insert(result, param) end
					end
				end
			end
		end
	end
	
	-- 2、其他触发器的执行触发器动作会产生事件参数
	-- 不是同个触发器里的此处不做校验 --
	return result
end

--触发器生物,区域,位置listview全局回调
function ToolObjLibBodyList1_tableCellAtIndex(tableView,index)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):tableCellAtIndex(tableView,index+1)
end

function ToolObjLibBodyList1_numberOfCellsInTableView(tableView)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):numberOfCellsInTableView(tableView)
end

function ToolObjLibBodyList1_tableCellSizeForIndex(tableView,index)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):tableCellSizeForIndex(tableView,index+1)
end

function ToolObjLibBodyList1_tableCellWillRecycle(tableView,cell)
	if cell then cell:Hide() end 
end

function ToolObjLibBodyList1_scrollViewDidScroll(tableView)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):scrollViewDidScroll(tableView);
end

function ToolObjLibBodyList2_tableCellAtIndex(tableView,index)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):tableCellAtIndex(tableView,index+1)
end

function ToolObjLibBodyList2_numberOfCellsInTableView(tableView)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):numberOfCellsInTableView(tableView)
end

function ToolObjLibBodyList2_tableCellSizeForIndex(tableView,index)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):tableCellSizeForIndex(tableView,index+1)
end

function ToolObjLibBodyList2_tableCellWillRecycle(tableView,cell)
	if cell then cell:Hide() end 
end

function ToolObjLibBodyList2_scrollViewDidScroll(tableView)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):scrollViewDidScroll(tableView);
end

function ToolObjLibBodyList3_tableCellAtIndex(tableView,index)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):tableCellAtIndex(tableView,index+1)
end

function ToolObjLibBodyList3_numberOfCellsInTableView(tableView)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):numberOfCellsInTableView(tableView)
end

function ToolObjLibBodyList3_tableCellSizeForIndex(tableView,index)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):tableCellSizeForIndex(tableView,index+1)
end

function ToolObjLibBodyList3_tableCellWillRecycle(tableView,cell)
	if cell then cell:Hide() end 
end

function ToolObjLibBodyList3_scrollViewDidScroll(tableView)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):scrollViewDidScroll(tableView);
end

function ToolObjLibBodyList4_tableCellAtIndex(tableView,index)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):tableCellAtIndex(tableView,index+1)
end

function ToolObjLibBodyList4_numberOfCellsInTableView(tableView)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):numberOfCellsInTableView(tableView)
end

function ToolObjLibBodyList4_tableCellSizeForIndex(tableView,index)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):tableCellSizeForIndex(tableView,index+1)
end

function ToolObjLibBodyList4_tableCellWillRecycle(tableView,cell)
	if cell then cell:Hide() end 
end

function ToolObjLibBodyList4_scrollViewDidScroll(tableView)
	return GetInst("UIManager"):GetCtrl("ToolObjLib"):scrollViewDidScroll(tableView);
end

--复制粘贴环境
-- evn = {
-- 	owid，--世界id
-- 	modPacketId,--插件包id
-- 	modId,--插件id
-- 	isNeedLeftValue --是要求必须是左值
-- }

DeveloperParamCopy = {

	m_data = {},

	clear = function(self)
		self.m_data = {};
	end,

	setClipboard = function(self,pType,pData)
		if type(pData) ~= 'table' then
			return false;
		end

		if pData.content == nil then
			return false;
		end

		if type(pData.content) == 'table' then
			if next(pData.content) == nil then
				return false;
			end
		end


		local stringType = tostring(pType);
		self.m_data[stringType] = copy_table(pData);
		return  true;
	end,

	getClipboard = function(self,pType,pEnv)
		local stringType = tostring(pType);
		if self.m_data[stringType] == nil then
			return nil;
		else

			local function emptyTable(pT)
				for k,v in pairs(pT) do
					pT[k] = nil;
				end
			end
			
			local function filter_Obj(pParam,pSrcEnv,pDesEnv)

				if pSrcEnv.owid == pDesEnv.owid then
					if pDesEnv.modId == "-1" or pSrcEnv.modId ~= "-1" then
						return;
					else
						emptyTable(pParam);
					end
				else
					emptyTable(pParam);
				end

				

				-- local paramDef = DefMgr:getTriggerParamDef(tonumber(pParam.paramtype))
				-- local ignorelist = StringSplit(paramDef and paramDef.ObjFilter,",")
				-- if ignorelist then
				-- 	ignorelist = StringSplit(ignorelist[2],"|")
				-- end
				-- local function checkIgnore(id)
				-- 	if ignorelist == nil then return true end
				-- 	for i = 1, #ignorelist do
				-- 		if tonumber(ignorelist[i]) == id then
				-- 			return false
				-- 		end
				-- 	end
				-- 	return true
				-- end

				-- if not checkIgnore(tonumber(pParam.param)) then
				-- 	emptyTable(pParam);
				-- end
			end

			local function filter_function(pParam,pSrcEnv,pDesEnv)

				if pDesEnv.modId == "-1" or pSrcEnv.modId ~= "-1" then
					return false;
				end 

				local triggerFunctionDef = ScriptSupportFunc:getTriggerFunctionDef(pParam.factor, true)
				if not triggerFunctionDef then 
					return false; 
				else
					local returnType = triggerFunctionDef.ReturnType
					local returnTypeTable = StringSplit(returnType,",")
					local localshow = returnTypeTable[3];
					if localshow and tonumber(localshow) == 0 then
						emptyTable(pParam);
						return true;
					else
						return false;
					end 
				end 
			end
			
			local function filter(pParam,pSrcEnv,pDesEnv)
				if type(pParam) == 'table' then
					

					local _factor = pParam.factor;
					if _factor == nil then

					elseif _factor == DeveloperEnumTypeID then

					elseif _factor == DeveloperScriptTypeID then

					elseif _factor == DeveloperVariableTypeID then

					elseif  _factor == DeveloperObjTypeID then
						filter_Obj(pParam,pSrcEnv,pDesEnv);
					elseif _factor == DeveloperTriggerTypeID then

					elseif _factor == DeveloperLibraryTypeID  then 

					elseif _factor == DeveloperDevGoodsFactorID then
						
					else
						if filter_function(pParam,pSrcEnv,pDesEnv) then
							return;
						else
							for i=1,#pParam.param do
								filter(pParam.param[i],pSrcEnv,pDesEnv);
							end
						end
					end
				else
					return;
				end
			end

			local _t = copy_table(self.m_data[stringType]);

			filter(_t.content,_t.env,pEnv);

			return _t;

		end
	end,

	isHave = function(self,pType)
		local stringType = tostring(pType);
		local _pasterData = self.m_data[stringType]
		if _pasterData then
			return true;
		else
			return false;
		end
	end,

	isEnabled = function(self,pType,pEnv)
		local stringType = tostring(pType);
		local _pasterData = self.m_data[stringType]
		local _errrorStr = GetS(13064)
		if _pasterData then
			--左值判断
			if pEnv.isNeedLeftValue then

				if type(_pasterData.content) ~= 'table' then
					return false,_errrorStr;
				end

				if type(_pasterData.content) == 'table' then
					--枚举值 不可以做左值
					local _factor = _pasterData.content.factor
					if _factor == DeveloperEnumTypeID then
						return false,_errrorStr;
					end

					if _factor == DeveloperEnumTypeID or _factor == DeveloperScriptTypeID
					or _factor == DeveloperVariableTypeID or _factor == DeveloperObjTypeID 
					or _factor == DeveloperTriggerTypeID or _factor == DeveloperLibraryTypeID
					or _factor == DeveloperDevGoodsFactorID then 

					else
						--函数类型
						local triggerFunctionDef = ScriptSupportFunc:getTriggerFunctionDef(_factor, true)
						if not triggerFunctionDef then 
							-- break 
						else
							local returnType = triggerFunctionDef.ReturnType
							local returnTypeTable = StringSplit(returnType,",")
							local returnReadOnly = returnTypeTable[2]
			
							if tonumber(returnReadOnly) == 1 then --右值
								return false,_errrorStr;
							end   
						end 
					end

				end
			end

			--触发器和触发器组类型
			if stringType == '1038' or stringType == '1039' then
				if _pasterData.env.owid ~= pEnv.owid or _pasterData.env.modPacketId ~= pEnv.modPacketId or _pasterData.env.modId ~= pEnv.modId then
					_errrorStr = GetS(13066)
					return false,_errrorStr;
				end
			end

			return true;
		else
			return false,_errrorStr;
		end
	end
}