#include "CraftMgr.h"
#include "backpack.h"
#include "ClientPlayer.h"
#include "container_crafting.h"
#include "CommonUtil.h"
#include "ActorManager.h"
#include "ClientItem.h"
//#include "ModManager.h"
#include "container_pot.h"
#include "container_world.h"
#include "DefManagerProxy.h"
#include "WorldManager.h"
#include "ActorLocoMotion.h"

#include "GameAnalytics.h"
#include "OgreUtils.h"
#include "ObserverEvent.h"
#include "ObserverEventManager.h"
#include "GameStatic.h"
#include "PlayerControl.h"
#include "SandboxIdDef.h"
#include "ModPackMgr.h"
#include "SandboxGame/Mgr/GunManager/GunSmithMgr.h"
#include "LuaCallHelper/LuaCallHelper.h"
#include <fstream>   // 文件流操作
#include <algorithm> // std::find
#include <vector>    // std::vector
#include <sstream>   // std::ostringstream
using namespace MNSandbox;
struct stResultCache
{
	bool bHasAnyItem = false;
	int nMiniNum = 0;
	Rainbow::MiniLua::LuaTable luaTab = nullptr;
};

static std::map<std::string, std::unordered_map<int, stResultCache>> s_ResultCaches;

CraftMgr::CraftMgr(PluginManager* p)
{
	m_pPluginManager = p;
	
}

CraftMgr::~CraftMgr()
{
	s_ResultCaches.clear();
}

bool CraftMgr::Awake()
{	
	m_pPluginManager->GetScriptVM()->setUserTypePointer("CraftMgr", "CraftMgr", this);

	CreateModuleEvent();
	return true;
}

bool CraftMgr::Init()
{
	//for (int i = 0; i < GetDefManagerProxy()->getItemNum(); i++)
	//{
	//	auto *def = GetDefManagerProxy()->getItemDef(i);
	//	if (!def)
	//		continue;
	//	if (def->ItemGroup != 0)
	//		m_GroupMaterial[def->ItemGroup].push_back(def);
	//}
	m_FavoriteSavePath = "crafting_favorites.bin";
	LoadFavorites(); // 初始化时加载已有收藏
	return true;
}

bool CraftMgr::Execute(float dtime)
{
	return true;
}

bool CraftMgr::Shut()
{
	unModuleEvent();
	return true;
}

void CraftMgr::OnLeaveWorld()
{
	s_ResultCaches.clear();
}

void CraftMgr::CreateModuleEvent()
{
}

void CraftMgr::unModuleEvent()
{
	std::map<std::string, MNSandbox::Callback>::iterator iter = m_callbackList.begin();
	for (; iter != m_callbackList.end(); iter++)
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe(iter->first, iter->second);
	}
	m_callbackList.clear();
}

int CraftMgr::GetCraftResult(int craftID, std::map<int, int> &backpackData, std::string* strHex)
{
	//auto def = GetDefManagerProxy()->getCraftingDef(craftID);
	//if (!def) return 0;

	//std::map<int, int> materials;
	//GetCraftMap(craftID, materials);

	//std::map<int, int> itemCount;
	//auto materialIter = materials.begin();
	//while (materialIter != materials.end())
	//{
	//	auto backpackIter = backpackData.begin();
	//	while (backpackIter != backpackData.end())
	//	{
	//		if (backpackIter->first == materialIter->first)
	//		{
	//			itemCount[backpackIter->first] += backpackIter->second;
	//		}
	//		else
	//		{
	//			if (def->IsGroup)
	//			{
	//				auto itemDef = GetDefManagerProxy()->getItemDef(backpackIter->first);
	//				if (itemDef &&
	//					itemDef->ItemGroup > 0 &&
	//					itemDef->ItemGroup == materialIter->first)
	//					itemCount[materialIter->first] += backpackIter->second;
	//			}
	//		}
	//		backpackIter++;
	//	}
	//	materialIter++;
	//}
	//materialIter = materials.begin();
	//int minNum = 999;
	//bool hasAnyItem = false;
	//while (materialIter != materials.end())
	//{
	//	if (materialIter->second != 0)
	//	{
	//		minNum = MINIW::Min(minNum, itemCount[materialIter->first] / materialIter->second);
	//	}
	//	if (itemCount[materialIter->first] > 0)
	//		hasAnyItem = true;
	//	materialIter++;
	//}
	//if (!hasAnyItem)
	//	return -1;	// û����һ�ֺϳɲ��ϲ���ʾ
	//return minNum;
	return BaseGetResult(craftID, backpackData, 1, strHex);
}

int CraftMgr::GetCookBookResult(int craftID, std::map<int, int>& backpackData)
{
	return BaseGetResult(craftID, backpackData, 2);
}

int CraftMgr::BaseGetResult(int craftID, std::map<int, int>& backpackData, int nType, std::string* strHex)
{
	/*std::vector<int> firstVec;
	std::vector<int> secondVec;
	for (auto iter = backpackData.begin(); iter != backpackData.end(); iter++)
	{
		firstVec.push_back(iter->first);
		secondVec.push_back(iter->second);
	}

	int minNumLua = 0;
	GetCoreLuaDirector().CallFunctionM("LuaCraftMgr", funname.c_str(), "iu[std::vector<int>]u[std::vector<int>]>i", craftID, &firstVec, &secondVec, &minNumLua);

	return minNumLua;*/
	if (strHex)
	{
		// 检查缓存
		auto ite = s_ResultCaches.find(*strHex);
		if (ite != s_ResultCaches.end())
		{
			auto ite2 = ite->second.find(craftID);
			if (ite2 != ite->second.end())
			{
				MINIW::LuaCallHelper::GetInstance().InstFuncCall("LuaCraftMgr", "addSaveData", ite2->second.luaTab);
				if (!ite2->second.bHasAnyItem)
				{
					return -1;
				}
				return ite2->second.nMiniNum;
			}
		}
	}
	CraftingDef* def = nullptr;
	if (nType == 1)
	{
		def = GetDefManagerProxy()->getCraftingDef(craftID);
	}
	else if (nType == 2)
	{
		def = GetDefManagerProxy()->getCookbookDef(craftID);
	}
	else
		return -1;
	if (!def)
		return -1;
	int resultID = def->ResultID;
	std::map<int, int> materials;
	GetCraftMap(def, materials);
	std::map<int, int> itemCount;
	int nMiniNum = 999;
	bool bHasAnyItem = false;
	MINIW::LuaTableBuilder& tabBuilder = MINIW::LuaCallHelper::GetInstance().GetTableBuilder();
	std::vector<Rainbow::MiniLua::LuaTable> vetMatLuaTabs;
	for (auto iteMat = materials.begin(); iteMat != materials.end(); ++iteMat)
	{
		int nMatId = iteMat->first;
		int nMatCount = iteMat->second;
		for (auto iteBpData = backpackData.begin(); iteBpData != backpackData.end(); ++iteBpData)
		{
			if (iteBpData->first == nMatId)
			{
				if (itemCount.find(nMatId) == itemCount.end())
				{
					itemCount[nMatId] = iteBpData->second;
				}
				else
				{
					itemCount[nMatId] += iteBpData->second;
				}
			}
			else if (def->IsGroup)
			{
				auto itemDef = GetDefManagerProxy()->getItemDef(iteBpData->first);
				if (itemDef && itemDef->ItemGroup > 0 && itemDef->ItemGroup == nMatId)
				{
					if (itemCount.find(nMatId) == itemCount.end())
					{
						itemCount[nMatId] = iteBpData->second;
					}
					else
					{
						itemCount[nMatId] += iteBpData->second;
					}
				}
			}
		}

		int nItemCount = itemCount.find(nMatId) != itemCount.end() ? itemCount[nMatId] : 0;
		if (nMatCount > 0) {
			nMiniNum = std::min(nMiniNum, nItemCount / nMatCount);
		}
		if (nItemCount > 0)
		{
			bHasAnyItem = true;
		}
		tabBuilder << "id" << nMatId << "count" << nMatCount;
		vetMatLuaTabs.push_back(tabBuilder.Get());
		tabBuilder.Reset();
	}
	Rainbow::MiniLua::LuaTable matLuaTab;
	tabBuilder.Reset(MINIW::LuaTableBuilder::pushForArray);
	for (auto& matLuaTab : vetMatLuaTabs)
	{
		tabBuilder << matLuaTab;
	}
	matLuaTab = tabBuilder.Get();
	tabBuilder.Reset();
	tabBuilder << "craftID" << craftID;
	tabBuilder << "minNum" << nMiniNum;
	tabBuilder << "hasAnyItem" << bHasAnyItem;
	tabBuilder << "sort" << def->DisplayOrder;
	tabBuilder << "resultID" << resultID;
	tabBuilder << "materials" << matLuaTab;

	// 怼到 CraftMgr.lua 里面去
	MINIW::LuaCallHelper::GetInstance().InstFuncCall("LuaCraftMgr", "addSaveData", tabBuilder.Get());

	if (strHex)
	{
		// 缓存一下
		auto ite = s_ResultCaches.find(*strHex);
		if (ite == s_ResultCaches.end())
		{
			if (s_ResultCaches.size() > 3)
			{
				// 保持最大3个缓存表
				s_ResultCaches.erase(s_ResultCaches.begin());
			}
			s_ResultCaches[*strHex];
			ite = s_ResultCaches.find(*strHex);
		}
		stResultCache cache;
		cache.bHasAnyItem = bHasAnyItem;
		cache.nMiniNum = nMiniNum;
		cache.luaTab = tabBuilder.Get();
		ite->second[craftID] = cache;
	}

	if (!bHasAnyItem)
	{
		return -1;
	}
	return nMiniNum;
}

void CraftMgr::GetCraftMap(const CraftingDef* def, std::map<int, int>& maplist)
{
	if (def)
	{
		for (int i = 0; i < 9; ++i)
		{
			if (def->MaterialID[i] <= 0)
				continue;
			if (maplist.find(def->MaterialID[i]) != maplist.end())
			{
				maplist[def->MaterialID[i]] += def->MaterialCount[i];
			}
			else
			{
				maplist[def->MaterialID[i]] = def->MaterialCount[i];
			}
		}
	}
}

bool CraftMgr::isCanUpdateCraftTable()
{
	if (!g_pPlayerCtrl || !g_pPlayerCtrl->getWorld()) return false;
	int toolid = g_pPlayerCtrl->getCurToolID();
	if (toolid == CRAFT_UPDATE_ITEM && g_pPlayerCtrl->m_PickResult.intersect_block)
	{
		Block block = g_pPlayerCtrl->getWorld()->getBlock(g_pPlayerCtrl->m_PickResult.block);
		if (block.getResID() == STONE_CRAFT) // 升级材料判断
		{
			return true;
		}
	}
	return false;
}
void CraftMgr::GetCraftMaterialVector(int craftID, std::vector<std::pair<int, int>>& vec)
{
	//std::map<int, int> mapList;
	//GetCraftMap(craftID, mapList);
	//std::for_each(mapList.begin(), mapList.end(), [&](std::pair<int, int> p) {
	//	vec.push_back(p); 
	//});
	BaseGetMaterialVector(craftID, vec, "getCraftMaterialVector");
}
void CraftMgr::GetCookBookMaterialVector(int craftID, std::vector<std::pair<int, int>>& vec)
{
	BaseGetMaterialVector(craftID, vec, "getCookBookMaterialVector");
}

int CraftMgr::GetCookBookRealMaterialVector(int craftID, const std::map<int, int>& backpackData, int num, std::vector<std::pair<int, int>>& vec)
{
	char strjson[1024] = {0};
	jsonxx::Object backobj;
	for (auto iter = backpackData.begin(); iter != backpackData.end(); iter++)
	{
		backobj.import(to_string((int64_t)(iter->first)), iter->second);
	}

	GetCoreLuaDirector().CallFunctionM("LuaCraftMgr", "getCookBookRealMaterialVector", "isi>s", craftID, backobj.json().c_str(), num, strjson);
	jsonxx::Object obj;
	int count = -1;
	if (obj.parse(strjson))
	{
		count =obj.get<jsonxx::Number>("resultcount");
		jsonxx::Object items = obj.get<jsonxx::Object>("items");
		const auto& temp = items.kv_map();
		for (auto iter = temp.begin(); iter != temp.end(); iter++)
		{
			const std::string keystr = iter->first;
			jsonxx::Value* value = iter->second;
			vec.push_back(std::make_pair(atoi(keystr.c_str()), (int)(value->number_value_)));
		}
	}
	return  count;
}


void CraftMgr::BaseGetMaterialVector(int craftID, std::vector<std::pair<int, int>>& vec, const std::string& funname)
{

	char strjson[1024] = "";
	GetCoreLuaDirector().CallFunctionM("LuaCraftMgr", funname.c_str(), "i>s", craftID, strjson);

	jsonxx::Object obj;
	if (obj.parse(strjson))
	{
		const auto& temp = obj.kv_map();
		for (auto iter = temp.begin(); iter != temp.end(); iter++)
		{
			const std::string keystr = iter->first;
			jsonxx::Value* value = iter->second;
			vec.push_back(std::make_pair(atoi(keystr.c_str()), (int)(value->number_value_)));
		}
	}
}


int CraftMgr::GetCookingSpeedPerTick(int craftID, int fireState, int curCountdownTick)
{
	//auto *craftDef = GetDefManagerProxy()->getCraftingDef(craftID);
	//if (!craftDef)
	//	return 0;
	//if (curCountdownTick == -1)
	//	curCountdownTick = craftDef->CookingTick;
	//int result = 0;
	//int heatRate = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.tick_rate_small;
	//if ((WorldBonFireType)fireState == FIRE_MEDIUM)
	//	heatRate = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.tick_rate_medium;
	//if ((WorldBonFireType)fireState == FIRE_BIG)
	//	heatRate = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.tick_rate_big;
	//result = curCountdownTick * 100 / heatRate;
	//return result;

	int nResult = 0;
	GetCoreLuaDirector().CallFunctionM("LuaCraftMgr", "getCookingSpeedPerTick", "iii>i", craftID, fireState, curCountdownTick, &nResult);
	return nResult;
}

bool CraftMgr::CanAddToCookProduct(const CraftingDef * def, int level, int baseIndex)
{
	if (!def)
		return false;
	auto* tooldef = GetDefManagerProxy()->getToolDef(def->CraftingItemID);
	if (!tooldef)
		return false;
	if (tooldef->Type != 28)
		return false;
	if (level >= tooldef->Level)
		return true;
	return false;

	/*bool bResult = false;
	GetCoreLuaDirector().CallFunctionM("LuaCraftMgr", "canAddToCookProduct", "u[CraftingDef]ii>b", def, level, baseIndex, &bResult);
	return bResult;*/
}

bool CraftMgr::CanAddToProduct(const CraftingDef * def, int level, int baseIndex)
{
	//if (!def)
	//	return false;
	//auto *tooldef = GetDefManagerProxy()->getToolDef(def->CraftingItemID);
	//if (!tooldef)
	//	return false;
	//if (level >= tooldef->Level)
	//	return true;
	//return false;

	bool bResult = false;
	GetCoreLuaDirector().CallFunctionM("LuaCraftMgr", "canAddToProduct", "u[CraftingDef]ii>b", def, level, baseIndex, &bResult);
	return bResult;
}

int CraftMgr::GetSameGroupItemNum(int groupID)
{
	//if (m_GroupMaterial.find(groupID) != m_GroupMaterial.end())
	//	return m_GroupMaterial[groupID].size();
	//return 0;

	int nResult = 0;
	GetCoreLuaDirector().CallFunctionM("LuaCraftMgr", "getSameGroupItemNum", "i>i", groupID, &nResult);
	return nResult;
}

int CraftMgr::GetSameGroupItemID(int groupID, int index)
{
	//if (m_GroupMaterial.find(groupID) != m_GroupMaterial.end())
	//{
	//	if (index >= 0 && index < m_GroupMaterial[groupID].size())
	//		return m_GroupMaterial[groupID][index]->ID;
	//}
	//return 0;

	int nResult = 0;
	GetCoreLuaDirector().CallFunctionM("LuaCraftMgr", "getSameGroupItemID", "ii>i", groupID, index, &nResult);
	return nResult;
}

// 核心收藏功能
void CraftMgr::ToggleFavorite(int craftID) {
	auto it = std::find(m_FavoriteVector.begin(), m_FavoriteVector.end(), craftID);
	if (it != m_FavoriteVector.end()) {
		m_FavoriteVector.erase(it);
		m_FavoriteCache.erase(craftID);
	}
	else {
		m_FavoriteVector.push_back(craftID);
		m_FavoriteCache.insert(craftID);
	}
	SaveFavorites();
}


bool CraftMgr::IsFavorite(int craftID) const {
	return m_FavoriteCache.find(craftID) != m_FavoriteCache.end();
}

const std::vector<int>& CraftMgr::GetFavorites() const {
	return m_FavoriteVector;
}

// 修改SaveFavorites函数
void CraftMgr::SaveFavorites() {
	std::ofstream file(m_FavoriteSavePath, std::ios::binary | std::ios::trunc); // 添加trunc模式
	if (file.is_open()) {
		try {
			// 写入文件头
			constexpr char kFileHeader[4] = { 'F','A','V','2' };
			file.write(kFileHeader, sizeof(kFileHeader));

			// 写入数据
			const size_t count = m_FavoriteVector.size();
			file.write(reinterpret_cast<const char*>(&count), sizeof(count));

			if (count > 0) {
				file.write(reinterpret_cast<const char*>(m_FavoriteVector.data()),
					count * sizeof(int));
			}

			file.flush(); // 强制刷新缓冲区
		}
		catch (const std::exception& e) {
			//LOG_ERROR("Save favorites failed: {}", e.what());
		}
		file.close(); // 显式关闭文件
	}
}

// 修改LoadFavorites函数
void CraftMgr::LoadFavorites() {
	std::ifstream file(m_FavoriteSavePath, std::ios::binary);
	if (file.is_open()) {
		try {
			// 验证文件头
			char header[4];
			if (!file.read(header, sizeof(header))) {
				throw std::runtime_error("Failed to read file header");
			}

			if (memcmp(header, "FAV2", 4) != 0) {
				throw std::runtime_error("Invalid file format");
			}

			// 读取数据量
			size_t count = 0;
			if (!file.read(reinterpret_cast<char*>(&count), sizeof(count))) {
				throw std::runtime_error("Failed to read item count");
			}

			// 读取数据
			m_FavoriteVector.resize(count);
			if (count > 0) {
				if (!file.read(reinterpret_cast<char*>(m_FavoriteVector.data()),
					count * sizeof(int))) {
					throw std::runtime_error("Failed to read favorite data");
				}
			}

			// 重建缓存
			m_FavoriteCache.clear();
			m_FavoriteCache.insert(m_FavoriteVector.begin(), m_FavoriteVector.end());
		}
		catch (const std::exception& e) {
			//LOG_ERROR("Load favorites failed: {}", e.what());
			m_FavoriteVector.clear();
			m_FavoriteCache.clear();
		}
		file.close(); // 显式关闭文件
	}
}

bool CraftHelper::PlayerDoCraft(ClientPlayer* player, int craftID, int resultNum/*=1*/, int* remainNum /*= NULL*/)
{
	BackPack* backpack = player->getBackPack();
	PackContainer* backpackContainer = (PackContainer*)backpack->getContainer(BACKPACK_START_INDEX);
	PackContainer* shortcutContainer = (PackContainer*)backpack->getContainer(backpack->getShortcutStartIndex());

	//����ȡ���������ǶԵ�
	const CraftingDef* craDef = GetDefManagerProxy()->getCraftingDef(craftID);
	if (!GetDefManagerProxy()->checkCrcCode(CRCCODE_CRAFTING))
	{
		//ge GetGameEventQue().postInfoTips(165);
		CommonUtil::GetInstance().PostInfoTips(165);
		return false;
	}

	if (craDef == NULL) return false;
	//û�������ܺϳ�
	auto itemDef = GetDefManagerProxy()->getItemDef(craDef->ResultID);
	if (itemDef &&
		itemDef->CondUnlcokType > 0 &&
		!GetWorldManagerPtr()->isUnlockItem(itemDef->CondUnlcokType))
		return false;
	if (!CraftHelper::CheckCraftingMaterial(backpack, craftID, resultNum))
		return false;

	//使用道具id（如：800不完整工匠台，797完整工匠台, 枪械101, 装备102）
	int craftItemID = craDef->CraftingItemID;
	//合成道具id
	int resultID = craDef->ResultID;
	//是否枪械/装备制作
	bool isEquipMake = false;
	//额外加成数量
	int extraNum = 0;
	//额外加成tips
	int addStringId = 0;
	//额外降低消耗比例
	float extraReduce = 0;
	//额外降低消耗tips
	int consumeStringId = 0;
	//装备类型:1枪械 2防具
	int equipType = 0;
	//是否军械所或者装备工厂
	bool isNewToolStand = false;
	std::string resId = g_ModPackMgr->GetResIdByCfgId(CustomModType::Mod_Item, resultID);
	if (resId != "" && (craftItemID == 101 || craftItemID == 102 || craftItemID == 800 || craftItemID == 797 || craftItemID == 11000))
	{
		//101军械所 102装备工厂
		if (craftItemID == 101 || craftItemID == 102)
		{
			isNewToolStand = true;
		}

		int moneyID = craDef->MoneyID;
		int moneyCount = craDef->MoneyCount;
		if (moneyID == 101 && moneyCount > 0)
		{
			moneyCount = moneyCount * resultNum;
			int succ = -1;
			MINIW::ScriptVM::game()->callFunction("DeducteMcoin", "ii>i", player->getUin(), moneyCount, &succ);
			if (succ != 0)
			{
				player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 80539);
				return false;
			}
		}

		ItemDef* def = GetDefManagerProxy()->getItemDef(resultID);
		if (def)
		{
			equipType = 1;
			if (def->IsDefCustomGun)
			{
				equipType = 1;
				isEquipMake = true;
			}
			else if (def->IsDefEquip)
			{
				equipType = 2;
				isEquipMake = true;
			}

			//工作词条只有在军械所或者装备工厂生效
			if (isNewToolStand)
			{
				float addRate = 0;
				MINIW::ScriptVM::game()->callFunction("GetEquipMakeExtraRate", "i>fi", equipType, &addRate, &addStringId);
				float consumeRate = 0;
				MINIW::ScriptVM::game()->callFunction("GetEquipMakeConsumeRate", "i>fi", equipType, &consumeRate, &consumeStringId);

				if (addRate > 0)
				{
					srand(time(0));
					addRate = addRate * 100;
					int iRate = (int)addRate;
					for (int i = 0; i < resultNum; i++)
					{
						int randNum = rand() % 10001;
						if (iRate >= randNum)
						{
							extraNum = extraNum + 1;
						}
					}
				}
				if (consumeRate > 0)
				{
					extraReduce = consumeRate;
				}
			}		
		}
	}

	int num = craDef->ResultCount * resultNum;
	int playerUin = player->getUin();

	std::vector<int> changedGridIndex;
	for (int i = 0; i < 9; i++)
	{
		int craftMatterId = craDef->MaterialID[i];
		int craftMatterNeed = craDef->MaterialCount[i] * resultNum;
		int craftContainerId = craDef->ContainerID[i];

		//枪械和装备的材料消耗减免
		if (isNewToolStand && extraReduce > 0)
		{
			craftMatterNeed = (int)ceil(craftMatterNeed * (100 - extraReduce) / 100.0f);
		}

		if (craftMatterId <= 0) continue;

		for (size_t j = 0; j < backpackContainer->m_Grids.size(); j++)
		{
			BackPackGrid& backpackGrid = backpackContainer->m_Grids[j];
			if (NULL == backpackGrid.def) continue;

			int defID = backpackGrid.def->ID;
			if (craDef->IsGroup && backpackGrid.def->ItemGroup > 0)
			{
				defID = backpackGrid.def->ItemGroup;
			}

			bool isappropriate = false;
			if (craDef->IsGroup && backpackGrid.def->ItemGroup > 0 && craftMatterId > 0)
			{
				ItemDef* mdef = GetDefManagerProxy()->getItemDef(craftMatterId);
				assert(mdef != NULL);
				if (mdef && mdef->ItemGroup == defID)
				{
					isappropriate = true;
				}
			}
			else if (craftMatterId == defID)
			{
				isappropriate = true;
			}
			if (isappropriate)
			{
				if (craftMatterNeed > 0 && backpackGrid.getNum() > 0)
				{
					// changed
					if (std::find(changedGridIndex.begin(), changedGridIndex.end(), backpackGrid.getIndex()) == changedGridIndex.end())
					{
						changedGridIndex.push_back(backpackGrid.getIndex());
					}
				}
				if (craftMatterNeed < backpackGrid.getNum())
				{
					backpackGrid.addNum(-craftMatterNeed);
					craftMatterNeed = 0;
				}
				else
				{
					craftMatterNeed -= backpackGrid.getNum();
					if (craftContainerId > 0)
					{
						auto def = GetDefManagerProxy()->getItemDef(craftContainerId);
						assert(def != NULL);
						backpackGrid.setItemDef(def);
						//backpackGrid.def = def;
					}
					else
					{
						backpackGrid.clear();
					}
				}
			}
		}

		if (0 < craftMatterNeed)				//����û��������Ʒ ���Ŀ������
		{
			for (size_t k = 0; k < shortcutContainer->m_Grids.size(); k++)
			{
				BackPackGrid& shortcurGrid = shortcutContainer->m_Grids[k];
				if (0 == shortcurGrid.def) continue;

				int defID = shortcurGrid.def->ID;
				if (craDef->IsGroup && shortcurGrid.def->ItemGroup > 0) defID = shortcurGrid.def->ItemGroup;

				bool isappropriate = false;
				if (craDef->IsGroup && shortcurGrid.def->ItemGroup > 0 && craftMatterId > 0)
				{
					ItemDef* mdef = GetDefManagerProxy()->getItemDef(craftMatterId);
					assert(mdef != NULL);
					if (mdef && mdef->ItemGroup == defID)
					{
						isappropriate = true;
					}
				}
				else if (craftMatterId == defID)
				{
					isappropriate = true;
				}
				if (isappropriate)
				{
					if (craftMatterNeed > 0 && shortcurGrid.getNum() > 0)
					{
						// changed
						if (std::find(changedGridIndex.begin(), changedGridIndex.end(), shortcurGrid.getIndex()) == changedGridIndex.end())
						{
							changedGridIndex.push_back(shortcurGrid.getIndex());
						}
					}
					if (craftMatterNeed < shortcurGrid.getNum())
					{
						shortcurGrid.addNum(-craftMatterNeed);
						break;
					}
					else
					{
						craftMatterNeed -= shortcurGrid.getNum();

						if (craftContainerId > 0)
						{
							auto def = GetDefManagerProxy()->getItemDef(craftContainerId);
							assert(def != NULL);
							shortcurGrid.setItemDef(def);
							//shortcurGrid.def = def;
						}
						else
						{
							shortcurGrid.clear();
						}
					}
				}
			}
		}
		if (0 == craftMatterNeed)
		{
			if (craftContainerId > 0)
			{
				player->gainItems(craftContainerId, craftMatterNeed, 1);
			}
		}
	}

	for (int i = 0; i < (int)changedGridIndex.size(); ++i)
	{
		backpack->afterChangeGrid(changedGridIndex[i]);
	}

	int addedNum = 0;
	if (isEquipMake)
	{
		if (isNewToolStand && extraNum > 0)
		{
			//额外加成
			num = num + extraNum;
		}
		//枪械/装备合成
		addedNum = GunSmithMgr::GetInstance().MakeEquipOrGunForPlayer(resId, num, player);
		int remNum = num - addedNum;
		if (remainNum != NULL)
		{
			*remainNum = remNum;
		}
	}
	else
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(resultID);
		GridCopyData data;

		if (tooldef && tooldef->Enchant > 0)
		{
			data.resid = resultID;
			data.num = num;
			data.enchantnum = 1;
			data.enchants = &tooldef->Enchant;
			addedNum = backpack->addItem_byGridCopyData(data, 1);
			//addedNum = backpack->addItem(resultID, num, 1, 1, &tooldef->Enchant);
		}
		else if (tooldef && tooldef->Tunestone > 0)
		{
			data.resid = resultID;
			data.num = num;
			data.tunestonenum = 1;
			data.tunestones = &tooldef->Tunestone;
			addedNum = backpack->addItem_byGridCopyData(data, 1);
			//addedNum = backpack->addItemWithTunestone(resultID, num, 1, 1, &tooldef->Tunestone);
		}
		else
		{
			if (isNewToolStand && extraNum > 0)
			{
				//额外加成
				num = num + extraNum;
			}
			addedNum = backpack->addItem(resultID, num, 1);
		}
		if (remainNum != NULL)
		{
			*remainNum = num - addedNum;
		}
	}


	player->addAchievement(1, ACHIEVEMENT_CRAFTITEM, resultID, num);
	player->updateTaskSysProcess(TASKSYS_CRAFT_ITEM, resultID, 0, num);
	player->updateTaskSysProcess(TASKSYS_GAIN_ITEM, resultID, 0, num);//renjie 加工获得的物品也要能完成拾取的任务
	if (resultID == 11627)//拾取石料改为拾取或合成石料也可以完成成就
	{
		player->addAchievement(1, ACHIEVEMENT_PICKITEM, resultID, num);
	}
	player->addOWScore(craDef->Score);

	if (num - addedNum > 0)
	{
		int h = player->getThrowItemHeight();
		auto am = player->getActorMgr();
		if (am == NULL) return false;

		ClientItem* item = NULL;
		if (isEquipMake)
		{
			for (int j = 0; j < num - addedNum; ++j)
			{
				ClientItem* item = am->spawnEquipOrGun(player->getPosition() + WCoord(0, h, 0), resultID);
				if (item != NULL) {
					Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
					Rainbow::Vector3f dir;

					float r = 30.0f;
					PitchYaw2Direction(dir, player->getLocoMotion()->m_RotateYaw,
						player->getLocoMotion()->m_RotationPitch);
					motion.x = dir.x * r;
					motion.z = dir.z * r;
					motion.y = dir.y * r + 10.0f;

					r = 2.0f * GenRandomFloat();
					float angle = GenRandomFloat() * 360.0f;

					motion.x += r * CosByAngle(angle);
					motion.z += r * SinByAngle(angle);
					motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
				}
			}
		}
		else
		{
			ClientItem* item = am->spawnItem(player->getPosition() + WCoord(0, h, 0), resultID, num - addedNum);
			if (item == NULL) return false;

			Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
			Rainbow::Vector3f dir;

			float r = 30.0f;
			PitchYaw2Direction(dir, player->getLocoMotion()->m_RotateYaw,
				player->getLocoMotion()->m_RotationPitch);
			motion.x = dir.x * r;
			motion.z = dir.z * r;
			motion.y = dir.y * r + 10.0f;

			r = 2.0f * GenRandomFloat();
			float angle = GenRandomFloat() * 360.0f;

			motion.x += r * CosByAngle(angle);
			motion.z += r * SinByAngle(angle);
			motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
		}
	}

	if (player && isNewToolStand)
	{
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 80561);
		// 上报装备合成 28
		MINIW::ScriptVM::game()->callFunction("GeneralTaskReportedForCpp", "iisi", player->getUin(), 28, resId.c_str(), num);
		// 上报获得物资 23
		MINIW::ScriptVM::game()->callFunction("GeneralTaskReportedForCpp", "iisi", player->getUin(), 23, resId.c_str(), num);

		if (extraNum > 0)
		{
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, addStringId);
		}
		if (extraReduce > 0)
		{
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, consumeStringId);
		}
	}

	if (player) {
		ObserverEvent obevent;
		obevent.SetData_EventObj((long long)player->getObjId());
		obevent.SetData_Craft(craftID);
		obevent.SetData_Item(resultID, craDef->ResultCount);
		GetObserverEventManager().OnTriggerEvent("Craft.end", &obevent);
	}
	return true;
}

bool CraftHelper::CheckCraftingMaterial(BackPack * backpack, int craftID, int mergeCount)
{
	PackContainer *backpackContainer = (PackContainer*)backpack->getContainer(BACKPACK_START_INDEX);
	PackContainer *shortcutContainer = (PackContainer*)backpack->getContainer(backpack->getShortcutStartIndex());

	const CraftingDef *craDef = GetDefManagerProxy()->getCraftingDef(craftID);
	if (craDef == NULL) return false;
	CraftingDef def = *craDef;
	for (int i = 0; i < 9; ++i)
	{
		for (int j = i + 1; j < 9; ++j)
		{
			if (def.MaterialID[i] == def.MaterialID[j])
			{
				def.MaterialCount[i] += def.MaterialCount[j];
				def.MaterialID[j] = 0;
				def.MaterialCount[j] = 0;
			}
		}
		def.MaterialCount[i] = def.MaterialCount[i] * mergeCount;
	}
	for (size_t i = 0; i < backpackContainer->m_Grids.size(); ++i)
	{
		BackPackGrid &backpackGrid = backpackContainer->m_Grids[i];
		if (backpackGrid.def == nullptr) continue;
		int itemId = backpackGrid.def->ID;
		int itemNum = backpackGrid.getNum();
		if (def.IsGroup && backpackGrid.def->ItemGroup > 0)
		{
			itemId = backpackGrid.def->ItemGroup;
		}
		if (itemId > 0)	for (int j = 0; j < 9; ++j)
		{
			if (def.IsGroup && backpackGrid.def->ItemGroup > 0 && def.MaterialID[j] > 0)
			{
				ItemDef* mdef = GetDefManagerProxy()->getItemDef(def.MaterialID[j]);
				assert(mdef != NULL);
				if (mdef && mdef->ItemGroup == itemId)
				{
					def.MaterialCount[j] -= itemNum;
				}
			}
			else if (itemId == def.MaterialID[j])
			{
				def.MaterialCount[j] -= itemNum;
			}
		}
	}
	for (size_t i = 0; i < shortcutContainer->m_Grids.size(); ++i)
	{
		BackPackGrid &backpackGrid = shortcutContainer->m_Grids[i];
		if (backpackGrid.def == nullptr) continue;
		int itemId = backpackGrid.def->ID;
		int itemNum = backpackGrid.getNum();
		if (def.IsGroup && backpackGrid.def->ItemGroup > 0)
		{
			itemId = backpackGrid.def->ItemGroup;
		}
		if (itemId > 0)
		{
			for (int j = 0; j < 9; ++j)
			{
				if (def.IsGroup && backpackGrid.def->ItemGroup > 0 && def.MaterialID[j] > 0)
				{
					ItemDef* mdef = GetDefManagerProxy()->getItemDef(def.MaterialID[j]);
					assert(mdef != NULL);
					if (mdef && mdef->ItemGroup == itemId)
					{
						def.MaterialCount[j] -= itemNum;
					}
				}
				else
				{
					if (itemId == def.MaterialID[j])
					{
						def.MaterialCount[j] -= itemNum;
					}
				}
			}
		}
	}

	for (int i = 0; i < 9; ++i)
	{
		if (def.MaterialID[i] > 0 && def.MaterialCount[i] > 0)
			return false;
	}

	return true;
}

bool CraftHelper::CheckRecipeUnlockNum(ClientPlayer * player)
{
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_CraftHelper_CheckRecipeUnlockNum", SandboxContext(NULL).SetData_Usertype("player", player));
	if (result.IsSuccessed()){
		if (result.GetData_Bool()){
			return true;
		}
	}
	for (int i = 0; i < GetDefManagerProxy()->getCraftingDefNum(); i++)	//����crafting.csv����
	{
		const CraftingDef *def = GetDefManagerProxy()->getCraftingDefByIndex(i);
		auto itemDef = GetDefManagerProxy()->getItemDef(def->ResultID);
		if (def->containNum(4) &&
			itemDef &&
			itemDef->CondUnlcokType == -1 &&
			player->isUnlockItem(itemDef->ID)) 
		{
			return true;
		}
	}
	return false;
}
int CraftHelper::GetPlayerCanCraftNumAndAnyItem(ClientPlayer * player, int craftID, std::map<int, int>* pBackpackData/* = nullptr*/, std::string* pStrHex/* = nullptr*/)
{
	int retval = 0;
	CraftMgr* craftModule = GET_SUB_SYSTEM(CraftMgr);
	if (!craftModule)
	{
		return retval;
	}
	if (!(pBackpackData && pStrHex))
	{
		std::map<int, int> backpackData;
		std::string strHex;
		player->getBackPack()->setBackPackDataForCraft(backpackData, &strHex);
		retval = craftModule->GetCraftResult(craftID, backpackData, &strHex);
	}
	else
	{
		retval = craftModule->GetCraftResult(craftID, *pBackpackData, pStrHex);
	}
	
	return retval;
}

int CraftHelper::GetPlayCanCookBookNumAndAnyItem(ClientPlayer* player, int craftID)
{
	std::map<int, int> backpackData;
	player->getBackPack()->setBackPackData(backpackData);
	int retval = 0;
	CraftMgr* craftModule = GET_SUB_SYSTEM(CraftMgr);
	if (craftModule)
	{
		retval = craftModule->GetCookBookResult(craftID, backpackData);
	}
	return retval;
}

int CraftHelper::GetPlayerCanCraftNum(ClientPlayer * player, int craftID)
{
	if (player == nullptr) return 0;
	if (craftID <= 0)
	{
		return 0;
	}
	std::map<int, int> backpackData;
	std::string strHex;
	player->getBackPack()->setBackPackDataForCraft(backpackData, &strHex);
	int retval = 0;
	CraftMgr* craftModule = GET_SUB_SYSTEM(CraftMgr);
	if (craftModule)
	{
		retval = craftModule->GetCraftResult(craftID, backpackData, &strHex);
	}
	return retval;
}

int CraftHelper::GetPlayerCanCookBookNum(ClientPlayer* player, int craftID)
{
	if (player == nullptr) return 0;
	std::map<int, int> backpackData;
	player->getBackPack()->setBackPackData(backpackData);
	int retval = 0;
	CraftMgr* craftModule = GET_SUB_SYSTEM(CraftMgr);
	if (craftModule)
	{
		retval = craftModule->GetCookBookResult(craftID, backpackData);
	}
	return retval;	
}

int CraftHelper::BaseUpdateCraftContainer(const CraftingDef* def,ClientPlayer* player, bool isCook, int craftID, int baseIndex, int enough, int makeNum)
{
	CraftingContainer* craftContainer = (CraftingContainer*)player->getBackPack()->getContainer(baseIndex);
	if (craftContainer == NULL)
		return 0;
	craftContainer->initGrids(baseIndex);
	for (int clearIdx = 0; clearIdx < (int)craftContainer->m_Grids.size(); clearIdx++)
	{
		craftContainer->m_Grids[clearIdx].clear();
	}
	if (!def)
		return 0;

	int canCraftNum = CraftHelper::GetPlayerCanCraftNum(player, def->ID);
	CraftingDef craDef = *def;
	std::vector<std::pair<int, int>> materialVec;

	CraftMgr* craftModule = GET_SUB_SYSTEM(CraftMgr);
	if (craftModule)
	{
		if (isCook)
		{
			craftModule->GetCookBookMaterialVector(craftID, materialVec);
		}
		else
		{
			craftModule->GetCraftMaterialVector(craftID, materialVec);
		}
		
	}

	int materialsCnt = 0;
	if (canCraftNum > 0 && canCraftNum >= makeNum)	//�����㹻
	{
		for (unsigned int i = 0; i < materialVec.size(); i++)
		{
			if (materialVec[i].first != 0)
			{
				materialsCnt++;
				if (i >= craftContainer->m_Grids.size())
				{
					assert(0);
					return 0;
				}
				BackPackGrid& grid = craftContainer->m_Grids[i];
				SetBackPackGrid(grid, materialVec[i].first, materialVec[i].second * makeNum, -1, -1, (void*)def, 1);
			}
		}
	}
	else
	{
		for (unsigned int i = 0; i < materialVec.size(); i++)
		{
			if (materialVec[i].first != 0)
			{
				materialsCnt++;
				int haveNum = player->getBackPack()->getItemCountInNormalPack(materialVec[i].first);
				if (def->IsGroup)
					haveNum = player->getBackPack()->getSameGroupItemCountInNormalPack(materialVec[i].first);
				int _enough = haveNum >= materialVec[i].second * makeNum ? 1 : -1;
				if (i >= craftContainer->m_Grids.size())
				{
					assert(0);
					return 0;
				}
				BackPackGrid& grid = craftContainer->m_Grids[i];
				SetBackPackGrid(grid, materialVec[i].first, materialVec[i].second * makeNum, -1, -1, (void*)def, _enough);
			}
		}
	}


	BackPackGrid& grid = craftContainer->m_Grids[craftContainer->m_Grids.size() - 1];
	SetBackPackGrid(grid, craDef.ResultID, craDef.ResultCount * makeNum, -1, -1, (void*)def, enough);

	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(craDef.ResultID);
	if (tooldef && tooldef->Enchant > 0)
	{
		grid.addEnchant(tooldef->Enchant);
	}

	return materialsCnt;
}

int CraftHelper::UpdateCraftContainer(ClientPlayer *player, int craftID, int baseIndex, int enough, int makeNum)
{
	const CraftingDef *def = GetDefManagerProxy()->getCraftingDef(craftID, false, false);

	return BaseUpdateCraftContainer(def, player,false, craftID, baseIndex, enough, makeNum);
}

int CraftHelper::UpdateCookBookContainer(ClientPlayer* player, int craftID, int baseIndex, int enough, int makeNum)
{
	const CraftingDef* def = GetDefManagerProxy()->getCookbookDef(craftID, false, false);
	return BaseUpdateCraftContainer(def, player, true,craftID, baseIndex, enough, makeNum);
}

int CraftHelper::UpdateProductContainer(ClientPlayer * player, int craftID, int baseIndex, int level)
{
	PackContainer *productContainer = (PackContainer*)player->getBackPack()->getContainer(baseIndex);
	if (productContainer == NULL) return 0;
	productContainer->initGrids(baseIndex);
	return 0;
}

int CraftHelper::UpdateCookingMaterialContainer(ClientPlayer * player, int makeNum, int x, int y, int z)
{
	if (!player)
		return 0;
	return 0;//CraftHelper::UpdateCraftContainer(player, craftID, , enough, makeNum);
}

int CraftHelper::UpdateRecipeBackpack(ClientPlayer * pPlayer, int level)
{
	if (!pPlayer)
		return 0;
	pPlayer->getBackPack()->updateProductContainer(COOKING_PRODUCT_LIST_INDEX, level);
	return 0;
}

int CraftHelper::UpdateSameCraftItemIDBackpack(ClientPlayer * pPlayer, int craftItemID)
{
	if (!pPlayer)
		return 0;
	PackContainer *productContainer = (PackContainer*)pPlayer->getBackPack()->getContainer(COMMON_PRODUCT_LIST_INDEX);
	if (productContainer == NULL) return 0;
	productContainer->initGrids(COMMON_PRODUCT_LIST_INDEX);
	unsigned int cnt = 0;
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_CraftHelper_UpdateSameCraftItemIDBackpack", SandboxContext(NULL).
		SetData_Usertype("productContainer", productContainer).
		SetData_Number("craftItemID", craftItemID));
	if (result.IsSuccessed())
	{
		cnt = (int)result.GetData_Number();
	}
	return cnt;
}
std::string CraftHelper::GetCraftingFatherByType(int type)
{
	std::vector<CraftingDef> Defs = GetDefManagerProxy()->getCraftingDefByType(type);
	string s = "";
	auto customCompare = [type](CraftingDef a, CraftingDef b) {
		if(a.SubTypeOrder.size() == 0 || b.SubTypeOrder.size() == 0)
		{
			return false;
		}
		int Ao = a.SubTypeOrder.size() == 1 ? a.SubTypeOrder[0] : a.Type[0] == type ? a.SubTypeOrder[0] : a.SubTypeOrder[1];
		int Bo = b.SubTypeOrder.size() == 1 ? b.SubTypeOrder[0] : b.Type[0] == type ? b.SubTypeOrder[0] : b.SubTypeOrder[1];
		return Ao < Bo;
	};
	
	std::vector<CraftingDef> SubType;
	auto Check = [](std::vector<CraftingDef> SubType, int b) {
		for(size_t i = 0;i<SubType.size();i++)
		{
			if(SubType[i].ID == b)
			{
				return false;
			}
		}
		return true;
	};
	for (auto iter = Defs.begin(); iter != Defs.end(); iter++)
	{
		int size = iter->Type.size();
		for(size_t i =0;i<size;i++)
		{
			if(iter->Type[i] == type)
			{
				if(iter->SubType.size() == 0)
				{
					continue;
				}
				if(iter->SubType.size() == size)
				{
					if (Check(SubType, iter->SubType[i]) && iter->SubType[i] != 0)
					{
						CraftingDef* def = GetDefManagerProxy()->getCraftingDef(iter->SubType[i]);
						SubType.push_back(*def);
					}
				}else
				{
					if (Check(SubType, iter->SubType[0]) && iter->SubType[0] != 0)
					{
						CraftingDef* def = GetDefManagerProxy()->getCraftingDef(iter->SubType[0]);
						SubType.push_back(*def);
					}

				}
				
			}
		}	
	}
	int A = 0;
	std::sort(SubType.begin(), SubType.end(), customCompare);
	for (auto iter = SubType.begin(); iter != SubType.end(); iter++)
	{
		char str[64];
		sprintf(str,"S%d", iter->ID);
		string temp = str;
		s += str;
	}
	return s;
}
std::string CraftHelper::GetCraftingChildBySubType(int type, int subtype)
{
	if(subtype == 0)
	{
		return "";
	}
	std::vector<CraftingDef> Defs = GetDefManagerProxy()->getCraftingDefBySubType(type,subtype);
	string s = "";
	auto customCompare = [type](CraftingDef a, CraftingDef b) {
		if (a.SubTypeOrder.size() == 0 || b.SubTypeOrder.size() == 0)
		{
			return false;
		}
		int Ao = a.SubTypeOrder.size() == 1 ? a.SubTypeOrder[0] : a.Type[0] == type ? a.SubTypeOrder[0] : a.SubTypeOrder[1];
		int Bo = b.SubTypeOrder.size() == 1 ? b.SubTypeOrder[0] : b.Type[0] == type ? b.SubTypeOrder[0] : b.SubTypeOrder[1];
		return Ao < Bo;
	};
	std::sort(Defs.begin(), Defs.end(), customCompare);
	for (auto iter = Defs.begin(); iter != Defs.end(); iter++)
	{
		char str[64];
		sprintf(str, "S%d", iter->ID);
		string temp = str;
		s += str;
	}
	return s;
}
std::string CraftHelper::GetCraftingChildByType(int type)
{
	// 新增收藏分类处理
	if (type == 99) {
		std::string s = "";
		if (CraftMgr* mgr = GET_SUB_SYSTEM(CraftMgr)) {
			// 获取收藏配方列表
			const std::vector<int>& favorites = mgr->GetFavorites();
			
			// 收集有效的配方定义
			std::vector<const CraftingDef*> favoriteDefs;
			for (int craftID : favorites) {
				if (const CraftingDef* def = GetDefManagerProxy()->getCraftingDef(craftID)) {
					favoriteDefs.push_back(def);
				}
			}

			// 按DisplayOrder排序
			std::sort(favoriteDefs.begin(), favoriteDefs.end(),
				[](const CraftingDef* a, const CraftingDef* b) {
					return a->DisplayOrder < b->DisplayOrder;
				});

			// 生成结果字符串
			for (const CraftingDef* def : favoriteDefs) {
				s += "S" + std::to_string(def->ID);
			}
		}
		return s;
	}
	std::vector<CraftingDef> Defs = GetDefManagerProxy()->getCraftingDefByType(type);
	string s = "";
	auto customCompare = [type](CraftingDef a, CraftingDef b)
	{
		return a.DisplayOrder < b.DisplayOrder;
	};
	std::vector<CraftingDef> Child;
	for (auto iter = Defs.begin(); iter != Defs.end(); iter++)
	{
		int size = iter->Type.size();
		for (size_t i = 0; i < size; i++)
		{
			if (iter->Type[i] == type && iter->SubType.size() != 0)
			{
				Child.push_back(*iter);
			}
		}
	}
	std::sort(Child.begin(), Child.end(), customCompare);
	for (auto iter = Child.begin(); iter != Child.end(); iter++)
	{
		char str[64];
		sprintf(str, "S%d", iter->ID);
		string temp = str;
		s += str;
	}
	return s;
}
void CraftHelper::WriteBagItemInfoToHistoryFile(ClientPlayer* pPlayer)
{
	if(pPlayer && pPlayer->getBackPack())
	{
		int objId = pPlayer->getObjId();
		BackPack* backPack = pPlayer->getBackPack();
		for (size_t i = 0; i < 8; i++)
		{
			int gridIndex = backPack->getShortcutStartIndex() + i;
			int targetId = backPack->getGridItem(gridIndex);
			if (targetId != 0)
			{
				MINIW::ScriptVM::game()->callFunction("AddItemHistory", "ii", targetId, objId);
			}
			
		}
		for (size_t i = 0; i < 30; i++)
		{
			int gridIndex = BACKPACK_START_INDEX + i;
			int targetId = backPack->getGridItem(gridIndex);
			if (targetId != 0)
			{
				MINIW::ScriptVM::game()->callFunction("AddItemHistory", "ii", targetId, objId);
			}
		}
	}
}

// CraftHelper接口实现
void CraftHelper::ToggleFavorite(int craftID) {
	if (auto mgr = GET_SUB_SYSTEM(CraftMgr)) {
		mgr->ToggleFavorite(craftID);
	}
}

bool CraftHelper::IsFavorite(int craftID) {
	if (auto mgr = GET_SUB_SYSTEM(CraftMgr)) {
		return mgr->IsFavorite(craftID);
	}
	return false;
}
/*MINIW::GameStatic<CraftMgr> s_CraftMgr(MINIW::kInitManual);
CraftMgr& GetCraftMgr()
{
	return *s_CraftMgr.EnsureInitialized();
}*/

bool CraftHelper::PlayerPreDeductCraftMaterials(ClientPlayer* player, int craftID, int resultNum)
{
	std::map<int, int> deductedMaterials; // Local tracking of deducted items.
	BackPack* backpack = player->getBackPack();
	if (!backpack) return false;

	const CraftingDef* craDef = GetDefManagerProxy()->getCraftingDef(craftID);
	if (!craDef) return false;

	// 1. Calculate Required Materials (similar to CheckCraftingMaterial)
	std::map<int, int> requiredMaterials;
	for (int i = 0; i < 9; ++i)
	{
		if (craDef->MaterialID[i] > 0)
		{
			requiredMaterials[craDef->MaterialID[i]] += craDef->MaterialCount[i] * resultNum;
		}
	}
	// 2. Check if enough materials (using a combined check for BACKPACK and SHORTCUT)
	if (!CheckCraftingMaterial(backpack, craftID, resultNum)) {
		return false;
	}

	// 3. Move items to WITHHOLD_BACKPACK_START_INDEX
	PackContainer* withholdContainer = (PackContainer*)backpack->getContainer(WITHHOLD_BACKPACK_START_INDEX);
	if (!withholdContainer) return false;
	//withholdContainer->clear(); // Clear any previous items

	PackContainer* backpackContainer = (PackContainer*)backpack->getContainer(BACKPACK_START_INDEX);
	PackContainer* shortcutContainer = (PackContainer*)backpack->getContainer(backpack->getShortcutStartIndex());
	std::vector<int> changedGridIndex;
	for (const auto& pair : requiredMaterials)
	{
		int itemId = pair.first;
		int requiredCount = pair.second;

		// Deduct from shortcut first
		if (shortcutContainer) {
			for (int i = 0; i < shortcutContainer->m_Grids.size(); ++i) {
				BackPackGrid& grid = shortcutContainer->m_Grids[i];
				if (grid.def == nullptr) continue;

				int defID = grid.def->ID;
				if (craDef->IsGroup && grid.def->ItemGroup > 0)
				{
					defID = grid.def->ItemGroup;
				}

				bool isappropriate = false;
				if (craDef->IsGroup && grid.def->ItemGroup > 0 && itemId > 0)
				{
					ItemDef* mdef = GetDefManagerProxy()->getItemDef(itemId);
					assert(mdef != NULL);
					if (mdef && mdef->ItemGroup == defID)
					{
						isappropriate = true;
					}
				}
				else if (itemId == defID)
				{
					isappropriate = true;
				}
				if (isappropriate)
				{
					int deductNum = std::min(requiredCount, grid.getNum());
					BackPackGrid withholdGrid;
					//SetBackPackGrid(withholdGrid, grid.def->ID, deductNum, grid.durable, grid.toughness, grid.userdata, grid.enough, grid.sortId, grid.userdata_str.c_str(), grid.sid_str.c_str());
					SetBackPackGrid(withholdGrid, grid.def->ID, deductNum);
					withholdContainer->addItem_byGrid(&withholdGrid); // Add to withhold
					deductedMaterials[grid.def->ID] += deductNum; // Keep track for potential return (local map)
					if (0 == grid.addNum(-deductNum)) // Remove from source
						grid.clear();
					requiredCount -= deductNum;
					if (std::find(changedGridIndex.begin(), changedGridIndex.end(), grid.getIndex()) == changedGridIndex.end())
					{
						changedGridIndex.push_back(grid.getIndex());
					}
					if (requiredCount <= 0) break;
				}
			}
		}
		// Deduct from backpack
		if (backpackContainer && requiredCount > 0) {
			for (int i = 0; i < backpackContainer->m_Grids.size(); ++i) {
				BackPackGrid& grid = backpackContainer->m_Grids[i];
				if (grid.def == nullptr) continue;

				int defID = grid.def->ID;
				if (craDef->IsGroup && grid.def->ItemGroup > 0)
				{
					defID = grid.def->ItemGroup;
				}

				bool isappropriate = false;
				if (craDef->IsGroup && grid.def->ItemGroup > 0 && itemId > 0)
				{
					ItemDef* mdef = GetDefManagerProxy()->getItemDef(itemId);
					assert(mdef != NULL);
					if (mdef && mdef->ItemGroup == defID)
					{
						isappropriate = true;
					}
				}
				else if (itemId == defID)
				{
					isappropriate = true;
				}
				if (isappropriate)
				{
					int deductNum = std::min(requiredCount, grid.getNum());
					BackPackGrid withholdGrid;
					//SetBackPackGrid(withholdGrid, grid.def->ID, deductNum, grid.durable, grid.toughness, grid.userdata, grid.enough, grid.sortId, grid.userdata_str.c_str(), grid.sid_str.c_str());
					SetBackPackGrid(withholdGrid, grid.def->ID, deductNum);
					withholdContainer->addItem_byGrid(&withholdGrid);
					deductedMaterials[grid.def->ID] += deductNum; // Keep track for potential return (local map)
					if (0 == grid.addNum(-deductNum))
						grid.clear();
					requiredCount -= deductNum;
					if (std::find(changedGridIndex.begin(), changedGridIndex.end(), grid.getIndex()) == changedGridIndex.end())
					{
						changedGridIndex.push_back(grid.getIndex());
					}
					if (requiredCount <= 0) break;
				}
			}
		}
	}
	for (int i = 0; i < (int)changedGridIndex.size(); ++i)
	{
		backpack->afterChangeGrid(changedGridIndex[i]);
	}
	return true;
}

bool CraftHelper::PlayerReturnPreDeductedMaterials(ClientPlayer* player)
{
	BackPack* backpack = player->getBackPack();
	if (!backpack) return false;

	PackContainer* withholdContainer = (PackContainer*)backpack->getContainer(WITHHOLD_BACKPACK_START_INDEX);
	if (!withholdContainer) return false;

	// Iterate through the withhold container and return items.
	for (int i = 0; i < withholdContainer->m_Grids.size(); ++i)
	{
		BackPackGrid& grid = withholdContainer->m_Grids[i];
		if (grid.def)
		{
			backpack->addItem(grid.def->ID, grid.getNum(), 1); // Add back to backpack/shortcut
		}
	}

	withholdContainer->clear(); // Clear the withhold container
	return true;
}

bool CraftHelper::PlayerCraftFromWithhold(ClientPlayer* player, int craftID, int resultNum, int* remainNum)
{
	BackPack* backpack = player->getBackPack();
	if (!backpack) return false;

	PackContainer* withholdContainer = (PackContainer*)backpack->getContainer(WITHHOLD_BACKPACK_START_INDEX);
	if (!withholdContainer) return false;

	const CraftingDef* craDef = GetDefManagerProxy()->getCraftingDef(craftID);
	if (!craDef) return false;

	// Check if we have enough in the WITHHOLD container.
	std::map<int, int> requiredMaterials;
	for (int i = 0; i < 9; ++i)
	{
		if (craDef->MaterialID[i] > 0)
		{
			requiredMaterials[craDef->MaterialID[i]] += craDef->MaterialCount[i] * resultNum;
		}
	}

	std::map<int, int> withholdContents;
	for (const auto& grid : withholdContainer->m_Grids)
	{
		if (grid.def)
		{
			withholdContents[grid.def->ID] += grid.getNum();
		}
	}


	for (const auto& pair : requiredMaterials) {
		int itemId = pair.first;
		int required = pair.second;
		int available = 0;

		if (craDef->IsGroup)
		{
			// 直接遍历预扣容器的实际物品
			for (const auto& grid : withholdContainer->m_Grids)
			{
				if (!grid.def) continue;

				// 获取物品的实际组别信息
				auto itemdef = GetDefManagerProxy()->getItemDef(grid.def->ID);
				// 添加三重验证：有效定义、有效组别、匹配组ID
				if (itemdef && itemdef->ItemGroup > 0 && itemdef->ItemGroup == itemId)
				{
					available += grid.getNum();
				}
				else if (grid.def->ID == itemId)
				{
					available += grid.getNum();
				}
			}
		}
		else
		{
			available = withholdContents[itemId];
		}

		if (available < required)
		{
			return false; // Not enough in withhold.
		}
	}

	// Deduct materials ONLY from WITHHOLD_BACKPACK_START_INDEX
	for (int i = 0; i < 9; ++i)
	{
		if (craDef->MaterialID[i] > 0)
		{
			int required = craDef->MaterialCount[i] * resultNum;

			for (int j = 0; j < withholdContainer->m_Grids.size(); ++j)
			{
				BackPackGrid& grid = withholdContainer->m_Grids[j];
				if (!grid.def) continue;

				int gridItemId = grid.def->ID;
				if (craDef->IsGroup)
				{
					if (grid.def->ItemGroup != craDef->MaterialID[i]) continue;
					gridItemId = grid.def->ItemGroup;
				}

				if (gridItemId == craDef->MaterialID[i])
				{
					int deductNum = std::min(required, grid.getNum());
					if (0 == grid.addNum(-deductNum))
						grid.clear();
					required -= deductNum;
					if (required <= 0) break;
				}
			}
		}
	}

	// Add result item (same as before)
	int resultID = craDef->ResultID;
	int resultCount = craDef->ResultCount * resultNum;
	GridCopyData grid_copy_data;
	grid_copy_data.resid = resultID;
	grid_copy_data.num = resultCount;
	//int resultReal = backpack->addItem(resultID, resultCount, 2);
	int resultReal = backpack->addItemWithPickUp_bySocGridCopyData(grid_copy_data);

	ItemDef* itemdef = GetDefManagerProxy()->getItemDef(resultID);
	if (itemdef)
	{
		// 获取玩家位置坐标
		WCoord playerPos = player->getPosition();
		std::ostringstream oss;
		oss << "" << playerPos.x << "," << playerPos.y << "," << playerPos.z << "";
		std::string location = oss.str();

		std::map<std::string, GameAnalytics::Value> eventData;
		std::string item_name = std::string(itemdef->Name.c_str());
		eventData["uin"] = GameAnalytics::Value(player->getUin());  
		eventData["item_id"] = GameAnalytics::Value(resultID);
		eventData["item_name"] = GameAnalytics::Value(item_name);
		eventData["item_type"] = GameAnalytics::Value(itemdef->Type);             
		eventData["count"] = GameAnalytics::Value(resultCount);      
		eventData["is_workbench_need"] = GameAnalytics::Value( craDef->TechId != 0 ? 1 : 0 );    
		eventData["workbench_level"] = GameAnalytics::Value(player->GetRangeWorkbenchMaxLevel());        
		eventData["loc"] = GameAnalytics::Value(location);

		// 物品制作埋点
		GameAnalytics::TrackEvent("item_craft", eventData, true, player->getUin());

		player->updateTaskSysProcess(TASKSYS_CRAFT_ITEM, resultID, 0, resultCount);
		player->updateTaskSysProcess(TASKSYS_GAIN_ITEM, resultID, 0, resultCount);//renjie 加工获得的物品也要能完成拾取的任务
	}

	if (resultReal < resultCount) // Add to backpack/shortcut (try shortcut first)
	{
		// Backpack full.  
		auto am = player->getActorMgr();
		if (am == NULL) return false;
		int h = player->getThrowItemHeight();
		ClientItem* item = am->spawnItem(player->getPosition() + WCoord(0, h, 0), resultID, resultCount - resultReal);
		if (item == NULL) return false;

		Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
		Rainbow::Vector3f dir;

		float r = 30.0f;
		PitchYaw2Direction(dir, player->getLocoMotion()->m_RotateYaw,
			player->getLocoMotion()->m_RotationPitch);
		motion.x = dir.x * r;
		motion.z = dir.z * r;
		motion.y = dir.y * r + 10.0f;

		r = 2.0f * GenRandomFloat();
		float angle = GenRandomFloat() * 360.0f;

		motion.x += r * CosByAngle(angle);
		motion.z += r * SinByAngle(angle);
		motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
		
	}

	//withholdContainer->clear(); //Clear withhold container after craft
	if (remainNum) *remainNum = 0;
	return true;
}

bool CraftHelper::PlayerReturnPreDeductedMaterialsByCraft(ClientPlayer* player, int craftID, int resultNum)
{
	if (!player || !player->getBackPack() || resultNum <= 0) {
		return false;
	}

	// 1. 获取合成配方所需材料
	std::vector<std::pair<int, int> > requiredMaterials;
	CraftMgr* craftModule = GET_SUB_SYSTEM(CraftMgr);
	if (craftModule) {
		craftModule->GetCraftMaterialVector(craftID, requiredMaterials);
	}

	// 2. 计算实际需要返还的数量
	std::map<int, int> returnQuantities;
	typedef std::pair<int, int> MatPair;
	for (auto it = requiredMaterials.begin(); it != requiredMaterials.end(); ++it) {
		const int matID = it->first;
		const int count = it->second;
		returnQuantities[matID] = count * resultNum;
	}

	// 3. 获取预扣容器
	BackPack* backpack = player->getBackPack();
	PackContainer* withholdContainer =
		static_cast<PackContainer*>(backpack->getContainer(WITHHOLD_BACKPACK_START_INDEX));
	if (!withholdContainer) {
		return false;
	}

	// 4. 获取原始容器
	PackContainer* backpackContainer =
		static_cast<PackContainer*>(backpack->getContainer(BACKPACK_START_INDEX));
	const int shortcutStart = backpack->getShortcutStartIndex();
	PackContainer* shortcutContainer =
		static_cast<PackContainer*>(backpack->getContainer(shortcutStart));

	// 5. 材料返还处理
	std::vector<int> changedGridIndex;
	const int defCount = GetDefManagerProxy()->getItemNum();

	for (int i = 0; i < withholdContainer->m_Grids.size(); ++i) {
		BackPackGrid& grid = withholdContainer->m_Grids[i];

		// 提前缓存关键信息
		const int currentItemID = grid.def ? grid.def->ID : 0;
		if (currentItemID == 0 || grid.getNum() <= 0) continue;

		// 获取物品组信息
		const ItemDef* itemDef = GetDefManagerProxy()->getItemDef(currentItemID);
		const int materialGroup = (itemDef && itemDef->ItemGroup > 0) ? itemDef->ItemGroup : 0;

		// 使用传统迭代器遍历
		for (auto rit = returnQuantities.begin(); rit != returnQuantities.end(); ) {
			const int reqID = rit->first;
			int& reqCount = rit->second;

			// 实时检查网格状态
			if (!grid.def || grid.getNum() <= 0) break;

			// 跳过不匹配项
			if (reqCount <= 0 ||
				(currentItemID != reqID && materialGroup != reqID)) {
				++rit;
				continue;
			}

			int canReturn = std::min(grid.getNum(), reqCount);
			while (canReturn > 0 && grid.def) { // 添加循环条件检查
				// 快捷栏返还
				bool returned = false;
				for (auto& targetGrid : shortcutContainer->m_Grids) {
					if (!grid.def) break;

					if (targetGrid.def &&
						targetGrid.def->ID == currentItemID &&
						targetGrid.getNum() < targetGrid.def->StackMax)
					{
						const int actualAdd = std::min(
							targetGrid.def->StackMax - targetGrid.getNum(),
							canReturn
						);

						targetGrid.addNum(actualAdd);
						changedGridIndex.push_back(targetGrid.getIndex());
						if (0 == grid.addNum(-actualAdd)) {
							grid.clear();
							canReturn = 0;
							break;
						}
						else {
							canReturn -= actualAdd;
							reqCount -= actualAdd;
						}

						returned = true;
					}
				}
				if (canReturn <= 0 || !grid.def) break;

				// 背包返还
				for (auto& targetGrid : backpackContainer->m_Grids) {
					if (!grid.def) break;

					if (targetGrid.def &&
						targetGrid.def->ID == currentItemID &&
						targetGrid.getNum() < targetGrid.def->StackMax)
					{
						const int actualAdd = std::min(
							targetGrid.def->StackMax - targetGrid.getNum(),
							canReturn
						);

						targetGrid.addNum(actualAdd);
						changedGridIndex.push_back(targetGrid.getIndex());
						if (0 == grid.addNum(-actualAdd)) {
							grid.clear();
							canReturn = 0;
							break;
						}
						else {
							canReturn -= actualAdd;
							reqCount -= actualAdd;
						}

						returned = true;
					}
				}
				if (canReturn <= 0 || !grid.def) break;

				// 常规添加
				if (canReturn > 0) {
					const int added = backpack->addItem(currentItemID, canReturn, 1);
					if (added > 0) {
						if (0 == grid.addNum(-added)) {
							grid.clear();
							canReturn = 0;
						}
						else {
							canReturn -= added;
							reqCount -= added;
						}
					}
					else {
						break;
					}
				}
			}

			// 检查网格状态决定是否继续
			if (!grid.def) {
				rit = returnQuantities.end(); // 终止外部循环
				break;
			}
			else {
				++rit;
			}
		}

		// 清空后立即跳过后处理
		if (!grid.def) continue;
	}

	// 清理空槽
	//withholdContainer->clearEmptyGrids();

	// 触发UI更新
	for (auto it = changedGridIndex.begin(); it != changedGridIndex.end(); ++it) {
		backpack->afterChangeGrid(*it);
	}

	return true;
}