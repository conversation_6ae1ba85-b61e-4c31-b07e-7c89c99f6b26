#include "PlayerControl.h"
#include "PCControlLua.h"
#include "TouchControl.h"
//#include "EffectManager.h"
#include "Entity/OgreModel.h"
#include "WorldRender.h"
#include "GameCamera.h"
#include "UGCGameCamera.h"
#include "CameraModel.h"
#include "CameraManager.h"
#include "SandBoxManager.h"
#include "DefManagerProxy.h"
#include "ClientActorManager.h"
#include "BlockMaterialMgr.h"
#include "BlockMesh.h"
#include "IClientGameManagerInterface.h"
#include "IClientGameInterface.h"
#include "BlockScene.h"
#include "LockCtrlComponent.h"
#include "SocRevivePointComponent.h"

#include "OgreUtils.h"
#include "GameMode.h"
#include "PlayerAttrib.h"
#include "PlayerStateController.h"
#include "GunUseComponent.h"
////#include "MpGameSurvive.h"
#include "IRecordInterface.h"
#include "InputInfo.h"
#include "PlayerInputHelper.h"
#include "VehicleControlInputs.h"
#include "ActorBoat.h"
#include "ActorTrainCar.h"
#include "ActorRocket.h"
#include "ActorPumpkinHorse.h"
#include "BlockRailNew.h"
#include "BlockRopeHead.h"
#include "CurveFace.h"
//#include "MusicalInstrumentsCsv.h"

#include "GameNetManager.h"
#include "ItemSkillComponent.h"
#include "Sound/MusicManager.h"
#include "OpenContainerComponent.h"
//#include "Core/GameEngine.h"
//#include "GameUI.h"
//#include "Graphics/ScreenManager.h"
//#include "ObserverEvent.h"
//#include "ObserverEventManager.h"
#include "ClientActorFuncWrapper.h"
#include "RiddenComponent.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "ActorInPortal.h"
#include "AttackedComponent.h"
#include "BindActorComponent.h"
//#include "ActorBall.h"
//#include "ActorBasketBall.h"
//#include "OgreRoot.h"
#include "ClientInfoProxy.h"
#include "timeutil.h"
#include "PlayerViewMask.h"
#include "GRoot.h"
#include "GGraph.h"
#include "UIPackage.h"

#include "GComponent.h"
#include "GRichTextField.h"
#include "ItemIconManager.h"

//#include "SandboxUnit.h"
//#include "SandboxGlobalNotify.h"
#include "SandboxCoreSubsystem.h"
#include "MapInfoRefreshCenter.h"
#include "ClientActorThornBall.h"
#include "ThornBallComponent.h"
#include "SandboxPlayersRoot.h"
#include "ClientActorIcicle.h"
#include "TemperatureComponent.h"
#include "ActorVehicleAssemble.h"
#include "EffectManager.h"
#include "navigationpath.h"
#include "LuaInterfaceProxy.h"
#include "CommonUtil.h"
#include "container_manualEmitter.h"
#include "MoveControl.h"
#include "ActorTravelingTrader.h"
#include "SkillComponent.h"
#include "OgreEntity.h"
#include "Entity/LegacySequenceMap.h"
#include "ActionIdleStateGunAdvance.h"
#include "CommonTween.h"
#include "BaseItemMesh.h"
#include "ItemUseComponent.h"
#include "PixelMapMgr.h"
#include "BlockPlacementPreview.h"
#include "SocToolsLogic.h"
#include "container_territory.h"
#include "BlockTerritory.h"
#include "BlockSocLogicInterface.h"
#include "ActorPlayerCorpse.h"
#include "TeamComponent.h"
#include <GameAnalytics.h>

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

EXPORT_SANDBOXGAME PlayerControl *g_pPlayerCtrl = NULL;

IMPLEMENT_SCENEOBJECTCLASS(PlayerControl)
unsigned int PlayerControl::s_currentLogicFrame = 0;

PlayerControl::PlayerControl() : m_pCamera(NULL), m_TouchCtrl(NULL),m_PCCtrl(NULL),m_InputHelper(NULL),m_VehicleControlInputs(NULL), m_CurrentCameraConfig()
, m_nGrabCDTick(0), m_IsEyeInWater(false), m_IsEyeInWaterLastFrame(false), m_InWaterNoJumpTicks(0),
m_CurMouseX(0.5f), m_CurMouseY(0.5f), m_IsHideHandModel(false), m_IsUsePolaroidCamera(false), m_ViewLock(false)

{

#ifndef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	SetHideOnSceneTree(true); // 不在场景树上显示
#endif

	g_pPlayerCtrl = this;
	m_PickResult.actor = NULL;
	m_PickResult.actors.clear();

	m_IsEquipedJetpack = false;
	m_lastToolId = 0;
	m_MoveForward = 0;
	m_MoveRight = 0;
	m_MoveUp = 0;
	m_SpaceClickTick = 0;
	m_WKeyClickTick = 0;
	m_LastSyncRotTick = 0;
	m_ViewMode = CameraControlMode::CAMERA_TPS_BACK_SHOULDER;
	m_ViewMode_ToSpectator = CameraControlMode::CAMERA_FPS;
	m_DieRecordTicks = -1;
	m_isDigging = false;

	m_IsJump = false;
	m_DisplayOxygen = -1;
	m_OldOxygen = 0;

	m_HorseCharge = -1;
	m_HorseHP = -1;
	m_CurBowStage = -1;
	m_EnableMoveInput = true;
	m_EnableInput = true;
	m_EnableActionInput = true;
	m_PlayerSounder = NULL;
	m_GuideSounder = NULL;
	m_InputHelper = NULL;
	m_LastJumpMark = -1;
	m_saveLastViewMode = false;

	CameraManager::GetInstance().newGameCamera();
	m_pCamera = CameraManager::GetInstance().m_GameCamera;
	m_PlayerAnimation = NULL;

	m_LerpRotation = false;
	m_LerpRotationDuration = 1;
	m_TargetYaw = 0;
	m_OriginYaw = 0;
	m_LerpRotationStartMarker = 0;
	m_NeedRevertToFPS = 0;
	m_isReloadMagazine = false;
	m_ActBeforeViewMode = -1;
	m_SkinningOnMobile = false;

	m_TipUI = nullptr;
	/*m_MoveDirective = g_BlockMtlMgr.getModel("particles/direction/direction.omod");
	if(m_MoveDirective != NULL){
		m_MoveDirective->SetSelfEmissive(&Rainbow::ColourValue::Blue);
		m_MoveDirective->Show(false);
	}*/

	m_nCheckSpectatorTick = 0;

	m_CatchBallCDTick = 0;
	m_TackleCDTick = 0;
	m_BlockShotCDTick = 0;
	m_cdRunTime = 0;

	m_BobbingByRocket = false;
	//20210724: 触发器新API  codeby:wangshuai
	m_IsShaking = false;
	m_DieMapID = -1;

	m_VehicleControlInputs = NULL;

	m_bIsShowHandWhenHideUI = false;

	m_bIsRidden = false;
	m_UploadStatusMap.clear();
	m_ActorAttribTrigger = NULL;
	m_iTickTriggerCount = 0;
	m_iTryShapeShiftID = 0;
	m_iShapeShiftTick = 0;

	m_iRecoverViewMode = -1;
	m_oldViewMode = -1;
	
	m_bWaitRecoverViewMode = false;

	m_wingForwardSpeed = 0.0f;
	m_wingFlyTime = 0.0f;

	m_CameraOffset = Vector3f(0, 0, 0);
	
	m_receiveChangeViewModeMsg = false;
	m_msgViewMode = 0;
	m_msgLock = false;
	m_intervalActorTravelingTrader = 0;
	isRightTool = false;
	RightToolId = 0;
	isShowExploit = false;
	m_pItemSkillComponent = CreateComponent<ControlItemSkillComponent>("ControlItemSkillComponent");
	CreateComponent<ControlOpenContainerComponent>("ControlOpenContainerComponent");
	/*SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().CreateEventDispatcher("buff_playerIsCanCtr");
	m_buffPlayerCanCtrCallBack = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().SubscribeEvent("buff_playerIsCanCtr", nullptr, [&](SandboxContext context) -> SandboxResult {
		LivingAttrib* attrib = getLivingAttrib();
		if (attrib && attrib->hasBuff(SUDDEN_ILLNESS_BUFF)) {
			return SandboxResult(nullptr, true);
		}
		return SandboxResult(nullptr, false);

     });*/

	m_playerViewMask = ENG_NEW(PlayerViewMask);
#ifdef IWORLD_TARGET_PC
	setUIControlMode(1);
#else
	setUIControlMode(0);
#endif
/*
#if defined(ANDROID) || defined(__ANDROID__)
	m_forbidden_event.reserve(1);
	m_forbidden_keyvalue.reserve(1);
#endif
*/	
	if (!m_pDebugCallback.IsValid())
	{
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("setAttackBlockCameraData");
		m_pDebugCallback = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("setAttackBlockCameraData", nullptr, [&](MNSandbox::SandboxContext context) ->  MNSandbox::SandboxResult {

			m_fThirdRecoilSpeed = (float)context.GetData_Number("m_fThirdRecoilSpeed");
			m_fThirdMaxRecoil = (float)context.GetData_Number("m_fThirdMaxRecoil");
			m_fThirdRecoverySpeed = (float)context.GetData_Number("m_fThirdRecoverySpeed");

			m_fFirstRecoilSpeed = (float)context.GetData_Number("m_fFirstRecoilSpeed");
			m_fFirstMaxRecoil = (float)context.GetData_Number("m_fFirstMaxRecoil");
			m_fFirstRecoverySpeed = (float)context.GetData_Number("m_fFirstRecoverySpeed");

			return MNSandbox::SandboxResult(nullptr, true);
			});
	}

	CreateEvent2();
	
	m_CurrentOutlineBlocks.clear();
}

PlayerControl::~PlayerControl()
{
	clearAllSpecialBlockOutline();
	ClearSelectedActor();
	
	ClearForbiddenEvent();
	ENG_DELETE(m_ActorAttribTrigger);

	ENG_DELETE(m_InputHelper);
	ENG_DELETE(m_TouchCtrl);
	ENG_DELETE(m_PCCtrl);
	ENG_DELETE(m_PlayerAnimation);
	ENG_DELETE(m_VehicleControlInputs);
	ENG_DELETE(m_playerViewMask);

	if (m_tweenID)
	{
		CommonTween::Free(m_tweenID);
	}

	if(m_pCamera)
	{
		if (getGunLogical())
		{
			getGunLogical()->unregisterFirstPersonEvent();
		}
		m_pCamera->setCameraMode(NULL);
	}
	m_pCamera = NULL;
	CameraManager::GetInstance().deleteGameCamera();
    //yinqing modify
	if (g_pPlayerCtrl == this)
	{
		g_pPlayerCtrl = NULL;
		SetIPlayerControl(nullptr);
	}

	if (m_PlayerSounder)
	{
		OGRE_RELEASE(m_PlayerSounder);
	}

	if(m_GuideSounder)
	{
		OGRE_RELEASE(m_GuideSounder);
	}

	m_TipUI->removeFromParent();

	if (m_pDebugCallback.IsValid())
	{
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe("setAttackBlockCameraData", m_pDebugCallback);
	}

	//DESTORY_GAMEOBJECT_BY_COMPOENT(m_MoveDirective);
	//SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Unsubscribe("buff_playerIsCanCtr", m_buffPlayerCanCtrCallBack);
	MINIW::ScriptVM* scriptvm = MINIW::ScriptVM::game();
	if (scriptvm) {
		scriptvm->setUserTypePointer("CurMainPlayer", "PlayerControl", NULL);
	}
	tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "PlayerControl");
}

void PlayerControl::OnLoadChunk(CHUNK_INDEX index)
{
	auto pixelmap = PixelMapMgr::GetInstancePtr()->GetCurrentPixelMap();
	if (pixelmap)
	{
		pixelmap->UpdateChunk(index.x, index.z);
	}

}

void PlayerControl::onTipClickItem(fairygui::FUIEventContext* context)
{
	//LOG_WARNING("PlayerControl::onTipClickItem");
	if (GetClientInfoProxy()->isMobile() && m_TouchCtrl)
		m_TouchCtrl->SetInteractive();
}

void PlayerControl::onMorebtnClickItem(fairygui::FUIEventContext* context)
{
	LOG_WARNING("PlayerControl::onMorebtnClickItem");
	if (GetClientInfoProxy()->isMobile() && m_TouchCtrl)
		m_TouchCtrl->SetMore();
}

void PlayerControl::SetSkinningOnMobile(bool b)
{
	if (GetClientInfoProxy()->isMobile()) {
		m_SkinningOnMobile = b;
	}
}

bool PlayerControl::GetSkinningOnMobile()
{
	if (GetClientInfoProxy()->isMobile()) {
		return m_SkinningOnMobile;
	}
	return false;
}

bool PlayerControl::init(int uin, const char *nickname, int playerindex, const char *customjson)
{
	if(!ClientPlayer::init(uin, nickname, playerindex, customjson)) return false;
	MNSandbox::Config::GetSingleton().SetLocalUin(uin); // 设置沙盒配置
	//switchCurrentItem();
	m_AccoutSkinID = GetClientInfoProxy()->getAccountInfo()->RoleInfo.SkinID;
	if (getBody())
	{
		ActorBody* actorBody = getBody();
		actorBody->show(false);
		if (actorBody->getModel() && actorBody->getModel()->GetModelAnimationPlayer())
		{
			actorBody->getModel()->GetModelAnimationPlayer()->SetEffectDisEnable(false);
		}
	}
	if (m_pCamera) m_pCamera->setCameraMode(m_CameraModel);
	//getAttrib()->equip(EQUIP_PIFENG, 2205, 100);

	//ge GetGameEventQue().postShortcutSelected(getPlayerAttrib()->m_CurShotcut);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("selectgrid", getPlayerAttrib()->m_CurShotcut);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SHORTCUT_SELECTED", sandboxContext);
	}

	m_InputHelper = ENG_NEW(PlayerInputHelper)();
	ScriptVM::game()->setUserTypePointer("InputHelper", "PlayerInputHelper", m_InputHelper);

	//TouchControlLua* pTouchControl = NULL;
	//GetCoreLuaDirector().CallFunctionM("TouchControlLuaImpl", "CreateTouchControl", ">u[TouchControlLua]", &pTouchControl);
	//m_TouchCtrl = pTouchControl;
	//PCControlLua* pPCControl = NULL;


	//GetCoreLuaDirector().CallFunctionM("PCControlLuaImpl", "CreatePCControl", ">u[PCControlLua]", &pPCControl);
	//m_PCCtrl = pPCControl;
	if (GetClientInfoProxy()->isMobile())
	{
		m_TouchCtrl = ENG_NEW(TouchControl);
		
		// 隐藏TouchControl UI，使用新的移动端界面
		if (m_TouchCtrl)
		{
			// m_TouchCtrl->showOperateUI(false);
			m_TouchCtrl->HideTouchControlUi(true);
			m_TouchCtrl->HideRockerUi(true);
			m_TouchCtrl->HideJumpUi(true);
			// m_TouchCtrl->HideActionUi(true);
		}
	} 
	else if(GetClientInfoProxy()->isPC())
	{
		m_PCCtrl = ENG_NEW(PCControl);
	}
	fairygui::UIPackage::addPackage("miniui/miniworld/soclogin");
	if (GetClientInfoProxy()->isPC())
	{
		m_TipUI = fairygui::UIPackage::createObject("soclogin", "Tip")->as<fairygui::GComponent>();
	}
	else
	{
		m_TipUI = fairygui::UIPackage::createObject("soclogin", "mobile_interact")->as<fairygui::GComponent>();
		m_TipUI->getChild("btn_mobile_interact")->addClickListener(CC_CALLBACK_1(PlayerControl::onTipClickItem, this), fairygui::EventTag(this));
		m_TipUI->getChild("morebtn")->addClickListener(CC_CALLBACK_1(PlayerControl::onMorebtnClickItem, this), fairygui::EventTag(this));
	}
	m_TipUI->setVisible(false);
	//对着tipui 无法开枪 https://www.tapd.cn/tapd_fe/62991019/bug/detail/1162991019001116101
	m_TipUI->setClickPass(true);
	fairygui::UIRoot->addChild(m_TipUI);

	m_PlayerAnimation = ENG_NEW(PlayerAnimation)(this);
	//GetActorBody()->setNeedUpdateAnim(true);

	m_VehicleControlInputs = ENG_NEW(VehicleControlInputs)();

	m_CurrentCameraConfig.init();

	{
		if(!m_ActorAttribTrigger)
			m_ActorAttribTrigger = ENG_NEW(ActorAttribut)();

		PlayerAttrib* pattr = getPlayerAttrib();
		LivingAttrib* lattr = getLivingAttrib();
		m_ActorAttribTrigger->MaxHP = getAttrib()->getMaxHP();
		m_ActorAttribTrigger->NowHP = getAttrib()->getHP();
		m_ActorAttribTrigger->HPRecover = getAttrib()->getHPRecover();
		m_ActorAttribTrigger->MaxHunger = 100.0f;
		m_ActorAttribTrigger->NowHunger = pattr->getFoodLevel();
		m_ActorAttribTrigger->MaxOxygen = lattr->getMaxOxygen();
		m_ActorAttribTrigger->NowOxygen = lattr->getOxygen();
		m_ActorAttribTrigger->MoveSpeed = getAttrib()->getSpeedAtt(Actor_Walk_Speed);
		m_ActorAttribTrigger->RunSpeed = getAttrib()->getSpeedAtt(Actor_Run_Speed);
		m_ActorAttribTrigger->SwimSpeed = getAttrib()->getSpeedAtt(Actor_Swim_Speed);
		m_ActorAttribTrigger->JumpSpeed = getAttrib()->getSpeedAtt(Actor_Jump_Speed);
		m_ActorAttribTrigger->SneakSpeed = getAttrib()->getSpeedAtt(Actor_Sneak_Speed);
		m_ActorAttribTrigger->Dodge = (float)getAiInvulnerableProb();
		m_ActorAttribTrigger->NearAttack = lattr->getAttackBaseLua(ATTACK_PUNCH);
		m_ActorAttribTrigger->RemoteAttack = lattr->getAttackBaseLua(ATTACK_RANGE);
		m_ActorAttribTrigger->NearArmor = lattr->getArmorBaseLua(ATTACK_PUNCH);
		m_ActorAttribTrigger->RemoteArmor = lattr->getArmorBaseLua(ATTACK_RANGE);
		m_ActorAttribTrigger->Dimension = getScale();
		m_ActorAttribTrigger->Level = (int)ceil((float)pattr->getExp()/100);
		m_ActorAttribTrigger->Armor = getArmor();
	}

	return true;
}
void PlayerControl::updateFishUp()
{
	//举鱼状态下，如果是第一人称视角则强行切换为第三人称视角。
	if (GetDefManagerProxy()->isFishNeedUp(m_Body->getCurShowEquipItemId(EQUIP_WEAPON)))
	{
		if (g_pPlayerCtrl->getViewMode() == CameraControlMode::CAMERA_FPS)
		{
			m_saveLastViewMode = true;
			g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_TPS_BACK);
		}
	}
	else
	{
		if (m_saveLastViewMode)
		{
			m_saveLastViewMode = false;
			g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_FPS);
		}
	}
}
void PlayerControl::changePlayerModel(int playerindex, int mutatemob, const char *customskins, const char* custommodel/* = NULL*/, int itemid, int blockid,bool force)
{
	//int geniuslv = GetClientInfoProxy()->getGenuisLv(playerindex);
	//if(geniuslv < 0) geniuslv = 0;

	//int playerindex = ComposePlayerIndex(playerindex, geniuslv, GetClientInfoProxy()->getAccountInfo()->RoleInfo.SkinID);
	if (playerindex <= 0)
		playerindex = 1;
	m_originSkinId = GetClientInfoProxy()->getAccountInfo()->RoleInfo.SkinID;
	m_strOriginCustomJson = GetClientInfoProxy()->getAccountInfo()->RoleInfo.CustomSkin;
	ClientPlayer::changePlayerModel(playerindex, mutatemob, customskins, custommodel);
	if (InTransform())
	{
		restoreSkin();
	}
}


void PlayerControl::applyEquips(EQUIP_SLOT_TYPE t /*= MAX_EQUIP_SLOTS*/)
{
	ClientPlayer::applyEquips(t);
	if (t == MAX_EQUIP_SLOTS || (EQUIP_BACK_INDEX)t == getLivingAttrib()->getEquipIdxByType(EQUIP_HEAD))
	{
		int itemid = getLivingAttrib()->getEquipItemWithType(EQUIP_HEAD);
		if (itemid == ITEM_DIVING_MASK || itemid == ITEM_DIVING_MASK_SUPER)
		{
			m_playerViewMask->equipMask();
		}
		else
		{
			m_playerViewMask->unequipMask();
		}
	}
	// if (t == EQUIP_HEAD || t == MAX_EQUIP_SLOTS)
	// {
	// 	int itemid = getLivingAttrib()->getEquipItem(EQUIP_HEAD);
	// 	if (itemid == ITEM_DIVING_MASK || itemid == ITEM_DIVING_MASK_SUPER)
	// 	{
	// 		m_playerViewMask->equipMask();
	// 	}
	// 	else
	// 	{
	// 		m_playerViewMask->unequipMask();
	// 	}
	// }
}

void PlayerControl::onDisApplyEquips(int itemid)
{
	ClientPlayer::onDisApplyEquips(itemid);
}

void PlayerControl::SetHideEquipAvatar(bool bShow)
{
	ClientPlayer::SetHideEquipAvatar(bShow);
}

bool PlayerControl::GetHideEquipAvatarState()
{
	return ClientPlayer::GetHideEquipAvatarState();
}

float PlayerControl::getRotaionLimitAngle()const
{
	if (m_OPWay == PLAYEROP_WAY_PUSHSNOWBALL && (m_ViewMode == CameraControlMode::CAMERA_FPS || m_ViewMode == CameraControlMode::CAMERA_TPS_BACK))
	{
		return 5.f;
	}

	if (m_ViewMode == CAMERA_TPS_BACK_2)
	{
		return GetLuaInterfaceProxy().get_lua_const()->cameraTpsBackPlayerRotationLimitAngle;
	}

	return ClientPlayer::getRotaionLimitAngle();
}

void PlayerControl::onApplyEquips(int itemid, int grid_index)
{
	ClientPlayer::onApplyEquips(itemid, grid_index);
}

bool PlayerControl::checkInteractive()
{
	fairygui::GObject* playermainbase = fairygui::UIRoot->getChild("playermainAutoGen");
	if (!playermainbase) return false;
	fairygui::GComponent* playermaincom = playermainbase->as<fairygui::GComponent>();
	if (!playermaincom) return false;
	fairygui::GController* c1 = playermaincom->getController("c1");
	if (!c1) return false;

	if (GetClientInfoProxy()->isMobile())
		return c1->getSelectedIndex() == 0 && playermainbase->isVisible();

	bool isOperateUI = GetIClientGameManagerInterface() && GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isOperateUI();
	//主界面ui的状态才显示交互
	return c1->getSelectedIndex() == 0 && playermainbase->isVisible() && !isOperateUI;
}

// 辅助函数，用于检查方块类型是否支持
bool PlayerControl::IsSupportedBlockType(const std::string& blockType)
{
	static const char* supportedTypes[] = {
		"multiceiling",   // 天花板-地基
		"multiwall",      // 墙
		//"socautodoor",    // 2518 扶梯门
		//"socdoor",        // 2524 1X2 木门
		//"socdoubledoor",  // 2516 2X2 双木门
		"multibranch",    // 1X2 立杆
		"multistair",     // 2X2楼梯
		"multitriangle",   // 2X2斜板
		"basiccocrrosive"
	};

	const int typeCount = sizeof(supportedTypes) / sizeof(supportedTypes[0]);
	for (int i = 0; i < typeCount; ++i)
	{
		if (blockType == supportedTypes[i])
			return true;
	}

	return false;
}

void PlayerControl::renderMobileMorebtn()
{
	fairygui::GObject *obj = m_TipUI->getChild("morebtn");
	if (!obj) return;

	if (m_PickResult.intersect_block) //是方块
	{
		if (GetPlayer()->GetComponent<LockCtrlComponent>()->IsOpenPieMenu(m_PickResult.block))
		{
			obj->setVisible(true);
			return;
		}
		else if (GetPlayer()->GetComponent<SocRevivePointComponent>()->IsOpenPieMenu(m_PickResult.block))
		{
			obj->setVisible(true);
			return;
		}
		else
		{
			int blockid = GetWorld()->getBlockID(m_PickResult.block);

			BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
			std::string blockType = blockDef->Type.c_str();

			if (blockType == "territory") // 2411 是领地柜的方块ID，具体ID需要根据实际情况调整
			{
				obj->setVisible(true);
				return;
			}

			if (IsSupportedBlockType(blockType))
			{
				obj->setVisible(true);
				return;
			}

			auto pmtl = getWorld()->getBlockMaterial(m_PickResult.block);
			if (pmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_WaterStorage)
			{
				obj->setVisible(true);
				return;
			}
		}
	}

	obj->setVisible(false);
}

void PlayerControl::renderUI()
{
	ClientPlayer::renderUI();
	if (m_playerViewMask)
		m_playerViewMask->renderUI();
	m_nOperateKeyEType = -1;
	if (!m_TipUI) return;
	//移动端点击不然会有瞬间的ui位移
	if (GetClientInfoProxy()->isMobile() && m_InputInfo->tap) return;
	m_TipUI->setVisible(false);
	if (!checkInteractive()) return;

	fairygui::GLoader* loader = nullptr; 
	fairygui::GObject* icon = nullptr;
	fairygui::GObject* bg = nullptr;
	fairygui::GTextField* textField = nullptr;
	if (GetClientInfoProxy()->isPC())
	{
		icon = m_TipUI->getChild("icon");
		bg = m_TipUI->getChild("bg");
		if (icon) loader = icon->as<fairygui::GLoader>();
		textField = m_TipUI->getChild("text")->as<fairygui::GTextField>();
	}
	else
	{
		textField = m_TipUI->getChild("btn_mobile_interact")->as<fairygui::GComponent>()
			->getChild("text")->as<fairygui::GTextField>();
		renderMobileMorebtn();
	}
	//之后需要把判断是否显示的逻辑放在tick中 ui显示逻辑放到lua（优化项） -- by charles xie
	
	//// 更新Actor选中轮廓效果
	//UpdateActorSelection();	

	//交互顺序优先方块 fix:https://project.feishu.cn/soc1/bug/detail/6328909877
	if (m_PickResult.intersect_block || m_PickResult.isIntersectLiquid)
	{
		int blockid = 0;
		if (m_PickResult.isIntersectLiquid)
		{
			auto block = m_pWorld->getBlock(m_PickResult.firstIntersectBlock);
			if (/*!m_pWorld->IsProtectedZone(m_PickResult.firstIntersectBlock) && */block.isWater() && !isMoving())
			{
				blockid = block.getResID();
				m_nOperateKeyEType = 3;
			}
		}
		else if (m_PickResult.intersect_block)
		{
			m_nOperateKeyEType = 2;
			blockid = m_pWorld->getBlockID(m_PickResult.block);
			const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(blockid);
			if (!(itemdef && itemdef->Usable))
			{
				// 移动端且手持维修锤才检查是否是支持的方块类型，如果是则不能设为0
				if (GetClientInfoProxy()->isMobile() && isToolHammer(getCurToolID()))
				{
					const BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
					if (!(blockDef && IsSupportedBlockType(blockDef->Type.c_str())))
					{
						blockid = 0;
					}
				}
				else
				{
					blockid = 0;
				}
			}
		}
		if (blockid)
		{
			m_TipUI->setVisible(true);
			int iconType = 2;
			int textId = 10000008;
			auto uidata = GetDefManagerProxy()->getOperateUIData(blockid);
			if (uidata)
			{
				iconType = uidata->IconType;
				textId = uidata->UIText;
			}

			if (bg) bg->setVisible(false);
			if (icon) icon->setVisible(true);
			if (iconType == 2)
			{
				if (bg) bg->setVisible(true);
				int u, v, width, height, r, g, b;
				SharePtr<Texture2D> tex = GetItemIconMgr().getItemIcon(blockid, u, v, width, height, r, g, b);
				if(loader) loader->setTexture(tex, cocos2d::Rect(u, v, width, height));
				if (loader) loader->setColor(cocos2d::Color3B(r, g, b));
			}
			else if (iconType == 1)
			{
				loader->setURL(uidata->IconName);
			}
			else
			{
				if (icon) icon->setVisible(false);
			}
			if (blockid == 2411) //领地柜
			{
				auto con_pos = BlockTerritory::GetRealTotemIceContainer(m_pWorld, m_PickResult.block);

				auto container = dynamic_cast<TerritoryContainer*>(m_pWorld->getContainerMgr()->getContainer(con_pos));
				if (container)
				{
					if (container->m_OwnerUin == 0) //表示没owner
						textId = 10000721;//领地柜 按E授权
				}
			}
			auto pmtl = g_BlockMtlMgr.getMaterial(blockid);
			if (pmtl)
			{
				if (pmtl->BlockTypeId() == BlockMaterial::BlockType_Door && pmtl->getBlockSpecialLogicType(0) & BlockMaterial::BlockSpceialLogicTeam0::IsElectricObject)
				{
					BlockSocElectricLogicInterface* psocmtl = dynamic_cast<BlockSocElectricLogicInterface*>(pmtl);
					if (!(psocmtl && psocmtl->isActive(m_pWorld, m_PickResult.block))) textId = 10001105;
					else
					{
						int blockdata = m_pWorld->getBlockData(m_PickResult.block);
						if ((blockdata & 8) != 0) textId = 10000110;
						else textId = 10001104;
					}
				}
			}
			//block 为静态直接使用分辨率中心就行
			m_TipUI->setPosition(fairygui::UIRoot->getSize().width * 0.5, fairygui::UIRoot->getSize().height * 0.5 - 80);
			if (textField) textField->setText(GetDefManagerProxy()->getStringDef(textId));
		}
	}
	else if (m_PickResult.actor)
	{
		m_nOperateKeyEType = 1;
		IClientActor* targetActor = m_PickResult.actor;
		std::string tips = getActorInteractionTips(targetActor);
		if (tips.length() > 0) {
			m_TipUI->setVisible(true);
			Rainbow::Vector3f vec = targetActor->GetAABB().m_Center;
			Rainbow::Vector3f uipos = m_pCamera->getEngineCamera()->WorldToScreenPoint(vec);
			uipos.x /= cocos2d::Director::getInstance()->getAdaptiveScaleX();
			uipos.y /= cocos2d::Director::getInstance()->getAdaptiveScaleX();

			if (bg) bg->setVisible(false);
			if (icon) icon->setVisible(false);
			m_TipUI->setPosition(uipos.x, uipos.y);

			if (textField) textField->setText(tips);
		}
		else {
			m_TipUI->setVisible(false);
		}
	}
}

/*
void PlayerControl::OnUseButtonClicked()
{
	if (m_SwitchTick > 0) return;

	if (getCurToolID() > 0 || getFacedHorse())
	{
		useItem(getCurToolID(), PLAYEROP_STATUS_BEGIN);
		m_SwitchTick = 5;
	}
	else m_UIOperate = UIOP_NULL;
}*/

bool PlayerControl::isIdolExpressionAction(int seq)
{
	if (seq == -1) return true;
	SequenceMap& sm = GetSequenceMap();
	SequenceMap::SeqDesc* smsd = sm.findSequenceDesc(seq);
	if (smsd == NULL) return false;
	int freeView = smsd->freeView;
	if (freeView > 0) //20210928 codeby:chenwei 新增两个动画
	{
		return true;
	}
	return false;
}

// 20210910：是否处于坐骑隐身技能中  codeby： keguanqiang
bool PlayerControl::isInvisibleByRide()
{
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		auto* ride = dynamic_cast<ActorHorse*>(RidComp->getRidingActor());
		if (ride && ride->isInvisioning())
			return true;

	}
	return false;
}

void PlayerControl::setBowStage(int stage)
{
	//stage 0 -> Draw; 1 -> Stay; -1 -> Idle
	if(stage != m_CurBowStage)
	{
		if(stage == 0)
		{
			m_pCamera->setBobbing(false);
			m_PlayerAnimation->performArrowAttackStart();
		}
		else if(stage == 1)
		{
			m_PlayerAnimation->performArrowAttackReady();
		}
		else if (stage == -1)
		{
			m_PlayerAnimation->performArrowAttackShoot();
			//getCamera()->setZoom(false);
			getCamera()->disableZoom();

			if(GetClientInfoProxy()->isMobile()){
				if(m_TouchCtrl) m_TouchCtrl->setAccumulatorState(-1);
			}
			else
			{
			    if(m_PCCtrl) m_PCCtrl->setAccumulatorState(-1);
			}


			if (m_StateController->getMovementState() == "Walk")
			{
				m_pCamera->setBobbing(true);
			}

			if (m_StateController->getMovementState() == "Idle")
			{
				m_PlayerAnimation->performIdle();
			}
			else if (m_StateController->getMovementState() == "Walk")
			{
				//need perform walk
				m_PlayerAnimation->performIdle();
			}
		}

		m_CurBowStage = stage;
	}
}

void PlayerControl::setAccumulatorState(float progress)
{
	if(GetClientInfoProxy()->isMobile()){
		if(m_TouchCtrl) m_TouchCtrl->setAccumulatorState(progress);
	}
	else
	{
		if(m_PCCtrl) m_PCCtrl->setAccumulatorState(progress);
	}
	ClientPlayer::setAccumulatorState(progress);
}

void PlayerControl::tryMountActor(ClientActor* actor, short shapeshiftid/* =0 */)
{
	bool riddenControl = true;

	ActorHorse* pHorse = dynamic_cast<ActorHorse*>(actor);
	if (pHorse && pHorse->getRiddenControlPriority())
		riddenControl = pHorse->getRiddenControl();

	if (mountActor(actor, false, -1, riddenControl))
		setFlying(false);
}

bool PlayerControl::tryShapeShift(short shapeshiftid)
{
	//在星站传送仓内部允许变身
	if (getBody()->isInStarStationCabin())
	{
		GetLuaInterfaceProxy().showGameTips(80041, 3, true);
		return false;
	}	

	if (isDead())
		return false;

	bool canShapeShift = true;
	RoleSkinDef *def = GetDefManagerProxy()->getRoleSkinDef(shapeshiftid);
	if (!def)
		canShapeShift = false;
	else if (def->ChangeType == 1|| def->ChangeType == 3 )					//变形成坐骑
		canShapeShift = true;
	else
	{
		if (def->ChangeType == 2)
		{
			if (!trySplitDisguise(shapeshiftid))
				canShapeShift = false;
		}
		else
		{
			canShapeShift = false;
		}
	}

	if (getCurDorsumID() == ITEM_FIRE_ROCKET || getCurDorsumID() == ITEM_SNAKEGOD_WING)  //使用冲天炮或蛇神之翼时，不能变形
		canShapeShift = false;

	auto *living = getLivingAttrib();
	if (living && living->hasBuffByNature(2)) //带着变身buff时，不能变形
		canShapeShift = false;

	if (!canShapeShift) 
	{
		if (!def || (def && def->ChangeType != 2)) //组合装扮不需要在这提示
		{
			//ge GetGameEventQue().postInfoTips(100261);
			CommonUtil::GetInstance().PostInfoTips(100261);
		}
		return false;
	}
	if (def->ChangeType == 2)
	{
		setCanControl(false);
	}

	auto *gunloc = getGunLogical();
	if(gunloc && gunloc->getZoom())
		gunloc->setZoom(false);

	if (m_iRecoverViewMode < 0)
	{
		if(m_NeedRevertToFPS & 1)
		{
			m_iRecoverViewMode = CAMERA_FPS;
			m_NeedRevertToFPS &= (~1);
		}
		else
			m_iRecoverViewMode = m_ViewMode;
	}
		
	m_bWaitRecoverViewMode = false;
	setViewMode(CAMERA_TPS_BACK, true);
	if (def->ChangeType == 2)
		changeViewMode(CAMERA_TPS_BACK, false);
	m_iTryShapeShiftID = shapeshiftid;

	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		if (getBody())
			getBody()->setCurAnim(-1, 1);
		playAnim(SEQ_SHAPE_SHIFT, false, -1);

		int destId = def->ChangeContact[0];
		if (destId == 3482) //兔子先改变一下遮挡特效
		{
			ActorLocoMotion *pActorMove = dynamic_cast<ActorLocoMotion *>(getLocoMotion());
			if (pActorMove)
			{
				Rainbow::Vector3f dir;
				PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, 0);
				int x = 0-(dir.x*30);
				int y = 50;
				int z = 0-(dir.z*40);
				m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/BUFF_CHICKEN.ent", getPosition() + WCoord(x, y, z), 30, 0, 0, false);
			}
		}
		else
		{
			if(destId == 3494)//牛魔//主机播放变形  特殊动画
				showSpecailTryShapeAnim();
			char effectName[64] = { 0 };
			sprintf(effectName, "horsechange_%d", destId);
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(effectName);
			}
		}
		char soundName[64] = { 0 };
		sprintf(soundName, "ent.%d.change1", destId);
		auto soundComp = getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound(soundName, 1.0f, 1.0f);
		}

	}

	return true;
}

void PlayerControl::shapeShift()
{
	hideSpecailTryShapeAnim();

	setCanControl(true);
	RoleSkinDef *def = GetDefManagerProxy()->getRoleSkinDef(m_iTryShapeShiftID);
	m_iTryShapeShiftID = 0;
	if (!def || isDead())
	{
		if (m_iRecoverViewMode >= 0)
			setViewMode(m_iRecoverViewMode);

		return;
	}
	if (def->ChangeType == 2)
	{
		DeformationSkin(def);
		return;
	}
	//playParticles("horsechange_3449.ent");
	ClientActor *actor = summonShapeShiftHorse(def->ChangeContact[0]);
	if (actor)
	{
		tryMountActor(actor);
	}
}

void PlayerControl::dismountActor()
{
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ClientActor* actor = RidComp->getRidingActor();
		if (actor && actor->canDismount())
			tryMountActor(NULL);
	}
	else if (isSleeping() || isRestInBed()) tryWakeup();
	else if (getUsingEmitter())
	{
		disMountEmitter(getUsingEmitterBlockPos());
	}
	else if (getSitting()) tryStandup();
}

MountType PlayerControl::getMountType()
{
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		if (getDrivingVehicle())	//物理机械驾驶和坐骑、火箭分开判断
			return MOUNT_DRIVE;
		else
			return MOUNT_RIDE;
	}
	else if (isSleeping() || isRestInBed())
		return MOUNT_SLEEP;
	else if (getUsingEmitter())
		return MOUNT_MANIPULATE_EMITTER;
	else if(getSitting())
		return MOUNT_SIT;
	return MOUNT_NOT;
}

ActorHorse *PlayerControl::getRidingHorse()
{
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		ClientActor* actor = RidComp->getRidingActor();
		return dynamic_cast<ActorHorse*>(actor);
	}
	return NULL;
}

ActorRocket *PlayerControl::getRidingRocket()
{
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		ClientActor* actor = RidComp->getRidingActor();
		return dynamic_cast<ActorRocket*>(actor);
	}
	return NULL;
}

ActorVehicleAssemble * PlayerControl::getDrivingVehicle()
{
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		ClientActor* actor = RidComp->getRidingActor();
		return dynamic_cast<ActorVehicleAssemble*>(actor);
	}
	return NULL;
}

bool PlayerControl::castShadow()
{
	if (IsRunSandboxPlayer()) return false;
	if (m_ViewMode == 0) return false;
	else return ClientPlayer::castShadow();
}
/*
void PlayerControl::setUIOperate(int op)
{
	if (m_UIOperate == op)
	{
		assert(m_CurOperate == PLAYEROP_NULL);
		return;
	}

	endCurOperate();
	m_UIOperate = op;
}*/

int PlayerControl::getBlockX()
{
	return CoordDivBlock(getPosition().x);
}

int PlayerControl::getBlockY()
{
	return CoordDivBlock(getPosition().y);
}

int PlayerControl::getBlockZ()
{
	return CoordDivBlock(getPosition().z);
}

int PlayerControl::getBlockLight()
{
	World* pworld = g_pPlayerCtrl->getWorld();
	if (pworld && getLocoMotion()) {
		return pworld->getBlockLightValue(CoordDivBlock(getLocoMotion()->getPosition()));
	}
	else {
		return 0;
	}
}

bool PlayerControl::playAct(int act, bool isSwitchViewMode)
{
	if (!ClientPlayer::playAct(act, isSwitchViewMode))
	   return false;
	
	//20210928 codeby:chenwei 所有动画都切可环绕视图
	//播动画的时候是否切视角，默认是切的
	if (isSwitchViewMode)
		switchSkinActView();
	return true;
}

//20210926:音乐方块同步播放动作 codeby：huangxin
void PlayerControl::MusicClubPlayAnim(int seq)
{
	playAnim(seq, false, 127);
}

//20210907 codeby:chenwei 装扮互动动画
bool PlayerControl::playSkinAct(int act, const int inviteUin, const int acceptUin)
{
	//20211008 codeby:chenwei 添加健壮性判断
	const PlayActDef* def = GetDefManagerProxy()->getPlayActDef(act);
	if (!getBody() || !m_pWorld)
	{
		return false;
	}

	if (!ClientPlayer::playAct(act))
		return false;

	//20210929 codeby:chenwei 迁移逻辑到playerContrl处理
	getBody()->setAnimSeq(getBody()->getAnimSeq() + 1); //通过提升序列号把装扮互动动作直接重置并播放


	//20210929 codeby:chenwei 为受邀请玩家设置
	setSkinPartnerUin(acceptUin);
	if (NULL != getActorMgr())
	{
		ClientPlayer *acceptPlayer = getActorMgr()->findPlayerByUin(acceptUin);
		if (acceptPlayer)//20211008 codeby:chenwei 修改判空对象
		{
			acceptPlayer->setSkinPartnerUin(inviteUin);
			auto soundComp = acceptPlayer->getSoundComponent();
			if (soundComp && def)
			{
				soundComp->playSoundByTrigger(def->Sound.c_str(), 1.0, 1.0, false, true);//20211008 codeby:chenwei 添加音效
			}
			//20211012 codeby:chenwei 受邀方设置动作
			if (acceptPlayer->getBody())
			{
				acceptPlayer->getBody()->setAct(act);
			}
			//设置接收方的位置 主机通知设置客机的位置
			ClientPlayer* invitePlayer = getActorMgr()->findPlayerByUin(inviteUin);
			if (invitePlayer && !m_pWorld->isRemoteMode())
			{
				WCoord invitePos = invitePlayer->getPosition();
				bool isReverse = (act != 23 && act != 24);
				WCoord acceptPointPos = invitePlayer->getSkinActTargetPos(act, invitePos, isReverse);
				Rainbow::Vector3f  dir = invitePos.toVector3();
				dir = MINIW::Normalize(dir);
				float yaw;
				float pitch;
				Direction2PitchYaw(&yaw, &pitch, dir);
				//acceptPlayer->getLocoMotion()->setMoveDir(dir);
				//acceptPlayer->setMoveControlYaw(invitePlayer->getLocoMotion()->m_RotateYaw);
				acceptPlayer->gotoPos(acceptPlayer->getWorld(), acceptPointPos, true);
				if (acceptPlayer->isNewMoveSyncSwitchOn())
				{
					acceptPlayer->setMoveControlYaw(yaw);
					acceptPlayer->setMoveControlPitch(pitch);
				} 
				else
				{
					acceptPlayer->setFaceYaw(yaw);
				}
				//acceptPlayer->getLocoMotion()->gotoPosition(acceptPointPos, invitePlayer->getLocoMotion()->m_RotateYaw, 0.0f);
				//acceptPlayer->getLocoMotion()->m_TickPosition.beginTick(acceptPointPos);
			}
		}
	}

	switchSkinActView(); //20210927 codeby:chenwei 装扮互动切换视角模式抽取到函数

	//播角色音效
	auto soundComp = getSoundComponent();
	if (soundComp && def)
	{
		soundComp->playSoundByTrigger(def->Sound.c_str(), 1.0, 1.0, false, true);//20211008 codeby:chenwei 添加音效
	}

	return true;
}

float PlayerControl::getPlayerYaw()
{
	return getLocoMotion()->m_RotateYaw;
}

void PlayerControl::setPlayerYaw(float nYaw)
{
	getLocoMotion()->setRotateRaw(nYaw);
}

bool PlayerControl::getBobbingByRocket()
{
	return m_BobbingByRocket;
}

void PlayerControl::setBobbingByRocket(bool bobbing)
{
	m_pCamera->m_CurrentShakeTime = 0;
	m_BobbingByRocket = bobbing;
}

float s_WheelDist = 0;
int s_CurPlayerIndex = 1;

bool PlayerControl::revive(int reviveType/* =0 */, int x/* =0 */, int y/* =-1 */, int z/* =0 */)
{
	bool ret = ClientPlayer::revive(reviveType, x, y, z);
	setViewMode(m_ViewMode);
	//ge GetGameEventQue().postPlayerAttrChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);

	// 添加self_revive埋点
	if (ret) {
		// 获取服务器信息
		std::string server_id = "";
		std::string server_setting = "";
		int current_player_count = 0;
		
		if (g_WorldMgr) {
			server_id = std::to_string(g_WorldMgr->getWorldId());
			//server_setting = g_WorldMgr->isSurviveMode() ? "survive" : "creative";
		}
		
		// 获取当前玩家数量
		World* pworld = getWorld();
		if (pworld) {
			ClientActorMgr* actormgr = pworld->getActorMgr()->ToCastMgr();
			if (actormgr) {
				current_player_count = actormgr->getNumPlayer();
			}
		}
		
		// 获取复活位置坐标
		std::string location = "" + std::to_string(x) + "," + std::to_string(y) + "," + std::to_string(z) + "";
		
		// 获取复活原因（根据reviveType）
		std::string reason = "";
		switch (reviveType) {
			case 0: reason = "normal"; break;
			case 1: reason = "bed"; break;
			case 2: reason = "respawn"; break;
			default: reason = "unknown"; break;
		}
		
		GameAnalytics::TrackEvent("self_revive", {
			{"server_id", GameAnalytics::Value(server_id)},
			//{"server_setting", GameAnalytics::Value(server_setting)},
			{"current_player", GameAnalytics::Value(current_player_count)},
			{"loc", GameAnalytics::Value(location)},
			{"reason", GameAnalytics::Value(reason)},
			{"player_id", GameAnalytics::Value(getUin())},
			{"is_player_teammate", GameAnalytics::Value(false)}  // 自复活，不是队友救援
		});
	}

	//抛事件: 玩家重生
	//if (ret) MNSandbox::GlobalNotify::GetInstance().m_PlayerRevive.Emit(getUin(), StaticToCast<SandboxNode>());
	if (ret)
	{
		if (auto playersService = MNSandbox::GetCurrentPlayersNodeRoot())
			playersService->m_notifyPlayerRevive.Emit(getUin(), StaticToCast<SandboxNode>());
	}

	if (GetIdleStateGunAdvance())
		GetIdleStateGunAdvance()->TryResetHandleEquip();
	
	World* pworld = getWorld();
	// 冒险模式下非简单模式，死了超过5次
	if (g_WorldMgr && g_WorldMgr->isSurviveMode() 
		&& !g_WorldMgr->isEasyMode() 
		&& (pworld && !pworld->IsEasyGuideTipsNeverShow())
		&& (m_DieTimes == 1 || (m_DieTimes-1) % 5 == 0) )
	{

		int gametype = GetClientInfoProxy()->getMultiPlayer();
		bool isRoomOwner = false;
		if (gametype == 0 || gametype == 1 || gametype == 3) {
			isRoomOwner = true;
		}

		int playerNum = 0;
		if (pworld)
		{
			ClientActorMgr* actormgr = pworld->getActorMgr()->ToCastMgr();
			if (actormgr)
			{
				playerNum = actormgr->getNumPlayer();
			}
		}
		
		if ((m_pWorld && !m_pWorld->isRemoteMode()) && (isRoomOwner && ((playerNum - 1) <= 0)) )
		{
			//弹窗提示是否切换简单模式
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr)
				.SetData_Number("uin", getUin())
				.SetData_Number("deathTimes", m_DieTimes);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_EASYMODE_GUIDE_CHECK", sandboxContext);
		}
	}

	return ret;
}


static void SetRidingHostMotivate(ClientActor *riding, bool hostmotivate)
{
	if(riding)
	{
		if(dynamic_cast<ActorBoat *>(riding)!=NULL)
		{
			static_cast<ActorBoat *>(riding)->m_HostMotivate = hostmotivate;
		}
		else if (dynamic_cast<ActorTrainCar *>(riding) != NULL)
		{
			static_cast<ActorTrainCar *>(riding)->m_HostMotivate = hostmotivate;
		}
	}
}

bool PlayerControl::mountActor(ClientActor *actor, bool isforce/* =false */, int seatIndex, bool bcontrol /*= true*/)
{
	auto RidComp = getRiddenComponent();
	ClientActor* oldriding = NULL;
	if (RidComp)
	{
		oldriding = RidComp->getRidingActor();
	}

	if(ClientPlayer::mountActor(actor, isforce, seatIndex, bcontrol))
	{ 
		if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
		{
			return true;
		}
		Rainbow::Quaternionf rot;
		ActorVehicleAssemble* actorVehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		// 如果是ActorVehicleAssemble 则不走下面关于镜头的逻辑
		if (actor)
		{
			auto actorRidComp = actor->getRiddenComponent();
			if (actorRidComp && actorVehicle == NULL && actorRidComp->getRiddenBindRot(this, rot))//曲线轨道重置相机角度
			{
				getLocoMotion()->m_RotateYaw = 180.0f;
				getLocoMotion()->m_RotationPitch = 0;
				if (getViewMode() == CAMERA_FPS)
				{
					m_pCamera->m_RotateYaw = 180.0f;
					m_pCamera->m_RotatePitch = 0;
				}
			}
		}

		//ge GetGameEventQue().postRidingChange(RidComp && RidComp->isRiding()?2:0);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("ridetype", RidComp && RidComp->isRiding() ? 2 : 0);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RIDING_CHANGE", sandboxContext);
		}
		//ge GetGameEventQue().postPlayerAttrChange();
		MNSandbox::SandboxContext sandboxContext1 = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext1);

		ClientActor* curriding = NULL;
		if (RidComp)
		{
			curriding = RidComp->getRidingActor();
		}

		if (curriding && curriding->getRiddenComponent() && curriding->getRiddenComponent()->getRiddenChangeFPSView())
		{
			if (getViewMode() == CAMERA_FPS)
			{
				m_NeedRevertToFPS |= 1;
				setViewMode(CAMERA_TPS_BACK);
			}
		}
		else if (m_NeedRevertToFPS & 1)
		{
			if (!isShapeShift())
			{
				setViewMode(CAMERA_FPS);
			}
			m_NeedRevertToFPS &= (~1);
		}


		SetRidingHostMotivate(oldriding, true);
		if (curriding)
		{
			//if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				auto curridingComp = curriding->getRiddenComponent();
				if (curridingComp && curridingComp->getRiddenByActor(0) == this)
				{
					SetRidingHostMotivate(curriding, false);
				}
			}
		}

		return true;
	}

	return false;
}

void PlayerControl::playGuideSound2D(const char *path)
{
	if(m_GuideSounder) m_GuideSounder->Release();
	m_GuideSounder = GetMusicManager().PlaySound2DControl(path, 1.0, false);
}

int g_PlaceTick = 0;
int g_BlockID = 1;
void PlayerControl::tick()
{
	if (GetWorldManagerPtr()->isNewSandboxNodeGame())
	{
        return;
    }
    SANDBOXPROFILING_LOG(log, "PlayerControl::tick");
	m_InputHelper->tick();
	//float deltaTime = ClientManager::getInstance()->m_frameDeltaTime;
	//deltaTime = GAMERECORD_INTERFACE_EXEC(getSpeed(), 1.0f) * deltaTime;
	m_pWorld->resetTempChunkCenter(CoordDivSection(getPosition().x), CoordDivSection(getPosition().z));
	if (m_StateController)
		m_StateController->tick(0);

	if (m_CameraModel && getLocoMotion() && GetWorldManagerPtr() && GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		m_CameraModel->show(false);
		GetWorldManagerPtr()->m_RenderEyeMap = m_pWorld->getCurMapID();
		GetWorldManagerPtr()->m_RenderEyePos = getEyePosition();
		ClientPlayer::tick();
		return;
	}
	//WCoord curpos = CoordDivBlock(getPosition())*BLOCK_SIZE;
	//WorldPos::m_Origin = curpos.toWorldPos();//WorldPos(-107629000, 0, 108604000);
	if (m_CatchBallCDTick > 0)
	{
		m_CatchBallCDTick--;
		if (m_CatchBallCDTick == 0 && m_CameraModel && (m_CameraModel->getMoveDirModeView() == CameraControlMode::CAMERA_TPS_BACK || m_CameraModel->getMoveDirModeView() == CameraControlMode::CAMERA_TPS_BACK_2 || m_CameraModel->getMoveDirModeView() == CameraControlMode::CAMERA_TPS_BACK_SHOULDER))
		{
			m_CameraModel->showMoveDir(false);
			m_CameraModel->setMoveTarget(WCoord(0, 0, 0), 0);
		}
	}
	if (m_TackleCDTick > 0) m_TackleCDTick--;
	if (m_BlockShotCDTick > 0 && getCurOperate() != PLAYEROP_BASKETBALL_BLOCK_SHOT)
	{
  		m_BlockShotCDTick--;
	}
	if (m_cdRunTime > 0) m_cdRunTime--;
	if (m_nGrabCDTick > 0)
	{
		m_nGrabCDTick--;
	}

	m_IsEyeInWaterLastFrame = m_IsEyeInWater;
	if(getLivingAttrib()){
		GetMusicManager().SetReversePlay(getLivingAttrib()->getEquipItemWithType(EQUIP_HEAD) == 12246);
	}

	/*随机的放置方块
	g_PlaceTick++;
	if(g_PlaceTick > 0)
	{
		g_PlaceTick = 0;

		const BlockDef *def = NULL;
		while(def == NULL)
		{
			def = GetDefManagerProxy()->getBlockDef(g_BlockID++);
			if(g_BlockID == 4096) g_BlockID = 1;
		}

		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(def->ID);
		for(int i=0; i<10; i++)
		{
			int x = CoordDivBlock(getPosition().x) + (rand()%64) - 32;
			int z = CoordDivBlock(getPosition().z) + (rand()%64) - 32;
			int y = (rand()%128) + 7;
			if(m_pWorld->isAirBlock(x,y,z) && mtl->canPlaceBlockOnSide(m_pWorld, WCoord(x,y,z), DIR_NEG_Y))
			{
				placeBlock(def->ID, x, y, z, DIR_NEG_Y);
				break;
			}
			else if(m_pWorld->isAirBlock(x,7,z) && mtl->canPlaceBlockOnSide(m_pWorld, WCoord(x,7,z), DIR_NEG_Y))
			{
				placeBlock(def->ID, x, 7, z, DIR_NEG_Y);
				break;
			}
		}
	}*/

	int eyeblock = 0;
	if(m_pWorld == NULL) return;
	if(GetWorldManagerPtr() == NULL) return;
	if (getLocoMotion() == NULL) return;
	if (m_pWorld->getCurMapID() == GetWorldManagerPtr()->m_RenderEyeMap)
	{
		WCoord eyepos = CoordDivBlock(GetWorldManagerPtr()->m_RenderEyePos);
		eyeblock = m_pWorld->getBlockID(eyepos);
		if (g_pPlayerCtrl)
		{
			auto portalComponent = g_pPlayerCtrl->getActorInPortal();
			if (portalComponent && portalComponent->isInPortal()) eyeblock = BLOCK_PORTAL;
		}
	}

	m_IsEyeInWater = IsWaterBlockID(eyeblock) || isWaterPlantID(eyeblock);
	// 眼睛入水
	if (m_IsEyeInWater && !m_IsEyeInWaterLastFrame)
	{
		OnPlayerEyeEnterWater();
	}
	// 眼睛出水
	if (!m_IsEyeInWater && m_IsEyeInWaterLastFrame)
	{
		OnPlayerEyeOutOfWater();
		m_InWaterNoJumpTicks = 0;
	}

	updateSwimTipsLogic();

	 //LOG_INFO("LightValue %d", getWorld()->getBlockLightValue(CoordDivBlock(getLocoMotion()->getPosition())));
	GetWorldManagerPtr()->m_RenderEyeMap = m_pWorld->getCurMapID();
	GetWorldManagerPtr()->m_RenderEyePos = getEyePosition();
	PitchYaw2Direction(GetWorldManagerPtr()->m_RenderEyeDir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);

	//LOG_INFO("motion:%f,%f,%f:", m_LocoMotion->m_Motion.x, m_LocoMotion->m_Motion.y, m_LocoMotion->m_Motion.z);
	//LOG_INFO("m_Position:%f,%f,%f:", m_LocoMotion->m_Position.x, m_LocoMotion->m_Position.y, m_LocoMotion->m_Position.z);
	
	if (m_CameraModel) {
		if (canShowCameraModel()) m_CameraModel->show(true);//&& !getSitting()
		else m_CameraModel->show(false);
	}
	if (m_pCamera && m_pCamera->getEngineCamera())
	{
		bool bodyInwater = isInWater();
		int blockid = m_pWorld->getBlockID(CoordDivBlock(m_pCamera->getEngineCamera()->GetWorldPosition()));
		bool isInWater = (IsWaterBlockID(blockid) && bodyInwater) || isWaterPlantID(blockid);
		if (isInWater != g_WorldMgr->m_CameraIsInWater) 
		{
			g_WorldMgr->m_CameraIsInWater = isInWater;
			
			WorldRenderer* worldRenderer = m_pWorld->GetWorldRenderer();
			if (worldRenderer) worldRenderer->SetWaterCullMode(isInWater ? Rainbow::CullMode::kCullFront : Rainbow::CullMode::kCullBack);
		}
	}
	/*
	int curgenius = GetClientInfoProxy()->getAccountData()->getGenuisLv(getBody()->getModelID());
	if(curgenius>=0 && getBody()->getGeniusLv() != curgenius)
	{
		getBody()->changeGeniusLv(curgenius);
	}*/
	checkThornBall();

	//刷新游商 code-by: 李元星
	if (!m_pWorld->isRemoteMode() && m_pWorld->GetWorldMgr() && m_pWorld->GetWorldMgr()->isOriginAdventureMode() && m_pWorld->getCurMapID() == MAPID_GROUND)
	{
		--m_intervalActorTravelingTrader;
		if (m_intervalActorTravelingTrader <= 0)
		{
			m_intervalActorTravelingTrader = 20;
			if (m_pWorld->GetWorldMgr()->GetTravelingTraderInfo().inHome)
			{
				WCoord pos_spawn = m_pWorld->GetWorldMgr()->getRevivePointEx(m_pWorld);
				if (pos_spawn.y < 0) pos_spawn = m_pWorld->GetWorldMgr()->getSpawnPointEx(m_pWorld);
				WCoord pos_self = CoordDivBlock(getPosition());
				if (pos_spawn.squareDistanceTo(pos_self) <= 576) //24 * 24
				{
					ActorTravelingTrader::Get(m_pWorld);
				}
			}
		}
	}

	// 睡在床上和使用灶台烹饪中不可移动、人物固定朝向 code-by: liya
	if(isSleeping() || isRestInBed() || isUseHearth())//|| getSitting()
	{
		if (getLocoMotion())
		{
			//躺下的时候把头放正
			getBody()->setRotationYawHead(getBody()->getRenderYawOffset());
			ClientPlayer::tick();
			checkAttribChange();
        return;
    }

	}

	if(GetClientInfoProxy()->isMobile())
	{
		bool showusebtn = true;   // 修改为长期显示Use按钮
		bool showGunUseBtns = true; // 修改为长期显示Gun按钮
		bool showCrosshair = false;
		ActorHorse *horse;
		int curtool = getCurToolID();
		const ItemDef *def = GetDefManagerProxy()->getItemDef(curtool);

		if (getUsingEmitter())
		{
			showusebtn = true;
		}
		else if (def)
		{
			if (def->UseTarget == ITEM_USE_BOW || def->UseTarget == ITEM_USE_CLICKBUTTON || def->UseTarget == ITEM_USE_GUN || def->UseTarget == ITEM_USE_ADVANCEDDIG ||def->UseTarget == ITEM_USE_CHARGETHROW || def->UseTarget == ITEM_USE_HOOK)
			{
				showCrosshair = true;
			}

			if(curtool>0 && def->UseTarget>=ITEM_USE_CLICKBUTTON) showusebtn = true;
			else if((horse=getFacedHorse()) && horse->getRiddenByActor()==NULL) showusebtn = true;

			if (def->UseTarget == ITEM_USE_GUN) showGunUseBtns = true;

			for(int i = 0; i<(int)def->SkillID.size(); i++)
			{
				if(def->SkillID[i])
				{
					const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
					for(int j = 0; j<(int)skilldef->SkillFuncions.size(); j++)
					{
						ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
						 if(functiondef->oper_id == 7)//有投射效果
						 {
							showCrosshair = true;
						 }
					}
					if(skilldef->Key == 2)
					{
						showusebtn = true;
						showCrosshair = true;
					}
				}
			}
			if (getSkillComponent() && !(getSkillComponent()->skillEmpty()))
			{
				showusebtn = true;
				showCrosshair = true;
			}
		}
		else if(curtool == 0)	//空手
		{
			//if((horse=getFacedHorse()) && horse->getRiddenByActor()==NULL) showusebtn = true;
			horse = getFacedHorse();
			if (horse && horse->getRiddenByActor() == NULL)  showusebtn = true;
		}

		//坐骑右键使用技能
		ActorHorse* riding = getRidingHorse();
		if (riding && riding->showUseBtn())
		{
			showusebtn = true;
		}

		bool showBallUseBtn = false;
		bool showBasketBallUseBtn = false;
		bool showPushBallUseBtn = false;
		if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
		{
			showusebtn = true;
			showCrosshair = false;
			// showGunUseBtns = false; // 注释掉，保持Gun按钮长期显示
			showBasketBallUseBtn = false;
			showBallUseBtn = true;
		}

		if (getOPWay() == PLAYEROP_WAY_BASKETBALLER)
		{
			showusebtn = true;
			showCrosshair = true;
			// showGunUseBtns = false; // 注释掉，保持Gun按钮长期显示
			showBallUseBtn = false;
			showBasketBallUseBtn = true;
		}

		if (getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
		{
			showusebtn = true;
			showCrosshair = false;
			// showGunUseBtns = false; // 注释掉，保持Gun按钮长期显示
			showBasketBallUseBtn = false;
			if (getCatchBall())
			{
				showPushBallUseBtn = true;
			}
		}

		if (isShapeShift())
		{
			// showusebtn = false;  // 注释掉，保持Use按钮长期显示
			showCrosshair = false;
			// showGunUseBtns = false; // 注释掉，保持Gun按钮长期显示
			showBallUseBtn = false;
			showBasketBallUseBtn = false;
		}

		//铲子 耙 显示使用按钮
		if (IsHoelID(curtool)|| IsShovelID(curtool))
		{
			showusebtn = true;
		}

		//右键使用
		if(GetDefManagerProxy()->IsShowUseBtnForItemRightUse(curtool))
		{
			showusebtn = true;
		}

		if(m_TouchCtrl){
			m_TouchCtrl->showUseBtn(showusebtn);
			m_TouchCtrl->showCrosshair(showCrosshair);
			m_TouchCtrl->showGunBtns(showGunUseBtns);
			m_TouchCtrl->showBallUseBtn(showBallUseBtn);
			m_TouchCtrl->showBasketBallUseBtn(showBasketBallUseBtn);
			m_TouchCtrl->showPushSnowBallUseBtn(showPushBallUseBtn);
			m_TouchCtrl->tick();
		}
	}
	else
	{
		if(m_PCCtrl) m_PCCtrl->tick();
	}

	if(GetWorldManagerPtr()->isGameMakerRunMode())
	{
		if( (!GetWorldManagerPtr()->m_RuleMgr->hasPrePoint(getTeam()) && GetWorldManagerPtr()->m_RuleMgr->getGameStage() != CGAME_STAGE_RUN) 
			|| GetWorldManagerPtr()->m_RuleMgr->getGameStage() == CGAME_STAGE_RESETCOUNTDOWN)
		{
			setStill();
			getLocoMotion()->m_Motion.Set(0.0f, 0.0f, 0.0f);
		}
	}

	if (getLocoMotion() && getLocoMotion()->isInLiquid() && !m_PlayerAttrib->isStrengthEnough(GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_swimming_per_second))
	{
		setStill();
		GetLuaInterfaceProxy().showGameTips(1571);
	}

	//铲球的时候 不允许移动
	if (getCurOperate() == PLAYEROP_TACKLE)
	{
		setStill();
	}
	//抢断球的时候 不允许移动
	if (getCurOperate() == PLAYEROP_BASKETBALL_GRAB)
	{
		setStill();
	}
// 	//盖帽的时候，不允许移动
// 	if (getCurOperate() == PLAYEROP_BASKETBALL_BLOCK_SHOT)
// 	{
// 		setStill();
// 	}
	//带球冲刺的时候，不允许移动
	if (getCurOperate() == PLAYEROP_BASKETBALL_DRIBBLERUN)
	{
		setStill();
	}

	// 坐在椅子上不可移动
	if(getSitting() && getLocoMotion())
	{
		setStill();
		getLocoMotion()->m_Motion.Set(0.0f, 0.0f, 0.0f);
	}

	//被限制行动时，不允许移动
	if (getFreezing() == FREEZING_STATE_NOMOVE)
	{
		setStill();
	}

	//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_CheckIdleAnimPlay", SandboxContext(nullptr).SetData_Userdata("PlayerControl", "player", this));
	MNSandbox::GetGlobalEvent().Emit<PlayerControl*>("WeaponSkin_System_CheckIdleAnimPlay", this);


	bool success = false;
	bool ret = false;
	bool succ = MNSandbox::GetGlobalEvent().Emit<bool&, bool&>("Music_CheckisCanMove", success, ret);

	//演奏时根据配置，不允许移动
	if (succ && !success) {
		setStill();
	}

	if( (m_MoveForward != 0 || m_MoveRight != 0 || m_MoveUp != 0) && !checkActionAttrState(ENABLE_MOVE))
	{
		setStill();
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14000);
	}

	//身上有无法移动效果
	if (getPlayerAttrib() && getPlayerAttrib()->hasStatusEffect(STATUS_EFFECT_CANNOTMOVE) ||
		getPlayerAttrib()->hasStatusEffect(STATUS_EFFECT_DROP) ||
		getPlayerAttrib()->hasStatusEffect(STATUS_EFFECT_PERCIPIENCE))
	{
		setStill();
	}
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding() && checkActionAttrState(ENABLE_VEHICLEAUTOFORWARD))
		m_MoveForward = 1;
	
	//骑乘蟹时只能横着走
	if (isRidingOnCrab())
		m_MoveForward = 0;

	resetEyeDir();

	if ((getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL) && getCatchBall())
	{
		float strafing = m_MoveRight;
		if (getViewMode() == CAMERA_FPS || getViewMode() == CAMERA_TPS_BACK || getViewMode() == CAMERA_TPS_BACK_SHOULDER)
		{
			strafing = 0;
		}

		float yaw = getLocoMotion()->m_RotateYaw + strafing * 5;
		getLocoMotion()->m_RotateYaw = yaw;
		if (isNewMoveSyncSwitchOn())
		{
			setMoveControlYaw(yaw);
		}
	}
	updateMoveControlFromInputinfo();
	ClientPlayer::tick();
	
	if(m_DieRecordTicks >= 0)
	{
		m_DieRecordTicks++;
		if(m_DieRecordTicks > 5*60*1000/GAME_TICK_MSEC)
		{
			m_DieRecordTicks = -1;
		}
	}

	tickUIOp();

	if(!isDead())
	{
		int displayoxygen = (int)getLivingAttrib()->getOxygen();
		float oxygen_userate = getOxygenUseRate();
		if (m_pWorld->getCurMapID() >= MAPID_MENGYANSTAR) oxygen_userate *= GetLuaInterfaceProxyPtr()->get_lua_const()->planet_oxygenuse_beilv;
		if(getLocoMotion() && (!getLocoMotion()->isInsideNoOxygenBlock() || oxygen_userate == 0 || oxygen_userate >=2 || !isInWater()) && displayoxygen>=START_OXYGEN)
		{
			displayoxygen = -1;
		}

		if(displayoxygen != m_DisplayOxygen)
		{
			m_DisplayOxygen = displayoxygen;
			//ge GetGameEventQue().postEnterWater(m_DisplayOxygen>=0);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Bool("show", m_DisplayOxygen >= 0);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SHOW_OXYGEN", sandboxContext);
			}
		}
	}

	checkAttribChange();

	if(m_pWorld->getOWID()==NEWBIEWORLDID)
	{
		auto target = getAtkingTarget();
		if(target && target->needClear())
		{
			checkNewbieWorldProgress(26, "killzombie");
			setAtkingTarget(NULL);
		}
	}

	//检查观战玩家位置
	if(m_nToSpectatorUin)
	{
		 m_nCheckSpectatorTick++;
		 if(m_nCheckSpectatorTick >= 20)
		 {
			 m_nCheckSpectatorTick = 0;
			 ClientPlayer* player =m_pWorld->getActorMgr()->ToCastMgr()->findPlayerByUin(m_nToSpectatorUin);
			 if	(player == NULL && getSpectatorType() == SPECTATOR_TYPE_FOLLW)
			 {
				auto game = GetIClientGameManagerInterface()->getICurGame(GameType::MpGameSurvive);
				if(game)
				{
					PlayerBriefInfo *info = game->findPlayerInfoByUin(m_nToSpectatorUin);
					if(info)
					{								  
						PB_ActorTeleportCH actorTeleportCH;
						actorTeleportCH.set_objid(getUin());
						actorTeleportCH.set_targetmap(getCurMapID());

						PB_Vector3* targetPos = actorTeleportCH.mutable_targetpos();
						targetPos->set_x(info->x);
						targetPos->set_y(info->y);
						targetPos->set_z(info->z);

						GetGameNetManagerPtr()->sendToHost(PB_ACTOR_TELEPORT_CH,actorTeleportCH);
					}
			   }
		    }
		}
	}

	if(m_iTickTriggerCount >= 10)
	{
		m_iTickTriggerCount = 0;
		triggerActorAttribChunk();
	}

	m_iTickTriggerCount++;

	/* //自动寻路导致的运动状态改变暂时不处理
	bool IsNaviFinished = true;
	if(m_NaviPath && m_NaviPath->getPath()) {
		IsNaviFinished = m_NaviPath->getPath()->isFinished();
		if (IsNaviFinished) m_InputInfo->moveForward = 0.0f;
	}*/

	if (m_iTryShapeShiftID > 0 && getBody() && !getBody()->hasAnimPlaying(SEQ_SHAPE_SHIFT))
	{
		shapeShift();
	}

	if (m_iRecoverViewMode >= 0 && m_bWaitRecoverViewMode && getBody() && !getBody()->hasAnimPlaying(SEQ_RE_SHAPE_SHIFT))
	{
		setViewMode(m_iRecoverViewMode);
		m_iRecoverViewMode = -1;
		m_bWaitRecoverViewMode = false;
	}
}

void PlayerControl::tickUIOp()
{
	if(m_pWorld == NULL) return;
	if(GetWorldManagerPtr() == NULL) return;
	//cold down
	if(m_SwitchTick > 0) m_SwitchTick--;

	doPick(false);
}

void PlayerControl::tickBlockLine()
{
	//Pick 到了 Block
	if(m_PickType == 1/* && m_TouchCtrl->isSightMode()*/ && (!GetWorldManagerPtr()->isCustomGame() || !GetClientInfoProxy()->isPC() || ((GetWorldManagerPtr()->m_RuleMgr) && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SHOW_SIGHT) == 1)))
	{
		auto blockid = m_pWorld->getBlockID(m_PickResult.block);

		// 原有的线框高亮效果
		if(m_pWorld->getRender())  m_pWorld->getRender()->setWireBlockPos(m_PickResult.block, isSpecialBlock(blockid));
		m_pWorld->showBlockHealthBar(m_PickResult.block, getPosition(), isToolHammer(getCurToolID()) ? 0 : 1);
	}
	else
	{
		// 清除高光和描边效果现在都在clearWireBlock中自动处理
		if( m_pWorld->getRender()) m_pWorld->getRender()->clearWireBlock();
		m_pWorld->hideBlockHealthBar();
	}
	showNearSpecialBlockOutline();
}

void PlayerControl::tickActorLine()
{
	//UpdateActorSelection();
}

bool PlayerControl::isTriggerTrap()
{
	LivingAttrib* attr = getLivingAttrib();
	if (attr)
	{
		return getLivingAttrib()->hasBuff(LOCK_MOB_BUFF);
	}

	return false;
}

void PlayerControl::tickOperate()
{
	if (m_CurOperate != PLAYEROP_DIG) {
		if (g_WorldMgr == NULL) return;
		LivingAttrib* attrib = dynamic_cast<LivingAttrib*> (getAttrib());
		if (attrib && attrib->hasBuff(77, 2))
		{
			attrib->removeBuff(77);
		}
	}
	if (m_CurOperate == PLAYEROP_NULL) return;
	m_OperateTicks++;
	if(GetWorldManagerPtr() == NULL) return;
	//LOG_INFO("PlayerControl::tickOperate(): m_OperateTicks = %d", m_OperateTicks);
	if (m_CurOperate == PLAYEROP_SHOOT || m_CurOperate == PLAYEROP_PASS_BALL)
	{
		if (m_OperateData <= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ignore_delay_max_charge || m_OperateTicks == 10)
		{
			if(m_pWorld != NULL){
				ClientActor *ball = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_lOperrateData);
				kickBall(m_CurOperate, (float)m_OperateData, ball);
			}
		}
	}
	if (m_CurOperate == PLAYEROP_PUSHSNOWBALL_SHOOT)
	{
		if (m_OperateData <= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ignore_delay_max_charge || m_OperateTicks == 10)
		{
			if (m_pWorld != NULL) {
				ClientActor* ball = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_lOperrateData);
				kickPushSnowBall(m_CurOperate, (float)m_OperateData, ball);
			}
		}
	}
	if (m_CurOperate == PLAYEROP_DIG)
	{
		//空手挖凝浆块每秒会受到伤害 code by: yangjie
		if (m_CurDigBlockID == BLOCK_COAGULATION && getCurToolID() == 0 && m_OperateTicks % 20 == 0 && !isDead() && !needClear())
		{
			ActorAttrib* attrib = getAttrib();
			if (attrib)
			{
				if (attrib->immuneToFire() <= 0) setFire(100, 1);
				if (attrib->immuneToFire() <= 1)
				{
					auto component = getAttackedComponent();
					if (component)
					{
						component->attackedFromType_Base(ATTACK_FIRE, 5.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv); //modify by null, 岩浆的伤害翻5倍
					}
				}
			}
		}
		//空手挖仙人掌会停止当前操作并受到伤害 code by: chenkaite

		else if ((m_CurDigBlockID == BLOCK_CACTUS || m_CurDigBlockID == BLOCK_CACTUSBRANCH || m_CurDigBlockID == BLOCK_CACTUSSEED) && getCurToolID() == 0 && m_OperateTicks % 10 == 0 && !isDead() && !needClear())
		{
			ActorAttrib* attrib = getAttrib();

			if (attrib)
			{
				auto component = getAttackedComponent();
				if (component)
				{
					setFire(77, 1);
					component->attackedFromType(ATTACK_CACTUS, 3.0f);
					auto motion = getLocoMotion()->m_Motion;
					motion.x += 5;
					motion.z += 5;
					setMotionChange(motion);

				}
			}
			m_CurOperate = PLAYEROP_NULL;
			m_OperateTicks = 0;
		}//挖咒岩每秒会受到伤害 code by: wuweiwei
		else if (m_CurDigBlockID == BLOCK_CURSE_STONE && !isDead())
		{
			LivingAttrib* attrib = dynamic_cast<LivingAttrib*> (getAttrib());
			if (attrib && !attrib->hasBuff(77, 2))
			{
				attrib->addBuff(77, 2, -1, 0);
			}
		}
		else if (m_CurDigBlockID == BLOCK_COCONUE_WOOD && m_OperateTicks > 0 && m_OperateTicks % 20 == 0 && !isDead() && !needClear())
		{
			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				SandboxCoreSubsystem* module = PluginManager::GetInstancePtr()->FindSubsystem<SandboxCoreSubsystem>();
				if (module == nullptr)
				{
        return;
    }
				if (m_OperateData <= 0)
				{
					if (GenRandomInt(10) >= 4)
					{
						module->clearCoconue();
						module->setCoconutKnock(true);
						m_pWorld->notifyBlock(m_CurDigBlockPos, m_CurDigBlockID);
					}
				}
				
			}
		}
		//空手挖掘 锯齿蕨
		else if ((m_CurDigBlockID == BLOCK_SAWTOOTH || m_CurDigBlockID == BLOCK_NO_SAWTOOTH) && getCurToolID() == 0 && m_OperateTicks % 10 == 0 && !isDead() && !needClear())
		{
			//给自己反弹
			auto thornComponent = sureThornBallComponent();
			if (thornComponent != nullptr)
			{
				int tmepdir = thornComponent->checkCrashDir();
				thornComponent->reboundsAttackedRound(3.0f, tmepdir);
			}
			if (m_CurDigBlockID == BLOCK_SAWTOOTH)
			{
				if (!m_pWorld->isRemoteMode())
				{
					//转换成 无球锯齿蕨
					int blockData = m_pWorld->getBlockData(m_CurDigBlockPos);
					m_pWorld->setBlockAll(m_CurDigBlockPos, BLOCK_NO_SAWTOOTH, blockData & 3);
					ClientActorThornBall::createSawtooth(getWorld(), m_CurDigBlockPos, this);
				}
			}
			m_CurOperate = PLAYEROP_NULL;
			m_OperateTicks = 0;
		}
		//空手挖掘 锯齿墙
		else if ((m_CurDigBlockID == BLOCK_STONE_WALL
			|| m_CurDigBlockID == BLOCK_MOSSYSTONE_WALL
			|| m_CurDigBlockID == BLOCK_HORASROCK_WALL) && getCurToolID() == 0 && m_OperateTicks % 10 == 0 && !isDead() && !needClear())
		{
			//弹开
			auto thornComponent = sureThornBallComponent();
			if (thornComponent != nullptr)
			{
				int tmepdir = thornComponent->checkCrashDir();
				thornComponent->reboundsAttackedRound(6.0f, tmepdir);
			}
			m_CurOperate = PLAYEROP_NULL;
			m_OperateTicks = 0;
		}
		else if (m_CurDigBlockID == BLOCK_WEAK_ICICLE && m_OperateTicks > 0 && m_OperateTicks % 10 == 0 && !isDead() && !needClear())//挖冰凌
		{
			if (GenRandomInt(100) < 10)
			{
				int blockcount = 0;
				int upblockcount = 0;
				vector<WCoord> posArray;
				WCoord _downblockpos = m_CurDigBlockPos;
				while (m_pWorld->getBlockID(_downblockpos) == BLOCK_WEAK_ICICLE)
				{
					posArray.emplace_back(_downblockpos);
					_downblockpos = _downblockpos - WCoord(0, 1, 0);
					blockcount++;
				}
				WCoord _upblockpos = m_CurDigBlockPos + WCoord(0, 1, 0);
				while (m_pWorld->getBlockID(_upblockpos) == BLOCK_WEAK_ICICLE)
				{
					posArray.emplace_back(_upblockpos);
					_upblockpos = _upblockpos + WCoord(0, 1, 0);
					blockcount++;
					upblockcount++;
				}
				//下方有至少1格空间则坠落并且敲击概率10%
				if (blockcount > 0 && m_pWorld->getBlockID(_downblockpos) == BLOCK_AIR)
				{
					for (int i =0; i < posArray.size();i++)
					{
						m_pWorld->setBlockAir(posArray[i]);
					}
					WCoord shootPos = (upblockcount > 0) ? DownCoord(_upblockpos) : m_CurDigBlockPos;
					auto pos = BlockBottomCenter(shootPos);
					Rainbow::GetMusicManager().PlaySound("sounds/item/32/prepare.ogg", pos.toVector3(), 1.0f, 1.0f);
					ClientActorIcicle::shootIcicleAuto(ITEM_WEAK_ICICLE, m_pWorld, BlockBottomCenter(shootPos), blockcount);
				}
			}
		}
	}
}

void PlayerControl::setStill()
{
	m_MoveForward = 0;
	m_MoveRight = 0;
	m_MoveUp = 0;
}

void PlayerControl::setOPWay(int way)
{
	if (m_OPWay == way  || isSleeping() || isRestInBed())
	{
		return;
	}
	int prevWay = m_OPWay;
	ClientPlayer::setOPWay(way);
	if (way == PLAYEROP_WAY_FOOTBALLER)
	{
		toActionState("ToFootball");
		if (getViewMode() < CAMERA_TPS_OVERLOOK)
		{
			setViewMode(CameraControlMode::CAMERA_TPS_BACK);
		}
	}
	else if (way == PLAYEROP_WAY_BASKETBALLER)
	{
		toActionState("ToBasketball");
		if (getViewMode() < CAMERA_CUSTOM_VIEW)
		{
			setViewMode(CAMERA_TPS_BACK_2);
		}
		if (!checkIfDataUpload(Upload_BasketBallWear_Use))
		{
			std::stringstream sParam1;
			sParam1 << m_pWorld->getOWID();

			statisticToWorld(g_pPlayerCtrl->getUin(), 30031, "", g_pPlayerCtrl->getCurWorldType(), "param_to_str", sParam1.str().c_str());
			setUploadStatus(Upload_BasketBallWear_Use);
		}
	}
	else if (way == PLAYEROP_WAY_PUSHSNOWBALL)
	{
		toActionState("ToPushSnowBall");

		setViewMode(CAMERA_TPS_BACK_2);

	}
	else if (prevWay >= PLAYEROP_WAY_FOOTBALLER && prevWay <= PLAYEROP_WAY_BASKETBALLER || prevWay == PLAYEROP_WAY_PUSHSNOWBALL)
	{
		if (nullptr != m_StateController)
		{
			if (prevWay == PLAYEROP_WAY_BASKETBALLER)
			{
				toActionState("ToBasketball");
			}
			else if (prevWay == PLAYEROP_WAY_FOOTBALLER)
			{
				toActionState("ToFootball");
			}
			toActionState("ToActionIdle");
		}
		if (getViewMode() < CAMERA_TPS_OVERLOOK && !isShapeShift())
		{
			if (g_pPlayerCtrl->m_pCamera)
			{
				g_pPlayerCtrl->m_pCamera->setDistMultiply(1.0f);
			}
			/*XMLNode node = Root::getSingleton().m_Config.getRootNode().getChild("GameData");
			if (!node.isNull())
			{
				XMLNode child = node.getChild("Settinig");
				if (!child.isNull())
				{
					int mode = child.attribToInt("view") - 1;
					g_pPlayerCtrl->setViewMode(mode);
				}
			}*/
			int mode = GetClientInfoProxy()->getGameData("view") - 1;
			if (mode >= 0)
			{
				g_pPlayerCtrl->setViewMode(mode);
			}
		}
	}

	MINIW::ScriptVM::game()->callFunction("UpdateUIBtnByOPWayChange", "");
}

void PlayerControl::checkAttribChange()
{
	PlayerAttrib *attr = getPlayerAttrib();
	if(m_OldOxygen!=attr->getOxygen())
	{
		m_OldOxygen = attr->getOxygen();
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}

	ActorHorse *horse = getRidingHorse();
	if(horse)
	{
		if((horse->CanDriver() && m_HorseCharge!=horse->getCurCharge()) || m_HorseHP!=int(horse->getAttrib()->getHP()))
		{		
			m_HorseCharge = horse->getCurCharge();
			m_HorseHP = int(horse->getAttrib()->getHP());
			//ge GetGameEventQue().postPlayerAttrChange();
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}
	}
	else
	{
		m_HorseCharge = -1;
		m_HorseHP = -1;
	}
}

int PlayerDistFromRailLastBlock(ClientPlayer *player, WCoord *ret_handpos)
{
	WCoord handpos = WCoord(0,BLOCK_SIZE/2,0) + WCoord(player->getLocoMotion()->getFramePosition());
	if(ret_handpos) *ret_handpos = handpos;
	float dist = (handpos - BlockCenterCoord(player->m_LastTriggerBlock)).length();
	if(dist > 3000.0f) return -1;
	else if(dist >1500.0f) return 1;
	else return 0;
}
void PlayerControl::renderWithLastTriggerBlocks()
{
	int blockid = m_pWorld->getBlockID(m_LastTriggerBlock);
	if(BlockRailKnot::isRailBlock(blockid))
	{
		if(!BlockRailKnot::isRailBlock(getCurToolID()))
		{
			m_LastTriggerBlock = WCoord(0,-1,0);
			return;
		}
		WCoord handpos;
		int disttype = PlayerDistFromRailLastBlock(this, &handpos);
		if(disttype >= 0)
		{
			CatmullRomCurve cc;
			BlockRailKnot::getCurveTo(cc, m_pWorld, m_LastTriggerBlock, handpos, m_LastTriggerBlock);
			m_pWorld->getRender()->getCurveRender()->addCurve(disttype, cc, m_LastTriggerBlock*BLOCK_SIZE);
		}
		else m_LastTriggerBlock = WCoord(0,-1,0);
	}
	else if (BlockRopeHeader::isRopeHead(blockid))
	{
		//判断超出车间范围外
		WCoord handpos;
		int disttype = PlayerDistFromRailLastBlock(this, &handpos);
		if (disttype >= 0)
		{
			CatmullRomCurve cc;
			BlockRailKnot::getCurveTo(cc, m_pWorld, m_LastTriggerBlock, handpos, m_LastTriggerBlock);
			m_pWorld->getRender()->getCurveRender()->addCurve(disttype, cc, m_LastTriggerBlock*BLOCK_SIZE);
		}
		else m_LastTriggerBlock = WCoord(0, -1, 0);
	}
}

void PlayerControl::updateSuper(float dtime)
{
	ClientPlayer::update(dtime);
}

void PlayerControl::onActorHorseMounted()
{
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	if (getBody() && !getBody()->getIsAttachModelView())
#else
	if (getBody() && !getBody()->getAttachedModelView())
#endif
	{
		auto RidComp = getRiddenComponent();
		if (RidComp && RidComp->isRiding() && RidComp->getRidingActor() != NULL)
		{
			ActorHorse* horse = getRidingHorse();
			if (horse)
				horse->OnHorseMounted();
		}
	}
}

void PlayerControl::updateMoveControlFromInputinfo()
{
	if (isNewMoveSyncSwitchOn())
	{		
		if (GetWorldManagerPtr())
		{
			if (m_InputInfo->sneak)
			{
				if (!getSneaking())
					changeMoveFlag(IFC_Sneak, true);
			}
			else
			{
				if (getSneaking())
					changeMoveFlag(IFC_Sneak, false);
			}
		}
		
		if (m_InputInfo->jump)
			addMoveStatus(IMT_Jump);
		
		int fly_up = m_InputInfo->flyUp;
		if (fly_up > 0)
			addMoveStatus(IMT_Up);
		else if (fly_up < 0)
			addMoveStatus(IMT_Down);

		// 看代码目前 m_MoveForward 和 m_MoveRight 只有 -1, 0, 1三种赋值
		if (m_MoveForward > 0.1)
			addMoveStatus(IMT_Forward);
		else if (m_MoveForward < -0.1)
			addMoveStatus(IMT_Back);
		
		if (m_MoveRight > 0.1)
			addMoveStatus(IMT_Right);
		else if (m_MoveRight < -0.1)
			addMoveStatus(IMT_Left);
	}
}

void PlayerControl::update(float dtime)
{
	if (GetWorldManagerPtr()->isNewSandboxNodeGame())
	{
		return;
	}
	m_InputHelper->onUpdate(dtime);

	UpdateActorSelection();
	// 相机编辑状态，不控制人物移动，只控制相机移动
	if (CameraManager::GetInstance().getCameraEditState() == CAMERA_EDIT_STATE_EDIT
		|| CameraManager::GetInstancePtr()->m_ControlType == FreeFlyControl)
	{
		m_InputInfo->moveForward = m_InputInfo->moveStrafe = m_InputInfo->flyUp = 0.0f;
	}

	updatePlayer(dtime);
	m_StateController->update(dtime);

	ClientPlayer::update(dtime);

	updateGameCamera(dtime);

	onActorHorseMounted();

	// Update block placement preview
	updateBlockPlacementPreview();
}

void PlayerControl::updatePlayer(float dtime)
{
	if (getFlying())
	{
		int dir = (int)m_InputInfo->flyUp;
		setMoveUp(dir);
		if (isNewMoveSyncSwitchOn())
		{
			if (dir == 1)
				addMoveStatus(IMT_Up);
			else if (dir == -1)
				addMoveStatus(IMT_Down);
		}
	}
	else {
		if (GetClientInfoProxy()->isMobile())
		{
			if (m_InputInfo->sneak)
			{
				// MINIW::ScriptVM::game()->callFunction("AccelKey_Shift", "");
			}
		}
		else
		{
			if (getSneaking() && !m_InputInfo->sneak)
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Shift", "");
			}
			else if (!getSneaking() && m_InputInfo->sneak)
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Shift", "");
			}
		}
	}

	if (m_InputInfo->triggerJump)
	{
		if (getCurDorsumID() != ITEM_JETPACK)
		{
			GetMusicManager().PlaySound2D("sounds/ui/button/jump.ogg", 1.0);
		}
	}

	updateRun();


	auto RidComp = getRiddenComponent();
	//玩家如果乘坐的是载具，在加速器激活的时候，做一下镜头拉伸
	if (RidComp && RidComp->isRiding()) {
		//auto actor = getWorld()->getActorMgr()->findActorByWID(m_RidingActor);
		auto actor = RidComp->getRidingActor();
		if (actor && actor->getObjType() == OBJ_TYPE_VEHICLE) {
			ActorVehicleAssemble* actorVehicleAssemble = static_cast<ActorVehicleAssemble*>(actor);
			if (actorVehicleAssemble->hasThrusterActive(1))
				m_pCamera->setZoomInOut(m_pCamera->getFov() + GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.propeller_lens_tensile, 3, 2);
			else
				m_pCamera->disableZoom();
		}
	}
	auto funcWrapper = getFuncWrapper();
	bool isBtreeControl = funcWrapper ? funcWrapper->getBtreeControl() : false;
	if (!(m_MotionCtrl.isValid() || isBtreeControl))	// 如果设定为保持移动，则不受输入控制
	{
		m_MoveForward = m_InputInfo->moveForward;
		m_MoveRight = m_InputInfo->moveStrafe;
	}

	if (getCurDorsumID() == ITEM_FIRE_ROCKET)
	{
		if (!isNewMoveSyncSwitchOn())
		{
			m_MoveForward = m_MoveRight = 0.0f;

			Rainbow::Vector3f lookdirToGo = m_pCamera->getLookDir();
			lookdirToGo *= 130.0f*dtime;
			getLocoMotion()->m_Motion += lookdirToGo;

			if(m_InputInfo->jump)
			{
				getLocoMotion()->m_Motion.y += 100.0f*dtime;
			}

			if(getLocoMotion()->m_OnGround)
			{
				getLocoMotion()->m_Motion.y += 25;
				auto functionWrapper = getFuncWrapper();
				if (functionWrapper)
				{
					functionWrapper->setImmuneFall(1);
				}
				getLocoMotion()->setOnGround(false);
			}
		}
		
		m_CameraModel->setCurDorsum(ITEM_FIRE_ROCKET);
	}
	else
	{
		m_CameraModel->setCurDorsum(0);
	}

	if (!isNewMoveSyncSwitchOn())
	{
		if (getCurDorsumID() == ITEM_SNAKEGOD_WING) //蛇神之翼
		{
			if (m_InputInfo->triggerJump && m_DisplayOxygen < 0 && !getFlying())
			{
				if (m_InputInfo->wingState >= 1 && !getLocoMotion()->m_OnGround && m_wingFlyTime <= -1.0f)//阶段1下落阶段可以进入阶段2
				{
					m_InputInfo->wingState = 2;
					m_wingForwardSpeed = 1.2f;
					m_wingFlyTime = 3.0f;
					getLocoMotion()->m_Motion.y += 10.0f;
				}
				else if (m_InputInfo->wingState == 0)
				{
					if (getRun() && getLocoMotion()->m_OnGround && m_MoveForward > 0.0f)//地面跑步起飞进入阶段1
					{
						m_InputInfo->wingState = 1;
						m_wingForwardSpeed = 1.2f;
						m_wingFlyTime = 3.0f;
						getLocoMotion()->setOnGround(false);
					}
					else if (!getLocoMotion()->m_OnGround && m_wingFlyTime <= -1.0f) //空中坠落进入阶段1
					{
						m_InputInfo->wingState = 1;
						m_wingForwardSpeed = 1.2f;
						m_wingFlyTime = 3.0f;
						getLocoMotion()->m_Motion.y += 10.0f;
						getLocoMotion()->setOnGround(false);
					}
				}
			}

			Rainbow::Vector3f lookdirToGo = m_pCamera->getLookDir();
			lookdirToGo.y = 0.0f;

			if (m_InputInfo->wingState > 0)
			{
				if (getFlying() || m_DisplayOxygen > 0)
					m_InputInfo->wingState = 0;
				m_MoveForward = m_MoveRight = 0.0f;
				if (m_wingForwardSpeed > 0.8f) 
				{
					m_wingForwardSpeed -= 0.2f * dtime;
					if (m_wingForwardSpeed < 0.8f)
						m_wingForwardSpeed = 0.8f;
				}

				m_wingFlyTime -= dtime;
				if (m_wingFlyTime > 0.0f) //上升
				{
					getLocoMotion()->m_Motion.y += 20 * dtime;
				}
				float r = (m_wingForwardSpeed * BLOCK_SIZE * dtime) / Sqrt(lookdirToGo.x*lookdirToGo.x + lookdirToGo.z * lookdirToGo.z);
				lookdirToGo *= r;
				getLocoMotion()->m_Motion += lookdirToGo;
				m_SnakeGodWingFlying = true;
			}
			else
			{
				if (getFlying())
					m_wingFlyTime = 0.0f;
				else if (!getLocoMotion()->m_OnGround && m_DisplayOxygen < 0)
					m_wingFlyTime -= dtime;
				m_SnakeGodWingFlying = false;
			}

			if (getLocoMotion()->m_OnGround)
			{
				if (m_InputInfo->wingState == 0)
				{
					m_wingFlyTime = 0.0f;
				}
				if (m_wingFlyTime <= 2.5f && m_InputInfo->wingState > 0)
				{
					m_InputInfo->wingState = 0;
					lookdirToGo *= 6.0f;
					getLocoMotion()->m_Motion += lookdirToGo;
				}
				auto functionWrapper = getFuncWrapper();
				if (functionWrapper)
				{
					functionWrapper->setImmuneFall(1);
				}
			}
		}
	}
	

	if (m_ViewMode == CAMERA_CUSTOM_VIEW)
	{
		int movement_limit = getCameraConfigOption(CAMERA_OPTION_INDEX_MOVEMENT_LIMIT);

		// 侧视角调整移动方向
		if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_FLATVIEW)
		{
			int rotate_type = getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING);
			int rotate_limit = getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING_LIMIT);

			if ( (rotate_type == CRT_CAMERA_AND_BODY || rotate_type == CRT_ONLY_BODY))
				//&& (rotate_limit == CRL_NO_LIMIT || rotate_limit == CRL_ONLY_X))
			{
				// 前进
				if (m_MoveRight > 0.5f)
				{
					if (m_CurrentCameraConfig.HasReversed) m_CurrentCameraConfig.NeedReverse = true;
				}
				// 后退
				if (m_MoveRight < -0.5f)
				{
					if (!m_CurrentCameraConfig.HasReversed) m_CurrentCameraConfig.NeedReverse = true;
				}
			}

			auto *rocket = getRidingRocket();
			if (rocket)
			{
				int rocketYaw = (int)rocket->getLocoMotion()->m_RotateYaw%360;
				int cameraYaw = (int)m_pCamera->m_RotateYaw%360;
				if (rocketYaw < 0) rocketYaw += 360;
				if (cameraYaw < 0) cameraYaw += 360;
				if (rocketYaw < cameraYaw && cameraYaw-rocketYaw < 180)  //火箭朝向在摄像机的左边
				{
					float tmp = m_MoveForward;
					m_MoveForward = -m_MoveRight;
					m_MoveRight = -tmp;
				}
				else
				{
					float tmp = m_MoveForward;
					m_MoveForward = m_MoveRight;
					m_MoveRight = tmp;
				}
			}
			else
			{
			if (!m_CurrentCameraConfig.HasReversed && !m_CurrentCameraConfig.NeedReverse
				|| m_CurrentCameraConfig.HasReversed && m_CurrentCameraConfig.NeedReverse)
			{
				float tmp = m_MoveForward;
				m_MoveForward = m_MoveRight;
				m_MoveRight = -tmp;
			}
			else
			{
				float tmp = m_MoveForward;
				m_MoveForward = -m_MoveRight;
				m_MoveRight = tmp;
			}
		}

		}

		// 移动限制
		switch (movement_limit)
		{
		case CML_ONLY_X:
			m_MoveForward = 0.0f;
			break;
		case CML_ONLY_Y:
			m_MoveRight = 0.0f;
			break;
		case CML_ONLY_XY:
			if (abs(m_MoveForward) > 0.1f && abs(m_MoveRight) > 0.1f)
			{
				m_MoveRight = 0.0f;
			}
			break;
		}
	}

	if(getRun())
	{
		if (GetClientInfoProxy()->isMobile())
		{
			if (m_TouchCtrl && m_TouchCtrl->isRockerMode())
			{
				if (m_InputInfo->dpadValueY < 0.5f)
				{
					if (isNewMoveSyncSwitchOn())
						changeMoveFlag(IFC_Run, false);
					else
						setRun(false);
				}
			}
		}
		if ((getOPWay() != PLAYEROP_WAY_BASKETBALLER  && m_MoveForward <= 0) 
		|| (getOPWay() == PLAYEROP_WAY_BASKETBALLER && m_MoveForward == 0 && m_MoveRight == 0)
		|| getOPWay() == PLAYEROP_WAY_BASKETBALLER && getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
		{
			if (isNewMoveSyncSwitchOn())
				changeMoveFlag(IFC_Run, false);
			else
				setRun(false);
		}
	}
	if (!(m_MotionCtrl.isValid() || isBtreeControl))	// 如果设定为保持移动，则不受输入控制
	{
		auto RidComp = getRiddenComponent();
		if (RidComp && RidComp->isRiding())
		{
			if (m_InputInfo->triggerJump)
			{
				if (isNewMoveSyncSwitchOn())
					addMoveStatus(IMT_Jump);
				else
					setJumping(true);
			}

			if (m_InputInfo->jumpRelease)
			{
				if (isNewMoveSyncSwitchOn())
					removeMoveStatus(IMT_Jump);
				else
					setJumping(false);
			}
		}
		else
		{
			if (m_InputInfo->jump && !isSittingInStarStationCabin())  //在传送舱里不允许跳跃
			{
				if (isNewMoveSyncSwitchOn())
					addMoveStatus(IMT_Jump);
				else
					setJumping(true);		//设置跳跃
				m_IsJump = true;
			}
			else
			{
				if (isNewMoveSyncSwitchOn())
					removeMoveStatus(IMT_Jump);
				else
					setJumping(false);
			}
		}
	}

	if (!m_InputInfo->isLongPress && !m_InputInfo->tap)
	{
		if(GetClientInfoProxy()->isMobile())
		{
			m_CurMouseX = 0.5f;
			m_CurMouseY = 0.5f;
		}
	}

	if (m_MoveForward != 0 || m_MoveRight != 0)
	{
		auto temperatureComponent = getTemperatureComponent();
		if (temperatureComponent) temperatureComponent->ShakeImpactDuration();
	}
	//检测是否需要打断技能
	{
		bool interrupt = false;
		if ((GetIClientGameManagerInterface() && GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isOperateUI()))
		{
			interrupt = true;
		}
		else
		{
			if (GetClientInfoProxy()->isMobile())
			{
				/*if ((m_TouchCtrl && !m_TouchCtrl->isSightMode()))
				{
					interrupt = true;
				}*/
			}
			else
			{
				if ((m_PCCtrl && !m_PCCtrl->isSightMode()))
				{
					interrupt = true;
				}
			}
		}
		if (interrupt && m_lastSkillInterrupt != interrupt)
		{
			auto comp = getSkillComponent();
			if (comp)
			{
				comp->interrupt();
			}
		}
		m_lastSkillInterrupt = interrupt;
	}
}




void PlayerControl::onDie()
{
	LOG_INFO("PlayerControl::onDie0");
	m_InputInfo->reset();
	ClientPlayer::onDie();

	m_SpaceClickTick = 0;
	m_WKeyClickTick = 0;
	m_DiePos = getPosition();
	m_DieMapID = getCurMapID();
	m_DieRecordTicks = 0;
	MNSandbox::GetGlobalEvent().Emit<>("StatisticTerrgen_PlayerDie",this->getWorld());
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("refresh_map", MNSandbox::SandboxContext(nullptr).SetData_Number("type", MRT_Death));
	int buffId,buffLv;
	getLivingAttrib()->getLastAttackBuff(buffId,buffLv);
	char buffStr[20] = "";
	sprintf(buffStr,"%d%03d", buffId, buffLv);
	notifyGameInfo2Self(PLAYER_NOTIFYINFO_DEATH, 0, 0, "", buffStr);
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("Client_Player_OnDie", MNSandbox::SandboxContext(nullptr).SetData_Userdata("ClientPlayer","player", this));
	if (!GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		SANDBOXPROFILING_FUNC("PlayerControl_onDie_1");
		//ge GetGameEventQue().postSimpleEvent("GE_MAINPLAYER_DIE");
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_MAINPLAYER_DIE", MNSandbox::SandboxContext(nullptr));
	}

	if(getGunLogical()){
		getGunLogical()->setZoom(false);
	}
	/*if (m_ViewMode == CAMERA_FPS)
	{
		m_NeedRevertToFPS = true;
		setViewMode(CAMERA_TPS_BACK);
	}*/

	do
	{
		SANDBOXPROFILING_FUNC("PlayerControl_onDie_2");
		m_StateController->onDie();
	} while(false);
	LOG_INFO("PlayerControl::onDie1");

	//抛事件: 玩家死亡
	//MNSandbox::GlobalNotify::GetInstance().m_PlayerDie.Emit(getUin(), StaticToCast<SandboxNode>());
	if (auto playersService = MNSandbox::GetCurrentPlayersNodeRoot())
		playersService->m_notifyPlayerDie.Emit(getUin(), StaticToCast<SandboxNode>());

	addAchievement(3, ACHIEVEMENT_DIECOUNT, 0, 1);
	updateTaskSysProcess(TASKSYS_PLAYER_DEATH);
	//死亡后修正视角
	if (m_ChangeViewMode)
	{
		m_ChangeViewMode = false;
		g_pPlayerCtrl->setViewMode(m_ViewMode);
	}
}

void PlayerControl::onBuffChange(int chgtype, int buffid, int bufflv, int buffticks, int buffInstanceId)
{
	//add
	//auto pAtt = getPlayerAttrib();
	//if (pAtt && pAtt->isNewStatus()) {
		if (chgtype == 0)
		{
			const BuffDef* def = GetDefManagerProxy()->getBuffDef(buffid, bufflv);
			if (def && def->NeedSwitchTPS)
			{
				if (m_ViewMode == CAMERA_FPS)
				{
					m_NeedRevertToFPS |= 2;
					setViewMode(CAMERA_TPS_BACK);
				}
			}
		}
		else if (getPlayerAttrib() && !getPlayerAttrib()->hasNeedSwitchTPSBuff() && (chgtype == 1 || chgtype == 2))
		{
			if (m_NeedRevertToFPS & 2)
			{
				m_NeedRevertToFPS &= (~2);
				setViewMode(CAMERA_FPS);
			}
		}
	//}

	ClientPlayer::onBuffChange(chgtype, buffid, bufflv, buffticks, buffInstanceId);
	//ge GetGameEventQue().postBuffChange(buffid, chgtype);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("buffId", buffid).
		SetData_Number("changeType", chgtype);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_BUFF_CHANGE", sandboxContext);
	}
}

void PlayerControl::getClientStatus(ClientStatus &status)
{
	status.curpos = CoordDivBlock(getLocoMotion()->getPosition());
	status.motion = getLocoMotion()->m_MotionStatus;
}

/*

bool PlayerControl::findNearestBlock(WCoord &blockpos, int blockid)
{
	std::vector<CHUNK_INDEX>indices;
	WCoord center = CoordDivBlock(getPosition());
	ChunkViewer::makeViewChunks(indices, BlockDivSection(center.x), BlockDivSection(center.z), ClientPlayer::m_ViewRangeSetting);

	bool findblock = false;
	for(size_t i=0; i<indices.size(); i++)
	{
		Chunk *pchunk = m_pWorld->getChunk(indices[i]);
		if(pchunk == NULL) continue;

		int mindist = MINIW::MAX_INT;
		int maxy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM-1;
		for(int y=0; y<maxy; y++)
		{
			for(int z=0; z<CHUNK_BLOCK_Z; z++)
			{
				for(int x=0; x<CHUNK_BLOCK_X; x++)
				{
					if(pchunk->getBlockID(x,y,z) == blockid)
					{
						WCoord pos = pchunk->m_Origin+WCoord(x,y,z);
						int dist = center.squareDistanceTo(pos);
						if(dist < mindist)
						{
							blockpos = pos;
							mindist = dist;
							findblock = true;
						}
					}
				}
			}
		}

		if(findblock) break;
	}

	return findblock;
}*/

void PlayerControl::triggerActorAttribChunk()
{
	if (getAttrib() && m_ActorAttribTrigger)
	{
		PlayerAttrib *pattr = getPlayerAttrib();
		LivingAttrib *lattr = getLivingAttrib();
		LivingAttrib* livAtt = getLivingAttrib();
		std::vector<int> mTriggerEvent;
		if (getAttrib()->getMaxHP() != m_ActorAttribTrigger->MaxHP)
		{
			m_ActorAttribTrigger->MaxHP = getAttrib()->getMaxHP();
			mTriggerEvent.push_back(OBAAMAXHP);
		}

		if (getAttrib()->getHP() != m_ActorAttribTrigger->NowHP)
		{
			m_ActorAttribTrigger->NowHP = getAttrib()->getHP();
			mTriggerEvent.push_back(OBAACURHP);
		}

		if (getAttrib()->getHPRecover() != m_ActorAttribTrigger->HPRecover)
		{
			m_ActorAttribTrigger->HPRecover = getAttrib()->getHPRecover();
			mTriggerEvent.push_back(OBAAHPRECOVER);
		}

		//if(100.0f != m_ActorAttribTrigger->MaxHunger)
		//{
		//	m_ActorAttribTrigger->MaxHunger = 100.0f;
		//	ObserverEvent_PlayerATTR obevent(getUin(), OBAAMAXHUNGER);
		//	GetObserverEventManager().OnTriggerEvent("Player.ChangeAttr", &obevent);
		//	mTriggerEvent.push_back(OBAAMAXHUNGER);
		//}

		if(pattr->getFoodLevel() != m_ActorAttribTrigger->NowHunger)
		{
			m_ActorAttribTrigger->NowHunger = pattr->getFoodLevel();
			mTriggerEvent.push_back(OBAACURHUNGER);
		}

		if(lattr->getMaxOxygen() != m_ActorAttribTrigger->MaxOxygen)
		{
			m_ActorAttribTrigger->MaxOxygen = lattr->getMaxOxygen();
			mTriggerEvent.push_back(OBAAMAXOXYGEN);
		}

		if(lattr->getOxygen() != m_ActorAttribTrigger->NowOxygen)
		{
			m_ActorAttribTrigger->NowOxygen = lattr->getOxygen();
			mTriggerEvent.push_back(OBAACUROXYGEN);
		}

		if (getAttrib()->getSpeedAtt(Actor_Walk_Speed) != m_ActorAttribTrigger->MoveSpeed)
		{
			m_ActorAttribTrigger->MoveSpeed = getAttrib()->getSpeedAtt(Actor_Walk_Speed);
			mTriggerEvent.push_back(OBAAWALKSPEED);
		}

		if (getAttrib()->getSpeedAtt(Actor_Run_Speed) != m_ActorAttribTrigger->RunSpeed)
		{
			m_ActorAttribTrigger->RunSpeed = getAttrib()->getSpeedAtt(Actor_Run_Speed);
			mTriggerEvent.push_back(OBAARUNSPEED);
		}

		if (getAttrib()->getSpeedAtt(Actor_Swim_Speed) != m_ActorAttribTrigger->SwimSpeed)
		{
			m_ActorAttribTrigger->SwimSpeed = getAttrib()->getSpeedAtt(Actor_Swim_Speed);
			mTriggerEvent.push_back(OBAASWINSPEED);
		}

		if (getAttrib()->getSpeedAtt(Actor_Jump_Speed) != m_ActorAttribTrigger->JumpSpeed)
		{
			m_ActorAttribTrigger->JumpSpeed = getAttrib()->getSpeedAtt(Actor_Jump_Speed);
			mTriggerEvent.push_back(OBAAJUMPPOWER);
		}

		if (getAttrib()->getSpeedAtt(Actor_Sneak_Speed) != m_ActorAttribTrigger->SneakSpeed)
		{
			m_ActorAttribTrigger->SneakSpeed = getAttrib()->getSpeedAtt(Actor_Sneak_Speed);
			mTriggerEvent.push_back(OBAASNEAKSPEED);
		}

		if(getAiInvulnerableProb() != m_ActorAttribTrigger->Dodge)
		{
			m_ActorAttribTrigger->Dodge = (float)getAiInvulnerableProb();
			mTriggerEvent.push_back(OBAADODGE);
		}

		float nattack = lattr->getAttackBaseLua(ATTACK_PUNCH);
		if(nattack != m_ActorAttribTrigger->NearAttack)
		{
			m_ActorAttribTrigger->NearAttack = nattack;
			mTriggerEvent.push_back(OBAAATKMELEE);
		}

		float rattack = lattr->getAttackBaseLua(ATTACK_RANGE);
		if(rattack != m_ActorAttribTrigger->RemoteAttack)
		{
			m_ActorAttribTrigger->RemoteAttack = rattack;
			mTriggerEvent.push_back(OBAAATKREMOTE);
		}

		float narmor = lattr->getArmorBaseLua(ATTACK_PUNCH);
		if( narmor != m_ActorAttribTrigger->NearArmor)
		{
			m_ActorAttribTrigger->NearArmor = narmor;
			mTriggerEvent.push_back(OBAADEFMELEE);
		}

		float rarmor = lattr->getArmorBaseLua(ATTACK_RANGE);
		if( rarmor != m_ActorAttribTrigger->RemoteArmor)
		{
			m_ActorAttribTrigger->RemoteArmor = rarmor;
			mTriggerEvent.push_back(OBAADEFREMOTE);
		}

		if(getScale() != m_ActorAttribTrigger->Dimension)
		{
			m_ActorAttribTrigger->Dimension = getScale();
			mTriggerEvent.push_back(OBAADIMENSION);
		}

		int level = (int)ceil((float)pattr->getExp()/100);
		if(level != m_ActorAttribTrigger->Level)
		{
			m_ActorAttribTrigger->Level = level;
			mTriggerEvent.push_back(OBAALEVEL);
		}

		int curLevelExp = pattr->getCurLevelExp();
		if (curLevelExp != m_ActorAttribTrigger->LevelModeExp)
		{
			m_ActorAttribTrigger->LevelModeExp = curLevelExp;
			mTriggerEvent.push_back(OBAALEVELMODLEXP);
		}

		int curLevel = pattr->getCurLevel();
		if (curLevel != m_ActorAttribTrigger->LevelModeLevel)
		{
			m_ActorAttribTrigger->LevelModeLevel = curLevel;
			mTriggerEvent.push_back(OBAALEVELMODLLEVEL);
		}

		// 体力值相关事件
		float strength = pattr->getStrength();
		if (strength != m_ActorAttribTrigger->Strength)
		{
			m_ActorAttribTrigger->Strength = strength;
			mTriggerEvent.push_back(OBAASTRENGTH);
		}

		float maxstrength = pattr->getBasicMaxStrength();
		if (maxstrength != m_ActorAttribTrigger->MaxStrength)
		{
			m_ActorAttribTrigger->MaxStrength = maxstrength;
			mTriggerEvent.push_back(OBAAMAXSTRENGTH);
		}

		float StrengthRestore = pattr->getBasicStrengthRestore();
		if (StrengthRestore != m_ActorAttribTrigger->StrengthRecover)
		{
			m_ActorAttribTrigger->StrengthRecover = StrengthRestore;
			mTriggerEvent.push_back(OBAASTRENGTHRECOVER);
		}

		float armor = getArmor();
		if (armor != m_ActorAttribTrigger->Armor)
		{
			m_ActorAttribTrigger->Armor = armor;
			mTriggerEvent.push_back(OBAAARMOR);
		}

		attriChangeOnTrigger(mTriggerEvent);
	}
}

int PlayerControl::getApiid()
{
	return GetClientInfoProxy()->GetAppId();
}

void PlayerControl::setLang(int lang)
{
	GetClientInfoProxy()->setLang(lang);
}

bool PlayerControl::checkIfDataUpload(int iType)
{
	return (m_UploadStatusMap.find(iType) != m_UploadStatusMap.end());
}

void PlayerControl::setUploadStatus(int iType, int iTime)
{
	if (iTime == 0) {
		iTime = GetClientInfoProxy()->getSvrTime();
		MINIW::ScriptVM::game()->callFunction("getServerTime", ">i", &iTime);
	}

	m_UploadStatusMap[iType] = iTime;
}

bool isSameDay(int t1, int t2)
{
	struct tm curr1;
	struct tm curr2;
	int iOffset = 6 * 3600;

	//凌晨6点切换天
	t1 -= iOffset;
	if (t1 < 0) t1 = 0;
	t2 -= iOffset;
	if (t2 < 0) t2 = 0;

	time_t time1 = t1;
	time_t time2 = t2;

	localtime_r(&time1, &curr1);
	localtime_r(&time2, &curr2);

	if (curr1.tm_year == curr2.tm_year && curr1.tm_yday == curr2.tm_yday)
        return true;

	return false;
}

void PlayerControl::loadWorldUploadStatus(WORLD_ID wid, int specialType/* = NORMAL_WORLD*/)
{
	if (wid <= 0) { return; }

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char filepath[256];
	sprintf(filepath, "%s/w%lld/upload/upload.json", rootpath.c_str(), wid);
	m_UploadStatusMap.clear();

	jsonxx::Object obj;
	AutoRefPtr<Rainbow::DataStream> fp = GetFileManager().OpenFileWritePath(filepath, true);
	if (!fp) { return; }

	size_t n = fp->Size();
	char *pData = (char *)malloc(n + 1);
	memcpy(pData, fp->GetMemoryImage(), n);

	pData[n] = 0; //确保字符串结尾是0

	if (pData) {
		bool result = obj.parse(pData);
		if (result) {
			jsonxx::Object data;
			int iUploadTime = 0;
			int nowtime = GetClientInfoProxy()->getSvrTime();
			MINIW::ScriptVM::game()->callFunction("getServerTime", ">i", &nowtime);
			unsigned int typelist[] =
			{
				Upload_BasketBall_Create,
				Upload_BasketBallWear_Use,
				Upload_BasketBallFrame_Add,
				Upload_BasketBall_Dribble,
			};
			int iLoadNum = sizeof(typelist)/sizeof(unsigned int);

			std::string sKey;
			for (int i = 0; i < iLoadNum; i++) {
				if (obj.has<jsonxx::Object>("param")) {
					data = obj.get<jsonxx::Object>("param");
					sKey = toString(typelist[i]);
					if (data.has<jsonxx::Number>(sKey)) {
						iUploadTime = (int)data.get<jsonxx::Number>(sKey);
						if (isSameDay(iUploadTime, nowtime)) {
							setUploadStatus(typelist[i], iUploadTime);
						}
					}
				}
			}
		}

		free(pData);
	}
}

void PlayerControl::saveWorldUploadStatus(WORLD_ID wid, int specialType/* = NORMAL_WORLD*/)
{
	if (wid <= 0 || m_UploadStatusMap.size() == 0) { return; }

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char filepath[256];
	sprintf(filepath, "%s/w%lld/upload/upload.json", rootpath.c_str(), wid);

	char dirpath[256];
	sprintf(dirpath, "%s/w%lld/upload/", rootpath.c_str(), wid);

	if (!GetFileManager().IsFileExistWritePath(dirpath))
		GetFileManager().CreateWritePathDir(dirpath);

	unsigned int typelist[] =
	{
		Upload_BasketBall_Create,
		Upload_BasketBallWear_Use,
		Upload_BasketBallFrame_Add,
		Upload_BasketBall_Dribble,
	};

	int iSaveNum = sizeof(typelist) / sizeof(unsigned int);
	jsonxx::Object destobj;
	jsonxx::Object paramobj;

	std::map<int, int>::iterator it;
	for (int i = 0; i < iSaveNum; i++) {
		it = m_UploadStatusMap.find(typelist[i]);
		if (it != m_UploadStatusMap.end()) {
			paramobj << toString(typelist[i]) << it->second;
		}
	}
	destobj << "param" << paramobj;
	GetFileManager().SaveToWritePath(filepath, destobj.json().c_str(), strlen(destobj.json().c_str()));
}


bool PlayerControl::isRidingOnCrab()
{
	//ClientActor* actor = getWorld()->getActorMgr()->findActorByWID(m_RidingActor);
	auto RidComp = getRiddenComponent();
	ClientActor* actor = NULL;
	if (RidComp)
	{
		actor = RidComp->getRidingActor();
	}
	if (actor)
	{
		return actor->getDefID() == 3896;
	}
	
    return false;
}


OpenContainerComponent*  PlayerControl::getOpenContainerCom()
{
	return GetComponent<ControlOpenContainerComponent>();
}

bool PlayerControl::setContainerText(int index, const char *text)
{
	return (static_cast<ControlOpenContainerComponent*>(getOpenContainerCom()))->setContainerText(index, text);
}

ItemSkillComponent* PlayerControl::getItemSkillComponent()
{
	return m_pItemSkillComponent;
}


// 20210926：是否检查可视（模型裁剪用）  codeby： keguanqiang
bool PlayerControl::needCheckVisible()
{
	auto *horse = getRidingHorse();
	if (horse)
		return horse->needCheckVisible();
	if (this == g_pPlayerCtrl) return false;
	return true;
}

ClientActor* PlayerControl::GetPickActor()
{
	if (!getWorld())
		return nullptr;

	return static_cast<ClientActorMgr*>(getWorld()->getActorMgr())->findActorByWID(m_PickResult.m_PickObjId);
}

void PlayerControl::changeViewModeByMsg()
{
	if (!m_receiveChangeViewModeMsg)
	{
		return;
	}
	changeViewMode(m_msgViewMode, m_msgLock);
}

void PlayerControl::saveMsgChangeViewMode(int mode, bool lock)
{
	m_receiveChangeViewModeMsg = true;
	m_msgViewMode = mode;
	m_msgLock = lock;
}

void PlayerControl::playEffect(ACTORBODY_EFFECT fx)
{
	if (fx == HUDFX_HEADSHOT)
	{
		triggerHeadshotTip();
	}
	else if (fx == HUDFX_NORMALSHOT)
	{
		triggerNormalshotTip();
	}
	else if (fx == HUDFX_DEADSHOT)
	{
		triggerNormalshotTip(true);
	}
	else if (fx == HUDFX_VEHICLESHOT)
	{
		triggerVehicleshotTip();
	}
	else
	{
		ClientActor::playEffect(fx);
	}
}

void	PlayerControl::sawtoothAttackedUp(float atkpoints)
{
	if (!m_pWorld->isRemoteMode())
	{
		auto thornBall = sureThornBallComponent();
		if (thornBall)
		{
			thornBall->reboundsAttackedUp(atkpoints);
		}
	}
	else
	{
		//通知主机
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "atkpoints" << atkpoints;
		SandBoxManager::getSingletonPtr()->sendToHost("PB_CH_NOTICE_ATTACKED_UP", context.bin(), context.binLen());
	}
}
void	PlayerControl::sawtoothAttackedRound(float atkpoints)
{
	auto thornBall = sureThornBallComponent();
	if (thornBall == nullptr)
	{
		return;
	}
	int tmepdir = thornBall->checkCrashDir();
	if (!m_pWorld->isRemoteMode())
	{
		thornBall->reboundsAttackedRound(atkpoints, tmepdir);
	}
	else
	{
		//通知主机
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "atkpoints" << atkpoints;
		context << "dir" << tmepdir;
		SandBoxManager::getSingletonPtr()->sendToHost("PB_CH_NOTICE_ATTACKED_ROUND", context.bin(), context.binLen());
	}
}

void PlayerControl::checkThornBall()
{
	auto thornComponent = getThornBallComponent();
	if (thornComponent == nullptr)
	{
		m_JumpTime.clear();
		return;
	}
	LivingLocoMotion* living = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
	if (living == nullptr)
	{
		return;
	}
	if (thornComponent->getThornAnchorNum() <= 0)
	{
		m_JumpTime.clear();
		return;
	}
	thornComponent->showThornBall(getViewMode() != CAMERA_FPS); //第一人称
	//有刺球不可以
	if (isSleeping() || isRestInBed())
	{
		tryWakeup();
		sawtoothAttackedUp(2.0f);
		return;
	}
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	//if (isRiding())
	{
		tryMountActor(nullptr);
		sawtoothAttackedUp(2.0f);
		return;
	}
	if (getSitting())
	{
		tryStandup();
		sawtoothAttackedUp(2.0f);
		return;
	}
	if (living->m_CollidedVertically)
	{
		LOG_INFO("doFlickdoFlickdoFlick");
		if (m_IsJump)
		{
			//m_JumpTime.insert(ClientAccountMgr::GetInstance().getSvrTime());
			m_JumpTime.insert(GetClientInfoProxy()->getSvrTime());
		}
		m_IsJump = false;
		//成功碰到 算一次
		living->m_CollidedVertically = false;
	}
	//横向处理
	else if (living->m_CollidedHorizontally)
	{
		sawtoothAttackedRound(3.0f);
	}
	if (m_JumpTime.size() >= 3)
	{
		int sumTime = 0;
		int index = 0;
		int count = 1;
		std::vector<int> removeVec;
		for (auto iter = m_JumpTime.begin(); iter != m_JumpTime.end(); iter++, index++)
		{
			int nextTime = 0;
			int curTime = *iter;
			int nextIndex = index + 1;
			if (m_JumpTime.size() > nextIndex)
			{
				//直接取一下时间
				auto nextIter = iter;
				nextIter++;
				nextTime = *nextIter;
			}
			if (nextTime != 0)
			{
				count++;
				auto tempIter = m_JumpTime.begin();
				sumTime += nextTime - curTime;
				if (sumTime <= 5) //5秒内连续3次就算
				{
					if (count == 3)
					{
						removeVec.push_back(*(tempIter));
						removeVec.push_back(*(++tempIter));
						removeVec.push_back(*(++tempIter));
						if (!m_pWorld->isRemoteMode())
						{
							thornComponent->removeThornBallModel(1);
							thornComponent->dropThornBall(1);
						}
						else
						{
							//通知主机要删除
							jsonxx::Object context;
							char objid_str[128];
							sprintf(objid_str, "%lld", getObjId());
							context << "objid" << objid_str;
							context << "num" << 1;
							SandBoxManager::getSingletonPtr()->sendToHost("PB_CH_NOTICE_REMOVE_SAWTOOTH_THORNBA", context.bin(), context.binLen());
						}
						break;
					}
				}
				else
				{
					removeVec.push_back(*tempIter);
					break;
				}
			}
		}
		for (auto iter = removeVec.begin(); iter != removeVec.end(); iter++)
		{
			auto remove = std::find(m_JumpTime.begin(), m_JumpTime.end(), *iter);
			if (remove != m_JumpTime.end())
			{
				m_JumpTime.erase(remove);
			}
		}
	}
}

#ifdef BUILD_MINI_EDITOR_APP
void PlayerControl::SetEnableChunkViewer(bool enable)
{
	if (enable)
	{
		if (m_ChunkViewer.isPause) {
			m_ChunkViewer.isPause = false;
			m_ChunkViewer.enterWorld(getWorld(), getLocoMotion()->getPosition(), ClientPlayer::GetCurViewRange(this)/*getCurViewRange()*/);
		}
	}
	else
	{
		if (!m_ChunkViewer.isPause) {
			m_ChunkViewer.leaveWorld(getWorld());
			m_ChunkViewer.isPause = true;
		}
	}
}
#endif

void PlayerControl::UpdateXrayEffectEnable()
{
	bool enable = m_bXrayEffectEnable || (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_AUTO_ZOOM) == CAZ_AUTO_ZOOM_NO_AND_XRAY);
	if (getEntity())
	{
		getEntity()->SetXrayEffectEnable(enable);
	}

	if (getBody() && getBody()->getWeaponModel())
	{
		getBody()->getWeaponModel()->SetXrayEffectEnable(enable);
	}
}

bool PlayerControl::IsXrayEffectEnable()
{
	return m_bXrayEffectEnable || (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_AUTO_ZOOM) == CAZ_AUTO_ZOOM_NO_AND_XRAY);
}

void PlayerControl::resetGameCameraUGC()
{
	UGCGameCamera* pUGCGameCam = dynamic_cast<UGCGameCamera*>(m_pCamera);
	if (pUGCGameCam)
		pUGCGameCam->resetGameCamera();
}

bool PlayerControl::canShowCameraModelExUGC()
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
		return true;

	if (m_pCamera == NULL)
	{
		return true;
	}
	UGCGameCamera* pUGCCamera = dynamic_cast<UGCGameCamera*>(m_pCamera);
	if (pUGCCamera == NULL || m_pWorld == NULL)
	{
		return true;
	}
	if (pUGCCamera->getMode() != CameraControlMode::CAMERA_FPS)
	{
		return true;
	}

	bool moveFollowable = pUGCCamera->getMoveFollowable();
	bool standFixedPoint = pUGCCamera->getStandFixedPoint();
	if (standFixedPoint || !moveFollowable)
		return false;

	long long actorObjId = pUGCCamera->getActorObjId();
	if (actorObjId == 0)
	{
		return true;
	}

	ClientActor* pActor = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(actorObjId);
	if (pActor == NULL)
	{
		return true;
	}
	//隐藏手臂
	return false;
}

bool PlayerControl::updateGameCameraExUGC(float dtime)
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
		return false;

	if (m_pCamera == NULL) return false;
	UGCGameCamera* pUGCCamera = dynamic_cast<UGCGameCamera*>(m_pCamera);
	if (pUGCCamera == NULL || m_pWorld == NULL)
	{
		return false;
	}

	long long actorObjId = pUGCCamera->getActorObjId();
	if (actorObjId == 0)
	{
		return false;
	}

	ClientActor* pActor = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(actorObjId);
	if (pActor == NULL)
	{
		return false;
	}

	Rainbow::Vector3f lookdir(m_pCamera->getLookDir());
	lookdir.y = 0;
	lookdir = MINIW::Normalize(lookdir);
	lookdir = lookdir * 0.0f;

	Rainbow::Vector3f offset(lookdir.x, getEyeHeight() + 10.0f, lookdir.z);
	Rainbow::Vector3f cameraPos = pActor->getPosition().toVector3();
	ActorLocoMotion* locomotion = pActor->getLocoMotion();
	if (locomotion) {
		cameraPos = locomotion->getFramePosition().toVector3();
	}

	if (pActor->getBody() != NULL)
		cameraPos = pActor->getBody()->getBindPointPos(0, &offset);
	pUGCCamera->setPosition(Rainbow::WorldPos::fromVector3(cameraPos));
	return true;
}

void PlayerControl::resetEyeDirExUGC(bool checkopway)
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
		return;

	//如果绑定相机到生物身上
	if (m_pCamera == NULL)
		return;
	UGCGameCamera* pUGCCamera = dynamic_cast<UGCGameCamera*>(m_pCamera);
	if (pUGCCamera == NULL)
		return;
	if (pUGCCamera->getMode() != CameraControlMode::CAMERA_FPS)
		return;
	bool moveFollowable = pUGCCamera->getMoveFollowable();
	bool standFixedPoint = pUGCCamera->getStandFixedPoint();
	if (standFixedPoint || !moveFollowable)
	{
		getBody()->show(true);
		return;
	}

	long long actorObjId = pUGCCamera->getActorObjId();
	if (actorObjId == 0)
		return;
	ClientActor* pActor = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(actorObjId);
	if (pActor == NULL)
		return;
	getBody()->show(true);
}

void PlayerControl::changeMoveFlag(unsigned flag_id, bool on)
{
	switch (flag_id)
	{
		case IFC_Fly:
		{
			setFlying(on);
			break;
		}
		case IFC_Sneak:
		{
			setRun(false);
			setSneaking(on);
			break;
		}
		case IFC_Run:
		{
			if(!getSneaking())
				setRun(on);
			break;
		}
		case IFC_Sit:
		{
			setSitting(on);
			break;
		}
	}
}

BlockScene* PlayerControl::getScene()
{
	if (m_pWorld == NULL)
		return NULL;
	return m_pWorld->getScene();
}

bool PlayerControl::isAimState()
{
	bool isAimState = GetIdleStateGunAdvance()->IsAimState();
	return isAimState;
}

void PlayerControl::registerFirstPersonIK()
{
	if (getGunLogical())
	{
		getGunLogical()->registerFirstPersonEvent();
	}
}

void PlayerControl::unRegisterFirstPersonIK()
{
	if (getGunLogical())
	{
		getGunLogical()->unregisterFirstPersonEvent();
	}
}

void PlayerControl::clearAllSpecialBlockOutline()
{
	if (m_pWorld && m_pWorld->GetWorldRenderer())
	{
		WorldRenderer* worldRenderer = m_pWorld->GetWorldRenderer();
		for (const WCoord& oldPos : m_CurrentOutlineBlocks)
		{
			worldRenderer->removeBlockOutline(oldPos);
		}
	}
	m_CurrentOutlineBlocks.clear();
}

void PlayerControl::showNearSpecialBlockOutline()
{
	WorldRenderer* worldRenderer = m_pWorld->GetWorldRenderer();
	if (!worldRenderer) return;
	clearAllSpecialBlockOutline();
	// 获取玩家眼部位置（用于射线检测）
	WCoord eyePos = getEyePosition();
	WCoord playerBlockPos = eyePos / BLOCK_SIZE;
	
	int range = 16; // 搜索半径，可根据需要调整
	// 存储新找到的可见方块
	std::set<WCoord> newVisibleBlocks;
	
	// 使用更高效的球形搜索算法
	std::vector<std::pair<float, WCoord>> candidateBlocks;
	
	// 首先收集范围内的所有特殊方块
	for (int x = playerBlockPos.x - range; x <= playerBlockPos.x + range; x++)
	{
		for (int z = playerBlockPos.z - range; z <= playerBlockPos.z + range; z++)
		{
			for (int y = playerBlockPos.y - range; y <= playerBlockPos.y + range; y++)
			{
				WCoord blockPos(x, y, z);
				
				// 计算距离，只考虑球形范围内的方块
				float distance = (blockPos - playerBlockPos).length();
				if (distance > range) continue;
				
				// 检查该位置是否有方块
				int blockID = m_pWorld->getBlockID(blockPos);
				if (blockID <= 0) continue; // 跳过空气方块
				
				// 检查该方块是否为特殊方块
				if (!isSpecialBlock(blockID)) continue;
				
				// 添加到候选列表，按距离排序
				candidateBlocks.push_back(std::make_pair(distance, blockPos));
			}
		}
	}
	
	// 按距离排序，优先检查近的方块
	std::sort(candidateBlocks.begin(), candidateBlocks.end());
	
	// 使用方向映射来避免在同一方向上重复检查
	std::set<std::pair<int, int>> checkedDirections;
	
	// 按距离从近到远检查可见性
	for (const auto& candidate : candidateBlocks)
	{
		const WCoord& blockPos = candidate.second;
		
		// 计算方向（简化的方向映射，将3D方向映射到2D网格）
		Rainbow::Vector3f direction = (blockPos - playerBlockPos).toVector3().Normalize();
		int horizontalDir = (int)(direction.x * 10); // 粗略的方向量化
		int verticalDir = (int)(direction.y * 10);
		std::pair<int, int> dirKey = std::make_pair(horizontalDir, verticalDir);
		
		// 如果这个方向已经找到了可见方块，跳过更远的方块
		if (checkedDirections.find(dirKey) != checkedDirections.end())
			continue;
		
		// 使用射线检测判断该方块是否可见
		if (isBlockVisibleFromPlayer(eyePos, blockPos))
		{
			newVisibleBlocks.insert(blockPos);
			checkedDirections.insert(dirKey); // 标记这个方向已找到可见方块
			
			// 限制最大可见方块数量，避免性能问题
			if (newVisibleBlocks.size() >= 50) break;
		}
	}
	
	// 不能使用增量刷新。因为宝箱会刷新变形
	
	
	// 2. 添加新可见的方块轮廓
	for (const WCoord& newPos : newVisibleBlocks)
	{
		// 该方块是新可见的，添加轮廓
		worldRenderer->setBlockOutline(newPos, true);
	}
	
	// 3. 更新当前轮廓方块集合
	m_CurrentOutlineBlocks = newVisibleBlocks;
}

bool PlayerControl::isSpecialBlock(int blockID)
{
	// 这里可以根据需要定义什么是"特殊方块"
	// 示例：检查是否为特定类型的方块
	if (blockID <= 0) return false;
	

	if (2300 <= blockID && 2302 >= blockID || 2310 <= blockID && 2335 >= blockID || 2429 == blockID || 2430 == blockID || 2425 == blockID || 2413 == blockID) return true;
	return false;
}

bool PlayerControl::isBlockVisibleFromPlayer(const WCoord& eyePos, const WCoord& blockPos)
{
	// 计算从玩家眼部到目标方块中心的射线
	Rainbow::Vector3f blockCenter = (blockPos * BLOCK_SIZE).toVector3() + Rainbow::Vector3f(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2);
	Rainbow::Vector3f eyeWorldPos = eyePos.toVector3();
	
	Rainbow::Vector3f direction = blockCenter - eyeWorldPos;
	float distance = direction.Length();
	
	// 如果距离太近，认为可见
	if (distance < BLOCK_SIZE) return true;
	
	direction /= distance;
	
	// 创建射线
	MINIW::WorldRay ray;
	ray.m_Origin = eyePos.toWorldPos();
	ray.m_Dir = direction;
	ray.m_Range = distance - BLOCK_FSIZE * 0.1f; // 稍微缩短射线长度，避免击中目标方块本身
	
	// 执行射线检测
	IntersectResult result;

	// 过滤函数：忽略透明或可穿透的方块
	std::function<bool(const WCoord&)> filter = [/*this, */&blockPos](const WCoord& pos) -> bool 
		{
			return pos == blockPos;
		//int blockId = m_pWorld->getBlockID(pos);
		//if (blockId <= 0) return true; // 空气方块不阻挡视线
		////BlockMaterial* blockMtl = g_BlockMtlMgr.getMaterial(blockId);
		////if (!blockMtl) return false;
		//return false;
		};
	
	// 如果射线没有击中任何阻挡物，则方块可见
	bool intersect = m_pWorld->pickGround(ray, &result, PICK_METHOD_CLICK, filter);
	return !intersect;
}


std::string PlayerControl::getActorInteractionTips(IClientActor* actor)
{
	if (!actor) return "";
	ObjType type = static_cast<ObjType>(actor->getObjType());
	switch (type) {
	case 0:
	{
		if (actor->IsKindOf<ClientMob>())
		{
			ClientMob* mob = static_cast<ClientMob*>(actor);
			// 如果怪物已死亡且可剥皮
			bool isDead = mob->isDead();
			bool isInteractiveCorpse = mob->getFlagBit(ACTORFLAG_INTERACTIVE_CORPSE);
			bool isSkinned = mob->getSkinned();
			if (isDead && isInteractiveCorpse && !isSkinned) {
				return GetDefManagerProxy()->getStringDef(10000001);//"按E剥皮";
			}
		}
	}
	return "";
	case OBJ_TYPE_VEHICLE:
		return GetDefManagerProxy()->getStringDef(10000002);//"按E驾驶";
	case OBJ_TYPE_BOX:
		return GetDefManagerProxy()->getStringDef(10000003);//"按E打开";
	case OBJ_TYPE_CUBECHEST:
		return GetDefManagerProxy()->getStringDef(10000003);//"按E打开";
	case OBJ_TYPE_NPC:
	{
		if (actor->IsKindOf<ClientMob>())
		{
			ClientMob* npc = static_cast<ClientMob*>(actor);
			bool isDead = npc->isDead();
			if (!isDead) {
				return GetDefManagerProxy()->getStringDef(10000011);//"按E交易";
			}
		}
		return "";
	}
		
	case OBJ_TYPE_CREATURE:
		return GetDefManagerProxy()->getStringDef(10000005);//"按E驯服";
	case OBJ_TYPE_HORSE:
	case OBJ_TYPE_MOON_MOUNT:
	case OBJ_TYPE_DRAGON_MOUNT:
	case OBJ_TYPE_DOUDU_MOUNT:
	case OBJ_TYPE_PUMPKIN_HORSE:
	case OBJ_TYPE_HIPPOCAMPUS_HORSE:
		if (actor->IsKindOf<ClientMob>())
		{
			ClientMob* mob = static_cast<ClientMob*>(actor);
			// 如果怪物已死亡且可剥皮
			bool isDead = mob->isDead();
			bool isInteractiveCorpse = mob->getFlagBit(ACTORFLAG_INTERACTIVE_CORPSE);
			bool isSkinned = mob->getSkinned();
			if (isDead && isInteractiveCorpse && !isSkinned) {
				return GetDefManagerProxy()->getStringDef(10000001);//"按E剥皮";
			} else {
				return GetDefManagerProxy()->getStringDef(10000006);//"按E骑乘";
			}
		}	
		return "";
	case OBJ_TYPE_BOAT:
		return GetDefManagerProxy()->getStringDef(10000007);//"按E划船";
		//case OBJ_TYPE_FURNACE:
		//    outTip = "按E使用熔炉";
		//    return true;
		//case OBJ_TYPE_WORKSHOP:
		//    outTip = "按E使用工作台";
		//    return true;
		//case OBJ_TYPE_TRANSFER:
		//    outTip = "按E传送";
		//    return true;
	case OBJ_TYPE_MOBCORPSE:
		/*if (getPlayerAttrib()->hasBuff(IN_SAFE_ZONE))
		{
			ActorMobCorpse* amc = dynamic_cast<ActorMobCorpse*>(actor);
			if (this->getUin() != amc->GetMobUin()) return "";
		}*/
		return GetDefManagerProxy()->getStringDef(10000009);//"按E采集";
	case OBJ_TYPE_PLAYERCORPSE:
		if (getPlayerAttrib()->hasBuff(IN_SAFE_ZONE))
		{
			ActorPlayerCorpse* papc = dynamic_cast<ActorPlayerCorpse*>(actor);
			if (this->getUin() != papc->GetPlayerUin()) return "";
		}
		return GetDefManagerProxy()->getStringDef(10000009);//"按E采集";
	case OBJ_TYPE_ROLE:
	{
		ClientPlayer* player = static_cast<ClientPlayer*>(actor);
		if (player->isPlayerDowned())
			return GetDefManagerProxy()->getStringDef(10000010);//"长按E救援";
		else {
			PlayerTeamComponent *team_c = player->GetComponent<PlayerTeamComponent>();
			if (!team_c) return "";
			//有队伍不显示邀请
			if (team_c->getTeam() != 0) return "";

			char str[50] = { 0 };
			MINIW::ScriptVM::game()->callFunction("GetRoleInfo", ">s", &str);
			return str;
		}
	}
	default:
		return "";
	}
}

void PlayerControl::handleInteraction()
{
    // 1. 优先处理Actor交互
    if (m_PickResult.actor) {
        IClientActor* targetActor = m_PickResult.actor;
        
        // 根据Actor类型执行不同的交互逻辑
        if (targetActor->getObjType() == OBJ_TYPE_NPC) {
            // NPC对话
            // interactActor(targetActor);
        }
        else if (targetActor->getObjType() == OBJ_TYPE_VEHICLE) {
            // 载具交互
            auto RidComp = getRiddenComponent();
            if (RidComp) {
                // 尝试驾驶
            }
        }
        return;
    }
    
    // 2. 处理方块交互
    if (m_PickResult.intersect_block) {
        interactBlock(m_PickResult.block, m_PickResult.face, m_PickResult.facepoint);
    }

    // if (targetActor->isType(OBJ_TYPE_PET)) {
    //     // 处理宠物交互
    //     petInteraction(targetActor);
    // }	
}

bool PlayerControl::IsHideHandModel()
{
	//第一视角才需要隐藏
	if (m_pCamera->getMode() != CameraControlMode::CAMERA_FPS)
		return false;
	return m_IsHideHandModel;
}

void PlayerControl::clearBuffAndStatus()
{
	LivingAttrib* atr = dynamic_cast<LivingAttrib*>(g_pPlayerCtrl->getAttrib());
	if (atr)
	{
		atr->clearBuffAndStatus();
	}
}
IActorLocoMotion* PlayerControl::getPlayerControlLocoMotion()
{
	return getLocoMotion();
}
WorldContainer* PlayerControl::GetPlayerControlCurOpenedContainer()
{
	return getCurOpenedContainer();
}

bool PlayerControl::HasReversed()
{
	return getCurCameraConfig()->HasReversed;
}


bool PlayerControl::HasGundef()
{
	return getGunLogical()->getGunDef() ? true : false;
}
int PlayerControl::getGunSpread()
{
	return getGunLogical()->getGunSpread();
}
int PlayerControl::GetGunCrosshair()
{
	return getGunLogical()->getGunDef()->Crosshair;
}
void PlayerControl::setZoom(bool open)
{
	getGunLogical()->setZoom(open);
}
bool PlayerControl::getZoom()
{
	return getGunLogical()->getZoom();
}
void PlayerControl::resetRecoil()
{
	getGunLogical()->resetRecoil();
}

WCoord PlayerControl::getCameraPosition()
{
	return m_CurrentCameraConfig.getCameraPosition();
}
int PlayerControl::getOption(unsigned char option)
{
	return m_CurrentCameraConfig.getOption(option);
}
float PlayerControl::getInitPlayerYaw()
{
	return m_CurrentCameraConfig.InitPlayerYaw;
}

void PlayerControl::onTouchInputEvent(const Rainbow::InputEvent& inevent)
{
	getTouchControl()->onInputEvent(inevent);
}
bool PlayerControl::IsGunHoldState(int state)
{
	return m_GunHoleState == state;
}
void PlayerControl::PlayGunByState(int state)
{
	if (state < 0)
	{
		if (GetIdleStateGunAdvance())
		{
			GetIdleStateGunAdvance()->TryResetHandleEquip();
		}
	}
	else if (state > 0)
	{
		if (getPlayerAnimation())
		{
			getPlayerAnimation()->performIdle();
		}
	}
}

const CustomGunDef* PlayerControl::GetCustomGunDef()
{
	return getCustomGunDef();
}
void PlayerControl::GetArmShakeParam(float& amplitude, float& speed)
{
	if (GetIdleStateGunAdvance())
	{
		GetIdleStateGunAdvance()->GetArmShakeParam(amplitude, speed);
	}
}
void PlayerControl::DoCurShotGunModEntry(bool enable)
{
	ItemUseComponent* itemUseComp = getItemUseComponent();
	if (itemUseComp)
	{
		itemUseComp->DoCurShotGunModEntry(false);
	}
}

ChunkViewer* PlayerControl::GetChunkViewerForCtrl()
{
	return getChunkViewer();
}

void PlayerControl::updateBlockPlacementPreview()
{
    if (!m_pWorld || !m_pCamera)
        return;
        
     WorldRenderer * pWorldRender = m_pWorld->getRender();
    if (!pWorldRender)
        return;

	if (IsOffline())
		return;
        
    BlockPlacementPreview* blockPreview = pWorldRender->GetBlockPlacementPreview();
    if (!blockPreview)
        return;
        
    // Get current item
    int itemId = getCurToolID();
	int blockID = itemId;
	// 检查是否是建筑蓝图ID (例如3030212)
	if (itemId == ItemIDs::BLUEPRINT) {
		// 尝试使用建筑蓝图放置方块
		int blueprintId = getPlayerAttrib()->getCurBuildingId();
		const ArchitecturalBlueprintCsvDef* blueprintDef = GetDefManagerProxy()->getArchitecturalBlueprintCsvDef(blueprintId);
		if (blueprintDef)
			blockID = blueprintDef->Wood.ProduceItemID;
	}

	////Update the preview based on camera position and direction
	//Rainbow::Vector3f eyePos = getEyePosition().toVector3();
	//Rainbow::Vector3f lookDir = getLookDir();


	// Update the preview
	blockPreview->UpdateFromRaycast(blockID, m_PickResult);
}

int PlayerControl::lootItem(int fromIndex, int num)
{
	return ClientPlayer::lootItem(fromIndex, num);
}

void PlayerControl::UpdateActorSelection()
{
	IClientActor* currentPickedActor = m_PickResult.actor;
	
	if (currentPickedActor != m_CurrentSelectedActor.get())
	{
		ClearSelectedActor();
		
		if (currentPickedActor)
		{
			SetSelectedActor(currentPickedActor);
		}
	}
}

void PlayerControl::SetSelectedActor(IClientActor* actor)
{
	if (!actor) return;
	
	// 设置轮廓效果 - 使用索引1，对应黄色轮廓
	// 从WorldRender.cpp可以看到：outlinePass->SetOutlineParams(OutlineParams(5.0f, ColorRGBAf::yellow), 1);
	ClientActor* clientActor = dynamic_cast<ClientActor*>(actor);
	if (clientActor)
	{
		clientActor->SetOutline(1);  // 使用轮廓参数索引1（黄色）
		m_CurrentSelectedActor = actor;  // AutoRef自动管理引用计数
	}
}

void PlayerControl::ClearSelectedActor()
{
	if (m_CurrentSelectedActor)
	{
		ClientActor* clientActor = dynamic_cast<ClientActor*>(m_CurrentSelectedActor.get());
		if (clientActor)
		{
			clientActor->SetOutline(0);  // 0表示无轮廓效果
		}
		m_CurrentSelectedActor = nullptr;  // AutoRef自动释放引用
	}
}

bool PlayerControl::getRun()
{
	return !m_gunFireDisableRun && ClientPlayer::getRun();
}
