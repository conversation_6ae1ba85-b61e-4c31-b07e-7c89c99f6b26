#pragma once
#include "world_types.h"
#include "SandboxEngine.h"
#include <unordered_map>
#include <vector>

class World;
class TorchMaterial;

// 动态光照管理器
class EXPORT_SANDBOXENGINE DynamicLightingManager
{
public:
    // 光照更新优先级
    enum class LightingPriority
    {
        HIGH = 0,    // 玩家周围 2 chunk 范围
        MEDIUM = 1,  // 玩家周围 4 chunk 范围  
        LOW = 2,     // 玩家周围 8 chunk 范围
        FROZEN = 3   // 超出范围，冻结更新
    };

    // 光源信息
    struct LightSource
    {
        WCoord position;
        int lightLevel;
        int blockType;
        bool isDirty;
        LightingPriority priority;
        uint32_t lastUpdateTick;
    };

    // 其他客户端手持光源管理接口
    void updateOtherClientHeldLight(int clientId, const WCoord& position, int itemId);
    void removeOtherClientHeldLight(int clientId);
    void clearAllOtherClientHeldLights();

private:
    World* m_World;
    WCoord m_LastPlayerPos;
    uint32_t m_CurrentTick;
    
    // 分级光源管理
    std::unordered_map<WCoord, LightSource, WCoordHashCoder> m_LightSources;
    std::vector<WCoord> m_UpdateQueue[4]; // 按优先级分组的更新队列
    
    // 新增：其他客户端手持光源信息结构
    struct HeldLightInfo
    {
        WCoord position;
        int itemId;
        int lightLevel;
        WCoord lastPosition;  // 用于检测位置变化
        
        HeldLightInfo() : position(0, -1, 0), itemId(0), lightLevel(0), lastPosition(0, -1, 0) {}
    };

    // 本地玩家专用
    WCoord m_LastLocalHeldLightPos;
    int m_LastLocalHeldItemID;
    bool m_HasLocalHeldLight;
    bool m_ForceUpdate;
    
    // 其他客户端手持光源映射表 (clientId -> HeldLightInfo)
    std::unordered_map<int, HeldLightInfo> m_OtherClientHeldLights;
    
    // 性能控制
    int m_MaxUpdatesPerFrame[4] = {10, 5, 2, 0}; // 各优先级每帧最大更新数
    int m_UpdateCooldown[4] = {1, 3, 10, 0};     // 各优先级更新冷却时间（tick）

public:
    DynamicLightingManager(World* world);
    ~DynamicLightingManager();

    // 主要接口
    void tick();
    void onPlayerMove(const WCoord& newPos);
    void addLightSource(const WCoord& pos, int lightLevel, int blockType);
    void removeLightSource(const WCoord& pos);
    void markLightSourceDirty(const WCoord& pos);
    void setForceUpdate(bool b);
    
    // 手持物品光照
    void updateLocalHeldItemLighting();

private:
    // 内部方法
    void updatePriorities(const WCoord& playerPos);
    void processUpdateQueue();
    void updateLightSource(const WCoord& pos);
    LightingPriority calculatePriority(const WCoord& lightPos, const WCoord& playerPos);
    
    // 优化方法
    void freezeDistantLights(const WCoord& playerPos);
    void unfreezeNearbyLights(const WCoord& playerPos);
    bool shouldUpdate(const LightSource& light) const;
    
    // 处理其他客户端手持光源
    void processOtherClientHeldLight(int clientId, HeldLightInfo& heldLight);
    int getItemLightLevel(int itemId) const;
    bool isLightEmittingItem(int itemId) const;
}; 