#ifdef DEDICATED_SERVER

#include "ZmqProxy.h"

#include "CenterGSMgr.h"
#include "ICloudProxy.h"

#include "IClientGameManagerInterface.h"
#include "RoleCloudLibVar_generated.h"
#include "File/FileManager.h"

#include "DataServerClient.h"
#include "chunkio.h"
#include "zmq.h"
#include <sstream>
#include <stdint.h>
#include <stdlib.h>
#include <string>
#include "OgreTimer.h"
#include "GameNetManager.h"
#include "Download/LegacyDownloader.h"
#include "json/jsonxx.h"

#include "RoomClient.h"
#include "LegacyOgreBigLittleEndian.h"
#include "ClientInfoProxy.h"
#include "Platforms/PlatformInterface.h"
#include "Utilities/Logs/LogAssert.h"
#include "IGameMode.h"
#include "WorldManager.h"
#include "ClientAppProxy.h"
#include "DataHubService.h"
#include "SandboxCloudServerConfigService.h"
#include "IClientPlayer.h"
#include "TeamSetterComponent.h"


#define ZMQ_PROXY_DEBUG_LOG 1
#if ZMQ_PROXY_DEBUG_LOG
#define ZMQ_PROXY_LOG(...) \
    do { \
        WarningStringMsg("[ZmqProxy] " __VA_ARGS__); \
    } while(0)
#else
#define ZMQ_PROXY_LOG(...)
#endif

#if PLATFORM_WIN
#include <io.h>
#ifdef _WIN64
#pragma comment(lib, "libzmq-v140-mt-4_3_4.lib")
#else
#pragma comment(lib, "libzmq-v100-mt-4_3_4.lib")
#endif
#endif // PLATFORM_WIN

#if defined(_MSC_VER)
#define strtoll _strtoi64
#endif

#define NACOS_TIMEOUT 3

using namespace Rainbow;
using namespace MINIW;

static const char *DATASERVER_TCP_ADDR = "dataserver.tcp.miniw.env";
static const char *MAPSERVER_TCP_ADDR = "mapserver.tcp.miniw.env";

// TODO: Begin. note by cloud.
static int g_Count = 0;
void increase_count(){
	g_Count += 1;
}
void init_count(){g_Count=0;}
int get_count(){return g_Count;}
// TODO: End. note by cloud.

static inline int ChunkDivRegion(int x)
{
	int n = x / REGION_DIM;
	return (x - n*REGION_DIM < 0) ? n - 1 : n;
}

inline unsigned int LittleToHost_L(unsigned int h)
{
	return checkCPUendian() ? h : BigLittleSwap32(h);
}

enum ZMQ_SERVER_START_FAIL_TYPE {
	ZMQ_SERVER_START_FAIL_NACOS_HTTP_GET_FAILED = 200,
	ZMQ_SERVER_START_FAIL_NACOS_DATA_PARSE_FAILED,
	ZMQ_SERVER_START_FAIL_NACOS_NO_HOST,
	ZMQ_SERVER_START_FAIL_NACOS_HOSTS_ZERO,
	ZMQ_SERVER_START_FAIL_NACOS_WEIGHT_INDEX_ERR,	
};

ZmqThread::ZmqThread(){
	TickCount = 0;
	DSSendCount = 0;
	DSRecvCount = 0;
	LastLogTs = 0;

	m_NextTick = 0;
	m_ThreadRunning = false;
	
	mMaxSendMessageNum = 10;
	mMaxRecvMessageNum = 50;
	mLogDataServerIORate = 50;
	mLogIOTimeCost = 10;

	mListenIP = "";
	mListenPort = 0;
	Context = NULL;

	mDataServerIP = "";
	mDataServerPort = 0;
}

void ZmqThread::Start()
{
	m_Thread.Run(ZmqThread::runEntry, this);
}

void ZmqThread::setThreadName(const char* name )
{
	m_thread_name = name;
}

unsigned int ZmqThread::getDefaultWaitTime()
{
	return 1;
}

void* ZmqThread::ConnectToServer(const char* ip, int port, ZmqClient* zmqC)
{
	if (!Context){
		Context = zmq_ctx_new();
	}

	void* Requester = nullptr;
	int threadNum = 1;
	zmq_setsockopt(Context, ZMQ_IO_THREADS, &threadNum, sizeof(threadNum));
	Requester = zmq_socket(Context, ZMQ_DEALER);
	
	int linger = 1000;
	zmq_setsockopt(Requester, ZMQ_LINGER, &linger, sizeof(linger));

	int hwm = 0;
	zmq_setsockopt(Requester, ZMQ_RCVHWM, &hwm, sizeof(hwm));
	zmq_setsockopt(Requester, ZMQ_SNDHWM, &hwm, sizeof(hwm));

	bool immediateFlag = true;
	zmq_setsockopt(Requester, ZMQ_IMMEDIATE, &immediateFlag, sizeof(immediateFlag));

	std::string identity = zmqC->getIdentity();
	zmq_setsockopt(Requester, ZMQ_IDENTITY, identity.data(), identity.size());

	std::ostringstream IPStr;
	IPStr << "tcp://";
	IPStr << ip;
	IPStr << ":";
	IPStr << port;
	SLOG(INFO) << "zmq version: " << ZMQ_VERSION << " identify is : " << identity.c_str() << " " << zmqC->getName() << " set zmq server ip :" << IPStr.str().c_str();
	ZMQ_PROXY_LOG("zmq version: %d, identify is : %s, %s set zmq server ip :%s", ZMQ_VERSION, identity.c_str(), zmqC->getName().c_str(), IPStr.str().c_str());
	int RC = zmq_connect(Requester, IPStr.str().c_str());
	if (RC == 0) 
	{
		SLOG(INFO) << zmqC->getName() << " connect to dataserver succeed.";
		ZMQ_PROXY_LOG("%s connect to dataserver succeed.", zmqC->getName().c_str());
		return Requester;
	}
	else
	{
		int Err = zmq_errno();
		if (Err == EINVAL)
		{
			ZMQ_PROXY_LOG("%s connect to dataserver failed: The endpoint supplied is invalid.", zmqC->getName().c_str());
			SLOG(INFO) << zmqC->getName() << "DataThread connect to zmq server failed: The endpoint supplied is invalid.";
		}
		else if (Err == ETERM)
		{
			ZMQ_PROXY_LOG("%s connect to dataserver failed: The ØMQ context associated with the specified socket was terminated.", zmqC->getName().c_str());
			SLOG(INFO) << zmqC->getName() << "DataThread connect to zmq server failed: The ØMQ context associated with the specified socket was terminated.";
		}
		else if (Err == ENOTSOCK)
		{
			ZMQ_PROXY_LOG("%s connect to dataserver failed: The provided socket was invalid.", zmqC->getName().c_str());
			SLOG(INFO) << zmqC->getName() << "DataThread connect to zmq server failed: The provided socket was invalid.";
		}
		else if (Err == EMTHREAD)
		{
			ZMQ_PROXY_LOG("%s connect to dataserver failed: No I/O thread is available to accomplish the task.", zmqC->getName().c_str());
			SLOG(INFO) << zmqC->getName() << "DataThread connect to zmq server failed. No I/O thread is available to accomplish the task.";
		}
		else
		{
			ZMQ_PROXY_LOG("%s connect to dataserver failed. Err = %d", zmqC->getName().c_str(), Err);
			SLOG(INFO) << zmqC->getName() << "DataThread connect to zmq server failed. Err = %d", Err;
		}
	}

	return nullptr;
}

bool ReceiveDataFromOneServer(const string service_name, ZmqClient* cli){
	void* conn = cli->getZmqConnection();

	if (!conn) {
		SLOG(WARNING) << "ReceiveDataFromOneServer conn not inited "<< service_name.c_str();
		return false;
	}
	int recv_max = cli->getRecvMsgLimitPerTick();
	int RecvCount = 0;
	while (RecvCount <= recv_max) 
	{
		zmq_msg_t Msg;
		zmq_msg_init(&Msg);
		int RC = zmq_msg_recv(&Msg, conn, ZMQ_DONTWAIT);
		if (RC < 0)
		{
            int eno = zmq_errno();
            if (eno != EAGAIN)
                SLOG(ERROR) << "zmq_msg_recv, RC=" << RC << ", errno=" << eno;
			zmq_msg_close(&Msg);
			break;
		}
		else
		{
			if (!cli->PushRespToQueue(zmq_msg_data(&Msg), RC))
			{
				zmq_msg_close(&Msg);
				SLOG(WARNING)<<"Parse message from dataserver failed.";
				break;
			}
			zmq_msg_close(&Msg);
			++RecvCount;
			increase_count();
		}
	}
	return true;
}

int ZmqThread::ReceiveDataFromServer()
{
	// LLM_SCOPE((ELLMTag)EDNHLLMTag::NET_RecvDataTag);
	init_count();
	ZmqClientMgr::Singleton().ForEachZmqClient(ReceiveDataFromOneServer);
	return get_count();
}

bool SendDataToOneServer(const string& service_name, ZmqClient* cli){
	void *conn = cli->getZmqConnection();

	if (!conn) {
        SLOG(WARNING)<<"SendDataToOneServer conn not init. " << service_name.c_str();
		return false;
	}
	int SendCount = 0;
	int send_limit = cli->getSendMsgLimitPerTick();
	std::string Data;
	uint32_t DataSize = 0;
	while (SendCount <= send_limit)
	{
		if (!cli->PopReqFromQueue(Data, DataSize))
		{
			break;
		}
		zmq_msg_t Msg;
		zmq_msg_init_size(&Msg, DataSize);
		memcpy(zmq_msg_data(&Msg), Data.c_str(), DataSize);

		int SentSize = zmq_msg_send(&Msg, conn, ZMQ_DONTWAIT);
		if (SentSize != DataSize)
		{
			if (EINTR == errno)
			{
				zmq_msg_close(&Msg);
				continue;
			}
			SLOG(WARNING)<<"send message to dataserver failed. errno = "<< errno;
			zmq_msg_close(&Msg);
			break;
		}
		zmq_msg_close(&Msg);
		increase_count();
		++SendCount;
	}

	return true;
}

int ZmqThread::SendDataToServer()
{
	init_count();
	ZmqClientMgr::Singleton().ForEachZmqClient(SendDataToOneServer);
	return get_count();
}

int ZmqThread::_init() {
	if (!g_zmqMgr)
	{
		ZMQ_PROXY_LOG("zmq thread init failed, reason: g_zmqMgr == null.");
		return -1;
	}
	int error_code = 0;
	ZmqClientMgr::Singleton().InitZmqClientMgr();

	std::string dsIP, busIP;
	int dsPort, busPort;

	// Data server init
	g_zmqMgr->GetBackendServiceInfo("dataserver", dsIP, dsPort);
	ZMQ_PROXY_LOG("dataserver ip: %s, port: %d", dsIP.c_str(), dsPort);
	if (dsIP.size() > 0 && dsPort > 0) {
		DataServerClient& dsc = DataServerClient::Singleton();
		void* requester = ConnectToServer(dsIP.c_str(), dsPort, &dsc);
		if (!requester)
		{
			SLOG(WARNING) << "DataThread connect to data server failed.";
			dsc.setConnectDSFailed();
			return -1;
		}
		dsc.setZmqConnection(requester);
		dsc.setConnectDSSucceed();
	}
	else {
		SLOG(WARNING) << "DataThread data server get info failed.";
		ZMQ_PROXY_LOG("DataThread data server get info failed.");
		return -1;
	}

	// Msg bus init.
	g_zmqMgr->GetBackendServiceInfo("msgbus", busIP, busPort);
	ZMQ_PROXY_LOG("msgbus ip: %s, port: %d", busIP.c_str(), busPort);
	if (busIP.size() > 0 && busPort > 0) {
		MsgBusClient& mbc = MsgBusClient::Singleton();
		void* requester = ConnectToServer(busIP.c_str(), busPort, &mbc);
		if (!requester)
		{
			SLOG(WARNING) << "DataThread connect to msg bus failed.";
			mbc.setConnectDSFailed();
			return -1;
		}
		mbc.setZmqConnection(requester);
		mbc.setConnectDSSucceed();
	}
	else {
		SLOG(WARNING) << "DataThread msg bus get info failed.";
		ZMQ_PROXY_LOG("DataThread msg bus get info failed.");
		return -1;
	}

	return 0;
}

void ZmqThread::_fini() {

	if (m_ThreadRunning)
	{
		m_Thread.SignalQuit();
		m_Thread.WaitForExit();
		m_ThreadRunning = false;
	}

	// 线程退出之前，执行数据发送操作，保证所有消息都可以正确发送
	do {
		MINIW::ThreadSleep(1);
	} while (SendDataToServer());
	MINIW::ThreadSleep(2);

	// 线程退出之前，关闭zmq相关的套接字和上下文
	ZmqClientMgr::Singleton().ForEachZmqClient(
		[](const string service_name, ZmqClient* cli) -> bool{
			auto conn = cli->getZmqConnection();
			cli->setZmqConnection(NULL);
			if (zmq_close(conn) < 0)
			{
                SLOG(WARNING) << "DataThread close socket failed. service_name=" << service_name.c_str();
				return false;
			}
			return true;
		}
	);
	
	if (zmq_ctx_term(Context) < 0)
	{
        SLOG(WARNING) << "DataThread context destroy failed.";
	}
	/*if (zmq_ctx_destroy(Context) < 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("DataThread context destroy failed."));
	}*/
}

void ZmqThread::shutdown()
{
	m_Thread.SignalQuit();
}

void* ZmqThread::runEntry(void* userData) {
	ZmqThread* zmqThread = (ZmqThread*)userData;
	zmqThread->_ThreadRun();
	return nullptr;
}

void ZmqThread::_ThreadRun() {
	while (!m_Thread.IsQuitSignaled())
	{
		if (m_NextTick > 0) {
			auto curTick = Rainbow::Timer::getSystemTick();
			if (curTick < m_NextTick) {
				MINIW::ThreadSleep(1);
				continue;
			}
		}

		_processRun();
		m_NextTick = Rainbow::Timer::getSystemTick() + 20;
	}

	_fini();
	m_ThreadRunning = false;
}

void ZmqThread::_processRun()
{
	static bool s_zmqthread_inited = false;
	if (!s_zmqthread_inited)
	{
		// if (!GetClientInfoProxy()->getCloudMode())
		// {
		// 	shutdown();
		// 	return;
		// }

		int ret = _init();
		s_zmqthread_inited = (ret == 0);
		if (ret != 0)
		{
			return;
		}
	}

	++TickCount;

	uint64_t Begin = Rainbow::Timer::getSystemTick();
	DSSendCount += SendDataToServer();
	DSRecvCount += ReceiveDataFromServer();
	int64_t End1 = Rainbow::Timer::getSystemTick();
	if (End1 - Begin > mLogIOTimeCost)
	{
        SLOG(INFO) << "DataThread, DataServer send & recv use " <<(End1 - Begin) <<"ms";
	}

	//		int UsedTime = End1 - Begin;
	if (End1 - LastLogTs > 1000)
	{
		LastLogTs = End1;
		if (DSSendCount > mLogDataServerIORate || DSRecvCount > mLogDataServerIORate)
		{
            SLOG(INFO) << "DSSend/s =" << DSSendCount <<", DSRecv/s = " << DSRecvCount;
		}
		DSSendCount = 0;
		DSRecvCount = 0;
	}
}

ZmqMgr* g_zmqMgr = NULL;

EXPORT_SANDBOXGAME void InitZmqMgr()
{
	if (!g_zmqMgr)
	{
		g_zmqMgr = new ZmqMgr();
		g_zmqMgr->Init();
	}
}

ZmqMgr::ZmqMgr() : m_ListenerNewRobotRequest(this, &ZmqMgr::ReqestAddTeamRobot) , m_ListenerDeleteRobotRequest(this, &ZmqMgr::RemoveTeamRobot)
{
	{
		int zmq1, zmq2, zmq3;
		zmq_version(&zmq1, &zmq2, &zmq3);
		// LOG_INFO("zmq create, zmq version = %d, %d:%d:%d", ZMQ_VERSION, zmq1, zmq2, zmq3);
	}

	std::string t = GetICloudProxyPtr()->GetConfigStr("develop_config", "ignoremd5");
	if (t.c_str()[0] != '\0')
	{
		is_develop_room = true;
	}
	else
	{
		is_develop_room = false;
	}
	use_db_map_data = true;

	mNacosAddr = GetICloudProxyPtr()->GetConfigStr("backend", "nacos");
	m_UsePublicIp = GetICloudProxyPtr()->GetConfigNum("args", "use_publicip") > 0.1;

	init_ok_time = 0;
	send_exit = false;
	stop_node_heartbeat = false;
	transfer_all_player = false;

	initMapConfig();
	initHDConfig();

	mZmqThread.setThreadName("zmq0");
	cur_map_name = "";
	map_label = 0;
	map_worldtype = 0;
	map_thumbnail = "";
	map_author_uin = 0;
	map_translate = "";
	map_share_version = "";
	max_player_in_room = 0;

	uin_roomid = GetClientInfoProxy()->getEnterParam("account");
	uin_roomid += "_";
	uin_roomid += GetClientInfoProxy()->getEnterParam("room_id");

	personalroom_timeout_freeSec1 = 0;
	personalroom_timeout_freeSec2 = 0;
	personalroom_timeout_maxSec = 0;
	personalroom_timeout_keepSec = 0;

	// studio机器人占位
	MNSandbox::CloudServerConfigService::GetStaticNotifyNewRobotRequest().Subscribe(m_ListenerNewRobotRequest);
	MNSandbox::CloudServerConfigService::GetStaticNotifyDeleteRobotRequest().Subscribe(m_ListenerDeleteRobotRequest);
}

ZmqMgr::~ZmqMgr()
{
	release();

	// studio机器人占位
	MNSandbox::CloudServerConfigService::GetStaticNotifyNewRobotRequest().Unsubscribe(m_ListenerNewRobotRequest);
	MNSandbox::CloudServerConfigService::GetStaticNotifyDeleteRobotRequest().Unsubscribe(m_ListenerDeleteRobotRequest);
}

bool ZmqMgr::Init()
{
	mZmqThread.Start();

	return true;
}

void ZmqMgr::release()
{
	mZmqThread.shutdown();  
}

void ZmqMgr::initHDConfig()
{
	pushserver_addr = NULL;
	rentserver_addr = NULL;
	int   fd = open("/data/rent_server/HD_config_v2.lua", O_RDONLY);
	if (fd < 0)
	{
		fd = open("/data/rent_server/HD_config.lua", O_RDONLY);
	}
	if (fd < 0)
		return;
	char  buf[4096];
	char* pbuf = &buf[0];
	pbuf += sprintf(pbuf, "hdconfig = ");
	int ret = read(fd, pbuf, 4096);
	pbuf[ret] = NULL;
	close(fd);

	lua_State* L;
	L = luaL_newstate();
	luaL_openlibs(L);
	ret = luaL_dostring(L, buf);
	if (ret != 0)
		return;
	lua_getglobal(L, "hdconfig");
	ret = lua_istable(L, -1);
	if (ret != 1)
		return;

    lua_pushstring(L, "pushserver");
    lua_gettable(L, -2);
    ret = lua_isstring(L, -1);
    if (ret == 1)
    {
        const char* ttt = lua_tostring(L, -1);
        pushserver_addr = strdup(ttt);
    }
	lua_pop(L, 1);

    lua_pushstring(L, "rent_node_group");
    lua_gettable(L, -2);
    ret = lua_isstring(L, -1);
    if (ret == 1)
    {
        const char* ttt = lua_tostring(L, -1);
        char* region_config_char = strdup(ttt);
		if (region_config_char)
		{
			char* p = strstr(region_config_char, "-cloud-node-");
			if (p)
			{
				int len = p - region_config_char + 6;
				region_config = std::string(region_config_char, len);
				if (region_config == "tx-nkg-cloud")
				{
					region_config = "rent-cloud";
				}
			}
			free(region_config_char);
		}
    }
	lua_pop(L, 1);

    lua_pushstring(L, "rentserver");
    lua_gettable(L, -2);
    ret = lua_isstring(L, -1);
    if (ret == 1)
    {
        const char* ttt = lua_tostring(L, -1);
        rentserver_addr = strdup(ttt);
    }
	lua_pop(L, 1);
	
	rentnode_multi_addr.clear();
	rentnode_multi_addr_out.clear();
	multi_out_addr = "";
    lua_pushstring(L, "multi_node_ip");
    lua_gettable(L, -2);
    ret = lua_istable(L, -1);
    if (ret == 1)
    {
		lua_pushnil(L); /* first key */
		while (lua_next(L, -2) != 0)
		{
			if (lua_istable(L, -1))
			{
			    lua_pushstring(L, "out_addr");
			    lua_gettable(L, -2);
				if (lua_isstring(L, -1))
				{
					if (multi_out_addr.size() == 0)
					{
						multi_out_addr = lua_tostring(L, -1);
					}
					else
					{
						multi_out_addr += ",";
						multi_out_addr += lua_tostring(L, -1);						
					}
					rentnode_multi_addr_out.push_back(strdup(lua_tostring(L, -1)));
				}
				lua_pop(L, 1);								

			    lua_pushstring(L, "in_addr");
			    lua_gettable(L, -2);				
				if (lua_isstring(L, -1))
				{
					rentnode_multi_addr.push_back(strdup(lua_tostring(L, -1)));
				}
				lua_pop(L, 1);				
			}
			lua_pop(L, 1); /* removes 'value'; keeps 'key' for next iteration */
		}
	}	
	lua_pop(L, 1);	
	lua_close(L);
}

void ZmqMgr::initMapConfig()
{
	auto gp = GetClientInfoProxy();

	if (gp->getEnterParam("can_trace")[0])
		GetICloudProxyPtr()->setExtraConfig("personal_room", "can_trace", atoi(gp->getEnterParam("can_trace")));
	if (gp->getEnterParam("ud_max_player")[0])
		GetICloudProxyPtr()->setExtraConfig("personal_room", "ud_max_player", atoi(gp->getEnterParam("ud_max_player")));
	if (gp->getEnterParam("public_type")[0])
		GetICloudProxyPtr()->setExtraConfig("personal_room", "public_type", atoi(gp->getEnterParam("public_type")));

	const char* aid = gp->getEnterParam("toloadmapid");
	long long llaid = atoll(aid);
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_setMapCSOwid", MNSandbox::SandboxContext(nullptr)
		.SetData_String("wid", aid)
		.SetData_Number("specialType", NORMAL_WORLD));	
	//PermitsManager::GetInstance().setMapCSOwid(llaid, NORMAL_WORLD);

	auto appid = gp->GetServerAppID();

	if (gp->isRentServerMode())
	{
		DataServerClient::Singleton().PushReqToQueue(miniw::DataSourceType::MYSQL,
							miniw::RTMySQL::LoadRoomConfig, atoi(gp->getEnterParam("account")), aid, 0, std::string(gp->getEnterParam("room_id")), 0);
	}

	jsonxx::Object oglobal;
	int no_heart_beat_time = -1;
	do {
		const char* fmtstr = "%s/nacos/v1/cs/configs?group=DEFAULT_GROUP&dataId=gs_global_config";
		char url[1024] = { 0 };
		snprintf(url, sizeof(url), fmtstr, mNacosAddr.c_str());
		url[sizeof(url) - 1] = '\0';
		GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::initMapConfig() gs_global_config url:%s", url);
		Rainbow::Downloader nacos_(NACOS_TIMEOUT);
		bool ret_ = nacos_.BlockDownload(url, 0, 4096 * 4);
		if (ret_ != true)
		{
			GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::initMapConfig() nacos http get global config failed.  httpcode:%d msg:%s", nacos_.GetHttpCode(), nacos_.GetHttpErrMsg());
			break;
		}
		
		ret_ = oglobal.parse(nacos_.GetDownloadMemory());
		if (ret_ != true)
		{
			GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::initMapConfig() nacos gs_global_config data json parse failed.");
			break;
		}

		GET_JSON_VALUE_NUM(m_ApiServerAppid, oglobal, "api_appid");
		GET_JSON_VALUE_STR(m_ApiServerAppKey, oglobal, "api_appkey");
		GET_JSON_VALUE_STR(m_PlayerCostKey, oglobal, "player_cost_key");

		GetICloudProxyPtr()->LoadGlobalNacos(oglobal);
#ifdef IWORLD_UNIVERSE_BUILD
		std::string country = "CN";
		GET_JSON_VALUE_STR(country, oglobal, "country");
		MINIW::SetSharedCountry(country);
#endif

		// 背包日志开关
		if (oglobal.has<jsonxx::Object>("backpack_log"))
		{ // {"backpack_log": {"maps":[1,2,3], "appids":[ 4,5,6]}}
			jsonxx::Object& backpack_log = oglobal.get<jsonxx::Object>("backpack_log");
		}
		
	} while (false);

	do {
		// APPID
		const char* fmtstr = "%s/nacos/v1/cs/configs?group=map_config%d&dataId=map_%s";
		char url[1024] = { 0 };
		char appidstr[32] = { 0 };
		if (appid > 0) {
			snprintf(appidstr, 32, "%lld", appid);
			aid = appidstr;
		}
		snprintf(url, sizeof(url), fmtstr, mNacosAddr.c_str(), gp->getGameData("game_env"), aid);
		url[sizeof(url) - 1] = '\0';
		GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::initMapConfig() nacos map_config url:%s", url);
		// Nacos
		Rainbow::Downloader nacos_(NACOS_TIMEOUT);
		bool ret_ = nacos_.BlockDownload(url, 0, 4096 * 4);
		if (ret_ != true)
		{
			GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::initMapConfig() nacos map_config get failed.  httpcode:%d msg:%s", nacos_.GetHttpCode(), nacos_.GetHttpErrMsg());
			break;
		}
		jsonxx::Object o;
		ret_ = o.parse(nacos_.GetDownloadMemory());
		if (ret_ != true)
		{
			GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::initMapConfig() nacos data json parse failed.");
			break;
		}

		GetICloudProxyPtr()->LoadMapNacos(o);

		GET_JSON_VALUE_NUM(use_db_map_data, o, "db_map_data");
		LOG_INFO("server config [db_map_data] = %d", (int)use_db_map_data);


	} while (false);
	GetICloudProxyPtr()->ShowMapConfig();
	DataHubService::GetInstance().Init();
}

bool TickOneClient(const string& client_name, ZmqClient* cli){
	return cli->Tick(50.0);
}

void ZmqMgr::Tick()
{
	ZmqClientMgr::Singleton().ForEachZmqClient(TickOneClient);
}

int ZmqMgr::GetChatbubble()
{
	return GetICloudProxyPtr()->GetChatbubble();
}

const char* ZmqMgr::GetPushServerAddr()
{
	return pushserver_addr;
}

const char *ZmqMgr::GetRentServerAddr()
{
    return rentserver_addr;
}

int RandomByWeight(const std::vector<int>& weights)
{
	int ret = -1;

	int totalWeight = 0;
	for (int v : weights) totalWeight += v;
	if (totalWeight <= 0)
		return ret;
	srand(time(nullptr));
	int randRet = rand() % totalWeight + 1;
	int randBak = randRet;
	for (int i = 0; i < weights.size(); ++i)
	{
		if (randRet <= weights[i])
		{
			ret = i;
			SLOG(INFO) << "RandomByWeight tw:" << totalWeight << " rand:" << randBak << " curR:" << randRet << " select:" << i << " w:" << weights[i];
			break;
		}
		randRet -= weights[i];
	}

	return ret;
}

int ZmqMgr::GetServiceAddr(const char * serviceName, std::string& ip, int& port, bool use_publicip)
{
	std::string ssn = serviceName;
	bool isMapserver = (ssn.find(MAPSERVER_TCP_ADDR) != std::string::npos);
	if (isMapserver)
	{
#ifdef WINDOWS_SERVER
		return -1;
#endif
	}
    std::string url = mNacosAddr + "/nacos/v1/ns/instance/list?serviceName=" + ssn;
	GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::GetServiceAddr url:%s", url.c_str());

	Rainbow::Downloader nacos_(NACOS_TIMEOUT);
    bool ret_ = nacos_.BlockDownload(url.c_str(), 0, 4096 * 4 * 2);
    if (ret_ != true)
    {
		GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::GetServiceAddr failed, httpcode:%d msg:%s", nacos_.GetHttpCode(), nacos_.GetHttpErrMsg());

		MINIW::ScriptVM::game()->callFunction("sendRentRoomStartFailed", "i", ZMQ_SERVER_START_FAIL_NACOS_HTTP_GET_FAILED);
		if (url.find("dataserver.tcp.miniw.env") != std::string::npos)
			GetICloudProxyPtr()->ReportServerErrorProxy("start_failed", std::string("get service addr: nacos http get failed.") + ssn);
        return -1;
    }
    jsonxx::Object o;
    ret_ = o.parse(nacos_.GetDownloadMemory());
    if (ret_ != true)
    {
		MINIW::ScriptVM::game()->callFunction("sendRentRoomStartFailed", "i", ZMQ_SERVER_START_FAIL_NACOS_DATA_PARSE_FAILED);		
		GetICloudProxyPtr()->ReportServerErrorProxy("start_failed", std::string("get service addr: nacos data json parse failed,") + ssn);
        return -10;
    }
    if (!o.has<jsonxx::Array>("hosts"))
    {
		MINIW::ScriptVM::game()->callFunction("sendRentRoomStartFailed", "i", ZMQ_SERVER_START_FAIL_NACOS_NO_HOST);				
		GetICloudProxyPtr()->ReportServerErrorProxy("start_failed", std::string("get service addr: no host field,") + ssn);
        return -100;
    }
	jsonxx::Array& hosts_ = o.get<jsonxx::Array>("hosts");
	if (hosts_.size() <= 0)
	{
		MINIW::ScriptVM::game()->callFunction("sendRentRoomStartFailed", "i", ZMQ_SERVER_START_FAIL_NACOS_HOSTS_ZERO);
		if (!isMapserver)
			GetICloudProxyPtr()->ReportServerErrorProxy("start_failed", std::string("ZmqMgr nacos failed. hosts_ == 0") + ssn);
        return -100;
	}
	
	int target_index = -1;
	// 使用权重来随机
	if (hosts_.size() > 1)
	{
		std::vector<int> hostIndex;
		for (size_t i = 0; i < hosts_.size(); ++i)
		{
			auto& h = hosts_.get<jsonxx::Object>(i);
			if (h.has<jsonxx::Number>("weight"))
			{
				hostIndex.push_back(h.get<jsonxx::Number>("weight"));
			}
		}
		target_index = RandomByWeight(hostIndex);
	}
	if (target_index < 0)
	{
		target_index = rand() % hosts_.size();  // 如果未配置weight则随机
	}
	
    if (!hosts_.has<jsonxx::Object>(target_index))
    {
		MINIW::ScriptVM::game()->callFunction("sendRentRoomStartFailed", "i", ZMQ_SERVER_START_FAIL_NACOS_WEIGHT_INDEX_ERR);
		GetICloudProxyPtr()->ReportServerErrorProxy("start_failed", std::string("ZmqMgr nacos get weight index err.") + ssn);
        return -200;
    }
    jsonxx::Object& rand_host_ = hosts_.get<jsonxx::Object>(target_index);
    if (!rand_host_.has<jsonxx::Object>("metadata"))
    {
		MINIW::ScriptVM::game()->callFunction("sendRentRoomStartFailed", "i", ZMQ_SERVER_START_FAIL_NACOS_DATA_PARSE_FAILED);		
		GetICloudProxyPtr()->ReportServerErrorProxy("start_failed", "ZmqMgr nacos data json parse failed.");
        return -300;
    }
    jsonxx::Object& metadata_ = rand_host_.get<jsonxx::Object>("metadata");
    if (!(metadata_.has<jsonxx::String>("port") && metadata_.has<jsonxx::String>("ip")))
    {
		MINIW::ScriptVM::game()->callFunction("sendRentRoomStartFailed", "i", ZMQ_SERVER_START_FAIL_NACOS_DATA_PARSE_FAILED);				
		GetICloudProxyPtr()->ReportServerErrorProxy("start_failed", "ZmqMgr nacos data json parse failed.");
        return -400;
    }
//#ifdef WINDOWS_SERVER
    //ip = "**************";
//#else
	if (use_publicip && metadata_.has<jsonxx::String>("public_ip"))
	{
		ip = metadata_.get<jsonxx::String>("public_ip");
	}
	else
	{
		ip = metadata_.get<jsonxx::String>("ip");
	}
//#endif
    port = atoi(metadata_.get<jsonxx::String>("port").c_str());
    return (0);
}

bool ZmqMgr::GetBackendServiceInfo(const char* serviceName, std::string& ip, int& port)
{
	return GetICloudProxyPtr()->GetBackendServiceInfo(serviceName, ip, port);
}

bool ZmqMgr::CheckForbidJoin()
{
	if (stop_node_heartbeat)
		return true;
	if (init_ok_time == 0)
		return true;
	auto game = GetIClientGameManagerInterface()->getICurGame();
	if (game && !game->canAcceptClientJoin())
		return true;
	if (GetICloudProxyPtr()->GetForbidJoinTime() < 1)
		return false;
	int now_ = Rainbow::Timer::getSystemTick() / 1000;
	if (now_ > init_ok_time + GetICloudProxyPtr()->GetForbidJoinTime())
		return true;
	return false;
}

void ZmqMgr::SetForbidJoin(int seconds)
{
	if (seconds == 0)
		seconds = 1;   // 0秒被认为未配置，这里填个1即房间立刻禁止加入了
	int lasttime = GetICloudProxyPtr()->GetForbidJoinTime();
	if (lasttime != seconds)
	{
		GetICloudProxyPtr()->SetForbidJoinTime(seconds);
		GetICloudProxyPtr()->SimpleErrLog(0, 0, "master_set_forbidjoin", "SetForbidJoinTime:" + to_string(seconds));
		static bool heartbeat = true;
		// 首次设置且需要立刻禁止加入时立刻上报，其他情况等心跳自动上报
		// if (heartbeat && seconds == 1)
		if (seconds == 1)		
		{
			heartbeat = false;
			HeartbeatToDataServer(nullptr, nullptr, nullptr, 0);
		}
	}
}

void ZmqMgr::CloseRoom(int delay, const std::string& msg)
{
	// 仅可调用一次
	static bool closed = false;
	if (closed)
		return;
	closed = true;

	jsonxx::Object log;
	log << "delay" << delay;
	log << "msg" << msg;
	GetICloudProxyPtr()->InfoLog(0, 0, "master_close_room", log);
	auto game = GetIClientGameManagerInterface()->getICurGame(GameType::MpGameSurvive);
	if (game)
	{
		game->OnRecvMapRoomClose(delay, msg);  // 这里面会调用SetForbidJoin
	}
}

void ZmqMgr::TeleportPlayerAndCloseRoom(int delay, const std::string& msg)
{
	if (!GetClientInfoProxy()->isRentServerMode() && !GetClientInfoProxy()->isPersonalCloudServer())
	{
		ScriptVM::game()->callFunction("DelayTeleportAllPlayers", "i", 5);
	}
	CloseRoom(delay + 30, msg);
}
int ZmqMgr::GetInitokTime()
{
	return init_ok_time;
}

bool ZmqMgr::CheckAutoExit()
{
	if (init_ok_time == 0 || GetICloudProxyPtr()->GetAutoExitTime() < 1)
		return false;
	int now_ = Rainbow::Timer::getSystemTick() / 1000;
	if (now_ > init_ok_time + GetICloudProxyPtr()->GetAutoExitTime())
		return true;
	return false;
}

bool ZmqMgr::CheckNoHeartbeat()
{
	if (init_ok_time == 0 || GetICloudProxyPtr()->GetNoHeatbeatTime() < 1)
		return false;
	int now_ = Rainbow::Timer::getSystemTick() / 1000;
	if (now_ > init_ok_time + GetICloudProxyPtr()->GetNoHeatbeatTime())
		return true;
	return false;
}

int ZmqMgr::SetInitOk()
{
	WarningStringMsg("ZmqMgr::SetInitOk \n"\
"      ___           ___       ___           ___           ___     \n" \
"     /  /\\         /  /\\     /  /\\         /  /\\         /  /\\    \n" \
"    /  /::\\       /  /:/    /  /::\\       /  /:/        /  /::\\   \n" \
"   /  /:/\\:\\     /  /:/    /  /:/\\:\\     /  /:/        /  /:/\\:\\  \n" \
"  /  /:/  \\:\\   /  /:/    /  /:/  \\:\\   /  /:/        /  /:/  \\:\\ \n" \
" /__/:/ \\  \\:\\ /__/:/    /__/:/ \\__\\:\\ /__/:/     /\\ /__/:/ \\__\\:|\n" \
" \\  \\:\\  \\__\\/ \\  \\:\\    \\  \\:\\ /  /:/ \\  \\:\\    /:/ \\  \\:\\ /  /:/\n" \
"  \\  \\:\\        \\  \\:\\    \\  \\:\\  /:/   \\  \\:\\  /:/   \\  \\:\\  /:/ \n" \
"   \\  \\:\\        \\  \\:\\    \\  \\:\\/:/     \\  \\:\\/:/     \\  \\:\\/:/  \n" \
"    \\  \\:\\        \\  \\:\\    \\  \\::/       \\  \\::/       \\__\\__/   \n" \
"     \\__\\/         \\__\\/     \\__\\/         \\__\\/           ");
	init_ok_time = Rainbow::Timer::getSystemTick() / 1000;
	GetClientAppProxy()->SetServerInitOk();
	GetICloudProxyPtr()->SimpleSLOG("ZmqMgr::SetInitOk() time:%d", init_ok_time);

	// 启动成功后，启动master
	CenterGSMgr::getInstance()->Init();
	return 0;
}

void ZmqMgr::SetMapInfo(int label, int worldtype, char *thumbnail, int author_uin, char *translate, char *share_version, char *map_name)
{
	map_label = label;
	map_worldtype = worldtype;
	map_thumbnail = thumbnail;
	map_author_uin = author_uin;
	map_translate = translate;
	map_share_version = share_version;
	auto&       info           = *GetClientInfoProxy();
	const char* str_author_uin = info.getEnterParam("author_uin");
	int         n_author_uin   = atoi(str_author_uin);
	if (n_author_uin > 0)
	{
		map_author_uin = n_author_uin;
	}
	
	SaveCurMapName(map_name);
}

const char* ZmqMgr::GetMapTranslate()
{
	return map_translate.c_str();
}

int ZmqMgr::HeartbeatToDataServer(const char *roomMods, const char *roomUILibs, const char *roomAudioConfig, int first_init)
{
	if (send_exit)
		return 0;		
	
	if (init_ok_time == 0)
		return 0;

	if (first_init == 0)
	{
#ifndef WINDOWS_SERVER
		return HeartbeatRunTime();
#else
		return 0;
#endif
	}

	GetICloudProxyPtr()->ReportServerStartOK();

#ifdef WINDOWS_SERVER
	return 0;
#endif

	auto info = GetClientInfoProxy();
	miniw::game_stat stat;
	const char* s_uin = info->getEnterParam("account");
	const char* aid = info->getEnterParam("toloadmapid");
	const char* roomid = info->getEnterParam("room_id");
	const char* reserved = info->getEnterParam("reserved_num");
	const char* private_ = info->getEnterParam("private");
	const char* room_tag = info->getEnterParam("tag");
	const char* room_from = info->getEnterParam("room_from");
	int player_num = GameNetManager::getInstance()? GameNetManager::getInstance()->getMaxPlayerNum():atoi(info->getEnterParam("playernum"));
	int reserved_num = 0;
	if (strlen(reserved) > 0)
	{
		reserved_num = atoi(reserved);
		player_num -= reserved_num;
	}

	const char* room_ver = info->getEnterParam("ver");
	const char* room_name = cur_map_name;
	if (!room_name)
		room_name = "";

	int64_t int_uin = strtoll(s_uin, NULL, 10);
	char    uin_roomid[256];
	sprintf(uin_roomid, "%s_%s", s_uin, roomid);
	stat.set_roomid(uin_roomid);
	stat.set_aid(aid);
	stat.set_cap(player_num);
	if (multi_out_addr.size() > 0)
	{
		stat.set_ipaddr(multi_out_addr);		
	}
	else
	{
		stat.set_ipaddr(info->getEnterParam("ip"));
	}
	stat.set_port(atoi(info->getEnterParam("port")));
	stat.set_room_ver(room_ver);
	stat.set_room_name(room_name);
	stat.set_room_mods(roomMods);
	stat.set_room_ui_libs(roomUILibs);
	stat.set_room_audio_config(roomAudioConfig);
	stat.set_label(map_label);
	stat.set_worldtype(map_worldtype);
	stat.set_thumbnail(map_thumbnail);
	stat.set_translate(map_translate);
	stat.set_personal(GetClientInfoProxy()->isPersonalCloudServer());
	stat.set_public_type(GetICloudProxyPtr()->GetConfigNum("personal_room", "public_type"));
	stat.set_can_trace(GetICloudProxyPtr()->GetConfigNum("personal_room", "can_trace"));
	stat.set_share_version(map_share_version);
	stat.set_tag(room_tag);
	stat.set_room_from(room_from);
	stat.set_region(region_config);

	auto service = MNSandbox::GetCurrentCloudServerConfigService();
    pair<int,int> roomWeight = service ? service->GetRoomMatchWeight() : std::make_pair(0, 0);
	stat.set_room_weight(roomWeight.first);
	stat.set_friend_weight(roomWeight.second);


	int editorSceneSwitch = GetClientInfoProxy()->GetEditorSceneSwitch();
	if (editorSceneSwitch != -1)
	{
		stat.set_editor_scene_switch(editorSceneSwitch);
	}

	//stat.set_reserved_num(reserved_num);
	if (CheckForbidJoin())
	{
		stat.set_forbid_join(1);
	}

	if (private_ && private_[0] != '\0')
	{
		stat.set_private_(1);
	}

	const char* password = info->getEnterParam("password");
	if (password && password[0] != '\0')
	{
		stat.set_private_(1);
		stat.set_passwd(password);
	}

	int         member_num  = 0;
	RoomClient* room_client = GameNetManager::getInstance()->getRoomClient();

	if (room_client)
	{
		WorldManager* worldMgr = GetWorldManagerPtr();
		if (!worldMgr) return 0;
		const std::set<int>& members = room_client->getMemberList();
		for (auto it = members.begin(); it != members.end(); ++it)
		{
			auto player = worldMgr->getPlayerByUin(*it);
			// 未登录完或已登录完未下线的玩家
			if (!player || !player->IsOffline()) {
				++member_num;
				char t_uin[64];
				sprintf(t_uin, "%d", *it);
				stat.add_uinlist(t_uin);
			}
		}
	}
	
	auto game = GetIClientGameManagerInterface()->getICurGame(GameType::MpGameSurvive);
	if (game)
	{// 带上所有队伍信息, 包括玩家列表, 人数上限等
		std::vector<TeamInfo> teams;
		if (game->getEnableTeamsInfo(teams))
		{
			char t_uin[64];
			for (auto it = teams.begin(); it!= teams.end(); ++it)
            {
				auto team_st = stat.add_teams();
				fillTeamInfo(team_st, *it);
            }
		}
	}

	stat.set_num(member_num);
	if (GetClientInfoProxy()->getEnterParam("trans_msg")[0])  // 向gsmgr透传的参数
	{
		stat.set_trans_msg(GetClientInfoProxy()->getEnterParam("trans_msg"));
	}

	LOG_INFO("%s: cap = %d", __FUNCTION__, stat.cap());
	
	std::string param2 = stat.SerializeAsString();

	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
	                      miniw::RTMySQL::UpdateGSState, int_uin, aid, 0, param2, 0);
	return (0);
}

std::string ZmqMgr::getSectionName()
{
#ifdef WINDOWS_SERVER
	int   fd = open("./server_root/HD_config.lua", O_RDONLY);
#else
	int   fd = open("/data/rent_server/HD_config_v2.lua", O_RDONLY);
	if (fd < 0)
	{
		fd = open("/data/rent_server/HD_config.lua", O_RDONLY);
	}
#endif
	if (fd < 0)
	{
		SLOG(WARNING) << "getSectionName return empty 1";
		return "";
	}
	char  buf[4096];
	char* pbuf = &buf[0];
	pbuf += sprintf(pbuf, "hdconfig = ");
	int nread = read(fd, pbuf, 4096);
	close(fd);
	pbuf[nread] = NULL;

	lua_State* L;
	L = luaL_newstate();
	luaL_openlibs(L);
	int ret = luaL_dostring(L, buf);
	if (ret != 0)
	{
		SLOG(WARNING) << "getSectionName return empty 2 " << buf;
		return "";
	}
	lua_getglobal(L, "hdconfig");
	ret = lua_istable(L, -1);
	if (ret != 1)
	{
		SLOG(WARNING) << "getSectionName return empty 3" << buf;
		return "";
	}

	lua_pushstring(L, "section");
	lua_gettable(L, -2);
	ret = lua_isstring(L, -1);
	if (ret != 1)
	{
		SLOG(WARNING) << "getSectionName return empty 4" << buf;
		return "";
	}

	std::string ttt = lua_tostring(L, -1);
	lua_close(L);
	return ttt;
}

int ZmqMgr::HeartbeatRunTime()
{
	if (CheckNoHeartbeat())
		return (0);
	miniw::game_stat_runtime stat;
	auto& info = *GetClientInfoProxy();
	const char* s_uin = info.getEnterParam("account");
	const char* roomid = info.getEnterParam("room_id");
	const char* reserved = info.getEnterParam("reserved_num");
	const char* aid = info.getEnterParam("toloadmapid");
	int player_num = GameNetManager::getInstance()? GameNetManager::getInstance()->getMaxPlayerNum(): atoi(info.getEnterParam("playernum"));
	int reserved_num = 0;
	if (strlen(reserved) > 0) {
		reserved_num = atoi(reserved);
		player_num -= reserved_num;
	}

	int64_t int_uin = strtoll(s_uin, NULL, 10);
	char uin_roomid[256];
	sprintf(uin_roomid, "%s_%s", s_uin, roomid);
	stat.set_roomid(uin_roomid);
	stat.set_cap(player_num);
	if (multi_out_addr.size() > 0)
	{
		stat.set_ipaddr(multi_out_addr);		
	}
	else
	{
		const char* ip = info.getEnterParam("ip");
		stat.set_ipaddr(ip);
	}
	if (CheckForbidJoin())
	{
		stat.set_forbid_join(1);
	}
	
	auto service = MNSandbox::GetCurrentCloudServerConfigService();
	pair<int, int> roomWeight = service ? service->GetRoomMatchWeight() : std::make_pair(0, 0);
	stat.set_room_weight(roomWeight.first);
	stat.set_region(region_config);

	int member_num = 0;
	if (GameNetManager::getInstance()->getConnection())
	{
		const RakPeerInterface* rakpeer = GameNetManager::getInstance()->getConnection()->GetRakPeerInterface();
		if (rakpeer)
		{
			DataStructures::List<SystemAddress> addresses;
			DataStructures::List<RakNetGUID>    guids;
			rakpeer->GetSystemList(addresses, guids);
			for (unsigned int i = 0; i < guids.Size(); i++)
			{
				++member_num;
				char t_uin[64];
				sprintf(t_uin, "%llu", guids[i].g);
				stat.add_uinlist(t_uin);
			}
		}
	}

	auto itrRob = m_StudioTeamRobot.begin();
	for (; itrRob != m_StudioTeamRobot.end(); ++itrRob)
	{
		auto itrRobUin = itrRob->second.begin();
		for (; itrRobUin != itrRob->second.end(); ++itrRobUin)
		{
			++member_num;
			char t_uin[64];
			sprintf(t_uin, "%d", *itrRobUin);
			stat.add_uinlist(t_uin);
		}
	}
	stat.set_num(member_num);

	auto game = GetIClientGameManagerInterface()->getICurGame(GameType::MpGameSurvive);
	if (game)
	{
		std::vector<TeamInfo> teams;
		if (game->getEnableTeamsInfo(teams))
		{
			for (auto it = teams.begin(); it != teams.end(); ++it)
			{
				auto team_st = stat.add_teams();
				fillTeamInfo(team_st, *it);
			}
		}
	}

	LOG_INFO("%s: cap = %d", __FUNCTION__, stat.cap());

	std::string param2 = stat.SerializeAsString();
	
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
		miniw::RTMySQL::UpdateGSStateRuntime,
		int_uin, aid, 0, param2, 0);

	return 0;
}

void ZmqMgr::fillTeamInfo(miniw::team_stat* team_st, const TeamInfo& teamInfo)
{
	char t_uin[64] = {0};
	team_st->set_teamid(teamInfo.m_TeamID);
	team_st->set_cap(teamInfo.m_Cap);
	for (auto it2 = teamInfo.m_Members.begin(); it2 != teamInfo.m_Members.end(); ++it2)
	{
		sprintf(t_uin, "%d", *it2);
		team_st->add_players(t_uin, strlen(t_uin));
	}

	// 附加机器人队友
	auto itrRob = m_StudioTeamRobot.find(teamInfo.m_TeamID);
	if (itrRob != m_StudioTeamRobot.end())
	{
		for (int id : itrRob->second)
		{
			sprintf(t_uin, "%d", id);
			team_st->add_players(t_uin, strlen(t_uin));
			if (team_st->players_size() > teamInfo.m_Cap)
			{
				jsonxx::Object log;
				log << "team" << teamInfo.m_TeamID;
				log << "cap" << teamInfo.m_Cap;
				log << "players" << teamInfo.m_Members.size();
				log << "robots" << itrRob->second.size();
				GetICloudProxyPtr()->InfoLog(0, 0, "studio_robot_err", log);

				break;
			}
		}
	}
}

int ZmqMgr::SendExitToDataServer()
{
	WorldManager *worldMgr = GetWorldManagerPtr();
	if (worldMgr)
		worldMgr->save();
	
	miniw::game_stat stat;
	auto& info = *GetClientInfoProxy();
	const char*      s_uin   = info.getEnterParam("account");
	const char*      aid     = info.getEnterParam("toloadmapid");
	const char*      roomid  = info.getEnterParam("room_id");

	const char* room_ver     = info.getEnterParam("ver");
	// const char* room_name    = cur_map_name;
	// if (!room_name)
	// 	room_name = "";

	int64_t          int_uin = strtoll(s_uin, NULL, 10);
	char             uin_roomid[256];
	sprintf(uin_roomid, "%s_%s", s_uin, roomid);
	stat.set_roomid(uin_roomid);	
	stat.set_aid(aid);
	stat.set_cap(atoi(info.getEnterParam("playernum")));
	if (multi_out_addr.size() > 0)
	{
		stat.set_ipaddr(multi_out_addr);
	}
	else
	{
		stat.set_ipaddr(info.getEnterParam("ip"));
	}
	stat.set_port(atoi(info.getEnterParam("port")));
	stat.set_room_ver(room_ver);
	// stat.set_room_name(room_name);
	stat.set_num(0);
	std::string param2 = stat.SerializeAsString();
	GetICloudProxyPtr()->SimpleSLOG("SendExit msg:%s", param2.c_str());

	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
		miniw::RTMySQL::UpdateGSState, int_uin, aid, 1, param2, 0);

	send_exit = true; //这样就不会再发心跳了
	UnloadMapDataToDataServer(int_uin, aid);

	if(GetICloudProxyPtr()->GetNeedTeam() && worldMgr && worldMgr->isGameMakerRunMode())
	{
		auto rule_mgr = worldMgr->m_RuleMgr;
		if (rule_mgr && (rule_mgr->getGameStage() == CGAME_STAGE_INIT || rule_mgr->getGameStage() == CGAME_STAGE_PREPARE))
		{
			jsonxx::Object obj;
			obj << "total_player_num" << 10;
			obj << "max_players" << 9;
			obj << "cap" << atoi(GetClientInfoProxy()->getEnterParam("playernum"));
			GetICloudProxyPtr()->InfoLog(0, 0, "end_never_start", obj);
		}
	}
	GameNetManager::getInstance()->EFF_statis();
	if (GetWorldManagerPtr())
		DataHubService::GetInstance().DoReport();//关服上报DHS失败/错误统计数据
	return (0);
}

int ZmqMgr::SavePlayerDataToDataServer(const void *data, int datalen, long long owid, int uin)
{
	ZMQ_PROXY_LOG("SavePlayerDataToDataServer uin:%d, owid:%lld, datalen:%d", uin, owid, datalen);
	return SaveDataToDataServer(miniw::RTMySQL::SavePlayerData, data, datalen, owid, uin);
}

int ZmqMgr::LoadPlayerDataFromDataServer(long long owid, int uin, const PlayerBriefInfo *pbi)
{
	ZMQ_PROXY_LOG("LoadPlayerDataFromDataServer uin:%d, owid:%lld", uin, owid);
	DataServerClient::Singleton().holdLoadPlayerBriefInfo(uin, pbi);
	return LoadDataFromDataServer(miniw::RTMySQL::LoadAllPlayerData, owid, uin);
}

int ZmqMgr::SaveDataToDataServer(miniw::RTMySQL type, const void *data, int datalen, long long owid, int uin)
{
	ZMQ_PROXY_LOG("SaveDataToDataServer type:%d, uin:%d, owid:%lld, datalen:%d", type, uin, owid, datalen);
	if (type == miniw::RTMySQL::SaveCloudLibVar || type == miniw::RTMySQL::SavePlayerData)
	{
		if (entered_uins.count(uin) == 0)
		{
			jsonxx::Object log;
			log << "type" << type;
			log << "owid" << owid;
			GetICloudProxyPtr()->InfoLog(uin, 0, "save_after_leave", log);
			return 0;
		}
	}

	assert(uin >= 1000);
    long long appid = GetClientInfoProxy()->GetServerAppID();
    long long wid = owid;
    if (appid > 0) {
		bool useAppID = Rainbow::GetICloudProxyPtr()->PlayerDataUseAppId();
		if (useAppID)
			wid = appid;
    }
    char c_wid[64];
    sprintf(c_wid, "%lld", wid);
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
		type, uin, c_wid, 0, data, datalen, 0);

	SLOG(INFO) << "SaveDataToDataServer type=" << (int)type << " uin=" << uin << " wid=" << wid << " datalen=" << datalen;
	return (0);
}

int ZmqMgr::LoadDataFromDataServer(miniw::RTMySQL type, long long owid, int uin)
{
    long long appid = GetClientInfoProxy()->GetServerAppID();
    long long wid = owid;
    if (appid > 0) {
		bool useAppID = Rainbow::GetICloudProxyPtr()->PlayerDataUseAppId();
		if (useAppID)
			wid = appid;
    }
    char c_wid[64];
    sprintf(c_wid, "%lld", wid);
	DataServerClient& Client = DataServerClient::Singleton();
	// 增加roomid数据，获取玩家数据时ds检测玩家是否在线
	auto info = GetClientInfoProxy();
	std::ostringstream os;
	os << info->getEnterParam("room_id") << ":" << info->getEnterParam("ip");
	miniw::load_all_player_data_req datareq;
	datareq.set_online_roomid(os.str());
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL, type, uin, c_wid, 0, datareq.SerializeAsString(), 0);
	return (0);
}

int ZmqMgr::SaveDataToRedisDataServer(miniw::RTRedis type, const void *data, int datalen, long long owid, int uin)
{
	LOG_DEBUG("SaveDataToRedisDataServer type=%d len=%d wid=%lld uin=%d", type, datalen, owid, uin);
	char c_wid[64];
	sprintf(c_wid, "%lld", owid);	
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::REDIS,
		type, uin, c_wid, MINIW::GetTimeStamp(), data, datalen, 0);
	if (type == miniw::RoleEnterRoom)
		entered_uins.insert(uin);
	else if (type == miniw::RoleLeaveRoom)
		entered_uins.erase(uin);
	return (0);
}

int ZmqMgr::LoadDataFromRedisDataServer(miniw::RTRedis type, long long owid, int uin)
{
	LOG_DEBUG("LoadDataFromRedisDataServer type=%d wid=%lld uin=%d", type, owid, uin);
	char c_wid[64];
	sprintf(c_wid, "%lld", owid);	
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::REDIS,
		type, uin, c_wid, 0, (void*)NULL, 0, 0);
	return (0);
}

void ZmqMgr::SaveCurMapName(char* name)
{
	if (name)
		cur_map_name = strdup(name);
}

int ZmqMgr::SaveStarStationToDataServer(const void* data, int datalen)
{
	if (!use_db_map_data)
		return -10;
	if (map_author_uin == 0)
		return -100;
	const char* roomid = GetUinroomid();
	miniw::map_starstation_data star_data;
	star_data.set_roomid(roomid);
	star_data.set_data(data, datalen);
	ZMQ_PROXY_LOG("SaveStarStationToDataServer roomid = %s, len = %d", roomid, datalen);
	std::string param2 = star_data.SerializeAsString();

	auto gp = GetClientInfoProxy();	
	const char* aid = gp->getEnterParam("toloadmapid");	
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
		miniw::RTMySQL::SaveStarStation, 0, aid, 0, param2, 0);
	return (0);
}

int ZmqMgr::LoadStarStationFromDataServer()
{
	if (!use_db_map_data)
		return -10;
	if (map_author_uin == 0)
		return -100;
	const char* roomid = GetUinroomid();
	miniw::map_starstation_data star_data;
	star_data.set_roomid(roomid);	
	ZMQ_PROXY_LOG("LoadStarStationFromDataServer roomid = %s", roomid);
	std::string param2 = star_data.SerializeAsString();
	
	auto gp = GetClientInfoProxy();
	const char* aid = gp->getEnterParam("toloadmapid");	
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
		miniw::RTMySQL::LoadStarStation, 0, aid, 0, param2, 0);
	return (0);
}

int ZmqMgr::SaveActorToDataServer(int uin, long long owid, int mapid, int x, int z, const void* data, int datalen)
{
	return _SaveAnyChunkToDataServer(uin, owid, mapid, x, z, data, datalen, true);
}
int ZmqMgr::SaveChunkToDataServer(int uin, long long owid, int mapid, int x, int z, const void* data, int datalen)
{
	return _SaveAnyChunkToDataServer(uin, owid, mapid, x, z, data, datalen, false);
}
int ZmqMgr::_SaveAnyChunkToDataServer(int uin, long long owid, int mapid, int x, int z, const void* data, int datalen, bool isActorData)
{
	if (!use_db_map_data)
		return -10;
	if (map_author_uin == 0)
		return -100;

	if (send_exit)
		return 0;

	// m_IOMgr->pushResult(cmd);

	//LOG_DEBUG("SaveChunkToDataServer wid=%lld x=%d z = %d, uin = %d", owid, x, z, uin);
	//const char* roomid = GetClientInfoProxy()->getEnterParam("room_id");
	const char* roomid = GetUinroomid();
	miniw::map_chunk_data chunk_data;
	chunk_data.set_worldid(mapid);
	chunk_data.set_roomid(roomid);
	chunk_data.set_reg_x(ChunkDivRegion(x));
	chunk_data.set_reg_z(ChunkDivRegion(z));
	chunk_data.set_author_uin(map_author_uin);
	auto&       info  = *GetClientInfoProxy();
	const char* rentserver_resid = info.getEnterParam("rentserver_resid");
	if (rentserver_resid)
	{
		chunk_data.set_rentserver_resid(rentserver_resid);
	}

	miniw::trunk_coord* coord = chunk_data.add_trunkpos();
	coord->set_x(x);
	coord->set_z(z);

	int totallen = datalen;
	int saveType = miniw::RTMySQL::SaveMapData;
	if (isActorData)
	{
		saveType = miniw::RTMySQL::SaveActorData;
		coord->set_actor_data(data, datalen);
	}
	else
	{
		const CHUNKSAVEDB* savedb = (const CHUNKSAVEDB*)data;
		datalen = savedb->ChunkBlob.BlobLen;
		int nempty = (datalen + 8) % SECTOR_SIZE;
		nempty = 0;
		if (nempty > 0) nempty = SECTOR_SIZE - nempty;
		int totallen = datalen + 8 + nempty;
		char* tmpbuf = (char*)malloc(totallen);

		*(unsigned int*)tmpbuf = LittleToHost_L(savedb->ChunkBlob.BlobLen); //ziplen
		*(unsigned int*)(tmpbuf + 4) = LittleToHost_L(savedb->ChunkBlob.UnzipLen); //unziplen
		memcpy(tmpbuf + 8, savedb->ChunkBlob.BlobDetail, datalen);
		if (nempty > 0) memset(tmpbuf + 8 + datalen, 0, nempty);

		coord->set_data(tmpbuf, totallen);
		free(tmpbuf);
	}

	std::string param2 = chunk_data.SerializeAsString();

	char c_wid[64];
	sprintf(c_wid, "%lld", owid);
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
		saveType, uin, c_wid, 0, param2, 0);
	return (0);
}
int ZmqMgr::LoadChunkFromDataServer(int uin, long long owid, int mapid, int x, int z, bool loadactor)
{
	if (!use_db_map_data)
		return -10;
	if (map_author_uin == 0)
		return -100;
	
	LOG_DEBUG("LoadChunkFromDataServer wid=%lld x=%d z = %d uin = %d", owid, x, z, uin);
	//const char* roomid = GetClientInfoProxy()->getEnterParam("room_id");
	const char* roomid = GetUinroomid();
	miniw::map_chunk_data chunk_data;
	chunk_data.set_worldid(mapid);
	chunk_data.set_roomid(roomid);
	chunk_data.set_reg_x(ChunkDivRegion(x));
	chunk_data.set_reg_z(ChunkDivRegion(z));
	chunk_data.set_author_uin(map_author_uin);
	auto&       info  = *GetClientInfoProxy();
	const char* rentserver_resid = info.getEnterParam("rentserver_resid");
	if (rentserver_resid)
	{
		chunk_data.set_rentserver_resid(rentserver_resid);
	}

	miniw::trunk_coord* coord = chunk_data.add_trunkpos();	
	coord->set_x(x);
	coord->set_z(z);
	std::string param2 = chunk_data.SerializeAsString();

	char c_wid[64];
	sprintf(c_wid, "%lld", owid);	
	DataServerClient& Client = DataServerClient::Singleton();
	if (loadactor)
	{
		Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
			miniw::RTMySQL::LoadActorData, uin, c_wid, 0, param2, 0);
	}
	else
	{
		Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
			miniw::RTMySQL::LoadMapAllData, uin, c_wid, 0, param2, 0);
	}
	return (0);	
}

int ZmqMgr::LoadActorFromDataServer(int uin, long long owid, int mapid, int x, int z)
{
	ZMQ_PROXY_LOG("LoadActorFromDataServer uin:%d, owid:%lld, mapid:%d, x:%d, z:%d", uin, owid, mapid, x, z);
	return LoadChunkFromDataServer(uin, owid, mapid, x, z, true);
}

int ZmqMgr::UnloadMapDataToDataServer(int uin, const char *cwid)
{
	if (!use_db_map_data)
		return -10;
	if (map_author_uin == 0)
		return -100;
	
	ZMQ_PROXY_LOG("UnloadMapDataToDataServer wid=%s uin = %d", cwid, uin);
	//const char* roomid = GetClientInfoProxy()->getEnterParam("room_id");
	const char* roomid = GetUinroomid();
	miniw::unload_map_data unload_data;
	unload_data.set_roomid(roomid);
	unload_data.set_author_uin(map_author_uin);		
	std::string param2 = unload_data.SerializeAsString();

	// char c_wid[64];
	// sprintf(c_wid, "%lld", owid);	
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
		miniw::RTMySQL::UnloadMapData, uin, cwid, 0, param2, 0);
	return (0);	
}

int ZmqMgr::SaveDataToGaussRedisDataServer(miniw::RTGaussRedis type, const void* data, int datalen, long long owid, int uin, int ext)
{
	char c_wid[64];
	sprintf(c_wid, "%lld", owid);
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::GAUSSREDIS,
		type, uin, c_wid, ext, data, datalen, 0);
	return (0);
}

int ZmqMgr::SaveDataToGaussRedisDataServer(miniw::RTGaussRedis type, const void* data, int datalen, const char* cwid, int uin, int ext)
{
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::GAUSSREDIS,
		type, uin, cwid, ext, data, datalen, 0);
	return (0);
}

int ZmqMgr::GetDataFromGaussRedisDataServer(miniw::RTGaussRedis type, const std::string& key, long long owid, int uin)
{
	char c_wid[64];
	sprintf(c_wid, "%lld", owid);
	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::GAUSSREDIS,
		type, uin, c_wid, 0, key.c_str(), key.length(), 0);
	return (0);
}

const char* ZmqMgr::GetUinroomid()
{
	return uin_roomid.c_str();
}

bool ZmqMgr::IsDevelopRoom()
{
	return is_develop_room;
}

/**
 * @brief 通知私人云服配置变化, 同步配置
 * <AUTHOR>
 * @param operate_uin 操作者uin, 目前只记录
 * @param change_type 变动的参数项, 目前只记录
 */
void ZmqMgr::NotifyPersonalCloudServerConfigChange(int operate_uin, int change_type){
	auto game = GetIClientGameManagerInterface()->getICurGame();
	if (!game){
		LOG_WARNING("NotifyPersonalCloudServerConfigChange error game not load");
		return;
	}

	int public_type, can_trace, max_player;
	public_type = game->getPublicType();
	can_trace = game->getCanTrace();
	max_player = game->getMaxPlayerNum();

	GetICloudProxyPtr()->setExtraConfig("personal_room", "public_type", public_type);
	GetICloudProxyPtr()->setExtraConfig("personal_room", "can_trace", can_trace);
	GetICloudProxyPtr()->setExtraConfig("personal_room", "ud_max_player", max_player);

	LOG_INFO("NotifyPersonalCloudServerConfigChange operator=%d change_type=%d public_type=%d can_trace=%d max_player=%d pwd=%s", 
		operate_uin, change_type, public_type, can_trace, max_player, game->getHostPassword());

	miniw::personal_cloud_server_config config_pd;
	char    uin_roomid[256];
	sprintf(uin_roomid, "%s_%s", GetClientInfoProxy()->getEnterParam("account"), 
		GetClientInfoProxy()->getEnterParam("room_id"));
	config_pd.set_room_id(uin_roomid);
	config_pd.set_password(game->getHostPassword());  // 房间密码
	config_pd.set_public_type(public_type);  // 房间是否公开
	config_pd.set_can_trace(can_trace);  // 房间是否可追踪
	config_pd.set_max_player_num(max_player);  // 玩家设置的最大玩家数量
	string data = config_pd.SerializeAsString();

	DataServerClient& Client = DataServerClient::Singleton();
	Client.PushReqToQueue(miniw::DataSourceType::MYSQL, miniw::RTMySQL::SyncPersonalCloudServerConfig, operate_uin,
		GetClientInfoProxy()->getEnterParam("toloadmapid"), 0, data.c_str(), data.length(), 0);
}

void ZmqMgr::PubNsqMsg(const char *topic, long long owid, const void *data, const int datalen)
{
	char c_wid[64];
	sprintf(c_wid, "%lld", owid);
	MsgBusClient::Singleton().PushReqToQueue(miniw::DataSourceType::NSQ,
		miniw::RTNsq::NsqPub, 0, c_wid, MINIW::GetTimeStamp(), data, datalen, 0, topic);
}
void ZmqMgr::PubNsqMsg(const char *topic, const char *owid, const void *data, const int datalen)
{
	MsgBusClient::Singleton().PushReqToQueue(miniw::DataSourceType::NSQ,
		miniw::RTNsq::NsqPub, 0, owid, MINIW::GetTimeStamp(), data, datalen, 0, topic);
}

void ZmqMgr::PubPvpActivity(long long owid, const void* data, const int datalen)
{
	char c_wid[64];
	sprintf(c_wid, "%lld", owid);
	MsgBusClient::Singleton().PushReqToQueue(miniw::DataSourceType::NSQ,
		miniw::RTNsq::NsqPub, 0, c_wid, MINIW::GetTimeStamp(), data, datalen, 0, "game_event");

}

void ZmqMgr::BroadcastMsgToMapRoom(int uin, std::string msg)
{
	static std::vector<std::string> desRoomids;
	BroadcastMsgToMapRoom(uin, msg, desRoomids);
}

void ZmqMgr::BroadcastMsgToMapRoom(int uin, std::string msg, const std::vector<std::string> desRoomids)
{
	static long long wid = GetWorldManagerPtr()->getWorldId();
	static long long appid = GetClientInfoProxy()->GetServerAppID();
	static char c_wid[64] = { 0 };
	if (c_wid[0] == 0)
		sprintf(c_wid, "%lld", wid);

	MsgBusClient::Singleton().PushReqToQueue(miniw::DataSourceType::NSQ,
		miniw::RTNsq::MapBroadcast, uin, c_wid, appid, msg.data(), msg.length(), 0, NULL, desRoomids);
}

void ZmqMgr::SaveRoomPermitConfig(long long wid, const void* buf, int size)
{
	char c_wid[64];
	sprintf(c_wid, "%lld", wid);
	DataServerClient::Singleton().PushReqToQueue(miniw::DataSourceType::MYSQL,
		miniw::RTMySQL::SaveRoomPermitData, atoll(GetClientInfoProxy()->getEnterParam("account")), c_wid, 0, buf, size, 0);
	SLOG(INFO) << "save roompermit wid" << wid << " len = " << size;
}

int ZmqMgr::SavePlayerDataToKV(const void* data, int datalen, const std::string& appid, int uin)
{	
	DataServerClient& dsc = DataServerClient::Singleton();
	miniw::DataSourceType dt = miniw::DataSourceType::GAUSSREDIS;
	int st = miniw::RTGaussRedis::SavePlayerAllData;
	miniw::player_data pd;
	pd.set_data(data, datalen);
	string saveStr = pd.SerializeAsString();
	dsc.PushReqToQueue(dt, st, uin, appid.c_str(), 0, saveStr.data(), saveStr.size(), 0);
	SLOG(INFO) << "save player data to kv. appid = " << appid << "uin =" << uin << " data len = " << datalen;

	return 0;
}

int ZmqMgr::LoadPlayerDataFromKV(const std::string& appid, int uin, const PlayerBriefInfo* pbi)
{
	DataServerClient::Singleton().holdLoadPlayerBriefInfo(uin, pbi);
	
	DataServerClient& dsc = DataServerClient::Singleton();
	miniw::DataSourceType dt = miniw::DataSourceType::GAUSSREDIS;
	int st = miniw::RTGaussRedis::LoadPlayerAllData;
	dsc.PushReqToQueue(dt, st, uin, appid.c_str(), 0, "", 0);
	SLOG(INFO) << "load player data from kv. appid = " << appid << "uin = " << uin;
	
	return 0;
}

/**
 * @brief 发送活动类型消息到nsq
 * 
 * @param owid 地图ID
 * @param data 消息主体
 * @param activity_id 活动ID
 */
void ZmqMgr::PubCloudServerActivity(long long owid, const std::string &data, int activity_id)
{
	char c_wid[64];
	sprintf(c_wid, "%lld", owid);
	PubCloudServerActivity(c_wid, data, activity_id);
}

/**
 * @brief 发送活动类型消息到nsq
 * 
 * @param owid 地图ID
 * @param data 消息主体
 * @param activity_id 活动ID
 */
void ZmqMgr::PubCloudServerActivity(const char *owid, const std::string &data, int activity_id)
{
	jsonxx::Object ob;
	ob.import("activity_id", activity_id);
	ob.import("data", data);

	auto st = ob.json_nospace();
	PubNsqMsg("game_activity", owid, st.c_str(), st.length());
}


int ZmqMgr::GetBussinessid()
{
	return GetICloudProxyPtr()->GetBussinessId();
}
bool ZmqMgr::IsNeedSaveChunk()
{
#ifdef WINDOWS_SERVER
	return false;
#endif
	// if (!use_db_map_data)
	// 	return false;

    return true;
}

int ZmqMgr::SaveStarStationToDataServer(long long owid, const void *data, int datalen)
{
    if (!use_db_map_data)
        return -10;
    char c_wid[64];
    sprintf(c_wid, "%lld", owid);
    DataServerClient& Client = DataServerClient::Singleton();
    Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
        miniw::RTMySQL::SaveStarStation, 0, c_wid, 0, data, datalen, 0, NULL);
    LOG_DEBUG(__FUNCTION__);
    return (0);

}
int ZmqMgr::LoadStarStationFromDataServer(long long owid)
{
    if (!use_db_map_data)
        return -10;
    char c_wid[64];
    sprintf(c_wid, "%lld", owid);
    DataServerClient& Client = DataServerClient::Singleton();
    Client.PushReqToQueue(miniw::DataSourceType::MYSQL,
        miniw::RTMySQL::LoadStarStation, 0, c_wid, 0, NULL, 0, 0, NULL);
    LOG_DEBUG(__FUNCTION__);
    return (0);

}

void ZmqMgr::SaveLibVarStrToFile(const std::string& fileName, const std::string& packData)
{
	flatbuffers::FlatBufferBuilder builder;
	auto res = FBSave::CreateCloudLibVar(builder, builder.CreateString(packData));
	builder.Finish(res);
	GetFileManager().SaveToWritePath(fileName.c_str(), builder.GetBufferPointer(), builder.GetSize());
}

void ZmqMgr::GetApiConfig(int& appid, std::string& appkey)
{
	appid = m_ApiServerAppid;
	appkey = m_ApiServerAppKey;
}

std::string ZmqMgr::PlayerCostStaticKey()
{
	return m_PlayerCostKey;
}

void ZmqMgr::ReqestAddTeamRobot(int robotId, int teamId)
{
	if (CheckForbidJoin())
	{
		return OnAddRobotRet(false, robotId, teamId); // 禁止加入了，直接返回false
	}

	const char* aid = GetClientInfoProxy()->getEnterParam("toloadmapid");
	LOG_INFO("ReqestAddTeamRobot robotId:%d teamId:%d, roomid:%s aid:%ll", robotId, teamId, uin_roomid.c_str(), aid);
	MINIW::ScriptVM::game()->callFunction("ReqAddRobot", "iiss", robotId, teamId, uin_roomid.c_str(), aid);
}

void ZmqMgr::OnAddRobotRet(bool success, int robotId, int teamId)
{
	LOG_INFO("OnAddRobotRet success:%d robotId:%d teamId:%d", success, robotId, teamId);
	if (success)
	{
		m_StudioTeamRobot[teamId].insert(robotId);
	}
	else
	{
		m_StudioTeamRobot[teamId].erase(robotId);
		if (m_StudioTeamRobot[teamId].empty())
			m_StudioTeamRobot.erase(teamId);
	}

	//call studio
	if (auto service = MNSandbox::GetCurrentCloudServerConfigService())
	{
		service->NewRobotResp(robotId, teamId, success);
	}	
}

bool ZmqMgr::IsManualClose()
{
	// 手动拉起测试房间是 避免比rentnode关闭
	return (GetClientInfoProxy()->getEnterParam("dddd")[0]);
}

#ifdef __PC_LINUX__
extern void LuajitProfCommandImpl(bool switch_);
#endif
void ZmqMgr::LuajitProf(bool b)
{
#ifdef __PC_LINUX__
	LuajitProfCommandImpl(b);
#endif
}

void ZmqMgr::set_stop_node_heartbeat(bool b)
{
	LOG_INFO("ZmqMgr::set_stop_node_heartbeat %d", b);
	stop_node_heartbeat = b;
}
void ZmqMgr::set_transfer_all_player(bool b)
{
	LOG_INFO("ZmqMgr::set_transfer_all_player %d", b);
	transfer_all_player = b;
	static bool s_closed = false;
	if (b && !s_closed)
	{
		s_closed = true;
		TeleportPlayerAndCloseRoom(5, "房间即将关闭...");
	}
}

void ZmqMgr::RemoveTeamRobot(int robotId, MNSandbox::AutoRef<MNSandbox::LuaFunction> func)
{
	size_t count = 0;
	for (auto& ids : m_StudioTeamRobot)
	{
		count += ids.second.erase(robotId);
	}
	if (count > 0)
	{
		HeartbeatToDataServer(nullptr, nullptr, nullptr, 0);
	}
	LOG_INFO("RemoveTeamRobot count:%d robotId:%d", count, robotId);

	//CALLBACK
	if (func)
	{
		func->CallLuaFunction<bool>(count > 0);
	}
}
bool ZmqMgr::CheckPlayerInCurRoom(const char *roomid, const char *online_roomid)
{
	for (auto ite = rentnode_multi_addr_out.begin(); ite != rentnode_multi_addr_out.end(); ++ite)
	{
		std::ostringstream os;
		os << roomid << ":" << *ite;
		if (os.str() == online_roomid)
			return true;
	}
	return false;
}
void ZmqMgr::SetPersonalRoomTimeoutParam(int freeSec1, int freeSec2, int maxSec, int keepSec)
{
	personalroom_timeout_freeSec1 = freeSec1;
	personalroom_timeout_freeSec2 = freeSec2;
	personalroom_timeout_maxSec = maxSec;
	personalroom_timeout_keepSec = keepSec;
}

void ZmqMgr::OnRespCenterServer(const std::string& roomid, const std::string& ip, const std::string& port)
{
	CenterGSMgr::getInstance()->OnRespCenterServer(roomid, ip, port);
}

// 1 未连接中心服 2，当前是重新服 0 发送成功
// int ZmqMgr::SendUserMsgToCenterServer(const std::string& msg)
// {
// 	return CenterGSMgr::getInstance()->SendUserMsgToMaster(msg);
// }

int ZmqMgr::SendUserMsgToOneServer(const std::string& roomid, const std::string& msg)
{
	return SendUserMsgToServers({ roomid }, msg);
}

int ZmqMgr::SendUserMsgToServers(const std::vector<std::string>& roomlist, const std::string& msg)
{
	return CenterGSMgr::getInstance()->SentUserMsgToExternList(roomlist, msg);
}

MNSandbox::Notify<std::string, std::string>& ZmqMgr::GetNotifyRecvUserMsg()
{
	return CenterGSMgr::getInstance()->GetNotifyRecvUserMsg();
}
#endif
