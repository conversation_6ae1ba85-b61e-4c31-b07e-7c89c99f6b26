#include "Platforms/PlatformInterface.h"
#include "Common/OgreShared.h"
#include "Math/FloatConversion.h"
#include "Common/OgreStringUtil.h"
#include "Utilities/UnicodeString.h"
//#include "math/OgreVector4.h"
#include "Misc/XMLData.h"
#include "Compress/CompressSystem.h"
#include "ScriptVM/OgreScriptLuaVM.h"
#include "Platforms/PlatformDefData.h"

//#include "Utilities/Logs/LogAssert.h"

#include <algorithm>

#include "Utilities/MD5.h"
#include <sstream>

#include <iostream>
#include <string>
#include <time.h>
#include <ctime>
#include "json/jsonxx.h"
#include <File/FileManager.h>
 
#include <tchar.h>  
//#include <strsafe.h> 
//#include <winsock2.h>

#include "winSystemInfo.h"
#include <Wininet.h>
#include <Sensapi.h>
#include <Tlhelp32.h>
#include <Psapi.h>
#include <timeapi.h>
#include <commdlg.h>
#include <shellapi.h>

#pragma comment(lib, "Psapi.lib")
#pragma comment(lib, "Iphlpapi.lib")
#pragma comment(lib, "Wininet.lib")
#pragma comment (lib,"Ws2_32.lib")
#pragma comment(lib,"Wininet.lib")  
#pragma comment(lib, "Sensapi.lib")
#pragma comment(lib, "Winmm.lib")
#include "IClientInfo.h"
static MINIW::ImagePickerCallback s_imagePickerCallback = NULL;
static std::string s_imagePickerTargetPath = "";
static MINIW::CameraQRScanerCallback s_cameraQRScannerCallback = NULL;
static MINIW::ContactPersonsCallback s_contactPersonsCallback = NULL;
static MINIW::WatchADCallback s_watchADCallback = NULL;


bool operator< ( const DEVMODE& lhs, const DEVMODE& rhs )
{
	return lhs.dmPelsWidth < rhs.dmPelsWidth;
}

bool operator== ( const DEVMODE& lhs, const DEVMODE& rhs )
{
	return ( lhs.dmPelsWidth == rhs.dmPelsWidth ) && ( lhs.dmPelsHeight == rhs.dmPelsHeight );
}

using namespace Rainbow;

namespace MINIW
{
	const int MIN_WIDTH		= 800;
	const int MIN_HEIGHT	= 600;
    
	int m_nApiid = 0;
	
    static char* m_pClientVersion = NULL;
	static int m_pClientEnv = 0;
	static statisticsJson m_statisticsJson = NULL;

	static DEVMODE s_DeskMode;//��ǰ����ģʽ
	static std::vector<DEVMODE>	s_AllModes;
	static std::vector<DEVMODE>	s_FullScreenModes;
	
	struct ThreadInfo
	{
		UINT32			m_nID;
		ULARGE_INTEGER	m_nKernelTime;
		ULARGE_INTEGER	m_nUserTime;
		float			m_fCupUsageKernel;
		float			m_fCupUsageUser;
	};

	static ThreadInfo			s_threadsInfo[32];			// ���������߳̾��
	static int s_num_thread = 0;

	void GetRuntimeSysInfo(RuntimeSysInfo &info)
	{
		static UINT s_LastQueryTick = ::timeGetTime();

		UINT curtick = timeGetTime();
		UINT passtick = curtick -s_LastQueryTick;


		// ���CPU������Ŀ
		SYSTEM_INFO systemInfo;
		GetSystemInfo(&systemInfo);
		info.num_cpu = systemInfo.dwNumberOfProcessors;

		// ��ý��̵��ڴ�ռ����Ŀ
		PROCESS_MEMORY_COUNTERS psmemCounters;
		GetProcessMemoryInfo(GetCurrentProcess(), &psmemCounters, sizeof(PROCESS_MEMORY_COUNTERS));
		info.memory_usage = psmemCounters.WorkingSetSize;

		// ����߳���Ŀ����ͳ��cpuռ����
		ThreadInfo threadsInfoTmp[32];
		THREADENTRY32 te32;
		DWORD processID = GetCurrentProcessId();
		HANDLE hThreadsSnap = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, processID);
		int nNumThreadsTmp = s_num_thread;
		memcpy( &threadsInfoTmp, &s_threadsInfo, sizeof(ThreadInfo) * nNumThreadsTmp);

		s_num_thread = 0;
		te32.dwSize = sizeof(THREADENTRY32);
		if( Thread32First(hThreadsSnap, &te32) ) 
		{ 
			do 
			{ 
				if( te32.th32OwnerProcessID == processID ) 
				{ 
					FILETIME kTime, uTime, k1, k2;
					ULARGE_INTEGER tk, tu;

					HANDLE hThread = OpenThread(THREAD_QUERY_INFORMATION, false, te32.th32ThreadID);	

					GetThreadTimes(hThread, &k1, &k2, &kTime, &uTime);
					tk.LowPart = kTime.dwLowDateTime;
					tk.HighPart = kTime.dwHighDateTime;
					tu.LowPart = uTime.dwLowDateTime;
					tu.HighPart = uTime.dwHighDateTime;

					CloseHandle(hThread);

					s_threadsInfo[s_num_thread].m_nID = te32.th32ThreadID;
					s_threadsInfo[s_num_thread].m_nKernelTime = tk;
					s_threadsInfo[s_num_thread].m_nUserTime = tu;
					for( int i=0; i<nNumThreadsTmp; i++ )
					{
						if( s_threadsInfo[s_num_thread].m_nID == threadsInfoTmp[i].m_nID )
						{
							tk.QuadPart = tk.QuadPart - threadsInfoTmp[i].m_nKernelTime.QuadPart;
							tu.QuadPart = tu.QuadPart - threadsInfoTmp[i].m_nUserTime.QuadPart;
							break;
						}
					}
					s_threadsInfo[s_num_thread].m_fCupUsageKernel = 
						(tk.QuadPart / 100) / (float)passtick;
					s_threadsInfo[s_num_thread].m_fCupUsageUser = 
						(tu.QuadPart / 100) / (float)passtick;
					s_num_thread++;
				} 
			} while( Thread32Next(hThreadsSnap, &te32) ); 

			info.num_thread = s_num_thread;
			info.kernel_cpu_usage = 0.0f;
			info.user_cpu_usage = 0.0f;
			for( int i=0; i<s_num_thread; i++ )
			{
				info.kernel_cpu_usage += s_threadsInfo[i].m_fCupUsageKernel;
				info.user_cpu_usage += s_threadsInfo[i].m_fCupUsageUser;
			}
			info.kernel_cpu_usage /= info.num_cpu;
			info.user_cpu_usage /= info.num_cpu;
		} 

		CloseHandle(hThreadsSnap);
		s_LastQueryTick = curtick;
	}

	void SetClientEnv(int env)
	{
		m_pClientEnv = env;
	}
	
	int GetClientEnv()
	{
		return m_pClientEnv;
	}

	void SetStatisticsJson(statisticsJson reportjson)
	{
		m_statisticsJson = reportjson;
	}

	statisticsJson getStatisticsJson()
	{
		return m_statisticsJson;
	}

	unsigned int GetTimeStamp()
	{
		time_t t;
		time(&t);
		return (unsigned int)t;
	}

	uint64_t GetTimeStampMS()
	{
		uint64_t nTimer = 0;
		SYSTEMTIME currentTime;
		GetLocalTime(&currentTime);
		tm temptm = {
			currentTime.wSecond,
			currentTime.wMinute,
			currentTime.wHour,
			currentTime.wDay,
			currentTime.wMonth - 1,
			currentTime.wYear - 1900
		};
		nTimer = mktime(&temptm) * 1000 + currentTime.wMilliseconds;
		return nTimer;
	}

	int GetTimeZoneStamp()
	{
		TIME_ZONE_INFORMATION tzi;
		DWORD result = GetTimeZoneInformation(&tzi);
		
		if (result == TIME_ZONE_ID_INVALID) {
			return 0;
		}
		
		int offset_seconds = -(tzi.Bias * 60);
		if (result == TIME_ZONE_ID_DAYLIGHT) {
			offset_seconds -= (tzi.DaylightBias * 60);
		}
		
		return offset_seconds / 3600;
	}
	
	void SetClientVersion(char* clientVersion)
	{
		m_pClientVersion = clientVersion;
	}

	char* GetClientVersion()
	{
		return m_pClientVersion;
	}
	
	void ThreadSleep(UInt32 tick)
	{
		::Sleep(tick);
	}

	void PopMessageBox(const char *content, const char *title)
	{
		UnicodeString contentU(content), titleU(title);

		::MessageBox(NULL, contentU, titleU, MB_OK);
	}

	void GetOpenFile(const char *title, const char *format, const char* initDir, char* resultBuffer)
	{
		UnicodeString titleU(title), formatU(format), initDirU(initDir);

		//��ȡ��ǰ����Ŀ¼
		TCHAR currentDir[MAX_PATH];
		GetCurrentDirectory(MAX_PATH, currentDir);

		TCHAR szBuffer[MAX_PATH] = { 0 };
		OPENFILENAME ofn = { 0 };
		ofn.lStructSize = sizeof(ofn);
		ofn.hwndOwner = NULL;
		//ofn.lpstrFilter = "png�ļ�(*.png)\0*.png\0�����ļ�(*.*)\0*.*\0";//Ҫѡ����ļ���׺ 
		ofn.lpstrFilter = formatU.getText();//Ҫѡ����ļ���׺ 
		ofn.lpstrInitialDir = initDirU.getText();//Ĭ�ϵ��ļ�·�� 
		ofn.lpstrFile = szBuffer;//����ļ��Ļ����� 
		ofn.nMaxFile = sizeof(szBuffer) / sizeof(*szBuffer);
		ofn.nFilterIndex = 0;
		//ofn.lpstrTitle = TEXT("��ѡ��һ��ͼƬ�ļ�");//ʹ��ϵͳĬ�ϱ������ռ���  
		ofn.lpstrTitle = titleU.getText();//ʹ��ϵͳĬ�ϱ������ռ���  
		ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_EXPLORER;//��־����Ƕ�ѡҪ����OFN_ALLOWMULTISELECT
		BOOL bSel = GetOpenFileName(&ofn);

		UnicodeString retbuf(szBuffer);
		strcpy(resultBuffer, retbuf.getUtf8());

		//��ԭ��ǰ����Ŀ¼
		SetCurrentDirectory(currentDir);

		//
		//LOG_INFO("GetOpenFile %s", szBuffer);
	}

	int GetProcessUsedMemory()
	{
		HANDLE handle = GetCurrentProcess();
		PROCESS_MEMORY_COUNTERS pmc;
		pmc.cb = sizeof(PROCESS_MEMORY_COUNTERS);

		GetProcessMemoryInfo(handle, &pmc, sizeof(pmc));
		return pmc.WorkingSetSize;
	}

	void InitDisplayMode()
	{
		DISPLAY_DEVICE device;
		DEVMODE devMode;
		device.cb = sizeof(DISPLAY_DEVICE);
		int nIndex = 0;
		s_AllModes.clear();
		s_FullScreenModes.clear();
		while( EnumDisplayDevices(NULL,nIndex,&device,0) )
		{
			if( device.StateFlags & DISPLAY_DEVICE_ATTACHED_TO_DESKTOP )
			{
				EnumDisplaySettings(device.DeviceName, ENUM_CURRENT_SETTINGS, &s_DeskMode);
				int modeExist = EnumDisplaySettings(device.DeviceName, 0, &devMode);
				if(modeExist == 0)
				{
					break;
				}
				for(int i=0; modeExist; i++)
				{
					if ( devMode.dmBitsPerPel == s_DeskMode.dmBitsPerPel && 
						devMode.dmPelsHeight >= MIN_HEIGHT && devMode.dmPelsWidth >= MIN_WIDTH )//ˢ��Ƶ�ʺ���ɫ�����ͬ������һ�¾ͱ�������
					{
						if ( devMode.dmPelsHeight * s_DeskMode.dmPelsWidth==devMode.dmPelsWidth * s_DeskMode.dmPelsHeight )
						{
							s_FullScreenModes.push_back( devMode );
						}
						s_AllModes.push_back(devMode);
					}
					modeExist = EnumDisplaySettings(NULL, i, &devMode);
				}
			}
			nIndex ++;
		}
		std::sort( s_AllModes.begin(),			s_AllModes.end() );
		std::sort( s_FullScreenModes.begin(),	s_FullScreenModes.end() );
		s_AllModes.resize(std::unique(s_AllModes.begin(),s_AllModes.end()) - s_AllModes.begin() );
		s_FullScreenModes.resize(std::unique(s_FullScreenModes.begin(),s_FullScreenModes.end()) - s_FullScreenModes.begin() );
	}

	bool ChangeDisplayMode(int w, int h)
	{
		DWORD dflags = CDS_FULLSCREEN;

		for(size_t i=0; i<s_AllModes.size(); i++)
		{
			DEVMODE &mode = s_AllModes[i];
			if(mode.dmPelsWidth==w && mode.dmPelsHeight==h)
			{
				LONG modeSwitch = ChangeDisplaySettings(&mode, dflags);
				if(modeSwitch==DISP_CHANGE_SUCCESSFUL) return true;
			}
		}
		return false;
	}

	void RestoreDisplayMode()
	{
		ChangeDisplaySettings(NULL,0);
	}

	size_t GetNumDisplayMode()
	{
		return s_AllModes.size();
	}
	void GetDisplayMode(size_t i, int &w, int &h)
	{
		assert(i<s_AllModes.size());
		w = s_AllModes[i].dmPelsWidth;
		h = s_AllModes[i].dmPelsHeight;
	}

	void GetScreenMode( bool bFullScreen, int& width, int& height )
	{
		DEVMODE destDisplayMode;
		destDisplayMode.dmPelsWidth		= width;
		destDisplayMode.dmPelsHeight	= height;
		std::vector<DEVMODE>* pModes = NULL;
		if ( bFullScreen )
		{
			pModes = & s_FullScreenModes;
		}
		else
		{
			pModes = &s_AllModes;
		}

		// �ȿ��Ƿ�����ȫƥ��
		std::vector<DEVMODE>::iterator pos = std::find( pModes->begin(), pModes->end(), destDisplayMode );
		if ( pos != pModes->end() )
		{
			width	= pos->dmPelsWidth;
			height	= pos->dmPelsHeight;
			return;
		}
		
		// ��û�о�ȷƥ�䣬ʹ����ӽ�ƥ��
		pos = std::lower_bound( pModes->begin(), pModes->end(), destDisplayMode );
		if ( pos == pModes->end() )
		{
			pos = --( pModes->end() );
		}

		width	= pos->dmPelsWidth;
		height	= pos->dmPelsHeight;
	}

	bool IsSolutionSupport( int width, int height )
	{
		DEVMODE destDisplayMode;
		destDisplayMode.dmPelsWidth		= width;
		destDisplayMode.dmPelsHeight	= height;
		return std::binary_search( s_AllModes.begin(), s_AllModes.end(), destDisplayMode );
	}
	
	bool GetMachineLocation(double &longitude, double &latitude)
	{
		longitude = 0;
		latitude = 0;
#ifdef _DEBUG
		FILE* f = fopen("res/locationtest.txt", "r");
		if (f)
		{
			fscanf(f, "%lf %lf", &longitude, &latitude);

			fclose(f);

			LOG_INFO("GetMachineLocation: longitude=%lf latitude=%lf", longitude, latitude);
			return true;
		}
#endif
		return false;
	}

	void GetMobileCurLocationState()
	{
		
	}

	std::string fetchLaunchIp()
	{
		return "";
	}

	int checkFirstLaunch() {
		return 0;
	}

	long fetchFirstLaunchTimeStamp() {
		return 0;
	}

	int fetchApn() {
		return 0;
	}


	int GetNetworkState()
	{
		return 1;
	}

	bool PullTPApp(const char* appname, const char* jsonstr)
	{
		return true;
	}

	void SetScreenBrightness(float bright)
	{
	}

	//�ж���������״̬ 
	//������������¿����ж��Ƿ����ӵ�internet
	//������״̬�޷��ж��Ƿ����ӵ�internet
	//return == 0 �����쳣
	int GetNetworkCardState()
	{
		int iConState =-1;
		DWORD   conFlags;	//������ʽ    
		BOOL   bConOnline=TRUE;	//�Ƿ�����  
		//��Ӧ����ʱ 
		bConOnline = InternetGetConnectedState(&conFlags,NULL);  
		if(bConOnline)   
		{   
			if ((conFlags & INTERNET_CONNECTION_MODEM) ==INTERNET_CONNECTION_MODEM)  
			{  
				//��������
				iConState = 1;
			}  
			if ((conFlags & INTERNET_CONNECTION_LAN) ==INTERNET_CONNECTION_LAN)  
			{  
				//������
				iConState = 2;
			}  
			if ((conFlags & INTERNET_CONNECTION_PROXY) ==INTERNET_CONNECTION_PROXY)  
			{  
				//ͨ������
				iConState = 3;
			}  
			if ((conFlags & INTERNET_CONNECTION_MODEM_BUSY) ==INTERNET_CONNECTION_MODEM_BUSY)  
			{ 
				//modem��ռ��
				iConState = 4;
			}  
		}  
		else  
			//�޷����ӵ�����
			iConState = 0;

		int iAliveState = -1;
		DWORD   aliveFlags;//������ʽ  
		BOOL	bAliveOnline=TRUE;//�Ƿ�����    
		//��IsNetworkAlive��ʵʱ ��ҪSystem Event Notification����֧��  ϵͳĬ����������
		bAliveOnline=IsNetworkAlive(&aliveFlags);    
		if(bAliveOnline)//����    
		{    
			if ((aliveFlags & NETWORK_ALIVE_WAN) ==NETWORK_ALIVE_WAN)  
			{  
				//��������
				iAliveState = 1;
			}  

			if ((aliveFlags & NETWORK_ALIVE_LAN) ==NETWORK_ALIVE_LAN)  
			{  
				 //������
				iAliveState = 2;
			}  

			if ((aliveFlags & NETWORK_ALIVE_AOL) ==NETWORK_ALIVE_AOL)  
			{  
				 //���ӵ�AOL
				iAliveState = 3;
			}  
		}  
		else  
			//�޷����ӵ�����
			iAliveState = 0;

		if( iConState == 0 || iAliveState == 0)
		{
			return 0;
		}

		return 1;
	}

	void crashlyticsLog(const char* strlog)
	{
		
	}

	float GetScreenDpi()
	{
		static float dpi = -1;
		
		if (dpi == -1)
		{
			HDC screen = GetDC(0);
			int physicalSizeW = GetDeviceCaps(screen, HORZSIZE);
			int physicalSizeH = GetDeviceCaps(screen, VERTSIZE);

			int pixelsW = GetDeviceCaps(screen, HORZRES);
			int pixelsH = GetDeviceCaps(screen, VERTRES);

			float  screenSize = Sqrt((float)(physicalSizeW * physicalSizeW + physicalSizeH * physicalSizeH)) * 0.03937008f;
			float  pixel = Sqrt((float)(pixelsW * pixelsW + pixelsH * pixelsH));

			ReleaseDC(0, screen);
			dpi = pixel / screenSize;
		}

		return dpi;
	}

	unsigned long int getPthreadSelf()
	{
		return GetCurrentThreadId();
	}
	
	/*
	void GetMacString( char* szMacAdress )
	{
		sprintf(szMacAdress,"");
		PIP_ADAPTER_INFO pAdapterInfo;
		PIP_ADAPTER_INFO pAdapter = NULL;
		DWORD dwRetVal = 0;
		pAdapterInfo = (IP_ADAPTER_INFO *) malloc( sizeof(IP_ADAPTER_INFO) );
		ULONG ulOutBufLen = sizeof(IP_ADAPTER_INFO);

		if (GetAdaptersInfo( pAdapterInfo, &ulOutBufLen) == ERROR_BUFFER_OVERFLOW)
		{
			free(pAdapterInfo);
			pAdapterInfo = (IP_ADAPTER_INFO *) malloc (ulOutBufLen);
		}

		if ((dwRetVal = GetAdaptersInfo( pAdapterInfo, &ulOutBufLen)) == NO_ERROR)
		{
			pAdapter = pAdapterInfo;
			while (pAdapter)
			{
				__int64 val = (__int64)pAdapter->Address;
				if( pAdapter->AddressLength > 0 && (__int64)pAdapter->Address != 0 )
				{
					sprintf(szMacAdress,"%02x-%02x-%02x-%02x-%02x-%02x",pAdapter->Address[0],pAdapter->Address[1],pAdapter->Address[2],
						pAdapter->Address[3],pAdapter->Address[4],pAdapter->Address[5]);
					//break;
				}
				pAdapter = pAdapter->Next;
			}
		}
		free(pAdapterInfo);
	}*/





	char Account[256] = {};
	reportfun m_reportfun = NULL;;
	callfunc m_callfunc = NULL;
	//int m_nApiid = 0;
	void SetAccount(const char* account)
	{
		strcpy(Account, account);
	}

	void SetReport(reportfun report)
	{
		m_reportfun = report;
	}

	void SetCallFunc(callfunc callback)
	{
		m_callfunc = callback;
	}

	void InvokeCallFunc(const char* funcname, const char* content)
	{
		if (m_callfunc) m_callfunc(funcname, content);
	}

    void developerCertificationToMinibox(const char* str)
    {
        
    }
    
	std::string UrlEncode(const std::string& szToEncode)
	{
		std::string src = szToEncode;
		char hex[] = "0123456789ABCDEF";
		std::string dst;
 
		for (size_t i = 0; i < src.size(); ++i)
		{
			unsigned char cc = src[i];
			if (isascii(cc))
			{
				if (cc == ' ')
				{
					dst += "%20";
				}
				else
					dst += cc;
			}
			else
			{
				unsigned char c = static_cast<unsigned char>(src[i]);
				dst += '%';
				dst += hex[c / 16];
				dst += hex[c % 16];
			}
		}
		return dst;
	}


	static size_t  if_report = 0;  //�Ƿ��ϱ�����
	void OnStatisticsGameEvent(const char *event, const char *paramsName1, const char *params1, const char *paramsName2, const char *params2, const char *paramsName3, const char *params3)
	{
		if (Rainbow::GetIClientInfo().GetAppId() == 59) { return; }//�����治��Ҫ�ϱ�

		if (if_report == 0) {
			if_report = time(0) % 100 + 1;
		}
		else if ( if_report == 99 ) {
			//�����ϱ� 1%����
		}
		else{
			return;   //���ϱ�
		}

		paramsName1 = paramsName1 ? paramsName1:"";
		params1 = 	params1 ? params1:"";
		paramsName2 = 	paramsName2 ? paramsName2:"";
		params2 = 	params2 ? params2:"";
		paramsName3 = 	paramsName3 ? paramsName3:"";
		params3 = 	params3 ? params3:"";

		LOG_INFO("Statistics: %s, param1:%s=%s, param2:%s=%s, param3:%s=%s", event, paramsName1, params1, paramsName2, params2, paramsName3, params3);
		std::string event_str = UrlEncode(event);
		std::string paramsName1_str = UrlEncode(paramsName1);
		std::string params1_str = UrlEncode(params1); 
		std::string paramsName2_str = UrlEncode(paramsName2);
		std::string params2_str = UrlEncode(params2); 
		std::string paramsName3_str = UrlEncode(paramsName3);
		std::string params3_str = UrlEncode(params3);
		std::string Account_str = UrlEncode(Account);
		//����MD5
		char szReportStrMd5[512];
		memset(szReportStrMd5,0,512);
		sprintf(szReportStrMd5,"%s%s%s%s%s%s%s%s%s"
			,"reportminidata", event, paramsName1, params1, paramsName2,
					params2, paramsName3, params3, Account);
		Rainbow::Md5Context md5;
		char hex[33];
		md5.begin();
		md5.append((const UInt8 *)szReportStrMd5, strlen(szReportStrMd5));
		md5.getMD5Base16Str(hex);

		char szReportStr[512];
		memset(szReportStr,0,512);
		sprintf(szReportStr,"http://139.199.5.123/takling_monitor.php?reportevent=%s&paramsName1=%s&paramsvalue1=%s&paramsName2=%s&paramsvalue2=%s&paramsName3=%s&paramsvalue3=%s&account=%s&sign=%s&apiid=%d"
			, event_str.c_str(), paramsName1_str.c_str(), params1_str.c_str(), paramsName2_str.c_str(),
					params2_str.c_str(), paramsName3_str.c_str(), params3_str.c_str(), Account_str.c_str(), hex, Rainbow::GetIClientInfo().GetAppId());

		/*
		HINTERNET it = InternetOpen(NULL,INTERNET_OPEN_TYPE_PRECONFIG,NULL,NULL,NULL);
		if( it != NULL )
		{
			HINTERNET it2 = InternetOpenUrl( it,szReportStr,NULL,0,INTERNET_FLAG_NO_CACHE_WRITE,NULL );
			InternetCloseHandle(it2);
		}
		InternetCloseHandle(it);
		 */
		if(m_reportfun)
			m_reportfun(szReportStr);
		else
		 	{
			   	HINTERNET it = InternetOpen(NULL,INTERNET_OPEN_TYPE_PRECONFIG,NULL,NULL,NULL);
				if( it != NULL )
				{
					UnicodeString urepstr(szReportStr);
					HINTERNET it2 = InternetOpenUrl( it,urepstr.getText(),NULL,0,INTERNET_FLAG_NO_CACHE_WRITE,NULL );
					InternetCloseHandle(it2);
				}
				InternetCloseHandle(it);
			}
	}
	//static size_t  if_AppsFlyer_report = 0;  //�Ƿ��ϱ�����
	void OnAppsFlyerStatisticsGameEvent(const char *event, const char *paramsName1 /*= ""*/, const char *params1 /*= ""*/, const char *paramsName2 /*= ""*/, const char *params2 /*= ""*/, const char *paramsName3 /*= ""*/, const char *params3 /*= ""*/)
	{
		//if (if_AppsFlyer_report == 0) {
		//	if_AppsFlyer_report = time(0) % 100 + 1;
		//}
		//else if (if_AppsFlyer_report == 99) {
		//	//�����ϱ� 1%����
		//}
		//else {
		//	return;   //���ϱ�
		//}

		//paramsName1 = paramsName1 ? paramsName1 : "";
		//params1 = params1 ? params1 : "";
		//paramsName2 = paramsName2 ? paramsName2 : "";
		//params2 = params2 ? params2 : "";
		//paramsName3 = paramsName3 ? paramsName3 : "";
		//params3 = params3 ? params3 : "";

		//LOG_INFO("Statistics: %s, param1:%s=%s, param2:%s=%s, param3:%s=%s", event, paramsName1, params1, paramsName2, params2, paramsName3, params3);
		//std::string event_str = UrlEncode(event);
		//std::string paramsName1_str = UrlEncode(paramsName1);
		//std::string params1_str = UrlEncode(params1);
		//std::string paramsName2_str = UrlEncode(paramsName2);
		//std::string params2_str = UrlEncode(params2);
		//std::string paramsName3_str = UrlEncode(paramsName3);
		//std::string params3_str = UrlEncode(params3);
		//std::string Account_str = UrlEncode(Account);
		////����MD5
		//char szReportStrMd5[512];
		//memset(szReportStrMd5, 0, 512);
		//sprintf(szReportStrMd5, "%s%s%s%s%s%s%s%s%s"
		//	, "reportminidata", event, paramsName1, params1, paramsName2,
		//	params2, paramsName3, params3, Account);
		//MINIW::Md5Context md5;
		//char hex[33];
		//md5.begin();
		//md5.append((const UInt8 *)szReportStrMd5, strlen(szReportStrMd5));
		//md5.getMD5Base16Str(hex);

		//char szReportStr[512];
		//memset(szReportStr, 0, 512);
		//sprintf(szReportStr, "http://139.199.5.123/takling_monitor.php?reportevent=%s&paramsName1=%s&paramsvalue1=%s&paramsName2=%s&paramsvalue2=%s&paramsName3=%s&paramsvalue3=%s&account=%s&sign=%s&apiid=%d"
		//	, event_str.c_str(), paramsName1_str.c_str(), params1_str.c_str(), paramsName2_str.c_str(),
		//	params2_str.c_str(), paramsName3_str.c_str(), params3_str.c_str(), Account_str.c_str(), hex, GameApiId());

		///*
		//HINTERNET it = InternetOpen(NULL,INTERNET_OPEN_TYPE_PRECONFIG,NULL,NULL,NULL);
		//if( it != NULL )
		//{
		//HINTERNET it2 = InternetOpenUrl( it,szReportStr,NULL,0,INTERNET_FLAG_NO_CACHE_WRITE,NULL );
		//InternetCloseHandle(it2);
		//}
		//InternetCloseHandle(it);
		//*/
		//if (m_reportfun)
		//	m_reportfun(szReportStr);
		//else
		//{
		//	HINTERNET it = InternetOpen(NULL, INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, NULL);
		//	if (it != NULL)
		//	{
		//		HINTERNET it2 = InternetOpenUrl(it, szReportStr, NULL, 0, INTERNET_FLAG_NO_CACHE_WRITE, NULL);
		//		InternetCloseHandle(it2);
		//	}
		//	InternetCloseHandle(it);
		//}
	}

	void FirebaseAnalyticsEvent(const char *event, const char *paramJson)
	{

	}

	void OnStatisticsGameChooseRole(const char *roleName, const char *nickName, int uin)
	{
		OnStatisticsGameEvent("create_actor", "roleName", roleName);
	}

	void OnStatisticsGameBuyRole(char *roleName)
	{
		OnStatisticsGameEvent("buy_actor", "roleName", roleName);
	}

	void OnStatisticsGameRewardMiniCoin(int num, char *reason)
	{
		char numstr[64];
		sprintf(numstr, "%d", num);
	    OnStatisticsGameEvent("reward_mini_coin", "num", numstr, "reason", reason);
	}

	void OnStatisticsGamePurchaseMiniCoin(const char *name, int num, float price)
	{
		char numstr[64];
		sprintf(numstr, "%d", num);

   		char numprice[64];
		sprintf(numprice, "%f", price);

	    OnStatisticsGameEvent("purchase_mini_coin", "name", name, "num", numstr, "price", numprice);
	}

	void OnStatisticsOnChargeRequest(const char *sid, const char *productname, const char *paymentType, float price, int coinnum)
	{

	}

	void OnStatisticsOnChargeSuccess(const char *sid)
	{

	}

	void OnClickCopy(const char* content)
	{
		if(!content)
			return;
		std::wstring wstr = Rainbow::CodeUtil::UTF8ToUnicode(content);
		if (!::OpenClipboard(NULL)) return;
		::EmptyClipboard();
		//�õ������ڴ�	
		HGLOBAL hGlobal = GlobalAlloc(GHND | GMEM_SHARE,sizeof(wchar_t)* (wstr.size()+1));
		if (!hGlobal) return;

		//��乲���ڴ�
		LPVOID pGlobal = GlobalLock(hGlobal);
		wcscpy((wchar_t*)pGlobal, wstr.c_str());
		GlobalUnlock(hGlobal);

//#ifdef _UNICODE
//		::SetClipboardData(CF_UNICODETEXT, hGlobal);
//#else
		::SetClipboardData(CF_UNICODETEXT, hGlobal);
//#endif
		::CloseClipboard();
	}

	std::string OnGetClipBoard()
	{
		if (::OpenClipboard(NULL))
		{
			HANDLE handle = ::GetClipboardData(CF_UNICODETEXT);
			::CloseClipboard();
			if (handle)
			{
				std::string  pasteTxt(StringUtil::UnicodeToUTF8((const wchar_t*)handle));
				return pasteTxt;
			}
		}
		return "";
	}
	
	void OnSdkLogin()
	{
	}

	void OnSdkSwitch()
	{

	}

	void OnSdkLogout()
	{
	}

	void OnSdkForum()
	{
	}

	void OnSdkGameCenter()
	{
	}

	void OnSdkAccountBinding(int type)
	{
	}

	void OnSdkLogin(int type)
	{

	}

	bool OnOpenMiniProgram()
	{
		return false;
	}

	bool OnOpenMiniProgramWithType(int type)
	{
		return false;
	}

	void OnSdkRealNameAuth()
	{
	}

	int OnSetGameEnv()
	{
		int value = 1;
		LCID localeID = GetUserDefaultLCID();  
		unsigned short lang = localeID & 0xFF;  

		switch(lang)  
		{  
		case LANG_CHINESE_SIMPLIFIED:  
			value = 0;
			break;  
		case LANG_ENGLISH:  
			value = 1;
			break;  
		case LANG_CHINESE_TRADITIONAL:
			value = 2;
			break;
		case LANG_THAI:
			value = 3;
			break;
		case LANG_SPANISH:
			value = 4;
			break;
		case LANG_PORTUGUESE:
			value = 5;
			break;
		case LANG_FRENCH:
			value = 6;
			break;
		case LANG_JAPANESE:
			value = 7;
			break;
		case LANG_ARABIC://��ǰ�������﷭�������ʱʹ��Ӣ�Ĵ���
			value = 1;
			break;
		case LANG_KOREAN:
			value = 9;
			break;
		case LANG_VIETNAMESE:
			value = 10;
			break;
		case LANG_RUSSIAN:
			value = 11;
			break;
		case LANG_TURKISH:
			value = 12;
			break;
		case LANG_ITALIAN:
			value = 13;
			break;
		case LANG_GERMAN:
			value = 14;
			break;
		case LANG_INDONESIAN:
			value = 15;
			break;
		default:  
			value = 1;
			break;  
		}  
		return value;
	}

	int OnReqSdkAD(const char *type, int adid, WatchADCallback callback,int adindex, int rewardValue)
	{
		return 0;
	}

	void OnRespWatchAD(int result)
	{

	}

	void OnPlayAdSuccessCallback(int positionId)
	{
	}

	void OnRequestReview()
	{

	}

	bool OnInitAdvertisementsSDK(int adid)
	{
		return true;
	}

	bool OnAdvertisementsLoadStatus(int adid, int position)
	{
		return true;
	}

	void OnLoadSdkAD(int adid, int position)
	{
		
	}

	std::string OnGetSdkAdvertisementsInfo(int platformId, int positionId)
	{
		return "";
	}

	void OnSdkAdvertisementsShow(int platformId, int positionId)
	{
		
	}

	void OnSdkAdvertisementsOnClick(int platformId, int positionId)
	{

	}

	void OnScreenCaptureCallback(const char * snapshotPath)
	{
		
	}

	void OnSetSdkRoleInfo(const char *rolename, const char *type, int uin, int coinnum)
	{

	}

	void OnSetSdkFloatMenu(int type)
	{
	}

	bool IsSdkToStartGame()
	{
		return false;
	}

	void removeSplashView()
	{
	}
	
    void GameExit(bool restart, const char* pidfile)
	{
#ifdef _WIN32
		PostQuitMessage(0);	
#endif
	}

	void SavevideoWithphoto(const char *imgpath)
	{
	}

	void GameVibrate(int val)
	{
	}

	void GameVibrateWithTimeAndAmplitude(int time, int amplitude)
	{
	}

	void StopGameVibrate()
	{
	}

	std::string GetSchemeJson()
	{
		std::string schemejson = "";
		//schemejson = "{\"type\":\"_m\"}";
		//schemejson = "{\"type\":\"_m\", \"luaString\"=\"MiniLobbyFrameCenterLocalMap_OnClick();\"}";
		//schemejson = "{\"type\":\"_m\", \"luaString\"=\"AREnginePresenter:displayShowHuaweiARTestFrame();\"}";
		//schemejson = "{\"type\":\"_m\", \"luaString\"=\"threadpool:work(function()OpenNewPlayerCenter(204914649);end)\"}";
		return schemejson;
	}
	std::string GetCountryFromIpAddress(const char* ipAddress)
	{
		return "";
//#define B2IL(b) (((b)[0] & 0xFF) | (((b)[1] << 8) & 0xFF00) | (((b)[2] << 16) & 0xFF0000) | (((b)[3] << 24) & 0xFF000000))
//#define B2IU(b) (((b)[3] & 0xFF) | (((b)[2] << 8) & 0xFF00) | (((b)[1] << 16) & 0xFF0000) | (((b)[0] << 24) & 0xFF000000))
//
//		char result[256] = { 0 };
//
//		IPIP ipip;
//		TCHAR szPath[MAX_PATH + 1] = { 0 };
//		GetModuleFileName(NULL, szPath, MAX_PATH);
//		PathRemoveFileSpec(szPath);
//		PathAppend(szPath, _T("17monipdb.dat"));
//		FILE* file = _tfopen(szPath, _T("rb"));
//		if (file == NULL)
//		{
//			return "";
//		}
//		fseek(file, 0, SEEK_END);
//		long size = ftell(file);
//		fseek(file, 0, SEEK_SET);
//		if (size < 4) //����С��4���ļ�����Ч�ļ�
//		{
//			fclose(file);
//			return "";
//		}
//
//		ipip.data = new unsigned char[size];
//		memset(ipip.data, 0, size * sizeof(unsigned char));
//		size_t r = fread((void*)ipip.data, sizeof(unsigned char), (size_t)size, file);
//		fclose(file);
//		if (r < (size_t)size)
//		{
//			OGRE_DELETE_ARRAY(ipip.data);
//			return "";
//		}
//
//		unsigned int length = B2IU(ipip.data);
//		if (length > (unsigned int)(size - 4) || length < 1024)
//		{
//			OGRE_DELETE_ARRAY(ipip.data);
//			return "";
//		}
//
//		ipip.index = new unsigned char[length];
//		memset(ipip.index, 0, sizeof(unsigned char) * length);
//		memcpy(ipip.index, ipip.data + 4, length);
//		ipip.offset = length;
//		ipip.flag = new unsigned int[256];
//		memset(ipip.flag, 0, 256 * sizeof(unsigned int));
//		memcpy((unsigned char*)ipip.flag, ipip.index, 256 * sizeof(unsigned int));
//
//		unsigned int ips[4] = { 0 };
//		int num = sscanf(ipAddress, "%u.%u.%u.%u", &ips[0], &ips[1], &ips[2], &ips[3]);
//		if (num == 4)
//		{
//			unsigned char ip_prefix_value = ips[0];
//			unsigned int ip2long_value = B2IU(ips);
//			unsigned int start = ipip.flag[ip_prefix_value];
//			unsigned int max_comp_len = ipip.offset - 1028;
//			unsigned int index_offset = 0;
//			unsigned int index_length = 0;
//			for (start = start * 8 + 1024; start < max_comp_len; start += 8)
//			{
//				if (B2IU(ipip.index + start) >= ip2long_value)
//				{
//					index_offset = B2IL(ipip.index + start + 4) & 0x00FFFFFF;
//					index_length = ipip.index[start + 7];
//					break;
//				}
//			}
//
//			if (ipip.offset + index_offset - 1024 + index_length >= (unsigned int)size || index_length == 0)
//			{
//				OGRE_DELETE_ARRAY(ipip.flag);
//				OGRE_DELETE_ARRAY(ipip.index);
//				OGRE_DELETE_ARRAY(ipip.data);
//				return "";
//			}
//
//			memcpy(result, ipip.data + ipip.offset + index_offset - 1024, index_length);
//			result[index_length] = '\0';
//		}
//
//		std::string countryStr = result;
//		countryStr.erase(std::remove_if(countryStr.begin(), countryStr.end(), [](char c)
//			{
//				return std::isspace(static_cast<unsigned char>(c));
//			}), countryStr.end());
//		OGRE_DELETE_ARRAY(ipip.flag);
//		OGRE_DELETE_ARRAY(ipip.index);
//		OGRE_DELETE_ARRAY(ipip.data);
//
//		return countryStr;
	}

	int ShowImagePickerEx(std::string path, int type, bool crop/* =true */, int x/* =1280 */, int y/* =1280 */)
	{
		return -1;
		//return ImagePickerInterface::getInstance()->showImagePicker(path, type, crop, x, y);
	}

	std::string ShowAudioPicker(std::string path, int type)
	{
		return "";
		//std::string fileName;
		//if (!onShowAudioPicker(path, type, fileName))
		//{
		//	return "";
		//}

		//return fileName;
	}

	bool CheckAppExist(const char* pkgname)
	{
		return true;
	}
	bool CheckAppInstall(const char* platformName)
	{
		return true;
	}
    void OpenSchemes(const char *url){
       
    }

	bool HotfixCopyFile(const char *fromPath, const char *toPath)
	{
		return true;
	}

	int GameHasTPPay()
	{
		return 1;
	}

	void GamePay(const char *productName, float amount, const char *productId, int payType, int orderId, const char *sid/* ="" */)
	{
	}

	void SetPayExtendParams(int i, char *buf, int bufsize)
	{
	}

	void GameMoreGame()
	{
	}

	void GameStartUpdate()
	{
	}

	void GameSetAccount(int uin, const char* nickname)
	{
	}

	void SetCrashReportUserId(const char* userId)
	{

	}

	bool HasBuiltWithARM64()
	{
		return false;
	}

	bool CanShowARCameraBackground()
	{
		return true;
	}

	void TakeARAvatar(const char* str, const int i, const bool b)
	{

	}
  
    bool ShowCameraQRScannerforARSkin()
    {
        return false;
    }
    
	void prepareARCameraBackground()
	{

	}

	void showARCameraBackground()
	{

	}
	
	void hideARCameraBackground()
	{

	}

	void GameDnsIps(const char *domain)
	{
	}

    std::string GetCountry()
    {
        return "";
    }

	std::string GetOperatorAndNetworkType()
	{
		return "{ operator=OverseasPC,networktype=PC}";
	}

	typedef ULONG IPAddr;       // IP��ַ��An IP address.

	typedef struct ip_option_information 
	{
		UCHAR   Ttl;                // Time To Live ����ʱ��
		UCHAR   Tos;                // Type Of Service ��������
		UCHAR   Flags;              // IP header flags ipͷ��־
									//IP_REVERSE(ip���ݰ�������ip·��)
									//IP_FLAG_DF(�������ݰ���ѹ)
		UCHAR   OptionsSize;        // Size in bytes of options data ���ݴ�С
		UCHAR * OptionsData;        // Pointer to options data ����ָ��
	} IP_OPTION_INFORMATION, *PIP_OPTION_INFORMATION;

	typedef struct icmp_echo_reply {
		IPAddr  Address;            // Replying address ���ڻظ���IP��ַ
		ULONG   Status;             // Reply IP_STATUS �ظ���״̬
		ULONG   RoundTripTime;      // RTT in milliseconds(����ʱ��)
		USHORT  DataSize;           // Reply data size in bytes �ظ����ݴ�С
		USHORT  Reserved;           // Reserved for system use ����
		PVOID   Data;               // Pointer to the reply data �ָ����ݵ�ָ��
		struct ip_option_information Options; // Reply options �ظ�ѡ��
	} ICMP_ECHO_REPLY, *PICMP_ECHO_REPLY;

	//��������򿪸�ICMP Echo ������ʹ�õľ��;
	typedef HANDLE(WINAPI *lpIcmpCreateFile)(VOID);
	//��������ر���IcmpCreateFile �򿪵ľ��;
	typedef BOOL(WINAPI *lpIcmpCloseHandle)(HANDLE IcmpHandle);
	//�����������Echo ���󲢵ȴ��ظ���ʱ��
	typedef DWORD(WINAPI *lpIcmpSendEcho)(HANDLE IcmpHandle,  // IcmpCreateFile �򿪵ľ��
		IPAddr DestinationAddress, //Echo�����Ŀ�ĵ�ַ
		LPVOID RequestData, //��������buffer
		WORD RequestSize, //�������ݳ���
		PIP_OPTION_INFORMATION RequestOptions, // IP_OPTION_INFORMATION ָ��
		LPVOID ReplyBuffer,  //���ջظ�buffer
		DWORD ReplySize,   //���ջظ�buffer��С
		DWORD Timeout //�ȴ���ʱ
		);

	///////////////////////////////////////////////////////////////////////////////////////////

	const int DEF_MAX_HOP = 30;		//�����վ��
	const int DATA_SIZE = 32;		//ICMP�������ֶδ�С
	const DWORD TIMEOUT = 3000;		//��ʱʱ�䣬��λms
	struct TraceRouteNodeInfo
	{
		int			 NodeID;
		std::string  Address;            // ����·�ɵ�IP��ַ
		std::string  RoundTripTime;      // RTT in milliseconds(����ʱ��)
	};

	std::string GetTraceRouteInfo(const char* ip)
	{
		
		return "";
	}

	std::string GetAccount()
    {
        return "";
    }
    std::string GetDeviceToken()
    {
        return "";
    }

    
    void SaveAccount(const char *jsonChar)
    {
        
    }
	void Logout() {

	}
	std::string GetSHA512String(const char *str) {
		return "";
	}
	std::string GetShouQParams() {
		return "";
	}
	std::string GetDeviceModel()
	{
		return "";
	}

	std::string GetMobilePhoneInfo()
	{
		return "";
	}

	void ScanImage(const char * imagePath)
	{
	}

	void WindowBrowserOpenWebpage(const char* url, int left, int top, int width, int height)
	{

	}

	void WindowBrowserCloseWebpage()
	{

	}

	void WindowBrowserShowWebpage()
	{

	}

	void WindowBrowserHideWebpage()
	{

	}

	void OpenWebView(const char* url, int type, const char* extend)
    {
        
    }

	void BrowserShowWebpage(const char* url, int type)
	{
		/*
		std::string cmd = "explorer \"" + std::string(url) + "\"";
		LOG_INFO("open url=[%s]", cmd.c_str());									  
		system(cmd.c_str());
		*/
		UnicodeString urlU(url);
		ShellExecute(NULL, __TEXT("open"), urlU, NULL, NULL, SW_SHOWNORMAL);
	}

	void StartOnlineShare(const char *jsonStr, const char *imgpath, const char *url, const char* title, const char* content)
	{

	}

	void StartMiniwShare(const char *platformName, const char *imgpath, const char *url, const char* title, const char* content)
	{
	}

	void ShareToQQ(const char *imgpath, const char *url, const char *title, const char *text)
	{
	}

	bool OpenQQBuLuo()
	{
		return false;
	}

	bool OpenQQVip(int type, int months)
	{
		return false;
	}

	std::string GetQQUserInfo()
	{
		return "";
	}

	void AddQQFriend(const char *openid, const char *label, const char *message)
	{
	}

	bool CheckQQLogin(bool dologin)
	{
		return false;
	}

	bool IsAppExist(const char *name)
	{
		UnicodeString nameU(name);

		PROCESSENTRY32 pe;
		pe.dwSize = sizeof(PROCESSENTRY32);

		HANDLE hSnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
		if (INVALID_HANDLE_VALUE == hSnap)
		{
			DWORD error = GetLastError();
			LOG_INFO("===========Error[%ld]===============", error);
			//MessageBox(NULL, __TEXT("Init Process Failed"), __TEXT("Error"), MB_ICONERROR);
			CloseHandle(hSnap);
			return FALSE;
		}
		if (Process32First(hSnap, &pe))
		{
			do
			{
				if (lstrcmpi(nameU, pe.szExeFile) == 0)
				{
					CloseHandle(hSnap);
					return TRUE;
				}
			} while (Process32Next(hSnap, &pe));
		}
		CloseHandle(hSnap);

		return FALSE; //���඼����FALSE
	}
	//����ʱ����Ƿ�װAPP
	bool IsAppInstall(const char *platformName)
	{
		return false;
	}

	bool ShowImagePicker(const char* targetPath, ImagePickerCallback callback, int type, bool crop, int x, int y)
	{
		return false;
	}
	void onImagePicked(int err)
	{
		return;
	}

	void QueryContactPersons(ContactPersonsCallback callback)
	{
        s_contactPersonsCallback = callback;
        OnContactPersons(0);
	}
    void OnContactPersons(int num)
    {
        s_contactPersonsCallback(num);
    }
	ContactPerson GetContactPerson(int index)
	{
		ContactPerson cp;
		return cp;
	}

	bool ShowCameraQRScanner(CameraQRScanerCallback callback)
	{
		return false;
	}
	void onCameraQRScanned(int result, const std::string& scaned_string)
	{
	}

	bool SendTextMessage(const std::string& phoneNumber, const std::string& message)
	{
		return false;
	}

	bool CheckHasPermission(DevicePermission perm)
	{
		return true;
	}
	
	void RequestPermission(DevicePermission perm)
	{
	}
	
	void RequestPermissionNoUI(DevicePermission perm)
	{
	}

	void OpenSystemSetting()
	{
	}

	int GetSpecialReviewMode()
	{
		return 0;
	}

	void CheckSupportWXGameLive(int uin)
	{

	}

	void LoadWXGameLiveView()
	{

	}
 
	int getSoundState()
	{
		return 1;
	}
	void setSoundState(int state)
	{
		
	}

	std::string GetIDFA()
	{
		return "";
	}
	
	std::string MINIW::GetIDFV()
	{
		return "";
	}
	
	std::string GetIDFVInfo()
	{
		return "";
	}
	
	void OpenAdTrackingSetting()
	{

	}

	int PatchVersion()
	{
		return 0;
	}
	
	void JumpPolicyPage(const char*type)
	{
	}
	
	void ShowPrivacyUpdateDialog(const char* version)
	{
	}

	std::string GetFlyerUID()
	{
		return "";
	}

	std::string GetDeviceStorageInfo()
	{
		return "";
	}
	std::string GetAdCampaignInfo()
	{
		return "";
	}
	std::string GetDeviceRegisterInfo()
	{
		return "";
	}

	void OnEnterGameCallback(int uin, bool isAdult)
	{
	}
	void SetCrashUserValue(const char* key, const char* value)
	{
	}

	std::string CallNativeFeatureQuery(int type, const char* params)
	{
		return "";
	}

	bool CallNativeView(int type, const char* params)
	{
		return false;
	}

	bool CallNativeFeature(int type, const char* params)
	{
		return false;
	}

	void setUserDataDir(const char* datas)
	{
	}

    std::string getUserManualData()
	{
		return "";
	}

	void saveKVForSP(const char* key, const char* value)
	{
	}

	std::string getValueForSP(const char* key)
	{
		return "";
	}

	void responseLuaWithCallback(const char *funcName, const char *sessionId, const char *responseJson)
	{
	}

    void setCrashTag(const char *tagKey, const char *tagValue)
	{

	}

    void rmCrashTag(const char *tagKey)
	{

	}

	std::string CallNativeFileSelector(const char* params)
	{
		static bool isopen = false;
		if (isopen)
			return "";
		isopen = true;

		if (params == nullptr) return "";

		jsonxx::Object jsonobj;
		jsonobj.parse(params);
		std::string filterString = "All Files(*.*)\0 *.*\0\0";
		// �Զ���
		if(jsonobj.has<jsonxx::String>("file_exts"))
		{
			filterString = jsonobj.get<jsonxx::String>("file_exts");
		}

		TCHAR   szFilename[MAX_PATH] = TEXT("");
		BOOL   bResult = FALSE;
		DWORD  dwError = NOERROR;
		OPENFILENAME  ofn = { 0 };

		ofn.lStructSize = sizeof(OPENFILENAME);
		ofn.lpstrFilter = __TEXT(filterString.c_str(););

		///�ļ�����szFilename���棻
		ofn.lpstrFile = szFilename;
		ofn.nMaxFile = MAX_PATH;
		ofn.Flags = OFN_EXPLORER |
			OFN_ENABLEHOOK |
			OFN_HIDEREADONLY |
			OFN_NOCHANGEDIR |
			OFN_PATHMUSTEXIST;
		bResult = GetOpenFileName(&ofn);
		if (bResult == FALSE)
		{
			isopen = false;
			dwError = CommDlgExtendedError();
			return "";
		}

		isopen = false;
		int file_size = GetFileManager().GetFullPathFileSize(szFilename);

		jsonxx::Object packedJsonobj;
		packedJsonobj.import("file_path", szFilename);
		packedJsonobj.import("file_size", file_size);
		return packedJsonobj.json();
	}

	void CallNativeFileCopy(const char* params)
	{
		if (params == nullptr) return;

		jsonxx::Object jsonobj;
		jsonobj.parse(params);
		// �Զ���
		if (jsonobj.has<jsonxx::String>("src_file_path") && jsonobj.has<jsonxx::String>("dst_file_path"))
		{
			std::string src_file_path = jsonobj.get<jsonxx::String>("src_file_path");
			std::string dst_file_path = jsonobj.get<jsonxx::String>("dst_file_path");
			core::string tempPath = GetFileManager().GetWritePathRoot();
			tempPath += dst_file_path;

			UnicodeString stdioSrc(src_file_path.c_str());
			UnicodeString stdioU(tempPath.c_str());
			if (!CopyFile(stdioSrc, stdioU, FALSE))
			{ 
				// ��ȡ�������
				DWORD errorCode = GetLastError();
				LOG_INFO("errorCode: %d", errorCode);
			}
		}
	}
}
