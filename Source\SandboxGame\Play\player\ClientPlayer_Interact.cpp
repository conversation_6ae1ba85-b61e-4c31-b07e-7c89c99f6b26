#include "ClientPlayer.h"
#include "DefManagerProxy.h"
#include <sstream>
#include "ActorHorse.h"
#include "backpack.h"
#include "world.h"
#include "MpActorManager.h"
#include "GameCamera.h"
#include "MpActorTrackerEntry.h"
#include "SandBoxManager.h"
#include "ClientActorProjectile.h"
#include "PlayerControl.h"
#include "ActorBoss.h"
#include "PlayerLocoMotion.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "PlayerAttrib.h"
#include "SandboxCoreDriver.h"
#include "ClientActorManager.h"
#include "BlockMaterialMgr.h"
#include "VehicleMgr.h"
#include "CustomModelPacking.h"
#include "BlockVillageTotem.h"
#include "container_buildblueprint.h"
#include "BlockVillagerFlag.h"
#include "ProjectileFactory.h"
#include "VehicleWorld.h"
#ifdef IWORLD_SERVER_BUILD	
#include "ICloudProxy.h"
#endif
#include "ClientItem.h"
#include "IClientGameManagerInterface.h"
#include "EventDefine.h"
#include "GameNetManager.h"
#include "ActorMoonMount.h"
#include "ActorPumpkinHorse.h"
#include "ActorDouDuMount.h"
#include "ActorDragonMount.h"

#include "AttackingTargetComponent.h"
#include "GunUseComponent.h"
#include "OgreTimer.h"
#include "PermitsDef.h"
#include "PlayerStateController.h"
#include "EatState.h"
#include "DrinkWaterState.h"
#include "RiddenComponent.h"
#include "CarryComponent.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "ParticlesComponent.h"
#include "AttackedComponent.h"
#include "FireBurnComponent.h"
#include "DirectionByDirComponent.h"
//#include "DirectionByDirComponent.h"
#include "CameraModel.h"
#include "OgreTimer.h"
#include "ObserverEventManager.h"
#include "GameInfoProxy.h"
#include "CommonUtil.h"
#include "LuaInterfaceProxy.h"
#include "OgreUtils.h"
#include "PlayerCheat.h"
#include "CameraModel.h"
#include "LightningChainComponent.h"
#include "container_solidsand.h"
#include "ClientActorCoconut.h"
#include "WeaponSkinMgr.h"
#include "ClientActorFuncWrapper.h"
#include "BlockVillageTotemIce.h"
#include "BlockVillagerFlagBuilding.h"
#include "PlotComponent.h"
#include "special_blockid.h"
#include "EffectManager.h"
#include "ActorVehicleAssemble.h"
#include "ClientInfoProxy.h"
#include "OgreModel.h"
#include "OgreEntity.h"
#include "SandboxRunService.h"
#include "chunk.h"
#include "container_deathJar.h"
#include "CustomGunUseComponent.h"
#include "SandboxGameDef.h"
#include "GunGridDataComponent.h"
#include "GunSmithMgr.h"
#include "ScriptComponent.h"
#include "BlockTerritory.h"
#include "IMiniDeveloperProxy.h"
#include "PermitsSubSystem.h"
#include "BlockWaterStorage.h"
#include "Utils/thinkingdata/GameAnalytics.h"
#include "container_sandboxGame.h"
#include "BlockSocMachineSource.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;
bool g_DisableDestroyEffect = false;


//notify
void ClientPlayer::notifyInteractBlock2Tracking(const WCoord &blockpos, DirectionType face)
{
	if (!m_pWorld->isRemoteMode())
	{
		PB_BlockInteractHC blockInteractHC;
		blockInteractHC.set_objid(getObjId());
		blockInteractHC.set_face(face);
		blockInteractHC.mutable_blockpos()->set_x(blockpos.x);
		blockInteractHC.mutable_blockpos()->set_y(blockpos.y);
		blockInteractHC.mutable_blockpos()->set_z(blockpos.z);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BLOCK_INTERACT_HC, blockInteractHC, this);
	}
}

void ClientPlayer::notifyPunchBlock2Tracking(const WCoord& blockpos, DirectionType face, int status, DIG_METHOD_T digmethod, long long vehID)
{
	if (!m_pWorld->isRemoteMode())
	{
		PB_BlockPunchHC blockPunchHC;
		blockPunchHC.set_objid(getObjId());
		blockPunchHC.set_status(status);
		blockPunchHC.set_face(face);
		blockPunchHC.set_digmethod(digmethod);
		blockPunchHC.mutable_blockpos()->set_x(blockpos.x);
		blockPunchHC.mutable_blockpos()->set_y(blockpos.y);
		blockPunchHC.mutable_blockpos()->set_z(blockpos.z);
		blockPunchHC.set_vehicleobjid(vehID);
		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BLOCK_PUNCH_HC, blockPunchHC, this);
	}
}

//void ClientPlayer::notifyExploitBlock2Tracking(const WCoord &blockpos, DirectionType face, int status, int pickType)
//{
//	if (!m_pWorld->isRemoteMode())
//	{
//		PB_BlockExploitHC blockExploitHC;
//		blockExploitHC.set_objid(getObjId());
//		blockExploitHC.set_status(status);
//		blockExploitHC.set_face(face);
//		blockExploitHC.mutable_blockpos()->set_x(blockpos.x);
//		blockExploitHC.mutable_blockpos()->set_y(blockpos.y);
//		blockExploitHC.mutable_blockpos()->set_z(blockpos.z);
//		blockExploitHC.set_picktype(pickType);
//
//		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BLOCK_EXPLOIT_HC, blockExploitHC, this);
//	}
//}

void ClientPlayer::notifyUseItem2Tracking(int itemid, int status, bool onshift/* =false */)
{
	// 2021/07/9 修改:合成 移动版 合成，客机打开创造锤，物品介绍界面 codeby:wudeshen
	if ((ITEM_STONE_HAMMER <= itemid && itemid <= ITEM_TITANIUM_HAMMER) || ITEM_HOLOGRAPHIC == itemid)
	{
		return;
	}
	if (!m_pWorld->isRemoteMode())
	{
		PB_ItemUseHC itemUseHC;
		itemUseHC.set_objid(getObjId());
		itemUseHC.set_itemid(itemid);
		itemUseHC.set_status(status);
		itemUseHC.set_shift(onshift ? 1 : 0);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ITEM_USE_HC, itemUseHC, this);
	}
}

void ClientPlayer::notifyInteractActor2Tracking(ClientActor *target, int itype, bool interactplot/* =false */)
{
	if (!m_pWorld->isRemoteMode())
	{

		if (target == NULL) return;
		PB_ActorInteractHC actorInteractHC;
		actorInteractHC.set_objid(getObjId());
		actorInteractHC.set_itype(itype);
		actorInteractHC.set_target(target->getObjId());
		actorInteractHC.set_iplot(interactplot ? 1 : 0);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_INTERACT_HC, actorInteractHC, this);
	}
}

static void DropArcheologyItems(World* pworld, std::vector<GenerateItemDesc>& items, const WCoord& blockpos);

void ClientPlayer::destroyBlockWithVehicle(const WCoord& pos, DIG_METHOD_T dgmethod, bool destroy_effect, bool gamerule_forbid, ActorVehicleAssemble* pVeh)
{
	auto pWorld = pVeh->getWorld();
	auto pVehWorld = pVeh->getVehicleWorld();
	if (pWorld->isRemoteMode()) return;

	
	bool bCanDestroyAllBlocks = GetClientInfoProxy()->IsCurrentUserOuterChecker();
	gamerule_forbid = gamerule_forbid && !bCanDestroyAllBlocks;

	if (g_DisableDestroyEffect) destroy_effect = false;

	int blockid = pVehWorld->getBlockID(pos);
	
	if (!GetDefManagerProxy()->checkItemCrc(blockid)) return;
	if (gamerule_forbid && blockid != BLOCK_STAR) return;

	std::vector<CSPermitBitType> csPermitType;
	csPermitType.push_back(CS_PERMIT_DESTROY_BLOCK);

	int curToolID = getCurToolID();//code by:tanzhenyu
	bool canInteractorBlockFlag = false;
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorBlock",
		SandboxContext(nullptr)
		.SetData_Number("uin", getUin())
		.SetData_Number("tool", curToolID)
		.SetData_Number("blockid", blockid)
		.SetData_Usertype<std::vector<CSPermitBitType>>("csPermitType", &csPermitType));
	if (result.IsExecSuccessed())
	{
		canInteractorBlockFlag = result.GetData_Bool();
	}
	else
	{
		return ;
	}
	if (!canInteractorBlockFlag) return;

	if (!checkActionAttrState(ENABLE_DESTROYBLOCK) && !bCanDestroyAllBlocks)
	{
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14003);
		return;
	}

	int blockdata = pVehWorld->getBlockData(pos);

	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid, true);

	if (!pmtl) { return; }
	if (pVehWorld->CheckBlockSettingEnable(pmtl, ENABLE_DESTROYED) == 0 && !g_WorldMgr->isGodMode() && pmtl->getDestroyHardness(blockdata, this)  < 0 && !bCanDestroyAllBlocks) return;
	if (pmtl && !pmtl->canDestroy(pVehWorld, pos))
		return;

	addAchievement(3, ACHIEVEMENT_DIGITEM, blockid);
	updateTaskSysProcess(TASKSYS_DESTORY_BLOCK, blockid);
	if (m_MineType != BLOCK_MINE_PRECISE && pmtl->hasDestroyScore(blockdata)) addOWScore(def->Score);

	if (pWorld && !pWorld->isRemoteMode())
		addSFActivity(SFACTIVITY_DIGITEM, blockid, 1, !this->hasUIControl());

	checkNewbieWorldProgress(5, "destroy");
	if (blockid == BLOCK_WOOD_OAK) checkNewbieWorldProgress(7, "lumber");

	if (destroy_effect && pWorld->CheckBlockSettingEnable(pmtl, ENABLE_DESTROYED) != 0)
	{
		//特效会有问题后面再改 @tangjie 	
		pWorld->getEffectMgr()->playBlockDestroyEffect(0, pVeh->getRealWorldPosWithPos(pos).toWorldPos() + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2), DIR_NEG_X, 40);
	}

	bool needdropitem = true;
	PLAYER_GENIUS_TYPE genius = getGeniusType();
	if ((genius == GENIUS_ARCHEOLOGY && blockid == BLOCK_STONE) || (genius == GENIUS_SEARCH_JAR && blockid >= 737 && blockid <= 739))
	{
		float extvalues[4];
		if (GenRandomFloat() < getGeniusValue(genius, extvalues))
		{
			std::vector<GenerateItemDesc>items;
			WorldContainerMgr::generateChestItems(items, int(extvalues[0]), NULL, 1);
			DropArcheologyItems(pVehWorld, items, pos);
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_ROLECOLLECT);
			}
			if (genius == GENIUS_ARCHEOLOGY) needdropitem = false;
		}
	}

	int realBpItemId = 0; //真正的家园蓝图id
	bool isHomeland = (g_WorldMgr && g_WorldMgr->getSpecialType() == HOME_GARDEN_WORLD);
	if (blockid == BLOCK_CRYSTAL)
	{
		int step = GetClientInfoProxy()->getCurGuideStep();
		if (step == 9 || step == 15)
			checkNewbieWorldProgress(1, step);

		if (step == 15)
		{
			needdropitem = false;
			getBackPack()->addItem(def->ToolMineDrops[0].item, 1);
		}
	}
	else if (g_pPlayerCtrl && blockid == BLOCK_PLANTSPACE_JEWS)
	{
		char sBlockID[8];
		sprintf(sBlockID, "%d", blockid);
		//g_pPlayerCtrl->statisticToWorld(getUin(), 30008, "", g_pPlayerCtrl->getCurWorldType(), sBlockID);
	}
	//载具销毁方块家园模式不支持
	//else if (blockid == BLOCK_BUILDBULEPRINT && isHomeland)
	//{
	//	//家园蓝图拿出来真正的id
	//	ContainerBuildBluePrint* bpContainer = dynamic_cast<ContainerBuildBluePrint*>(m_pWorld->getContainerMgr()->getContainer(pos));
	//	if (bpContainer)
	//	{
	//		std::string datastr = bpContainer->getBPDataStr();
	//		jsonxx::Object jsonData;
	//		if (jsonData.parse(datastr))
	//		{
	//			if (jsonData.has<jsonxx::Number>("realitemid"))
	//				realBpItemId = jsonData.get<jsonxx::Number>("realitemid");
	//		}
	//	}
	//}
	/*if (blockid == BLOCK_BOAT_THRUSTER && (blockdata & 8) == 0)
	{
		needdropitem = false;
	}*/
	bool sucDestroy = false; //是否成功销毁
	if (needdropitem) {
		float fAddRadio = getPlayerAttrib()->getRandomAttValueWithStatus(BuffAttrType::BUFFATTRT_GOODLUCK_DIG);
		if (fAddRadio > 0.001f) {
			BlockMaterial::m_DigLuckBuff = (int)fAddRadio;
		}
		//增加传入参数:使用工具id 新增需求:掉落规则与使用工具等级不同 掉率不同//code by:tanzhenyu
		sucDestroy = pVehWorld->playerDestroyBlock(pos, m_MineType, getLivingAttrib()->getDigProbEnchant(), curToolID);
		BlockMaterial::m_DigLuckBuff = 0;
	}
	else
	{
		//增加传入参数:使用工具id 新增需求:掉落规则与使用工具等级不同 掉率不同//code by:tanzhenyu
		sucDestroy = pVehWorld->playerDestroyBlock(pos, BLOCK_MINE_NONE, 0, curToolID);
	}
	/*if (sucDestroy)
	{
		BIOME_TYPE biome_type = pVehWorld->getBiomeType(pos.x, pos.z);
		if (biome_type == BIOME_OCEAN || biome_type == BIOME_DEEPOCEAN)
		{
			int point = 10;
			if (getLivingAttrib()->hasBuff(1028))
			{
				point = 5;
			}
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("spawnSharkPoint", MNSandbox::SandboxContext(nullptr).SetData_Number("point", point));
		}
	}*/
	//if (sucDestroy && isHomeland)
	//{
	//	//家园敲掉一个方块 就加回到可操作背包里面去
	//	SandboxResult result = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().
	//		Emit("Homeland_AddBackpackItem",
	//			SandboxContext(nullptr).
	//			SetData_Number("blockId", blockid).
	//			SetData_Number("blockdata", blockdata).
	//			SetData_Number("realitemId", realBpItemId)
	//		);
	//}

	MNSandbox::GetGlobalEvent().Emit<>("WorldArchiveMgr_OnBlockChangedByManual");
	if (sucDestroy)
	{
		
	}
	if (destroy_effect)
	{
		WCoord centerpos = BlockCenterCoord(pos);
		if (def->DigSound[0]) pWorld->getEffectMgr()->playSound(pVeh->getRealWorldPosWithPos(CoordDivBlock(centerpos)), def->DigSound[0] ? def->DigSound : "blockd.grass", GSOUND_DESTROY);
	}

	pmtl->onBlockDestroyedBy(pVehWorld, pos, blockdata, BLOCK_DESTROY_PLAYER, this);
	
	//后面使用新的事件@tangjie
	//// 观察者事件接口
	//ObserverEvent_ActorBlock obevent((long long)getObjId(), pmtl->getBlockResID(), pos.x, pos.y, pos.z);
	//ObserverEventManager::getSingleton().OnTriggerEvent("Block.DestroyBy", &obevent);

	// 用来保存改变的block坐标
	std::vector<WCoord> changeBlocksPos;
	changeBlocksPos.push_back(pos);
	//载具上不支持放载具车间
	/*if (VehicleMgr::getSingletonPtr())
	{
		VehicleMgr::getSingletonPtr()->resetWorkshopConnectCoreBlock(changeBlocksPos);
	}*/

	if (m_OperateTicks > 0)
	{
		int method_factor = dgmethod == DIG_METHOD_NORMAL ? 1 : 2;

		getPlayerAttrib()->useStamina(STAMINA_DESTROYBLOCK, (float)method_factor);
		BackPackGrid* pgrid = getBackPack()->index2Grid(getCurShortcut() + getShortcutStartIndex());
		if (pgrid->def)
		{
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(pgrid->def->ID);
			if (tooldef)
			{
				if (!(dgmethod == DIG_METHOD_MULTI && def->Hardness < 1)) addCurToolDuration(-(tooldef->CollectDuration * method_factor));
			}
		}

		if (m_MineType == BLOCK_MINE_TOOLFIT && def->DropExp > 0 && GenRandomInt(10000) < def->DropExpOdds)
		{
			/*	if (NS_SANDBOX::IMiniGameProxy::getMiniGameProxy()->getFcmRate() == 0)
			{
			GameEventQue::GetInstance().postInfoTips(3692);
			return;
			}*/
			//ActorExpOrb::SpawnExpOrb(m_pWorld, def->DropExp, pos*BLOCK_SIZE, WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
			//挖掉方块不掉落经验球, 改成直接加经验
			OnGainedExp(def->DropExp);
		}
	}
	//载具上不支持这种
	////凝浆块不用铲子破坏会生成流动岩浆 code by: yangjie
	//if (blockid == BLOCK_COAGULATION && !IsShovelID(curToolID))
	//{
	//	m_pWorld->setBlockAll(pos, BLOCK_FLOW_LAVA, 0, 3);

	//	//破坏凝浆块 变成岩浆的时候 就播放特效
	//	WCoord effectpos = pos * BLOCK_SIZE + WCoord(50, 100, 50);
	//	m_pWorld->getEffectMgr()->playParticleEffect("particles/item_yanjiang.ent", effectpos, 100);
	//}

	//武器熟练度 破坏方块新增对应熟练度 code-by:lizhibao
	// @tangjie  参考以前代码暂时不需要
	//int weaponType = 0;
	//SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("WeaponSkin_HelperModule", "GetOwndSkinItemType", "ii>i", getUin(), curToolID, &weaponType);
	//if (weaponType == 6 || weaponType == 7 || weaponType == 8) //斧头镐子铲子的破坏方块计算在内
	//{
	//	int pointType = WEAPON_SKILLED_TYPE_DIGBLOCK; //镐子
	//	if (weaponType == 7) pointType = WEAPON_SKILLED_TYPE_CUTTREE; //斧头
	//	if (weaponType == 8) pointType = WEAPON_SKILLED_TYPE_PLANTLNAD;//铲子
	//	this->addWeaponSkilledPoint(pointType, curToolID);
	//}
}

void ClientPlayer::notifyGameInfo2Self(int infotype, int id, int num, const char* name, const char* buff)
{

	if (hasUIControl())
	{
		if (infotype == PLAYER_NOTIFYINFO_TASKGET)
		{
			MINIW::ScriptVM::game()->callFunction("TaskGetItem", "ii", id, num);
			return;
		}
		if (infotype == PLAYER_NOTIFYINFO_GETITEM)
		{
			if (!isRemote())
			{
				std::string backpackcount = "0";
				if (getBackPack())
				{
					backpackcount = std::to_string(getBackPack()->getItemCountInNormalPack(id));
				}

				MINIW::ScriptVM::game()->callFunction("GetItemTips", "iis", id, num, backpackcount.c_str());
				return;
			}
			MINIW::ScriptVM::game()->callFunction("GetItemTips", "iis", id, num, name);
		}
		else if (infotype == PLAYER_NOTIFYINFO_TIPS)
		{
			if (id == 0 && name && name[0] != '\0')
			{
				std::string  str = name;
				SandboxResult sandboxResult = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("WorldStringTranslateMgr_getTransByMultiplekeys", SandboxContext(nullptr)
					.SetData_Number("type", 14)
					.SetData_String("oldVal", name));
				if (sandboxResult.IsExecSuccessed())
				{
					str = sandboxResult.GetData_String();
				}
				//ge GetGameEventQue().postInfoTips(str.c_str());
				CommonUtil::GetInstance().PostInfoTips(str.c_str());
			}
			else
			{
				if ((id >= 14000 && id <= 14009) || id == 9076 || id == 9077) //不可移动等提示添加间隔
				{
					if (MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->GetTriggerOperateAttr(getUin(), 3)) //AttrOperateTips 
					{
						return;
					}
					int starttime = (RunService::GetSingleton().CurrentMilliSecondTimeStamp() / 1000);
					const ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
					int curtime = 0;
					auto iter = m_TipTime.find(id);
					if (iter != m_TipTime.end())
					{
						curtime = iter->second;
					}
					if (lua_const && (starttime - curtime) < lua_const->player_actionattrtip) //提示间隔
					{
						return;
					}
					else
					{
						m_TipTime[id] = starttime;
					}
				}

				//ge GameEventQue::GetInstance().postInfoTips(id, num);
				CommonUtil::GetInstance().PostInfoTips(id, num);
				if (id == 1001300 && num > 0)
				{
					MINIW::ScriptVM::game()->callFunction("RoomCheatReport", "i", num);
				}
			}
		}
		else if (infotype == PLAYER_NOTIFYINFO_DEATH)
		{
			MINIW::ScriptVM::game()->callFunction("UpdateDeath", "iiss", id, num, name, buff);
		}
		else if (infotype == PLAYER_NOTIFYINFO_HORSEEGG)
		{
			//...
			if (id == 0)
			{
				//ge GetGameEventQue().postInfoTips(251);
				CommonUtil::GetInstance().PostInfoTips(251);
			}
		}
		else if (infotype == PLAYER_NOTIFYINFO_KILLPLAYER)
		{
			//LLDO:击杀玩家, 展示击杀信息: id = 被击杀者uin. num = 击杀者连杀数, 即player->m_CWKills.
			MINIW::ScriptVM::game()->callFunction("ShowKillInfoFrame", "ii", id, num);
		}
		else if (infotype == PLAYER_NOTIFYINFO_GETMULTIITEM)
		{
			MINIW::ScriptVM::game()->callFunction("GetMultiItemTips", "s", name);
		}
	}
	else
	{
		PB_GameTipsHC gameTipsHC;
		gameTipsHC.set_tipstype(infotype);
		gameTipsHC.set_id(id);
		gameTipsHC.set_num(num);
		std::string backpackcount = "0";
		if (getBackPack())
		{
			backpackcount = std::to_string(getBackPack()->getItemCountInNormalPack(id));
		}
		gameTipsHC.set_othername(backpackcount);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_GAME_TIPS_HC, gameTipsHC);
	}
}

void ClientPlayer::notifyOpenWindow2Self(int blockid, int x, int y, int z)
{
	if (hasUIControl())
	{
		MINIW::ScriptVM::game()->callFunction("OpenWindowOnBlock", "iiii", blockid, x, y, z);
	}
	else
	{
		PB_OpenWindowHC openWindowHC;
		openWindowHC.set_id(blockid);
		openWindowHC.set_x(x);
		openWindowHC.set_y(y);
		openWindowHC.set_z(z);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_OPENWINDOW_HC, openWindowHC);
	}
}

//notify end

bool ClientPlayer::interactHorse(ActorHorse *horse, bool onshift/* =false */)
{
	getBody()->playAttack();
	if (horse->getCanRideByPlayer())
		return horse->ActorContainerMob::interact(this, onshift);
	else
		return false;
}

int ClientPlayer::lootItem(int fromIndex, int num)
{
	int remain = getBackPack()->lootItem(fromIndex, num);
	if (remain > 0)
	{
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 2119);
	}
	return remain;
}

WCoord ClientPlayer::getCurOperatePos(int operatedistance /* = 5 */)
{
	WCoord pos = Rainbow::Vector3f(0, 0, 0);
	bool isGet = getAimPos(pos.x, pos.y, pos.z, operatedistance);
	Rainbow::Vector3f dir  = MINIW::Normalize(getLocoMotion()->getLookDir());
	if (isGet)
	{
		return CoordDivBlock(pos);
	}

	return WCoord(0, -1, 0);
}

bool ClientPlayer::canShowInteract(int type, int id, bool ignorecondition/* =false */)
{
	bool canShow = true;
	if (type == PLOT_INTERACT)	//剧情
	{
		auto def = GetDefManagerProxy()->getNpcPlotDef(id);
		if (def)
		{
			if (!ignorecondition)
			{
				for (int i = 0; i < (int)def->Conditions.size(); i++)
				{
					const NpcPlotDef::ConditionDef &condition = def->Conditions[i];

					//前置任务
					for (int j = 0; j < (int)condition.TaskIDs.size(); j++)
					{
						if (getTaskState(condition.TaskIDs[i]) != TASK_COMPLETED)	//未完成
						{
							canShow = false;
							break;
						}
					}

					//时间要求
					if (condition.StartTime >= 0 && condition.EndTime >= 0)
					{
						int curHours = (int)GetWorldManagerPtr()->getHours();
						if (condition.StartTime <= condition.EndTime && (condition.StartTime > curHours || condition.EndTime < curHours))
							canShow = false;
						else if (condition.StartTime > condition.EndTime && (condition.StartTime > curHours && condition.EndTime < curHours))
							canShow = false;
					}

					//物品要求
					if (condition.ItemID > 0)
					{
						if (getBackPack()->getItemCountInNormalPack(condition.ItemID) < condition.ItemNum) //物品不足
							canShow = false;
					}
				}
			}

			int taskid;
			auto taskInfo = getTaskInfoByPlot(def->ID, taskid);
			if (taskInfo && taskInfo->state == TASK_COMPLETED)	//这个剧情的任务已经完成了就不再显示
				canShow = false;
		}
		else
			canShow = false;
	}
	else if (type == TASK_INTERACT) //交付任务
	{
		auto taskInfo = getTaskInfo(id);
		if (!taskInfo) //没接任务
			canShow = false;
		else if (taskInfo->state == TASK_COMPLETED) //已经完成这个任务
			canShow = false;
	}

	return canShow;
}

bool ClientPlayer::doSpecialSkill()
{
	auto RidComp = getRiddenComponent();
	ActorHorse *riding = NULL;
	if (RidComp)
	{
		riding = dynamic_cast<ActorHorse *>(RidComp->getRidingActor());
	}
	if (riding && (riding->getRiddenByActorID() == getObjId()))
	{
		if (riding->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE ||
			riding->getObjType() == OBJ_TYPE_DRAGON_MOUNT ||
			riding->getObjType() == OBJ_TYPE_MOON_MOUNT ||
			riding->getObjType() == OBJ_TYPE_DOUDU_MOUNT ||
			riding->getObjType() == OBJ_TYPE_PUMPKIN_HORSE)
		{
			riding->doSkillAttack(NULL);
			return  true;
		}
		else
		{
			riding->useSkill();
			return true;
		}
	}
	return false;
}

bool ClientPlayer::attackedFrom(OneAttackData &atkdata, ClientActor *inputattacker)
{
	//LOG_INFO("ClientPlayer::attackedFrom(): %d <- %d", getUin(), inputattacker->getObjId());
	if (isInSpectatorMode())
	{
		return false;
	}

	if (isAttrShapeShift())
	{
		attrShapeShiftAttackedFrom(atkdata, inputattacker);
	}

	if (!checkActionAttrState(ENABLE_BEATTACKED))
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer*>(inputattacker);
		if (player)
		{
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14006);
		}

		return false;
	}
	bool isSafeZoneNoPvp = false;

	// 记录直接攻击者，盾牌防御需要用到
	if (!(atkdata.directAttacker))
	{
		atkdata.directAttacker = inputattacker;
	}

	// 如果是PVE游戏，则跳过pvp伤害
	if (atkdata.fromplayer && GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->IsPVEGame())
	{
		return false;
	}

	if (atkdata.fromplayer)
	{
		// LOG_INFO("~~~~!!!!!!!~~~~ player attack player");
		inputattacker = dynamic_cast<ClientActor*>(atkdata.fromplayer);
		ClientPlayer* atkPlayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
		if (getPlayerAttrib()->hasBuff(IN_SAFE_ZONE))
		{
			// LOG_INFO("~~~~!!!!!!!~~~~ player attack player Injuried player in safezone, attack fail");
			isSafeZoneNoPvp = true;
			return false;  // 安全区跳过pvp伤害
		}
		atkPlayer->getPlayerAttrib()->addBuff(DANGER_PLAYER, 1);
	}

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ActorHorse *horse = dynamic_cast<ActorHorse *>(RidComp->getRidingActor());
		if (horse && horse != inputattacker) //避免自己打自己
		{
			bool b = horse->attackedFrom(atkdata, inputattacker);
			if (horse->isDead()) mountActor(NULL);
			return b;
		}
		else {
			return false;
		}
	}

	if (atkdata.atktype != ATTACK_SUN && getAiInvulnerableProb() > 0)
	{
		if (GenRandomInt(100) < getAiInvulnerableProb())
		{
			if (!checkActionAttrState(ENABLE_FORBIDDODGEANIM))
			{
				playAnim(SEQ_MOBDODGE);
			}

			auto pScriptComponent = getScriptComponent();
			if (pScriptComponent)
			{
				pScriptComponent->OnEvent((unsigned int)CE_OnPlayerDodge, true);
			}
			return false;
		}
	}

	/*if (atkdata.atktype != ATTACK_SUN && isInvulnerable(inputattacker))
	{
		playAnim(SEQ_MOBDODGE);
		return false;
	}*/

	//白天躺床上受到伤害起来(将该语句移动到受到伤害之前，避免在睡觉时死亡出现BUG by:zhangzhendong)
	if (m_pWorld && (!m_pWorld->isRemoteMode()) && (isSleeping() || isRestInBed()))
	{
		wakeUp(true, false, false);
	}

	int lasthp = int(getAttrib()->getHP());//受伤害之前的hp
	PlayerAttrib* playerAttrib = dynamic_cast<PlayerAttrib*>(this->getAttrib());
	bool isDowned = false;
	if (playerAttrib && playerAttrib->isPlayerDowned())
		isDowned = true;

	if (!isSafeZoneNoPvp)
	{
		LOG_INFO("~~~~!!!!!!!~~~~ player attack player Injuried player out safezone, attack success");
		updataLastAttackDataInfo(atkdata, inputattacker);
		if (!ActorLiving::attackedFrom(atkdata, inputattacker))//受击改变了hp值
		{
			return false;
		}
	}

	//被怪物攻击
	if (dynamic_cast<ClientMob*>(inputattacker))
	{
		ClientMob *pMob = (ClientMob*)inputattacker;
		assert(pMob->m_Def);

		if (pMob->m_Def)
		{
			MNSandbox::GetGlobalEvent().Emit<int, ClientActor*, int>("StatisticTerrgen_PlayerAttackMonster", pMob->m_Def->ID, inputattacker, 2);
			if (g_pPlayerCtrl)
			{
				std::vector<WORLD_ID> allVillagers = GetWorldManagerPtr()->getWorldInfoManager()->getAllVillagers(g_pPlayerCtrl->getObjId());
				for (size_t i = 0; i < allVillagers.size(); i++)
				{
					auto actor = g_pPlayerCtrl->getActorMgr()->findActorByWID(allVillagers[i]);
					if (!actor)
						continue;
					actor->Event2().Emit<ClientActor*, ClientActor*>("bePlayerReqHelpOnTrigger", inputattacker, this);
				}
			}
		}
	}
	return true;
}

void ClientPlayer::updataLastAttackDataInfo(OneAttackData& atkdata, ClientActor* inputattacker)
{
	m_socAttackInfo.clean();

	m_socAttackInfo.atktype = atkdata.atktype;
	m_socAttackInfo.buffId = atkdata.buffId;
	m_socAttackInfo.buffLevel = atkdata.buffLevel;

	//暴击就是爆头
	if (atkdata.critical)
		m_socAttackInfo.atktype = HEAD_KILL;

	if (!inputattacker)
	{
		return;
	}

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(inputattacker);
	if (player != NULL)
	{
		m_socAttackInfo.playerid = player->getUin();
		m_socAttackInfo.toolid = player->getCurToolID();
		m_socAttackInfo.length = (getLocoMotion()->m_Position - player->getLocoMotion()->m_Position).length() / 100;
		return;
	}

	ClientMob* mob = dynamic_cast<ClientMob*>(inputattacker);
	if (mob != nullptr)
	{
		m_socAttackInfo.atktype = ANIMAL_KILL;
		m_socAttackInfo.mobid = mob->getDefID();
		return;
	}

	ClientActorProjectile* projectile = dynamic_cast<ClientActorProjectile*>(inputattacker);
	if (projectile == nullptr) return;

	// projectile->getShootingActor() 接口的返回值可能是mob或者boss
	// ActorBoss和ClientMob类型并无衍生关系
	mob = dynamic_cast<ClientMob*>(projectile->getShootingActor());
	if (mob != nullptr)
	{
		m_socAttackInfo.atktype = ANIMAL_KILL;
		m_socAttackInfo.mobid = mob->getDefID();
		return;
	}

	//再判断一下boss类型
	ActorBoss* boss = dynamic_cast<ActorBoss*>(projectile->getShootingActor());
	if (boss)
	{
		m_socAttackInfo.atktype = ANIMAL_KILL;
		m_socAttackInfo.mobid = boss->getDefID();
		return;
	}
}

bool ClientPlayer::mountActor(ClientActor* actor, bool isforce /* = false */, int seatIndex, bool bcontrol /*= true*/)
{
	bool syncpos = false;
	ClientActor *another = NULL;
	ClientActor *ridingactor = NULL;
	PlayerControl* playerControl = dynamic_cast<PlayerControl*>(this);

	auto RidComp = sureRiddenComponent();
	//用户从载具或者载具上下来
	if (RidComp && RidComp->isRiding() && actor == NULL)
	{
		ridingactor = RidComp->getRidingActor();
		if (ridingactor)
		{
			auto ridingComp = ridingactor->sureRiddenComponent();
			if (!(ridingComp && ridingComp->canClearRidden(this, isforce))) return false;
			//干掉文字提示
			//if (playerControl)
			//{
			//	if (ridingactor->getObjType() == OBJ_TYPE_VEHICLE)
			//	{
			//		playerControl->postInfoTips(21055);
			//	}
			//}
			if (!ridingactor->isDead() && !ridingactor->needClear() && ridingComp && ridingComp->getNumRiddenPos() > 1)
			{
				if (!(ridingComp->getRiddenByActor(0) && ridingComp->getRiddenByActor(0) != this))
				{
					std::vector<ClientActor*> Riddeners(ridingComp->getNumRiddenPos(), NULL);
					for (size_t i = 0; i < (unsigned int)ridingComp->getNumRiddenPos(); i++)
					{
						Riddeners[i] = ridingComp->getRiddenByActor(i);
					}
					for (size_t k = 0; k < (unsigned int)ridingComp->getNumRiddenPos(); k++)
					{
						if (Riddeners[k] && Riddeners[k] != this)
						{
							another = Riddeners[k];
							break;
						}
					}
				}

				/*	if (ridingactor->getRiddenByActor(0))
					{
						if (ridingactor->getRiddenByActor(0) == this)
						{
							if (ridingactor->getRiddenByActor(1))
							{
								another = ridingactor->getRiddenByActor(1);
							}
							else if (ridingactor->getRiddenByActor(2))
							{
								another = ridingactor->getRiddenByActor(2);
							}
							else if (ridingactor->getRiddenByActor(3))
							{
								another = ridingactor->getRiddenByActor(3);
							}
						}
					}
					else
					{
						if (ridingactor->getRiddenByActor(1))
						{
							if (ridingactor->getRiddenByActor(1) == this)
							{
								if (ridingactor->getRiddenByActor(2))
								{
									another = ridingactor->getRiddenByActor(2);
								}
								else if (ridingactor->getRiddenByActor(3))
								{
									another = ridingactor->getRiddenByActor(3);
								}
							}
							else
							{
								another = ridingactor->getRiddenByActor(1);
							}
						}
						else
						{
							if (ridingactor->getRiddenByActor(2))
							{
								if (ridingactor->getRiddenByActor(2) == this)
								{
									if (ridingactor->getRiddenByActor(3))
									{
										another = ridingactor->getRiddenByActor(3);
									}
								}
								else
								{
									another = ridingactor->getRiddenByActor(2);
								}
							}
						}
					}*/
			}
			ActorLiving *living = dynamic_cast<ActorLiving *>(ridingactor);
			if (living)
			{
				living->breakInvisible();			// 20210910：下坐骑打断隐身  codeby： keguanqiang
			}


			//调整函数位置， clearRiddenActor函数会使用到 m_RidingActor 来判断用户是否是乘客
			//2024.8.13调整, 把函数位置提前, calUnmountPos会设置玩家及坐骑位置, 必须先把坐骑清掉 codeby: caozegang
			setRidingActor(NULL);

			if (!isSittingInStarStationCabin() && !m_pWorld->isRemoteMode())//������״̬�����ս���£�����Ҫλ�ƽ�ɫ
				calUnmountPos(ridingactor);
			syncpos = true;

			if (ridingComp)
			{
				ridingComp->clearRiddenActor(this);
				RidComp->setRiddenControl(true);// 恢复默认值
			}
			setGuardSafeTick(3000);

			showAccountPet();

			dismountActorOnTrigger(ridingactor->getObjId(), ridingactor->getDefID());

			if (ridingactor->getObjType() == OBJ_TYPE_MONSTER) 
			{
				ActorBody* actorBody = ridingactor->getBody();
				if (actorBody && actorBody->getModel())
				{
					actorBody->getModel()->SetRenderGroup(DefaultRenderGroup, true);
				}
			}


			if (ridingactor->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
			{
				static_cast<PlayerLocoMotion*>(getLocoMotion())->attachPhysActor();
				if (getBody())
				{
					getBody()->shareShift(false);
				}

				if (ridingactor->getBody())
					ridingactor->getBody()->show(false, true);

				if (!m_pWorld->isRemoteMode())
				{
					int defId = ridingactor->getDefID();
					ridingactor->setNeedClear();
					if (getBody())
						getBody()->stopAllAnim();
					playAnim(SEQ_RE_SHAPE_SHIFT, true, -1);

					if (defId == 3494 && getBody()) {//牛魔要特殊处理 恢复周身特效
						const RoleSkinDef *skindef = GetDefManagerProxy()->getRoleSkinDef(getBody()->getSkinID());
						if (skindef && skindef->getEffect(3))
						{
							auto effectComponent = getEffectComponent();
							if (effectComponent)
							{
								effectComponent->playBodyEffect(skindef->getEffect(3));//("140103_2");
							}
						}
						getBody()->revoverShapeHeight();
						//getBody()->m_NameObjHeight = 16.0f;
					}

					/*char effectName[64] = { 0 };
					sprintf(effectName, "horsechange_%d", defId);
					playBodyEffect(effectName);*/

					char soundName[64] = { 0 };
					sprintf(soundName, "ent.%d.change2", defId);
					auto soundComp = getSoundComponent();
					if (soundComp)
					{
						soundComp->playSound(soundName, 1.0f, 1.0f);
					}
				}

				if (hasUIControl())
				{
					if (g_pPlayerCtrl)
						g_pPlayerCtrl->m_bWaitRecoverViewMode = true;
					//ge GetGameEventQue().postShapeShiftInfo(false);
					MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
						SetData_Number("state", false);
					if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
						MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_SHAPE_SHIFT", sandboxContext);
					}
				}
			}
			else if (ridingactor->getObjType() == OBJ_TYPE_DRAGON_MOUNT)
			{
				if (hasUIControl())
				{
					ActorDragonMount* dragonMount = dynamic_cast<ActorDragonMount*>(ridingactor);
					if (dragonMount)
					{
						dragonMount->OnActorDragonDismounted();
					}
				}
			}
			else if (ridingactor->getObjType() == OBJ_TYPE_MOON_MOUNT)
			{
				if (hasUIControl())
				{
					ActorMoonMount* moonMount = dynamic_cast<ActorMoonMount*>(ridingactor);
					if (moonMount)
					{
						moonMount->OnActorMoonDismounted();
					}
				}
			}
			else if (ridingactor->getObjType() == OBJ_TYPE_DOUDU_MOUNT)
			{
				if (hasUIControl())
				{
					ActorDouDuMount* douDuMount = dynamic_cast<ActorDouDuMount*>(ridingactor);
					if (douDuMount)
					{
						douDuMount->OnActorDouDuDismounted();
					}
				}
			}
			else if (ridingactor->getObjType() == OBJ_TYPE_PUMPKIN_HORSE) // 调用lua的下坐骑
			{
				if (hasUIControl())
				{
					ActorPumpkinHorse* horse = dynamic_cast<ActorPumpkinHorse*>(ridingactor);
					if (horse)
					{
						horse->OnActorHorseMounted(false);
					}
				}
			}

			if (hasUIControl())
			{
				ActorHorse* horse = dynamic_cast<ActorHorse*>(ridingactor);
				if (horse)
					horse->OnHorseDismounted();
			}

		}
	}
	else if (RidComp && RidComp->isRiding() && actor != NULL) //坐在座位上的情况，再去点击其它座位
	{
		ActorVehicleAssemble* actorVehicleAssemble = dynamic_cast<ActorVehicleAssemble*>(actor);
		if (actorVehicleAssemble && !actorVehicleAssemble->canBeRidedWithStandPos(this)) return false;
		if (!m_pWorld->isRemoteMode())
		{
			if ((seatIndex < 0) && (actor->getDefID() == 4002))
			{

				int x, y, z;
				ActorVehicleAssemble* actorVehicleAssemble = dynamic_cast<ActorVehicleAssemble*>(actor);
				ClientPlayer *player = dynamic_cast<ClientPlayer *>(this);
				actorVehicleAssemble->intersect(player, x, y, z);
				WCoord blockpos(x, y, z);
				seatIndex = actorVehicleAssemble->getSeatIndex(blockpos * BLOCK_SIZE);
			}
		}

		auto actorRidComp = actor->sureRiddenComponent();
		int emptyindex = 0;
		if (actorRidComp)
		{
			emptyindex = actorRidComp->findEmptyRiddenIndex(seatIndex);
		}
		
		if (emptyindex >= 0)
		{
			//要坐的座位是空的，才能做这样的操作
			ridingactor = RidComp->getRidingActor();
			if (ridingactor)
			{
				auto ridingComp = ridingactor->sureRiddenComponent();
				if (!(ridingComp && ridingComp->canClearRidden(this, isforce))) return false;

				if (!ridingactor->isDead() && !ridingactor->needClear() && ridingComp && ridingComp->getNumRiddenPos() > 1)
				{
					if (!(ridingComp->getRiddenByActor(0) && ridingComp->getRiddenByActor(0) != this))
					{
						std::vector<ClientActor*> Riddeners(ridingComp->getNumRiddenPos(), NULL);
						for (size_t i = 0; i < (unsigned int)ridingComp->getNumRiddenPos(); i++)
						{
							Riddeners[i] = ridingComp->getRiddenByActor(i);
						}
						for (size_t k = 0; k < (unsigned int)ridingComp->getNumRiddenPos(); k++)
						{
							if (Riddeners[k] && Riddeners[k] != this)
							{
								another = Riddeners[k];
								break;
							}
						}
					}
					/*if (ridingactor->getRiddenByActor(0))
					{
						if (ridingactor->getRiddenByActor(0) == this)
						{
							if (ridingactor->getRiddenByActor(1))
							{
								another = ridingactor->getRiddenByActor(1);
							}
							else if (ridingactor->getRiddenByActor(2))
							{
								another = ridingactor->getRiddenByActor(2);
							}
							else if (ridingactor->getRiddenByActor(3))
							{
								another = ridingactor->getRiddenByActor(3);
							}
						}
					}
					else
					{
						if (ridingactor->getRiddenByActor(1))
						{
							if (ridingactor->getRiddenByActor(1) == this)
							{
								if (ridingactor->getRiddenByActor(2))
								{
									another = ridingactor->getRiddenByActor(2);
								}
								else if (ridingactor->getRiddenByActor(3))
								{
									another = ridingactor->getRiddenByActor(3);
								}
							}
							else
							{
								another = ridingactor->getRiddenByActor(1);
							}
						}
						else
						{
							if (ridingactor->getRiddenByActor(2))
							{
								if (ridingactor->getRiddenByActor(2) == this)
								{
									if (ridingactor->getRiddenByActor(3))
									{
										another = ridingactor->getRiddenByActor(3);
									}
								}
								else
								{
									another = ridingactor->getRiddenByActor(2);
								}
							}
						}
					}*/
				}
				if (!m_pWorld->isRemoteMode())
					calUnmountPos(ridingactor);
				syncpos = true;

				if (ridingComp)
				{
					ridingComp->clearRiddenActor(this);
				}

				setGuardSafeTick(3000);

				setRidingActor(NULL);

				showAccountPet();

				dismountActorOnTrigger(ridingactor->getObjId(), ridingactor->getDefID());
				if (ridingactor->getObjType() == OBJ_TYPE_DRAGON_MOUNT)
				{
					if (hasUIControl())
					{
						ActorDragonMount* dragonMount = dynamic_cast<ActorDragonMount*>(ridingactor);
						if (dragonMount)
						{
							dragonMount->OnActorDragonDismounted();
						}
					}
				}

				if (ridingactor->getObjType() == OBJ_TYPE_MOON_MOUNT)
				{
					if (hasUIControl())
					{
						ActorMoonMount* moonMount = dynamic_cast<ActorMoonMount*>(ridingactor);
						if (moonMount)
						{
							moonMount->OnActorMoonDismounted();
						}
					}
				}
				if (ridingactor->getObjType() == OBJ_TYPE_DOUDU_MOUNT)
				{
					if (hasUIControl())
					{
						ActorDouDuMount* douDuMount = dynamic_cast<ActorDouDuMount*>(ridingactor);
						if (douDuMount)
						{
							douDuMount->OnActorDouDuDismounted();
						}
					}
				}
				if (ridingactor->getObjType() == OBJ_TYPE_PUMPKIN_HORSE) // 调用lua的下坐骑
				{
					if (hasUIControl())
					{
						ActorPumpkinHorse* horse = dynamic_cast<ActorPumpkinHorse*>(ridingactor);
						if (horse)
						{
							horse->OnActorHorseMounted(false);
						}
					}
				}
			}
			if (RidComp && !RidComp->mountActor_Base(actor, isforce, seatIndex)) return false;
			if (actorRidComp) { actorRidComp->setRiddenControl(bcontrol); }
			setGuardSafeTick(3000);
			hideAccountPet();

			mountActorOnTrigger(actor->getObjId(), actor->getDefID());
			if (actor->getObjType() == OBJ_TYPE_DRAGON_MOUNT)
			{
				if (hasUIControl())
				{
					ActorDragonMount* dragonMount = dynamic_cast<ActorDragonMount*>(actor);
					if (dragonMount)
					{
						dragonMount->OnActorDragonMounted();
					}
				}
			}

			if (actor->getObjType() == OBJ_TYPE_MOON_MOUNT)
			{
				if (hasUIControl())
				{
					ActorMoonMount* moonMount = dynamic_cast<ActorMoonMount*>(actor);
					if (moonMount)
					{
						moonMount->OnActorMoonMounted();
					}
				}
			}

			if (actor->getObjType() == OBJ_TYPE_DOUDU_MOUNT)
			{
				if (hasUIControl())
				{
					ActorDouDuMount* douDuMount = dynamic_cast<ActorDouDuMount*>(actor);
					if (douDuMount)
					{
						douDuMount->OnActorDouDuMounted();
					}
				}
			}
			if (actor->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
			{
				static_cast<PlayerLocoMotion*>(getLocoMotion())->detachPhysActor();
			}
			if (actor->getObjType() == OBJ_TYPE_PUMPKIN_HORSE)
			{
				if (hasUIControl())
				{
					ActorPumpkinHorse* horse = dynamic_cast<ActorPumpkinHorse*>(actor);
					if (horse)
					{
						horse->OnActorHorseMounted(true);
					}
				}
			}

			if (hasUIControl())
			{
				ActorHorse* horse = dynamic_cast<ActorHorse*>(ridingactor);
				if (horse)
					horse->OnHorseDismounted();
			}
			ActorLiving *living = dynamic_cast<ActorLiving *>(actor);
			if (living)
			{
				living->breakInvisible();			// 20210910：上坐骑打断隐身  codeby： keguanqiang
			}
		}
	}
	else    // 未骑乘的状态下，去坐载具或者上坐骑
	{
		ActorVehicleAssemble* actorVehicleAssemble = dynamic_cast<ActorVehicleAssemble*>(actor);
		if (actorVehicleAssemble && !actorVehicleAssemble->canBeRidedWithStandPos(this)) return false;
		if (isSittingInStarStationCabin())
		{
			standUpFromChair();
		}

		if (RidComp && !RidComp->mountActor_Base(actor, isforce, seatIndex)) return false;
		//干掉文字提示
		//if (actor && playerControl)
		//{
		//	if (actor->getObjType() == OBJ_TYPE_VEHICLE)
		//	{
		//		playerControl->postInfoTips(21054);
		//	}
		//}

		setGuardSafeTick(3000);
		hideAccountPet();

		mountActorOnTrigger(actor->getObjId(), actor->getDefID());

		if (actor && actor->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE && getBody())
		{
			getBody()->shareShift(true);

			if (hasUIControl())
			{
				//ge GetGameEventQue().postShapeShiftInfo(true);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("state", true);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_SHAPE_SHIFT", sandboxContext);
				}
			}
		}
		else if (actor && actor->getObjType() == OBJ_TYPE_DRAGON_MOUNT)
		{
			if (hasUIControl())
			{
				ActorDragonMount* dragonMount = dynamic_cast<ActorDragonMount*>(actor);
				if (dragonMount)
				{
					dragonMount->OnActorDragonMounted();
				}
			}
		}
		else if (actor && actor->getObjType() == OBJ_TYPE_MOON_MOUNT)
		{
			if (hasUIControl())
			{
				ActorMoonMount* moonMount = dynamic_cast<ActorMoonMount*>(actor);
				if (moonMount)
				{
					moonMount->OnActorMoonMounted();
				}
			}
		}
		else if (actor && actor->getObjType() == OBJ_TYPE_DOUDU_MOUNT)
		{
			if (hasUIControl())
			{
				ActorDouDuMount* douDuMount = dynamic_cast<ActorDouDuMount*>(actor);
				if (douDuMount)
				{
					douDuMount->OnActorDouDuMounted();
				}
			}
		}
		if (actor->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
		{
			static_cast<PlayerLocoMotion*>(getLocoMotion())->detachPhysActor();
		}
		if (actor->getObjType() == OBJ_TYPE_PUMPKIN_HORSE)
		{
			if (hasUIControl())
			{
				ActorPumpkinHorse* horse = dynamic_cast<ActorPumpkinHorse*>(actor);
				if (horse)
				{
					horse->OnActorHorseMounted(true);
				}
			}
		}

		if (actor->getObjType() == OBJ_TYPE_MONSTER) 
		{
			ActorBody* actorBody = actor->getBody();
			if (actorBody && actorBody->getModel())
			{
				actorBody->getModel()->SetRenderGroup(HorseEntityRenderGroup, true);
			}
		}

		ActorLiving *living = dynamic_cast<ActorLiving *>(actor);
		if (living)
		{
			living->breakInvisible();			// 20210910：上坐骑打断隐身  codeby： keguanqiang
		}
	}

	if (getLivingAttrib()->getEquipItem(EQUIP_WEAPON) == ITEM_FOOTBALLWEAR || getLivingAttrib()->getEquipItem(EQUIP_WEAPON) == ITEM_BASKETBALLWEAR || getLivingAttrib()->getEquipItem(EQUIP_WEAPON) == ITEM_WINTER_GLOVES)
		changeOPWay();

	if (!m_pWorld->isRemoteMode())
	{
		if ((seatIndex < 0) && ((actor != NULL) && (actor->getDefID() == 4002))) {
			int x, y, z;
			ActorVehicleAssemble* actorVehicleAssemble = dynamic_cast<ActorVehicleAssemble*>(actor);
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(this);
			actorVehicleAssemble->intersect(player, x, y, z);

			WCoord blockpos(x, y, z);

			LOG_INFO("mount pos: %d %d %d", x, y, z);
			seatIndex = actorVehicleAssemble->getSeatIndex(blockpos * BLOCK_SIZE);
		}

		
		int RiddenIndex = 0;
		if (actor)
		{
			auto actorRidComp = actor->sureRiddenComponent();
			if (actorRidComp)
			{
				RiddenIndex = actorRidComp->findRiddenIndex(this);
				actorRidComp->setRiddenControl(bcontrol);
			}
		}

		PB_PlayerMountActorHC playerMountActorHC;
		playerMountActorHC.set_playeruin(getUin());
		playerMountActorHC.set_actorid(actor == NULL ? 0 : actor->getObjId());
		playerMountActorHC.set_rideposindex(RiddenIndex);
		playerMountActorHC.set_force(isforce ? 1 : 0);
		playerMountActorHC.set_interactblockid(seatIndex);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_MOUNTACTOR_HC, playerMountActorHC, this, true);

		if (syncpos)
		{
			MpActorTrackerEntry *entry = m_pWorld->getMpActorMgr()->getTrackerEntry(getObjId());
			if (entry) entry->sendActorMovementToClient(getUin(), this, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		}
	}

	if (another && ridingactor && (ridingactor->getObjType() != OBJ_TYPE_VEHICLE))
	{
		auto anotherRidComp = another->sureRiddenComponent();
		if (anotherRidComp)
		{
			anotherRidComp->mountActor(ridingactor); //第一个位置空缺, 往前移动
		}
	}
	setHPProgressDirty();
	return true;
}

bool ClientPlayer::carryActor(ClientActor *actor, WCoord pos /* = WCoord(0, -1, 0) */)
{
	auto CarryComp = sureCarryComponent();
	bool iscarrying = false;
	if (CarryComp)
	{
		iscarrying = CarryComp->isCarrying();
	}
	
	if (CarryComp && iscarrying && actor == NULL)  //放下扛起的生物
	{
		ClientActor *carringActor = CarryComp->getCarringActor();
		if (!carringActor)
			return false;

		auto ActorCarryComp = carringActor->sureCarryComponent();
		if (ActorCarryComp)
		{
			ActorCarryComp->setCarriedActor(NULL);
		}

		CarryComp->setCarryingActor(NULL);

		if (!m_pWorld->isRemoteMode())
			carringActor->setPosition(pos);

		updateBound(180, 60);

		if (hasUIControl())
		{
			//ge GetGameEventQue().postShapeShiftInfo(false);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("state", false);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_SHAPE_SHIFT", sandboxContext);
			}
			if (g_pPlayerCtrl->getViewMode() < CAMERA_TPS_OVERLOOK && !isShapeShift())
			{
				if (g_pPlayerCtrl->m_pCamera)
					g_pPlayerCtrl->m_pCamera->setDistMultiply(1.0f);
				Rainbow::XMLNode rootNode = GetIWorldConfigProxy()->getRootNode();
				Rainbow::XMLNode node = rootNode.getChild("GameData");
				if (!node.isNull())
				{
					Rainbow::XMLNode child = node.getChild("Settinig");
					if (!child.isNull())
					{
						int mode = child.attribToInt("view") - 1;
						g_pPlayerCtrl->setViewMode(mode);
					}
				}
			}
		}
	}
	else if (CarryComp && !iscarrying && actor)
	{

		auto ActorCarryComp = actor->sureCarryComponent();
		if (ActorCarryComp)
		{
			ActorCarryComp->setCarriedActor(this);
		}

		CarryComp->setCarryingActor(actor);

		updateBound(GetLuaInterfaceProxy().get_lua_const()->actor_bound_height, GetLuaInterfaceProxy().get_lua_const()->actor_bound_width);

		if (hasUIControl())
		{
			//ge GetGameEventQue().postShapeShiftInfo(true);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("state", true);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_SHAPE_SHIFT", sandboxContext);
			}
			g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_TPS_BACK);
		}
	}

	if (getBody() && getLivingAttrib())
	{
		getLivingAttrib()->applyEquips(getBody(), EQUIP_WEAPON);
	}

	if (!m_pWorld->isRemoteMode())
	{
		PB_PlayerCarryActorHC playerCarryActorHC;
		if (actor)
			playerCarryActorHC.set_actorid(actor->getObjId());
		else
			playerCarryActorHC.set_actorid(0);
		playerCarryActorHC.set_playeruin(getUin());

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_CARRYACTOR_HC, playerCarryActorHC, this, true);

		ClientMob *mob = dynamic_cast<ClientMob *>(actor);
		if (mob)
			mob->ResetBTree();
	}

	return true;
}

bool ClientPlayer::canHurtActor(ClientActor *target)
{
	LOG_INFO("rent server can hurt Actor in");
	ClientPlayer *player = dynamic_cast<ClientPlayer *>(target);
	// 租赁服 攻击生物 权限判断
	if (ROOM_SERVER_RENT == GetGameInfoProxy()->GetRoomHostType())
	{
		ClientMob* mob = dynamic_cast<ClientMob *>(target);
		LOG_INFO("攻击生物");

		SandboxResult resultCanCSPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
			SandboxContext(nullptr)
			.SetData_Number("uin", getUin())
			.SetData_Number("blockid", 0)
			.SetData_Number("bit", CS_PERMIT_ATTACK_LIVES));
		bool canCSPermitFlag = false;
		if (resultCanCSPermit.IsExecSuccessed())
		{
			canCSPermitFlag = resultCanCSPermit.GetData_Bool();
		}
		if (mob && canCSPermitFlag == 0)
		{
			LOG_INFO("攻击生物提示ʾ");
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_ATTACK_LIVES));
			else
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9639, int(CS_PERMIT_ATTACK_LIVES));
			return false;
		}

		resultCanCSPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
			SandboxContext(nullptr)
			.SetData_Number("uin", getUin())
			.SetData_Number("blockid", 0)
			.SetData_Number("bit", CS_PERMIT_ATTACK_TARGET));
		canCSPermitFlag = false;
		if (resultCanCSPermit.IsExecSuccessed())
		{
			canCSPermitFlag = resultCanCSPermit.GetData_Bool();
		}
		if (player && canCSPermitFlag == 0)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_ATTACK_TARGET));
			else
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9639, int(CS_PERMIT_ATTACK_TARGET));
			return false;
		}
	}
	else
	{
		ClientMob* mob = dynamic_cast<ClientMob *>(target);

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
			SandboxContext(nullptr)
			.SetData_Number("uin", getUin())
			.SetData_Number("itemid", 0)
			.SetData_Number("bit", CS_PERMIT_ATTACK_LIVES));
		bool canPermitFlag = false;
		if (result.IsExecSuccessed())
		{
			canPermitFlag = result.GetData_Bool();
		}
		if (mob && canPermitFlag == 0)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_ATTACK_LIVES));
			else
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9720, int(CS_PERMIT_ATTACK_LIVES));
			return false;
		}

		result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
			SandboxContext(nullptr)
			.SetData_Number("uin", getUin())
			.SetData_Number("itemid", 0)
			.SetData_Number("bit", CS_PERMIT_ATTACK_TARGET));
		canPermitFlag = false;
		if (result.IsExecSuccessed())
		{
			canPermitFlag = result.GetData_Bool();
		}
		if (player && canPermitFlag == 0)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_ATTACK_TARGET));
			else
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9720, int(CS_PERMIT_ATTACK_TARGET));
			return false;
		}
	}
	if (GetWorldManagerPtr()->isGameMakerRunMode())
	{
		if (GetWorldManagerPtr()->m_RuleMgr->getGameStage() != CGAME_STAGE_RUN) return false;

		int opt = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_ATTACKPLAYER);
		if (opt == 1)
		{
			if (player != NULL) return false;//禁止攻击玩家
		}
		else if (opt == 2) //只有队伍间允许攻击
		{
			//ActorLiving *living = dynamic_cast<ActorLiving *>(target);
			//if (living && isSameTeam(living)) return false;
			return true;
		}

		if (!checkActionAttrState(ENABLE_ATTACK))
		{
			notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14005);
			return false;
		}
	}
	else if (GetClientInfoProxy()->getMultiPlayer() > 0)
	{
		LOG_INFO("rent server can hurt actor mp state: %d", GetClientInfoProxy()->getMultiPlayer());
		if (ROOM_SERVER_RENT != GetGameInfoProxy()->GetRoomHostType())
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
				SandboxContext(nullptr)
				.SetData_Number("uin", getUin())
				.SetData_Number("itemid", 0)
				.SetData_Number("bit", CS_PERMIT_ATTACK_TARGET));
			bool canPermitFlag = false;
			if (result.IsExecSuccessed())
			{
				canPermitFlag = result.GetData_Bool();
			}
			if (!canPermitFlag && player != NULL)
			{

				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
					SandboxContext(nullptr).SetData_Number("uin", getUin()));
				bool isHostFlag = false;
				if (result.IsExecSuccessed())
				{
					isHostFlag = result.GetData_Bool();
				}
				if (isHostFlag)
					MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_ATTACK_TARGET));
				else
					MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9720, int(CS_PERMIT_ATTACK_TARGET));
				return false;
			}
		}
		else
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
				SandboxContext(nullptr)
				.SetData_Number("uin", getUin())
				.SetData_Number("blockid", 0)
				.SetData_Number("bit", CS_PERMIT_ATTACK_TARGET));
			bool canCSPermitFlag = false;
			if (result.IsExecSuccessed())
			{
				canCSPermitFlag = result.GetData_Bool();
			}
			if (player != NULL && !canCSPermitFlag)
			{
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
					SandboxContext(nullptr).SetData_Number("uin", getUin()));
				bool isHostFlag = false;
				if (result.IsExecSuccessed())
				{
					isHostFlag = result.GetData_Bool();
				}
				if (isHostFlag)
					MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_ATTACK_TARGET));
				else
					MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9639, int(CS_PERMIT_ATTACK_TARGET));
				return false;
			}
		}
	}

	if (player && player->isInSpectatorMode())
	{
		return false;
	}

	return true;
}

// 2022-01-19 codeby:liusijia 检测交互距离及角度
bool CanInteractWithTarget(ClientPlayer* player, ClientActor* target)
{
	if (dynamic_cast<ActorLiving*>(target) == nullptr)
		return true;
	auto vehicle = dynamic_cast<ActorVehicleAssemble*>(target);
	//可以自定义的载具不进行距离判断
	if (vehicle )
	{
		return true;
	}
	WCoord& pos1 = player->getPosition();
	WCoord& pos2 = target->getPosition();
	float length = pos1.squareDistanceTo(pos2);
	float boundRadius = (target->getLocoMotion()->m_HitBoundWidth / 2);
	float max_length = boundRadius * boundRadius + 640000.0f; // 距离超过8格 //判断距离时加上包围盒半径 code by keguanqiang
	const ItemSkillDef* skilldef = player->getCurItemSkillDef();
	if (skilldef && skilldef->Distance > 0.0001f)
	{
		max_length = skilldef->Distance * skilldef->Distance + 1000;
	}

	if (length > max_length)
	{
#ifdef IWORLD_SERVER_BUILD
		jsonxx::Object check;
		check << "client_target_id" << target->getObjId();
		check << "server_distencesq" << (int)length;
		Rainbow::GetICloudProxyPtr()->InfoLog(player->getUin(), player->getOWID(), "cheat_interact_outrange", check);
#endif
		return false;
	}

	// 角度先不判断
	// float yaw = 0;
	// Direction2PitchYaw(&yaw, nullptr, (pos2 - pos2).toVector3());

	return true;
}

bool ClientPlayer::interactActor(ClientActor* target, int interactType /* = 1 */, bool interactplot /* = false */)
{
	return ClientPlayer::performInteractActor(target, interactType, interactplot, false);
}

//注意！！！ 当参数 onlyCheckAtk 为 true 时，将改变函数原有的逻辑，变为只用于检测是否可攻击的函数。如果该 target 可对话、可交互、可喂食、可驯服 等等，那么就属于不可攻击。
bool ClientPlayer::performInteractActor(ClientActor* target, int interactType, bool interactplot, bool onlyCheckAtk)
{
	if (!CanInteractWithTarget(this, target))
	{
		LOG_INFO("%d interactActor to %d out of range", getUin(), target->getObjId());
		return false;
	}

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorActor",
		SandboxContext(nullptr).SetData_Number("uin", getUin()));
	bool canInteractorActorFlag = false;
	if (result.IsExecSuccessed())
	{
		canInteractorActorFlag = result.GetData_Bool();
	}
	if (!canInteractorActorFlag)
	{
		if (onlyCheckAtk)
			return false;

		if (!m_pWorld->isRemoteMode())
		{
			if (ROOM_SERVER_RENT != GetGameInfoProxy()->GetRoomHostType())
				notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 411);
		}
		//LOG_INFO("ClientPlayer::interactActor(): false 0");
		return false;
	}

	// 家园使用灶台后不可和其他npc交互 code-by: liya  by zuokuan
	if ((GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD) && isUseHearth())
	{
		return false;
	}

	//移动端使用右键点击生效
	if (GetClientInfoProxy()->isMobile())
	{
		int actionStatus = 0;
		SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "IsSocialActionOpen", ">i", &actionStatus);
		//动作互动开关打开后，点击的是玩家就不可交互
		if (actionStatus == 1 && target->getObjType() == OBJ_TYPE_ROLE)
		{
			MINIW::ScriptVM::game()->callFunction("Playmain_OnShowInviteActionBtn", "i", target->getObjId());
		}
	}


	//getBody()->playAttack();
	// 联机不打开手持触发器ui 返回false 不执行后面发消息给主机的操作
	//if (getCurToolID() == ITEM_DEVELOPER && IsInWorld() && GetClientInfoProxy()->getMultiPlayer() > 0)
	//{
	//	//LOG_INFO("ClientPlayer::interactActor(): false mp 0");
	//	return false;
	//}

	if (target != NULL && target->getDefID() == 3020)
	{
		if (onlyCheckAtk)
			return false;

		if ((interactType == 1 || interactType == 2) && hasUIControl())
		{
			// 新版广告商人
			std::string objId = to_string(getObjId());
			MINIW::ScriptVM::game()->callFunction("OpenNewShopAdNpc", "s", objId.c_str());
		}
		if (GetCheatHandler())
			GetCheatHandler()->onInteractADTrader(target->getObjId());
	}
	else if (!m_pWorld->isRemoteMode())
	{
		LivingAttrib* livattr = nullptr;
		if (!onlyCheckAtk)
		{
			livattr = getLivingAttrib();
			if (livattr) {
				livattr->removeBuff(INVULNERABLE_BUFF);
			}

			SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("Homeland_OpenedDialogueHomeNpc", SandboxContext(nullptr).SetData_Userdata("ClientActor", "clientactor", target));
		}

		//TODO MINIW::ScriptVM::game()->setUserTypePointer("OpenedDialogueHomeNpc", "ActorHomeNpc", (dynamic_cast<ActorHomeNpc*>(target) ? target : NULL));

		//0 - attack, 1 - check interact then attack, especially for mobile 2 - only interact
		//LOG_INFO("ClientPlayer::interactActor(): interactType = %d", interactType);
		switch (interactType)
		{
		case 0:
		{
			if (interactplot)
			{
				if (onlyCheckAtk)
					return false;

				if (target && 
					  ((target->GetComponent<PlotComponent>() && target->GetComponent<PlotComponent>()->interactPlot(this)) || interactDialogueDirectUse(MOB_INTERACTPLOTTYPE, target->GetItemId())))
				{
					notifyInteractActor2Tracking(target, 0, interactplot);
					return true;
				}
				else
				{
					//LOG_INFO("ClientPlayer::interactActor(): false 1");
					return false;
				}
				break;
			}
			else
			{
				if (target && target->leftClickInteract(this))
				{
					if (onlyCheckAtk)
						return false;

					return true;
				}
				if (target == NULL)
				{
					//LOG_INFO("ClientPlayer::interactActor(): false 2");
					return false;
				}
				//如果是开发者道具，打开开发者面板
				if (checkDeveloperHandleForActor(true, target, !onlyCheckAtk))
				{
					if (onlyCheckAtk)
						return false;

					return true;
				}

				if (!canHurtActor(target))
				{
					//LOG_INFO("ClientPlayer::interactActor(): false 3");
					return false;
				}

				if (onlyCheckAtk)
					return true;

				notifyInteractActor2Tracking(target, 0, interactplot);
				//复活之后的无敌buff
				if (livattr)
					livattr->removeBuff(INVULNERABLE_BUFF);

				GameMode *gmaker = static_cast<GameMode*>(GetWorldManagerPtr()->m_RuleMgr);
				if (gmaker && target)
				{
					// 观察者事件接口
					attackOnTrigger();
					attackHitOnTrigger(target->getObjId(), target->getDefID());
				}
				return ActorLiving::attackActor(target);
			}
		}
		case 1:
		{
			//如果是开发者道具，打开开发者面板
			if (checkDeveloperHandleForActor(false, target, !onlyCheckAtk))
			{
				if (onlyCheckAtk)
					return false;

				return true;
			}

			if (target && target->interact(this, false, true))
			{
				if (onlyCheckAtk)
					return false;

				notifyInteractActor2Tracking(target, 1, interactplot);
				return true;
			}

			if (interactplot)
			{
				if (target && 
					((target->GetComponent<PlotComponent>() && target->GetComponent<PlotComponent>()->interactPlot(this)) || interactDialogueDirectUse(MOB_INTERACTPLOTTYPE, target->GetItemId())))
				{
					notifyInteractActor2Tracking(target, 1, interactplot);
					//移动端的判断比较特殊，会触发打开对白
					if (onlyCheckAtk)
						return false;

					return true;
				}
			}

			if (!canHurtActor(target))
			{
				//LOG_INFO("ClientPlayer::interactActor(): false 4");
				return false;
			}
			if (target && target->getDefID() == 3021)
			{
				//LOG_INFO("ClientPlayer::interactActor(): false 5");
				return false;
			}

			if (onlyCheckAtk)
				return true;

			notifyInteractActor2Tracking(target, 1, interactplot);
			//复活之后的无敌buff
			getLivingAttrib()->removeBuff(INVULNERABLE_BUFF);

			GameMode *gmaker = static_cast<GameMode*>(GetWorldManagerPtr()->m_RuleMgr);
			if (gmaker && target != NULL)
			{
				// 观察者事件接口
				attackOnTrigger();
				attackHitOnTrigger(target->getObjId(), target->getDefID());
			}

			return ActorLiving::attackActor(target);
		}
		case 2:
			if (onlyCheckAtk)
				return false;

			//如果是开发者道具，打开开发者面板
			if (checkDeveloperHandleForActor(false, target))
			{
				return true;
			}
			if (target != NULL && target->interact(this))
			{
				notifyInteractActor2Tracking(target, 2, interactplot);
				return true;
			}
			else if (interactplot && target != NULL && 
				      ((target->GetComponent<PlotComponent>() && target->GetComponent<PlotComponent>()->interactPlot(this)) || interactDialogueDirectUse(MOB_INTERACTPLOTTYPE, target->GetItemId())))
			{
				notifyInteractActor2Tracking(target, 2, interactplot);
				return true;
			}
			//冰冻扛起暂时屏蔽
			/*else if (target)
			{
				LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(target->getAttrib());
				if (attrib != NULL)
				{
					if (attrib->hasBuff(FORZEN_BUFF))
					{
						carryActor(target);
						return true;
					}
				}
			}*/
			//LOG_INFO("ClientPlayer::interactActor(): false 6");
			return false;
		default:
			//LOG_INFO("ClientPlayer::interactActor(): false 7");
			return false;
		}
	}
	else if (target) 
	{
		auto vehActor = dynamic_cast<ActorVehicleAssemble*>(target);
		if (vehActor && (interactType == 2 || interactType == 1 ))
		{
			if (onlyCheckAtk)
				return false;

			return !vehActor->interactDriverSeat(this);
		}
	}

	//一般到这里就是 客机
	if (onlyCheckAtk)
	{
		switch (interactType)
		{
		case 0: //PC 左键
			return true;
			break;
		case 1: //移动端触屏
		{
			if (!target)
				return false;

			if (GetClientInfoProxy()->isMobile() && m_pWorld->isRemoteMode())
			{
				if (target->IsObject())
				{
					return false;
				}
				//移动端的客机，如果生物有对话，就不能攻击
				MonsterDef* mDef = target->getMonsterDef();
				PlotComponent* plotCom = target->GetComponent<PlotComponent>();
				if ((mDef && (mDef->CanTalk || mDef->CanTame)) || (plotCom && plotCom->checkInteractPlot(this)))
					return false;
				else
					return true;
			}
			break;
		}
		case 2: //PC 右键
			return false;
			break;
		}
	}

	//联机状态直接返回true
	return true;
}

bool ClientPlayer::canAttack()
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerMode())
	{
		return true;
	}
	PlayerAttrib* playerAttrib = getPlayerAttrib();
	if (!playerAttrib->isStrengthEnough(GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_attack))
	{
		GetLuaInterfaceProxy().showGameTips(1571);
		return false;
	}
	return true;
}

bool ClientPlayer::attackActor(ClientActor *target, int seq, int targetIndex) //916ð�� 2021/08/18 codeby:wudeshen
{
	if (!m_pWorld->isRemoteMode())
	{
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorActor",
			SandboxContext(nullptr).SetData_Number("uin",getUin()));
		bool canInteractorActorFlag = false;
		if (result.IsExecSuccessed())
		{
			canInteractorActorFlag = result.GetData_Bool();
		}
		if (!canInteractorActorFlag)
		{
			notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 411);
			return true;
		}
		if (!canHurtActor(target))
		{
			return true;
		}
	}

	if (ActorLiving::attackActor(target, 2, targetIndex))
	{
		ClientPlayer* player = this;  //近战武器加buff  code-by:yanfengying, xiehaijiao
		ActorLiving* living = dynamic_cast<ActorLiving*>(target);
		if (player && living)
		{
			BackPackGrid* grid = player->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
			int toolId = player->getCurToolID();
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(toolId);
			if (toolId != 0 && tooldef != NULL && tooldef->punchBuffId && grid && grid->getDuration() > 0)
			{
				living->getLivingAttrib()->addBuff(tooldef->punchBuffId, tooldef->punchBuffV, -1, 0, getObjId());
			}
		}
		return true;
	}
	return false;
}

bool ClientPlayer::isRidingMutateFlyHorse()
{
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ActorHorse *riding = dynamic_cast<ActorHorse *>(RidComp->getRidingActor());
		if (riding->hasHorseSkill(HORSE_SKILL_MUTATE_FLY))
		{
			return true;
		}
	}
	return false;
}

float ClientPlayer::getHorseEnergy()
{
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ActorHorse *riding = dynamic_cast<ActorHorse *>(RidComp->getRidingActor());
		if (riding)
		{
			return riding->getEnergy();
		}
	}
	return 0;
}

bool ClientPlayer::isHorseTired()
{
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ActorHorse *riding = dynamic_cast<ActorHorse *>(RidComp->getRidingActor());
		if (riding)
		{
			return riding->isTired();
		}
	}
	return false;
}

bool ClientPlayer::canAttackByItemSkill(int skillId, ClientPlayer* player)
{
	const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(skillId);
	if (skilldef)
	{
		return skilldef->TargetID == 0 && skilldef->TargetClass == -1
			&& (skilldef->TargetCamp == 0 || (skilldef->TargetCamp == 1 && getTeam() == player->getTeam()) || (skilldef->TargetCamp == 2 && (getTeam() != player->getTeam() || getTeam() == 0)));
	}
	else
	{
		return false;
	}
}

static void DropArcheologyItems(World *pworld, std::vector<GenerateItemDesc>&items, const WCoord &blockpos)
{
	const int LEN = BLOCK_SIZE / 4;
	for (size_t i = 0; i < items.size(); i++)
	{
		WCoord pos = BlockCenterCoord(blockpos) + WCoord(GenRandomInt(-LEN, LEN), 0, GenRandomInt(-LEN, LEN));
		static_cast<ClientActorMgr*>(pworld->getActorMgr())->spawnItem(pos, items[i].itemid, items[i].itemnum);
	}
}

void ClientPlayer::destroyBlock(const WCoord &pos, DIG_METHOD_T dgmethod, bool destroy_effect, bool gamerule_forbid)
{
	{ //遗落的盒子客机也需要标记破坏方块的玩家
		int blockid = m_pWorld->getBlockID(pos);
		if (blockid == BLOCK_DEATH_JAR)
		{
			WorldContainer* container = m_pWorld->getContainerMgr()->getContainer(pos);
			ContainerDeathJar* deathJar = dynamic_cast<ContainerDeathJar*>(container);
			if (deathJar)
			{
				deathJar->setDestroyByUin(getUin());
			}
		}
	}


	if (m_pWorld->isRemoteMode()) return;

	bool bCanDestroyAllBlocks = GetClientInfoProxy()->IsCurrentUserOuterChecker();
	gamerule_forbid = gamerule_forbid && !bCanDestroyAllBlocks;

	if (g_DisableDestroyEffect) destroy_effect = false;

	int blockid = m_pWorld->getBlockID(pos);
	if (!GetDefManagerProxy()->checkItemCrc(blockid)) return;
	if (gamerule_forbid && blockid != BLOCK_STAR) return;

	std::vector<CSPermitBitType> csPermitType;
	csPermitType.push_back(CS_PERMIT_DESTROY_BLOCK);

	int curToolID = getCurToolID();//code by:tanzhenyu

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorBlock",
		SandboxContext(nullptr)
		.SetData_Number("uin", getUin())
		.SetData_Number("tool", curToolID)
		.SetData_Number("blockid", blockid)
		.SetData_Usertype<std::vector<CSPermitBitType>>("csPermitType",&csPermitType));
	bool canInteractorBlockFlag = false;
	if (result.IsExecSuccessed())
	{
		canInteractorBlockFlag = result.GetData_Bool();
	}
	if (!canInteractorBlockFlag) return;

	if (!checkActionAttrState(ENABLE_DESTROYBLOCK) && !bCanDestroyAllBlocks)
	{
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14003);
		return;
	}

	int blockdata = m_pWorld->getBlockData(pos);

	BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
	const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid, true);

	if (!pmtl) { return; }
	if (m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_DESTROYED) == 0 && !GetWorldManagerPtr()->isGodMode() && pmtl->getDestroyHardness(blockdata, this) < 0 && !bCanDestroyAllBlocks) return;
	if (pmtl && !pmtl->canDestroy(m_pWorld, pos))
		return;

	addAchievement(3, ACHIEVEMENT_DIGITEM, blockid);
	updateTaskSysProcess(TASKSYS_DESTORY_BLOCK, blockid);
	if (m_MineType != BLOCK_MINE_PRECISE && pmtl->hasDestroyScore(blockdata)) addOWScore(def->Score);

	if (m_pWorld && !m_pWorld->isRemoteMode())
		addSFActivity(SFACTIVITY_DIGITEM, blockid, 1, !this->hasUIControl());

	checkNewbieWorldProgress(5, "destroy");
	if (blockid == BLOCK_WOOD_OAK) checkNewbieWorldProgress(7, "lumber");

	if (destroy_effect && m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_DESTROYED) != 0)
	{
		m_pWorld->getEffectMgr()->playBlockDestroyEffect(0, pos*BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2), DIR_NEG_X, 40);
	}

	bool needdropitem = true;
	PLAYER_GENIUS_TYPE genius = getGeniusType();
	if ((genius == GENIUS_ARCHEOLOGY && blockid == BLOCK_STONE) || (genius == GENIUS_SEARCH_JAR && blockid >= 737 && blockid <= 739))
	{
		float extvalues[4];
		if (GenRandomFloat() < getGeniusValue(genius, extvalues))
		{
			std::vector<GenerateItemDesc>items;
			WorldContainerMgr::generateChestItems(items, int(extvalues[0]), NULL, 1);
			DropArcheologyItems(m_pWorld, items, pos);
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_ROLECOLLECT);
			}
			if (genius == GENIUS_ARCHEOLOGY) needdropitem = false;
		}
	}

	int realBpItemId = 0; //真正的家园蓝图id
	bool isHomeland = (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD);
	if (blockid == BLOCK_CRYSTAL)
	{
		int step = GetClientInfoProxy()->getCurGuideStep();
		if (step == 9 || step == 15)
			checkNewbieWorldProgress(1, step);

		if (step == 15)
		{
			needdropitem = false;
			getBackPack()->addItem(pmtl->getBlockToolMineDropId(0), 1);//def->ToolMineDrops[0].item, 1);
		}
	}
	else if (g_pPlayerCtrl && blockid == BLOCK_PLANTSPACE_JEWS)
	{
		char sBlockID[8];
		sprintf(sBlockID, "%d", blockid);
		//g_pPlayerCtrl->statisticToWorld(getUin(), 30008, "", g_pPlayerCtrl->getCurWorldType(), sBlockID);
	}
	else if (blockid == BLOCK_BUILDBULEPRINT && isHomeland)
	{
		//家园蓝图拿出来真正的id
		ContainerBuildBluePrint *bpContainer = dynamic_cast<ContainerBuildBluePrint *>(m_pWorld->getContainerMgr()->getContainer(pos));
		if (bpContainer)
		{
			std::string datastr = bpContainer->getBPDataStr();
			jsonxx::Object jsonData;
			if (jsonData.parse(datastr))
			{
				if (jsonData.has<jsonxx::Number>("realitemid"))
					realBpItemId = jsonData.get<jsonxx::Number>("realitemid");
			}
		}
	}

	ObserverEvent_ActorBlock obevent((long long)getObjId(), pmtl->getBlockResID(), pos.x, pos.y, pos.z);
	GetObserverEventManager().OnBlockEvent(CE_OnBlockDestroyBy, &obevent);

	bool sucDestroy = false; //是否成功销毁
	if (needdropitem) {
		float fAddRadio = getPlayerAttrib()->getRandomAttValueWithStatus(BuffAttrType::BUFFATTRT_GOODLUCK_DIG);
		if (fAddRadio > 0.001f) {
			BlockMaterial::m_DigLuckBuff = (int)fAddRadio;
		}
		//增加传入参数:使用工具id 新增需求:掉落规则与使用工具等级不同 掉率不同//code by:tanzhenyu
		sucDestroy = m_pWorld->playerDestroyBlock(pos, BLOCK_MINE_TOOLFIT, getLivingAttrib()->getDigProbEnchant(), curToolID, getUin());
		BlockMaterial::m_DigLuckBuff = 0;
	}
	else
	{
		//增加传入参数:使用工具id 新增需求:掉落规则与使用工具等级不同 掉率不同//code by:tanzhenyu
		sucDestroy = m_pWorld->playerDestroyBlock(pos, BLOCK_MINE_NONE, 0, curToolID, getUin());
	}

	if (sucDestroy)
	{
		BIOME_TYPE biome_type = m_pWorld->getBiomeType(pos.x, pos.z);
		if (biome_type == BIOME_OCEAN || biome_type == BIOME_DEEPOCEAN)
		{
			int point = 10;
			if (getLivingAttrib()->hasBuff(1028))
			{
				point = 5;
			}
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("spawnSharkPoint", MNSandbox::SandboxContext(nullptr).SetData_Number("point", point));
		}
	}

	if (sucDestroy && isHomeland)
	{
		//��԰�õ�һ������ �ͼӻص��ɲ�����������ȥ
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_AddBackpackItem",
				SandboxContext(nullptr).
				SetData_Number("blockId", blockid).
				SetData_Number("blockdata", blockdata).
				SetData_Number("realitemId", realBpItemId)
			);
	}

	if (sucDestroy) //破坏方块-皮肤加成 
	{
		int attrValue = 0;
		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != NULL)
		{
			// 皮肤加成 -- 恢复X点体力值/饥饿值 
			const char* attrType = "WeaponSkin_System_PhysicalPower";
			attrValue = (int)weaponSkinMgr->GetSkinAdditionValue(attrType, this);
		}

		if (attrValue > 0) //概率恢复 
		{
			PlayerAttrib* playerAttrib = getPlayerAttrib();
			if (playerAttrib != NULL)
			{
				if (playerAttrib->strengthFoodShowState() != SFS_Empty)
				{
					if (playerAttrib->useCompatibleStrength()) // 使用的是体力 
					{
						playerAttrib->addStrength(attrValue);
					}
					else //恢复饥饿度 
					{
						int curFoodLevel = playerAttrib->getFoodLevel();
						playerAttrib->setFoodLevel(curFoodLevel + attrValue);
					}
				}
			}
		}
		PlayerAttrib* playerAttrib = getPlayerAttrib();//挖掘方块固定消耗体力strength_consumption_of_digging_block_min
		if (playerAttrib != NULL)
		{
			if (playerAttrib->strengthFoodShowState() != SFS_Empty)
			{
				if (playerAttrib->useCompatibleStrength()) // 使用的是体力 
				{
					//- 挖掘体力降低：降低挖掘时的体力消耗，百分比数值 renjie
					float strDec=playerAttrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_DIGGING_STRENGTH_DEC, ATTACK_ALL, ATTACK_TARGET_ALL);
					float countStr = (float)GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_digging_block_min * strDec;
					float finalStr = -1 * (float)GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_digging_block_min + countStr;
					playerAttrib->addStrength(finalStr);
				}
			}
		}
	}

	MNSandbox::GetGlobalEvent().Emit<>("WorldArchiveMgr_OnBlockChangedByManual");

	if (destroy_effect)
	{
		playBlockDigSound(blockid, pos.x, pos.y, pos.z);
		//WCoord centerpos = BlockCenterCoord(pos);
		//if (!def->DigSound.empty()) m_pWorld->getEffectMgr()->playSound(centerpos, def->DigSound.c_str(), GSOUND_DESTROY);
	}

	pmtl->DoOnBlockDestroyedBy(m_pWorld, pos, blockid, blockdata, BLOCK_DESTROY_PLAYER, this);
	// 观察者事件接口
	GetObserverEventManager().OnTriggerEvent("Block.DestroyBy", &obevent);

	// 用来保存改变的block坐标
	std::vector<WCoord> changeBlocksPos;
	changeBlocksPos.push_back(pos);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_resetWorkshopConnectCoreBlock",
		SandboxContext(nullptr)
		.SetData_UserObject("changeBlocksPos", changeBlocksPos));

	if (m_OperateTicks > 0)
	{
		int method_factor = dgmethod == DIG_METHOD_NORMAL ? 1 : 2;

		getPlayerAttrib()->useStamina(STAMINA_DESTROYBLOCK, (float)method_factor);
		BackPackGrid *pgrid = getBackPack()->index2Grid(getCurShortcut() + getShortcutStartIndex());
		if (pgrid->def)
		{
			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(pgrid->def->ID);
			if (tooldef)
			{
				if (!(dgmethod == DIG_METHOD_MULTI && pmtl->getBlockHardness() < 1))//def->Hardness < 1))
				{

					int duration = -(tooldef->CollectDuration * method_factor);
					/* 策划说保留逻辑后续可能会改 by:chenhuaguang 20240106
					//当前即将破坏区域所有树状破坏所需消耗总和的【80%】
					if (dgmethod == DIG_METHOD_CHARGE || dgmethod == DIG_METHOD_MULTI)
					{
						duration *= 1; //
					}
					*/
					addCurToolDuration(duration);
				}
			}
		}

		if (m_MineType == BLOCK_MINE_TOOLFIT && pmtl->getBlockDropMineExp()/*def->DropExp*/ && GenRandomInt(10000) < pmtl->getBlockDropMineExpProb()/*def->DropExpOdds*/)
		{
			/*	if (GetClientInfoProxy()->getFcmRate() == 0)
			{
			GetGameEventQue().postInfoTips(3692);
			return;
			}*/
			//ActorExpOrb::SpawnExpOrb(m_pWorld, def->DropExp, pos*BLOCK_SIZE, WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
			//挖掉方块不掉落经验球, 改成直接加经验
			OnGainedExp(pmtl->getBlockDropMineExp()/*def->DropExp*/);
		}
	}

	//�����鲻�ò����ƻ������������ҽ� code by: yangjie
	if (blockid == BLOCK_COAGULATION && !IsShovelID(curToolID))
	{
		m_pWorld->setBlockAll(pos, BLOCK_FLOW_LAVA, 0, 3);

		//�ƻ������� ����ҽ���ʱ�� �Ͳ�����Ч
		WCoord effectpos = pos * BLOCK_SIZE + WCoord(50, 100, 50);
		m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/item_yanjiang.ent", effectpos, 100);
	}
	
	//武器熟练度 破坏方块新增对应熟练度 code-by:lizi 
	int weaponType = 0;
	SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("WeaponSkin_HelperModule", "GetOwndSkinItemType", "ii>i", getUin(), curToolID, &weaponType);
	if (weaponType == 6 || weaponType == 7 || weaponType == 8) //斧头镐子铲子的破坏方块计算在内 
	{
		int pointType = WEAPON_SKILLED_TYPE_DIGBLOCK; //镐子 
		if (weaponType == 7) pointType = WEAPON_SKILLED_TYPE_CUTTREE; //斧头 
		if (weaponType == 8) pointType = WEAPON_SKILLED_TYPE_PLANTLNAD;//铲子 
		this->addWeaponSkilledPoint(pointType, curToolID);
	}

	//// 许愿星方块，被挖掉以后尝试隐藏特效
	//if (blockid == 200430 && g_WorldMgr && g_WorldMgr->GetSandboxMgrOrCreate("MeteorShowerMgr"))
	//{
	//	MeteorShowerManager* pMeteorShowerMgr = static_cast<MeteorShowerManager*>(g_WorldMgr->GetSandboxMgrOrCreate("MeteorShowerMgr"));
	//	if (pMeteorShowerMgr->isActive() && pMeteorShowerMgr->isStartGenerated())
	//	{
	//		pMeteorShowerMgr->StopStarEffect();
	//	}
	//}

	/*
	if(!GetClientInfoProxy()->isMobile())
	{
	char buffer[256];
	sprintf(buffer, "BlockID=%d, CalTime=%.2fs, RealTime=%.2fs", blockid, m_DestroyTotalTicks*50/1000.0f, (Rainbow::Timer::getSystemTick()-s_BlockOpBeginTime)/1000.0f);

	GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(buffer);
	}*/
}

bool CanActivateBlock(int blockid, int toolid)
{
	int toolids[] = { 725, 729, 717, 839, 801, 802, 969, 974, 979,BLOCK_HARDWIRE_B, BLOCK_HARDWIRE_R, 555, 556, 798, 799, 1180, 1181 ,1229,1230 , 369, 150023 };
	int blocks[] = { 717, 839, 801, 802, 968, 973, 978, 969, 974, 979 , 555, 556, 1045, 798, 799, 1180, 1181,1229,1230 , 369, 150023, ITEM_POLAROID_RARE_FRAME, ITEM_POLAROID_FRAME, ITEM_POLAROID_INNER_FRAME };

	//手持软皮革 不能触发帐篷睡觉功能，而是调用脚本修复帐篷 
	if (blockid == BLOCK_CANVAS && toolid == 11307)
	{
		return false;
	}

	BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);

	//��Ϲ�
	if (toolid == blockid && pmtl && pmtl->m_TypeName.compare("chest") == 0)
	{
		return false;
	}

	//������Ϲ�
	if (toolid == blockid && pmtl && pmtl->m_TypeName.compare("bigchest") == 0)
	{
		return false; // ������֮���ٷŴ�����
	}

	if (blockid == 0) return false;
	else
	{
		for (int i = 0; i < sizeof(toolids) / sizeof(int); i++)
		{
			if (toolid == toolids[i])
			{
				for (int j = 0; j < sizeof(blocks) / sizeof(int); j++)
				{
					if (blockid == blocks[j]) return false;
				}
			}
		}
	}

	if (blockid >= 690 && blockid <= 699 && (toolid >= 690 && toolid <= 699 || toolid == BLOCK_SIGNS_STAND) && toolid != blockid) return false;

	if ((blockid >= 702 || blockid <= 705) && (toolid == BLOCK_HARDWIRE_B || toolid == BLOCK_HARDWIRE_R)) return false;

	if (blockid == BLOCK_BOOK_CABINET && (toolid != ITEM_BOOK && toolid != BLOCK_AIR)) return false;

	if (toolid == ITEM_CUSTOMMODELPACKING_TOOL && CustomModelPacking::GetInstance().isStartPacking()) return false;

	// �����ý����Ȩ�޸�ֵ���������
	//if (GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_BLOCKUSE) == 0)
	//	return false;

	if (blockid == toolid && pmtl && pmtl->GetBlockScriptComponent())
	{
		return false;
	}

	return true;
}

bool ClientPlayer::triggerBlock(const WCoord& blockpos, DirectionType targetface, const Rainbow::Vector3f& colpoint)
{
	int toolid = getCurToolID();
	int blockid = m_pWorld->getBlockID(blockpos);

	auto CarryComp = getCarryComponent();
	bool iscarrying = false;
	if (CarryComp)
	{
		iscarrying = CarryComp->isCarrying();
	}

	//块有行为
	if (CanActivateBlock(blockid, toolid) && checkActionAttrState(ENABLE_OPERATEBLOCK) && !iscarrying)
	{
		BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
		if (pmtl && m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_BEOPERATED) > 0 && pmtl->DoOnTrigger(m_pWorld, blockpos, targetface, this, colpoint))
		{
			if (m_pWorld && !m_pWorld->isRemoteMode()) {
				// 观察者事件接口
				ObserverEvent_ActorBlock obevent((long long)getUin(), pmtl->getBlockResID(), blockpos.x, blockpos.y, blockpos.z);
				GetObserverEventManager().OnTriggerEvent("Block.Trigger", &obevent);
			}

			setLastTriggerBlock(blockpos);
			// getBody()->playAttack();
			addAchievement(1, ACHIEVEMENT_INTERACT_BLOCK, blockid); //产生实际的交互时，触发冒险成就任务 code_by:huangfubin
			updateTaskSysProcess(TASKSYS_INTERACT_BLOCK, toolid, blockid);
			// 巨人雕像和神秘图腾，触发的时候，不走对话系统 code-by:liya 2023.2.27
			if (blockid != BLOCK_STONE_MONUMENT && blockid != BLOCK_PLANTSPACE_TOTEM)
			{
				interactDialogueDirectUse(BLOCK_INTERACTPLOTTYPE, blockid, blockpos);//对话系统 code_by:shipeng
			}
			return true;
		}
	}
	return false;
}

bool ClientPlayer::interactBlock(const WCoord &blockpos, DirectionType targetface, const Rainbow::Vector3f &colpoint)
{
    OPTICK_EVENT();
	//if (m_pWorld->isRemoteMode())
	//{
	//	//客机看主机摆块，播放动作
	//	getBody()->playAttack();
	//	return true;
	//}

	//getLivingAttrib()->removeBuff(INVULNERABLE_BUFF);

	auto pre_placepos = NeighborCoord(blockpos, targetface);

	//bool b =  m_pWorld->CanBuildAtPosition(pre_placepos, getUin());

	int toolid = getCurToolID();
	int blockid = m_pWorld->getBlockID(blockpos);

	//地形编辑在使用的时候，其他道具不能使用
	SandboxResult result1 = SandboxEventDispatcherManager::GetGlobalInstance().Emit("MapEditManager_interactBlock",SandboxContext(nullptr)
		.SetData_Number("toolid", toolid)
		.SetData_Usertype("player",this));
	if (result1.IsExecSuccessed())
	{
		if (result1.GetData_Bool())
		{
			return true;
		}
	}
	// 手持连线钳 先返回
	if (blockid == ITEM_VEHICLE_LINK_TOOL)
	{
		return true;
	}
	//如果是开发者道具，打开开发者面板
	if (checkDeveloperHandleForBlock(false, blockid, blockpos.x, blockpos.y, blockpos.z))
	{
		return true;
	}

	//如果是触发器区域道具
	if (toolid == 1152)
	{
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerToolMode())
		{
			return true;
		}
	}
	std::vector<CSPermitBitType> csPermitType;
	csPermitType.push_back(CS_PERMIT_PLACE_BLOCK);
	//没有Interact的权限

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorBlock",
		SandboxContext(nullptr)
		.SetData_Number("uin", getUin())
		.SetData_Number("tool", toolid)
		.SetData_Number("blockid", blockid)
		.SetData_Usertype<std::vector<CSPermitBitType>>("csPermitType",&csPermitType));
	bool canInteractorBlockFlag = false;
	if (result.IsExecSuccessed())
	{
		canInteractorBlockFlag = result.GetData_Bool();
	}
	if (!canInteractorBlockFlag)
	{
		// getBody()->playAttack();
		if (!m_pWorld->isRemoteMode()) {
			notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 9077);
			/*
			// canInteractorActor 已无实质内容, 此处屏蔽 2022.10.12 by huanglin
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorActor",
				SandboxContext(nullptr).SetData_Number("uin", getUin()));
			bool canInteractorActorFlag = false;
			if (result.IsExecSuccessed())
			{
				canInteractorActorFlag = result.GetData_Bool();
			}
			if (!canInteractorActorFlag) {
				notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 411);
			}
			else { //道具被禁用
				notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
			}
			*/
		}
		return true;
	}

	// 家园使用灶台后不可点击方块 code-by: liya
	bool isHomeland = (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD);
	if (isHomeland && isUseHearth())
	{
		return true;
	}

	SandboxContext context;
	context.SetData_Number("blockid", blockid);
	context.SetData_UserObject("blockpos", blockpos);
	Event().Emit("interactBlock", context);
	if (blockid == 758)
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("OPEN_BLOCK_BOX", SandboxContext(nullptr).SetData_Number("blockid", blockid).SetData_UserObject("blockpos", blockpos));
	}

	notifyInteractBlock2Tracking(blockpos, targetface);

	auto CarryComp = getCarryComponent();
	bool iscarrying = false;
	if (CarryComp)
	{
		iscarrying = CarryComp->isCarrying();
	}

#ifdef IWORLD_SERVER_BUILD

	//领地柜检查放置权限
	if (!m_pWorld->CanBuildAtPosition(pre_placepos, getUin(), blockid))
	{
		//char content[256];
		//sprintf(content, "%s", "You Are Not allow place block here");
		/*	PB_ChatHC chatHC;
			chatHC.set_chattype(5);
			chatHC.set_content(content);
			chatHC.set_speaker("");
			chatHC.set_uin(0);
			GetGameNetManagerPtr()->sendToClient(getUin(), PB_CHAT_HC, chatHC, 0, false);*/
		//notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 0, 0, content);
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000200);
		return false;
	}
#endif

	//  20210712: 家园需要过滤方块对话交互  codeby:yangzhenyu 
	if (!isHomeland)
	{
		//触发对话系统禁止摆块code_by:shipeng
		if (interactDialogueDirectUse(BLOCK_INTERACTPLOTTYPE, blockid, blockpos)) return true;
	}

	const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(toolid);
	if (toolid != 0 && itemdef)
	{
		//判断一下 家园地图 玩法模式下 使用创造背包的物品 提前拦截提示
		SandboxResult checkret = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_CheckItemCanUse",
				SandboxContext(nullptr).
				SetData_Number("toolid", toolid).
				SetData_Userdata("ClientPlayer", "clientplayer", this));
		if (checkret.IsSuccessed())
		{
			return false;
		}

		//定义了脚本
		if (!itemdef->UseScript.empty() && (itemdef->UseTarget == ITEM_USE_CLICKBLOCK || itemdef->UseTarget == ITEM_USE_CLICKLIQUID || itemdef->UseTarget == ITEM_USE_PRESSFOOD) && !iscarrying)
		{
			// getBody()->playAttack();
			if (m_pWorld->isRemoteMode())
			{
				//客机添加对应的 ITEM_TITANIUM_BUCKET 返回false 让客机的逻辑可以在上层继续执行下去
				if (IsAbleUseShovel(toolid) || IsAbleUseHoelID(toolid) || IsBucketID(toolid))
				{
					return false;
				}

				//客机看主机摆块，播放动作
				return true;
			}

			if (!checkActionAttrState(ENABLE_OPERATEBLOCK))
			{
				notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14002);
				return true;
			}

			bool scripthandled = false;
			int setBlockAllRet = 0;//脚本中使用setBlockAll是否成功返回值。返回值0为修改了block数据（setBlockAll,setBlockData, placeBlock等调用成功），1为没有修改，2,3为其他(0和1返回值是在使用的)

			BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
			if (pmtl && m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_BEOPERATED) == 0) { return false; }
			WCoord tmpPos = blockpos;
			DirectionType tmpface = targetface;
			if (pmtl && pmtl->getBlockResID() == BLOCK_TALL_GRASS)
			{
				tmpPos = DownCoord(tmpPos);
				tmpface = DIR_POS_Y;
			}
			//家园--放置生物蛋需要验证chunk是否准确 
			if (isHomeland)
			{
				SandboxResult eggUseRet = SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("Homeland_eggOnUse",
						SandboxContext(nullptr).SetData_String("script", itemdef->UseScript.c_str()).
						SetData_UserObject("tmpPos", tmpPos).
						SetData_Number("toolid", toolid).
						SetData_Number("tmpface", tmpface).
						SetData_Userdata("ClientPlayer", "clientplayer", this));
				if (eggUseRet.IsSuccessed())
				{
					scripthandled = eggUseRet.GetData_Bool("scripthandled");
					setBlockAllRet = eggUseRet.GetData_Number("setBlockAllRet");
					return eggUseRet.GetData_Bool("ret");
				}

			}

			//生物蛋放置-存储数据初始化 code-by:lizb
			std::string itemuse = "MobEgg_OnUse";
			if (itemuse.compare(itemdef->UseScript.c_str()) == 0)
			{
#ifdef DEDICATED_SERVER
				if (!GetICloudProxyPtr()->GetCanUseMobEgg())
				{
					GetICloudProxyPtr()->SimpleErrLog(getUin(), 0, "use_mob_egg", "server not allow use mob egg");
					return false;
				}
#endif

				double objId = 0; //生物蛋使用需获取到对应生物的objId
				BackPackGrid *pCurTool = getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
				std::string userdataStr = pCurTool == NULL ? "" : pCurTool->getUserdataStr();

				MINIW::ScriptVM::game()->callFunction(itemdef->UseScript.c_str(), "u[ClientPlayer]u[World]iiii>bid", this, getWorld(), tmpPos.x, tmpPos.y, tmpPos.z, tmpface, &scripthandled, &setBlockAllRet, &objId);
				if (!userdataStr.empty() && objId > 0)
				{
					jsonxx::Object jsonObj;
					if (jsonObj.parse(userdataStr))
						this->initMobByEggData((long long)objId, jsonObj);
				}
			}
			else
			{
				if (isFishNeedUp() && isInWater())//当人物在水下放置海洋生物时，生物坐标为人物面朝方向前两格
				{
					Rainbow::Vector3f vec;
					PitchYaw2Direction(vec, getFaceYaw(), getFacePitch());
					vec *= 200;
					WCoord pos;
					pos = getPosition();
					tmpPos.x = (vec.x + pos.x) / 100;
					tmpPos.y = pos.y / 100;
					tmpPos.z = (vec.z + pos.z) / 100;
				}
				if (itemdef->ItemGroup == 11500 && m_pWorld) //颜料瓶之类道具
				{
					TriggerBlockAddRemoveDisable Tmp2(m_pWorld); //染色方块不多次触发创建和删除
					MINIW::ScriptVM::game()->callFunction(itemdef->UseScript.c_str(), "u[ClientPlayer]u[World]iiii>bi", this, getWorld(), tmpPos.x, tmpPos.y, tmpPos.z, tmpface, &scripthandled, &setBlockAllRet);
				}
				else
				{
					MINIW::ScriptVM::game()->callFunction(itemdef->UseScript.c_str(), "u[ClientPlayer]u[World]iiii>bi", this, getWorld(), tmpPos.x, tmpPos.y, tmpPos.z, tmpface, &scripthandled, &setBlockAllRet);
				}
			}

			if (scripthandled)
			{
				if (setBlockAllRet != 1)
				{
					updateTaskSysProcess(TASKSYS_INTERACT_BLOCK, toolid, blockid);
				}
				WorldContainer*  container = NULL;
						
				WCoord workshoppos;
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_getInWorkshopPos",
					SandboxContext(nullptr));
				if (result.IsExecSuccessed())
				{
					workshoppos = result.GetData_UserObject<WCoord>("m_InWorkshopPos");
				}

				container = m_pWorld->getContainerMgr()->getContainer(workshoppos);

				ContainerWorkshop* workshopContainer = dynamic_cast<ContainerWorkshop*>(container);
				if (workshopContainer&&workshopContainer->checkIfBlockInWorkshop(blockpos))
				{
					workshopContainer->calculateConnectMap();
				}

				//这里加个事件。床需要记录放置者,就不继续像后面那一坨裸id记录了
				SandboxContext context;
				context.SetData_Number("blockid", toolid);
				context.SetData_UserObject("blockpos", WCoord(tmpPos.x, tmpPos.y + 1, tmpPos.z));
				Event().Emit("interactBlockEnd", context);

				//村庄图腾需要记录放置者
				if (toolid == 1202)
				{
					BlockVillageTotem::OnRecordBlockOwner(this, getWorld(), WCoord(tmpPos.x, tmpPos.y + 1, tmpPos.z));
				}
				else if (toolid == 150000)
				{
					BlockVillageTotemIce::OnRecordBlockOwner(this, getWorld(), WCoord(tmpPos.x, tmpPos.y + 1, tmpPos.z));
				}
				else if (toolid >= 1203 && toolid <= 1205 || toolid == 150002)
				{
					if (getWorld()->getBlockID(tmpPos) == toolid)
					{
						BlockVillagerFlag::OnRecordBlockOwner(this, getWorld(), WCoord(tmpPos.x, tmpPos.y, tmpPos.z), toolid);
					}
					else 
					{
						BlockVillagerFlag::OnRecordBlockOwner(this, getWorld(), WCoord(tmpPos.x, tmpPos.y + 1, tmpPos.z), toolid);
					}
				}
				else if (toolid == 150004 || toolid == 150005)
				{
					BlockVillagerFlagBuilding::OnRecordBlockOwner(this, getWorld(), WCoord(tmpPos.x, tmpPos.y + 1, tmpPos.z), toolid);
				}
				else if (151606 == toolid || toolid == 2411)
				{
					BlockTerritory::OnRecordBlockOwner(this, getWorld(), WCoord(tmpPos.x, tmpPos.y + 1, tmpPos.z));
				}

				// 检查是否为床方块类型，如果是则调用DoOnBlockPlacedBy
				const BlockDef* blockdef = GetDefManagerProxy()->getBlockDef(toolid);
				if (blockdef && blockdef->Type == "bed")
				{
					BlockMaterial* bedMtl = g_BlockMtlMgr.getMaterial(toolid);
					if (bedMtl)
					{
						// 检查床是否被放置在tmpPos (替换了可替换方块如雪方块115) 或 tmpPos.y+1 (放置在方块上方)
						WCoord placePos;
						if (getWorld()->getBlockID(tmpPos) == toolid)
						{
							placePos = WCoord(tmpPos.x, tmpPos.y, tmpPos.z);
						}
						else
						{
							placePos = WCoord(tmpPos.x, tmpPos.y + 1, tmpPos.z);
						}
						
						// 确认方块确实被成功放置了才执行DoOnBlockPlacedBy
						if (getWorld()->getBlockID(placePos) == toolid)
						{
							bedMtl->DoOnBlockPlacedBy(getWorld(), placePos, this, 0, Rainbow::Vector3f::zero, false, 0);
						}
					}
				}

				SandboxResult checkBlockRet = SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("Homeland_checkEditBlock",
						SandboxContext(nullptr).SetData_UserObject("blockPos", tmpPos).SetData_Number("iSetBlockAllRet", setBlockAllRet));

				if (1 == setBlockAllRet && checkBlockRet.IsSuccessed())
				{
					// 				【准星放置】在非自建区域放置，提示非搭建区域无法编辑——stringid：41610
					// 				【准星放置】在自建区域，但放置的方块其他格数在非自建区域或未升级区域，提示放置超出范围——stringid：41611
					// 				【准星放置】在未升级的自建区域，提示扩建家园后才能搭建——stringid：41609
					return false;
				}
				return true;
			}
			else if ((toolid >= 1202 && toolid <= 1205) || (toolid >= 150000 && toolid <= 150006)) //村庄图腾和旗帜特殊处理
			{
				return false;
			}
			else if (toolid == 226 || toolid == 227 || toolid == 465) //蘑菇使用未成功后不能放置 code-by:DemonYan
			{
				return false;
			}
			//else if (toolid == 115)  //积雪使用未成功后不能放置 code-by:keguanqiang
			//{
			//	return false;
			//}
		}
		{
			BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
			if (pmtl && m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_BEOPERATED) == 0) { return false; }
			WCoord tmpPos = blockpos;
			DirectionType tmpface = targetface;
			if (pmtl && pmtl->getBlockResID() == BLOCK_TALL_GRASS)
			{
				tmpPos = DownCoord(tmpPos);
				tmpface = DIR_POS_Y;
			}
			if (isInWater())//当人物在水下放置海洋生物时，生物坐标为人物面朝方向前两格
			{
				Rainbow::Vector3f vec;
				PitchYaw2Direction(vec, getFaceYaw(), getFacePitch());
				vec *= 200;
				WCoord pos;
				pos = getPosition();
				tmpPos.x = (vec.x + pos.x) / 100;
				tmpPos.y = pos.y / 100;
				tmpPos.z = (vec.z + pos.z) / 100;
			}
			ScriptComponent* pComponent = this->getScriptComponent();
			if (nullptr != pComponent)
			{
				pComponent->OnCustomEvent("Player.UseItemScriptEvent", true, tmpPos.x, tmpPos.y, tmpPos.z, toolid, blockid);
			}
		}
		//摆块
		if (toolid < SOC_BLOCKID_MAX/* || (toolid >= EX_BLOCKID_MIN && toolid <= EX_BLOCKID_MAX)*/)//SOC 方块ID 范围 0-4095 不支持扩展id
		{
			if (this == g_pPlayerCtrl)
			{
				WCoord placepos;
				DirectionType hitface; //相对于placepos那个block
				BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
				hitface = ReverseDirection(targetface);
				if (pmtl && pmtl->isReplaceable() && toolid != blockid)
				{
					placepos = blockpos;
					hitface = DIR_NEG_Y;
				}
				else if (pmtl && pmtl->canPlacedAgain(m_pWorld, toolid, colpoint, blockpos, true, hitface))
				{
					placepos = blockpos;
				}
				else
				{
					placepos = NeighborCoord(blockpos, targetface);
					if (!m_pWorld)
					{
						return false;
					}
					BlockMaterial* blockMat = m_pWorld->getBlockMaterial(placepos);
					if (!blockMat)
					{
						return false;
					}
					if (!blockMat->isReplaceable()
						&& !m_pWorld->getBlockMaterial(placepos)->canPlacedAgain(m_pWorld, toolid, colpoint, blockpos, false, hitface))
					{
						return false;
					}
				}

				int iRet = -100;
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_checkIfCanPlaceBlockInWorkShop",
					SandboxContext(nullptr)
					.SetData_Userdata("ClientPlayer", "player", this)
					.SetData_Number("toolid", toolid)
					.SetData_UserObject("placepos", placepos));
				if (result.IsExecSuccessed())
				{
					iRet = result.GetData_Number("iRet");
				}
				if (iRet != -100)
				{
					getBody()->playAttack();
					return false;
				}
			}

			if (toolid == BLOCK_BASKETFRAME && g_pPlayerCtrl && !g_pPlayerCtrl->checkIfDataUpload(Upload_BasketBallFrame_Add)) {
				std::stringstream sParam1;
				sParam1 << m_pWorld->getOWID();

				//g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 30032, "", g_pPlayerCtrl->getCurWorldType(), "param_to_str", sParam1.str().c_str());
				g_pPlayerCtrl->setUploadStatus(Upload_BasketBallFrame_Add);
			}

			if (m_pWorld->isRemoteMode())
			{
				//客机看主机摆块，播放动作
				getBody()->playAttack();
				return true;
			}

			/*if (g_pPlayerCtrl && (toolid == 1067))
			{
				g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 30002, "", g_pPlayerCtrl->getCurWorldType(), "1067");
			}*/

			/*if (g_pPlayerCtrl && (toolid == 1081))
			{
				g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 30002, "", g_pPlayerCtrl->getCurWorldType(), "1081");
			}*/

			if (g_pPlayerCtrl && (toolid == BLOCK_TRANSFERCORE))
			{
				auto worldDesc = GetClientInfoProxy()->getCurWorldDesc();
				if (worldDesc)
				{
					char sWorldId[64];
					sprintf(sWorldId, "%lld", worldDesc->worldid);
					//g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 61000, "", g_pPlayerCtrl->getCurWorldType(), sWorldId);
				}
			}

			if (g_pPlayerCtrl && (toolid == BLOCK_TRANSFER))
			{
				auto worldDesc = GetClientInfoProxy()->getCurWorldDesc();
				if (worldDesc)
				{
					char sWorldId[64];
					sprintf(sWorldId, "%lld", worldDesc->worldid);
					//g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 61001, "", g_pPlayerCtrl->getCurWorldType(), sWorldId);
				}
			}

			if (g_pPlayerCtrl)
			{
				auto worldDesc = GetClientInfoProxy()->getCurWorldDesc();
				if (worldDesc)
				{
					if (toolid == BLOCK_BOOK_EDITOR)
					{
						char sWorldId[64];
						sprintf(sWorldId, "%lld", worldDesc->worldid);
						//g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 61010, "", g_pPlayerCtrl->getCurWorldType());
					}

					/*if (toolid == BLOCK_BOOK_CABINET)
					{
						g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 61015, "", g_pPlayerCtrl->getCurWorldType());
					}*/
				}
			}

			getBody()->playAttack();

			if (GetWorldManagerPtr()->isGameMakerRunMode())
			{
				// 将设置界面的权限赋值到玩家身上
				//if (GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_BLOCKPLACE) == 0)
				//	return false;

				if (!checkActionAttrState(ENABLE_PLACEBLOCK))
				{
					notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14001);
					return true;
				}
			}

			WCoord placepos;
			DirectionType hitface; //相对于placepos那个block
			BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
			bool isCover = false;
			bool isagain = false;
			bool placeInto = false;
			hitface = ReverseDirection(targetface);

			if (pmtl && pmtl->isReplaceable() && toolid != blockid)
			{
				placepos = blockpos;
				hitface = DIR_NEG_Y;
			}
			else if (pmtl && pmtl->canPlacedAgain(m_pWorld, toolid, colpoint, blockpos, true, hitface))
			{
				placepos = blockpos;
				isagain = true;
				placeInto = true;
			}
			else
			{
				std::vector<int> sandCoverId = GetLuaInterfaceProxy().get_lua_const()->sandCoverId;
				auto iter = std::find(sandCoverId.begin(), sandCoverId.end(), blockid);
				if (iter != sandCoverId.end() && (toolid == BLOCK_SOLIDSAND || toolid == BLOCK_REDSAND))//红沙，新黄沙 可以被埋物品
				{
					placepos = blockpos;
					isCover = true;
				}
				else
				{
					placepos = NeighborCoord(blockpos, targetface);
					isagain = m_pWorld->getBlockMaterial(placepos)->canPlacedAgain(m_pWorld, toolid, colpoint, blockpos, false, hitface);
					if (!m_pWorld->getBlockMaterial(placepos)->isReplaceable()
						&& !isagain)
					{
						return false;
					}
				}
			}

			int iRet = -100;
			SandboxResult result1 = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_checkIfCanPlaceBlockInWorkShop",
				SandboxContext(nullptr)
				.SetData_Userdata("ClientPlayer", "player", this)
				.SetData_Number("toolid", toolid)
				.SetData_UserObject("placepos", placepos));
			if (result1.IsExecSuccessed())
			{
				iRet = result1.GetData_Number("iRet");
			}
			if (iRet != -100)
			{
				getBody()->playAttack();
				return false;
			}

			//if (GET_SUB_SYSTEM(VehicleMgr)) {
			//	int iRet = GET_SUB_SYSTEM(VehicleMgr)->checkIfCanPlaceBlockInWorkShop(this, toolid, placepos);
			//	if (iRet != -100) { return true; }
			//}

			//auto pos = NeighborCoord(blockpos, ReverseDirection(hitface));
			if (m_pWorld->getBlockID(blockpos) == 150000 || m_pWorld->getBlockID(blockpos) == 150001)
			{
				return false;
			}
			else if (ITEM_SEA_WEED == toolid)//生海带不允许放置，海带块可以放置
            {
                return false;
            }
			if (toolid == BLOCK_LIGHTMUSHROOM && hitface != DIR_NEG_Y)
			{
				return false;
			}
			bool result = placeBlock(toolid, placepos.x, placepos.y, placepos.z, hitface, colpoint.x, colpoint.y, colpoint.z, placeInto, isagain);
			if (!result && toolid == BLOCK_CACTUS)//拿着仙人掌主干对着地图上的仙人掌主干右键交互时会在对应位置生成仙人掌分支
			{
				if (!m_pWorld->getBlockID(TopCoord(NeighborCoord(blockpos, ReverseDirection(hitface)))) &&
					!m_pWorld->getBlockID(DownCoord(NeighborCoord(blockpos, ReverseDirection(hitface)))) &&
					m_pWorld->getBlockID(DownCoord(blockpos)) == BLOCK_CACTUS &&
					m_pWorld->getBlockID(TopCoord(blockpos)) == BLOCK_CACTUS)
				{
					m_pWorld->setBlockAll(NeighborCoord(blockpos, targetface), BLOCK_CACTUSBRANCH, ReverseDirection(hitface), 2);
					shortcutItemUsed();
					result = true;
				}
			}
			if (!result && isCover)
			{
				int data = m_pWorld->getBlockData(blockpos);
				m_pWorld->setBlockAll(blockpos, toolid, 0, 3);
				auto container = dynamic_cast<SolidSandContainer*>(m_pWorld->getContainerMgr()->getContainer(blockpos));
				if (container)
				{
					container->setCoverInfo(blockid, data);
				}
				else
				{
					SolidSandContainer* container = SANDBOX_NEW(SolidSandContainer, blockpos);
					if(container)
					{
						container->init();
						container->setCoverInfo(blockid, data);
						Chunk* pchunk = m_pWorld->getChunk(blockpos);
						if(pchunk)
						{
							pchunk->addContainer(container);
							m_pWorld->getContainerMgr()->addContainerByChunk(container);
						}
						else
						{
							ENG_DELETE(container);
						}
					}
				}
				result = true;
			}
			if (toolid == BLOCK_CRAFTTABLE) checkNewbieWorldProgress(14, "placeCT");
			if (toolid == BLOCK_REPLICATOR_OFF)
			{
				if (m_RegionStartPos.y < 0 || m_pWorld->getBlockID(m_RegionStartPos) != BLOCK_REPLICATOR_ON) m_RegionStartPos = placepos;
				else
				{
					WCoord minpos = Min(m_RegionStartPos, placepos);
					WCoord maxpos = Max(m_RegionStartPos, placepos);

					if (Rainbow::Max(maxpos.x - minpos.x, Rainbow::Max(maxpos.y - minpos.y, maxpos.z - minpos.z)) <= 16)
					{
						int fillid = m_pWorld->getBlockID(TopCoord(m_RegionStartPos));
						if (fillid > 0)
						{
							int filldata = m_pWorld->getBlockData(TopCoord(m_RegionStartPos));
							m_pWorld->setBlockAll(m_RegionStartPos, fillid, filldata);
							m_pWorld->setBlockAll(placepos, fillid, filldata);
							fillBlocks(minpos, maxpos, fillid, filldata);

							m_RegionStartPos.y = -1;
						}
					}
				}
			}

			if (result)
			{
				MNSandbox::GetGlobalEvent().Emit<>("WorldArchiveMgr_OnBlockChangedByManual");
				addAchievement(1, ACHIEVEMENT_INTERACT_BLOCK, blockid); //产生实际的交互时，触发冒险成就任务 code_by:huangfubin
				updateTaskSysProcess(TASKSYS_INTERACT_BLOCK, toolid, blockid);
				if (DIR_NEG_Y == hitface && (BLOCK_MILA_STAR_PEDESTAL == blockid && BLOCK_KEY_OF_FRUIT == toolid ||
					BLOCK_FLAME_STAR_PEDESTAL == blockid && BLOCK_KEY_OF_BROKEN_SWORD == toolid ||
					BLOCK_Q_EYE_STAR_PEDESTAL == blockid && BLOCK_KEY_OF_STONE_EYE == toolid))
				{
					updateTaskSysProcess(TASKSYS_INTERACT_BLOCK_D, toolid, blockid);
				}

				return true;
			}
		}
	}

	// 检查是否是建筑蓝图ID (例如3030212)
	if (toolid == ItemIDs::BLUEPRINT) {
		// 尝试使用建筑蓝图放置方块
		int blueprintId = getPlayerAttrib()->getCurBuildingId();
		if (TryPlaceBlueprintBlock(blueprintId, blockpos, targetface, colpoint)) {
			return true;
		}
	}

	return false;
}

// 点击block
bool ClientPlayer::attackBlock(const WCoord& blockpos, DIG_METHOD_T dgmethod, ATTACK_TYPE attacktype)
{
	int blockid = m_pWorld->getBlockID(blockpos);
	if (blockid == 0)
		return false;
	auto blockdef = GetDefManagerProxy()->getBlockDef(blockid);
	if (!blockdef || blockdef->Hardness < 0)
		return false;
	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pmtl) 
		return false;

	// if (m_pWorld->IsProtectedZone(blockpos) && blockdef->ProtectZoneDestroy == 0)
	// {
	// 	//notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000200); //多余的提示？
	// 	return false;
	// }

	//if (m_pWorld->IsProtectedZone(blockpos))
	//	return false;


	// 不检测地图设置的破坏权限了
	// PermitsSubSystem* permitsModule = GET_SUB_SYSTEM(PermitsSubSystem);
	// if (permitsModule && !permitsModule->canCSPermit(getUin(), 0, CS_PERMIT_DESTROY_BLOCK))
	// {
	// 	return false;
	// }

	auto CarryComp = getCarryComponent();
	if (CarryComp && CarryComp->isCarrying())
	{
		return false;
	}

	// 检查工具耐久
	int toolid = getCurToolID();
	auto tooldef = GetDefManagerProxy()->getToolDef(toolid);
	BackPackGrid* pgrid = getBackPack()->index2Grid(getCurShortcut() + getShortcutStartIndex());
	if (pgrid && pgrid->def)
	{
		auto curDur = pgrid->getDuration();
		if (curDur == 0 && pgrid->getDefMaxDuration() != 0)
		{
			notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 700007);
			return false;
		}
		if (curDur > 0)
		{
			if (tooldef && tooldef->AtkDuration > 0 && curDur < tooldef->AtkDuration)
			{
				notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 700007);
				return false;
			}
		}
	}

	//播放攻击动画
	// ActorLiving::playAnim(2);
	//if (!isRemote())
	//{
	//	pmtl->ShowBlockCrack(m_pWorld, blockpos);
	//}
	//else
	//{
	//	// 客机不实际执行
	//	return true;
	//}
	if (isRemote()) return true;// 客机不实际执行
	// 播放攻击音效
	playBlockDigSound(blockid, blockpos.x, blockpos.y, blockpos.z);

	const ItemDef* def = GetDefManagerProxy()->getItemDef(toolid);
	if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR) //修复方块
	{
		DoRepairBlock(blockpos);
	} 
	else
	{
		bool isDestroy = false;
		DoHurtBlock(blockpos, ATTACK_PUNCH, 0, isDestroy);
		if (isDestroy)
		{
			bool gamerule_forbid = !checkActionAttrState(ENABLE_DESTROYBLOCK);
			destroyBlock(blockpos, DIG_METHOD_NORMAL, true, gamerule_forbid);

			if (!isRobot())
			{
				std::ostringstream oss;
				oss << "" << blockpos.x << "," << blockpos.y << "," << blockpos.z << "";
				std::string location = oss.str();

				GameAnalytics::TrackEvent("block_destroy", {
					{"block_id",blockid},
					{"loc",location},
					{"inhand_item_id", toolid},
				}, true, getUin());				
			}
		}
	}

	// 扣除耐久
	DoCurToolAtkDuration();
	return true;
}

void ClientPlayer::fillBlocks(const WCoord &minpos, const WCoord &maxpos, int fillid, int filldata)
{
	BlockMaterial *fillmtl = g_BlockMtlMgr.getMaterial(fillid);

	for (int y = minpos.y; y <= maxpos.y; y++)
	{
		for (int z = minpos.z; z <= maxpos.z; z++)
		{
			for (int x = minpos.x; x <= maxpos.x; x++)
			{
				if (fillmtl->canPutOntoPos(m_pWorld->getWorldProxy(), WCoord(x, y, z))) m_pWorld->setBlockAll(WCoord(x, y, z), fillid, filldata);
			}
		}
	}
}

void ClientPlayer::doGunFire(int id)
{
	if (!m_pWorld->isRemoteMode())
	{
		if (m_pGunComponent)
			m_pGunComponent->doGunFire(id);

		useItemOnTrigger(getCurToolID());
	}
	if (getGunLogical())
		getGunLogical()->increaseSpreadOnFire();
}

//天赋：回弹专用（主机处理客机）
void ClientPlayer::doReloadWithoutCheck(int num, int checkShortcut)
{
	if (m_pWorld->isRemoteMode())
		return;

	PlayerAttrib* attr = getPlayerAttrib();
	BackPackGrid* itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
	if (itemgrid && itemgrid->def && itemgrid->getGunDataComponent())
	{
		CustomGunUseComponent* comp = getCustomGunComponent();
		if (comp && comp->getGunDef())
		{
			int maxMagazines = comp->maxAmmo();
			int leftCount = comp->getMagazine();
			if (leftCount < 0)
				leftCount = 0;
			if (leftCount > maxMagazines)
				leftCount = maxMagazines;

			int canReloadCount = maxMagazines - leftCount;
			if (num < 0 || num > canReloadCount)
				num = canReloadCount;
					
			comp->addMagazine(num);
			PB_GunDoReloadHC gunDoReloadHC;
			//gunDoReloadHC.set_num(num);
			gunDoReloadHC.set_total(comp->getMagazine());
			gunDoReloadHC.set_iscustomgun(true);
			gunDoReloadHC.set_curshortcut(checkShortcut);
			GameNetManager::getInstance()->sendToClient(getUin(), PB_GUN_DORELOAD_HC, gunDoReloadHC);
		}
	}
	
}

void ClientPlayer::doReload(int bulletid, int num, bool isCustomGun, int checkShortcut)
{
	if (!m_pWorld->isRemoteMode())
	{
		PlayerAttrib *attr = getPlayerAttrib();
		BackPackGrid *itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
		if (itemgrid && itemgrid->def)
		{
			const GunDef* def = GetDefManagerProxy()->getGunDef(itemgrid->def->ID);
			if (def != NULL)
			{
				// 数量超过上限，可能是作弊 先记录
				int maxMagazines = getGunLogical()->getMaxMagazines();
				int leftCount = itemgrid->getUserDataInt();
				if (leftCount < 0 || leftCount > maxMagazines)
				{
					leftCount = 0;
					itemgrid->setUserDataInt(0);
				}
				
				// 2022-02-16 codeby:liusijia 客户端可能会重复触发，需要过滤可能的正常情况
				int canReloadCount = maxMagazines - leftCount;
				num = canReloadCount;
				int itemCount = getGunLogical()->getBulletNum();
				if (num > itemCount)
					num = itemCount;

				if (num == 0 || (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) || 0 == def->NeedBullet || getBackPack()->removeItemInNormalPack(bulletid, num))
				{
					if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) && 0 != def->NeedBullet)
					{
						consumeItemOnTrigger(bulletid, num);
					}
					itemgrid->setUserDataInt(leftCount + num);

					PB_GunDoReloadHC gunDoReloadHC;
					gunDoReloadHC.set_bulletid(bulletid);
					gunDoReloadHC.set_num(num);
					gunDoReloadHC.set_total(leftCount + num);
					gunDoReloadHC.set_iscustomgun(isCustomGun);
					gunDoReloadHC.set_curshortcut(checkShortcut);
					GameNetManager::getInstance()->sendToClient(getUin(), PB_GUN_DORELOAD_HC, gunDoReloadHC);
				}
			}

			CustomGunUseComponent* comp = getCustomGunComponent();
			if (comp && comp->getGunDef() && itemgrid->getGunDataComponent())
			{
				// 数量超过上限，可能是作弊 先记录
				int maxMagazines = comp->maxAmmo();
				int leftCount = comp->getMagazine();
				if (leftCount < 0)
					leftCount = 0;
				if (leftCount > maxMagazines)
					leftCount = maxMagazines;

				// 数量超过上限，可能是作弊 先记录
				int canReloadCount = maxMagazines - leftCount;
				if (num < 0 || num > canReloadCount)
					num = canReloadCount;

				//可能的异常，只同步一下最新数据
				if (num <= 0 && leftCount > 0)
				{
					PB_GunDoReloadHC gunDoReloadHC;
					gunDoReloadHC.set_bulletid(bulletid);
					gunDoReloadHC.set_num(0);
					gunDoReloadHC.set_total(leftCount);
					gunDoReloadHC.set_iscustomgun(isCustomGun);
					gunDoReloadHC.set_curshortcut(checkShortcut);
					GameNetManager::getInstance()->sendToClient(getUin(), PB_GUN_DORELOAD_HC, gunDoReloadHC);
					return;
				}

				if ((GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) || getBackPack()->removeItemInNormalPack(bulletid, num))
				{
					if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
					{
						consumeItemOnTrigger(bulletid, num);
					}

					comp->addMagazine(num);
					PB_GunDoReloadHC gunDoReloadHC;
					gunDoReloadHC.set_bulletid(bulletid);
					gunDoReloadHC.set_num(num);
					gunDoReloadHC.set_total(leftCount + num);
					gunDoReloadHC.set_iscustomgun(isCustomGun);
					gunDoReloadHC.set_curshortcut(checkShortcut);
					GameNetManager::getInstance()->sendToClient(getUin(), PB_GUN_DORELOAD_HC, gunDoReloadHC);
				}
			}
		}
	}
}

bool ClientPlayer::attackCharge(int status)
{
	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
	if (tooldef == NULL) return false;

	if (status == PLAYEROP_STATUS_BEGIN) //长按开始
	{
		setAtkingTarget(NULL);
		if (getAttackingTargetComponent())
			getAttackingTargetComponent()->setAttackAnim(ATTACK_RANGE);
		//auto attackingTargetComp = GetComponent<PlayerAttackingTargetComponent>();//by__Logo
		//if (attackingTargetComp) attackingTargetComp->setAttackAnim(ATTACK_RANGE);
		setOperate(PLAYEROP_ATTACK_BOW, MAX_INT, getCurToolID());
		
		//蓄能特效和音效
        SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_PlayCustomChargeEffect",
            SandboxContext(nullptr)
            .SetData_Userdata("ClientPlayer", "player", this)
        );
		if (!result.IsExecuted() || result.IsFailed())
		{
			playToolEffect(1, true);
		}

		//蓄能特效和音效
		SandboxResult result2 = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_PlayCustomChargeSound",
			SandboxContext(nullptr)
			.SetData_Userdata("ClientPlayer", "player", this)
		);
		if (!result2.IsExecuted() || result2.IsFailed())
		{
			playToolSound(1, true);
		}
		/*if(tooldef->Type == 6 && tooldef->Level == 5)
		{
			MINIW::ScriptVM::game()->callFunction("SpeedLine", "i", 1);
		}*/
	}
	//松手
	else
	{
		playToolEffect(-1, true); //停止
		playToolSound(-1, true);

		if (status == PLAYEROP_STATUS_END)
		{
			doActualChargeAttack();
			if (getAttackingTargetComponent())
				getAttackingTargetComponent()->setAttackAnim(ATTACK_ALL);
			//auto attackingTargetComp = GetComponent<PlayerAttackingTargetComponent>();//by__Logo
			//if (attackingTargetComp) attackingTargetComp->setAttackAnim(ATTACK_ALL);
		}

		onOperateEnded();
	}
	return true;
}

bool ClientPlayer::SheildDefence(int itemId, int status)
{
    const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(itemId);
    if (tooldef == NULL) {
        return false;
    }
    getPlayerAttrib()->SetDefenceBuffStatus(itemId, status);
    if (status == PLAYEROP_STATUS_BEGIN) {
        setOperate(PLAYEROP_SHEILD_DEFENCE_BEGIN, MAX_INT, itemId);
    } else {
        onOperateEnded();
    }
    
    return true;
}

void ClientPlayer::kill()
{
	OneAttackData atkdata;
	atkdata.atktype = THIS_KILL;
	
	updataLastAttackDataInfo(atkdata,nullptr);
	ClientActor::kill();
}

bool ClientPlayer::attackRangedFree(int status)
{
	//LOG_INFO("attackRangedFree: %d", status);
	bool isEnchantArrorwFree = getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_FREE) > 0;
	if (status == PLAYEROP_STATUS_BEGIN) //长按开始
	{
		const ToolDef *def = GetDefManagerProxy()->getToolDef(getCurToolID());
		if (def)
		{
			if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) && !isEnchantArrorwFree && def->Type != 24)
			{
				//看数量是否够
				if (getBackPack()->getItemCountInNormalPack(def->ConsumeID == -1 ? def->ID : def->ConsumeID) < def->ConsumeCount)
				{
					return false;
				}
			}
		}

		setAtkingTarget(NULL);
		if (getAttackingTargetComponent())
			getAttackingTargetComponent()->setAttackAnim(ATTACK_RANGE);
		//auto attackingTargetComp = GetComponent<PlayerAttackingTargetComponent>();//by__Logo
		//if (attackingTargetComp) attackingTargetComp->setAttackAnim(ATTACK_RANGE);

		setOperate(PLAYEROP_ATTACK_BOW, MAX_INT, getCurToolID());


		useItemOnTrigger(getCurToolID());
	}
	//����
	else
	{
		//auto attackingTargetComp = GetComponent<PlayerAttackingTargetComponent>();//by__Logo
		if (/*attackingTargetComp && attackingTargetComp->needDoActualRangeAttack()*/getAttackingTargetComponent()->needDoActualRangeAttack())
		{
			m_RangeAttackPower = 1.0f;
			const ToolDef *def = GetDefManagerProxy()->getToolDef(getCurToolID());
			if (def && GetDefManagerProxy()->getItemDef(getCurToolID(), true)->UseTarget != 3)
			{
				float at = def->AccumulatorTime;
				if (at <= 0)  //所需蓄力时间小于等于0的，默认给最大力度
					m_RangeAttackPower = 1;
				else
					m_RangeAttackPower = float(m_OperateTicks) / (20.0f * at);
			}

			doActualRangeAttack(NULL);
			if (getAttackingTargetComponent())
				getAttackingTargetComponent()->setAttackAnim(ATTACK_ALL);
			//auto attackingTargetComp = GetComponent<PlayerAttackingTargetComponent>();//by__Logo
			//if (attackingTargetComp) attackingTargetComp->setAttackAnim(ATTACK_ALL);
		}

		onOperateEnded();
	}
	return true;
}
//
//bool ClientPlayer::eatFood(int itemid, int status)
//{
//	if (status == PLAYEROP_STATUS_BEGIN)
//	{
//		//LOG_INFO("ClientPlayer::eatFood(): BEGIN itemid = %d", itemid);
//		const FoodDef *fooddef = FoodDefCsv::getInstance()->get(itemid);
//		if (fooddef == NULL) return false;
//
//		setOperate(PLAYEROP_EATFOOD, fooddef->UseTime, itemid);
//
//
//		useItemOnTrigger(itemid);
//	}
//	else
//	{
//		assert(status == PLAYEROP_STATUS_END || status == PLAYEROP_STATUS_CANCEL);
//
//		if (status == PLAYEROP_STATUS_END)
//		{
//			//LOG_INFO("ClientPlayer::eatFood(): END itemid = %d", itemid);
//			if (m_pWorld && !m_pWorld->isRemoteMode())
//			{
//				getPlayerAttrib()->eatFood(itemid);
//				addAchievement(3, ACHIEVEMENT_USEITEM, itemid, 1);
//				if (itemid == 12502) checkNewbieWorldProgress(19, "eatBread");
//				// 观察者事件接口
//				ObserverEvent_ActorItem obevent(getUin(), itemid, 1);
//				ObserverEventManager::getSingleton().OnTriggerEvent("Item.expend", &obevent);
//
//				if (g_WorldMgr)
//					g_WorldMgr->syncBattlePassEventToClient(this, "eatfood", itemid);
//
//
//				breakHorseInvisible();	// 20210910���������  codeby�� keguanqiang
//
//			}
//
//			// 20210831�����ӻ�¼�  codeby�� yaoxinqun
//			if (g_WorldMgr && hasUIControl())
//			{
//				g_WorldMgr->syncFestivalActivitiesEventToClient(this, "eatfood", itemid);
//			}
//			auto soundComp = getSoundComponent();
//			if (soundComp)
//			{
//				soundComp->playSound("misc.burp", 1.0f, 1.0f);
//			}
//		}
//
//
//		onOperateEnded();
//	}
//
//	return true;
//}

bool ClientPlayer::useItem(int itemid, int status, bool onshift/* =false */, unsigned int useTick/*=0*/)
{
	if (useTick == 0)
		useTick = Timer::getSystemTick();

	setCurItemSkillID(0);

	if (itemid != getCurToolID()) return false;

	//if(!GetDefManagerProxy()->checkItemCrc(itemid)) return false;
	if (ROOM_SERVER_RENT != GetGameInfoProxy()->GetRoomHostType())
	{

		SandboxResult canUseItemRet = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canUseItem",
			SandboxContext(nullptr).SetData_Number("uin", getUin()).SetData_Number("itemid", itemid));
		bool canUseItemFlag = false;
		if (canUseItemRet.IsExecSuccessed())
		{
			canUseItemFlag = canUseItemRet.GetData_Bool();
		}
		if (!canUseItemFlag)
		{
			notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
			return false;
		}

		SandboxResult canPermitRet = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
			SandboxContext(nullptr).SetData_Number("uin", getUin()).SetData_Number("itemid", itemid).SetData_Number("bit", CS_PERMIT_USE_ITEM));
		bool canPermitFlag = false;
		if (canPermitRet.IsExecSuccessed())
		{
			canPermitFlag = canPermitRet.GetData_Bool();
		}
		if (!canPermitFlag)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 700307, int(CS_PERMIT_USE_ITEM));
			else
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 700307, int(CS_PERMIT_USE_ITEM));
			return false;
		}
	}
	else
	{
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
			SandboxContext(nullptr).SetData_Number("uin", getUin()).SetData_Number("blockid", itemid).SetData_Number("bit", CS_PERMIT_DANGER));
		bool canCSPermitFlag = false;
		if (result.IsExecSuccessed())
		{
			canCSPermitFlag = result.GetData_Bool();
		}
		if (!canCSPermitFlag)
		{
			return false;
		}

		result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
			SandboxContext(nullptr).SetData_Number("uin", getUin()).SetData_Number("blockid", itemid).SetData_Number("bit", CS_PERMIT_USE_ITEM));
		canCSPermitFlag = false;
		if (result.IsExecSuccessed())
		{
			canCSPermitFlag = result.GetData_Bool();
		}
		if (!canCSPermitFlag)
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 700307, int(CS_PERMIT_USE_ITEM));
			else
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", getUin(), int(PLAYER_NOTIFYINFO_TIPS), 700307, int(CS_PERMIT_USE_ITEM));
			return false;
		}
	}
	// 2021/07/9 修改:合成 移动版 合成，客机打开创造锤，物品介绍界面 codeby:wudeshen
	notifyUseItem2Tracking(itemid, status, onshift);

	if (itemid != ITEM_SEA_SPIRIT_STONE)// 海灵变身石不需要清除该buff
		getLivingAttrib()->removeBuff(INVULNERABLE_BUFF);

	const GunDef* gunDef = GetDefManagerProxy()->getGunDef(itemid);
	if (status == PLAYEROP_STATUS_BEGIN)
	{
		//Warning !!!  解决客机坐骑背包打开的问题
		//if (GetClientInfoProxy()->isMobile())
		//{
		ActorHorse *horse = getFacedHorse();
		if (horse && gunDef == NULL)
		{
			if (onshift)
				return interactHorse(horse, onshift);
			else
				return false;
		}
		//}
	}
	const ItemDef *def = GetDefManagerProxy()->getItemDef(itemid);
	if (def)
	{
		if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
		{
			if (def->UseTarget == ITEM_USE_HOOK ||
				def->UseTarget == ITEM_USE_ADVANCEDDIG ||
				def->UseTarget == ITEM_USE_BOW ||
				def->UseTarget == ITEM_USE_CHARGETHROW ) 
			{
				if (isSkillCD())
				{
					return false;
				}
			}
		}
		if (!m_pWorld->isRemoteMode() && !checkActionAttrState(ENABLE_USEITEM))
		{
			notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14004);
			return false;
		}
		//右键使用特效
		{
			if (GetDefManagerProxy()->IsShowUseBtnForItemRightUse(itemid))
			{
				useItemOnTrigger(def->ID);
				return true;
			}
		}
		if (def->UseTarget == ITEM_USE_PRESSFOOD)
		{
#if 1
			//if (isCurrentActionState("Eat"))
			{
				auto pState = dynamic_cast<EatState*>(getActionStatePtr("Eat"));
				if (pState && pState->eatFood(status, itemid, def->Score))
				{
					if (m_CameraModel)
					{
						// if (status == 0)
						// {
						// 	m_CameraModel->playHandAnim(101110);
						// }
						// else
						// {
						// 	m_CameraModel->playHandAnim(101100);
						// }
					}
					return true;
				}
				else
				{
					return false;
				}
			}
#else		
			const FoodDef *fooddef = FoodDefCsv::getInstance()->get(itemid);
			if (fooddef)
			{
				if (status == PLAYEROP_STATUS_END)
				{
					addOWScore(def->Score);
				}
				return eatFood(itemid, status);
			}
			return false;
#endif
		}
		else if (def->UseTarget == ITEM_USE_WATER)
		{
			return UseWater(status, itemid, 1);
		}
		else if (def->UseTarget == ITEM_USE_BOW )
		{
			return attackRangedFree(status);
		}
		else if (def->UseTarget == ITEM_USE_CHARGETHROW )
		{
			if (nullptr != GetDefManagerProxy()->getToolDef(def->ID))
			{
				if (GetDefManagerProxy()->getToolDef(def->ID)->CanThrow &&
					GetDefManagerProxy()->getToolDef(def->ID)->AccumulatorType != 2)
				{
					return attackRangedFree(status);
				}
				else if (GetDefManagerProxy()->getToolDef(def->ID)->AccumulatorType == 2)
				{
					m_RangeAttackPower = 1.0f;
					throwBall(def->ID);
					useItemOnTrigger(def->ID);
					return true;
				}
			}
			//shortcutItemUsed();
		}
		else if (def->UseTarget == ITEM_USE_HOOK) //钩子
		{
			if (status == PLAYEROP_STATUS_CANCEL)
			{
				return false;
			}
			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(itemid);
			if (tooldef == NULL)
			{
				return false;
			}
			if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
			{
				if (tooldef->SkillCD > 0)
				{
					setSkillCD(tooldef->ID, tooldef->SkillCD);
					syncSkillCD(tooldef->ID, tooldef->SkillCD);
				}
			}
			if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
			{
				//所有模式不消耗饥饿度
				/*
				if (getPlayerAttrib()->getFoodLevel() < def->CostFoodLevel)
				{
					return false;
				}
				getPlayerAttrib()->m_FoodLevel -= def->CostFoodLevel;
				*/
			}
			if (GetDefManagerProxy()->getToolDef(def->ID)->AccumulatorType == 2)
			{
				m_RangeAttackPower = 1;
				throwBall(def->ID);
				useItemOnTrigger(def->ID);
			}
			else
			{
				return attackRangedFree(status);
			}
			return true;
		}
		else if (def->UseTarget == ITEM_USE_GUN)
		{
			PlayerAttrib* attr = getPlayerAttrib();
			BackPackGrid* itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
			if (itemgrid)
			{
				auto duration = itemgrid->getDuration();
				if (duration == 0)
				{
					if (hasUIControl())
					{
						GetLuaInterfaceProxy().showGameTips(700007);
					}
					return false;
				}
			}
			
			if (status == PLAYEROP_STATUS_BEGIN && !onshift)
			{
				//getBody()->playAttack();
				//getBody()->playShoot();
				//if (!m_pWorld->isRemoteMode())
				//{

				if (!hasUIControl() && !GetCheatHandler()->checkGunShoot(useTick))
					return false;
				if (m_pGunComponent->getFireInterval() > 0)
				{
					useTick = useTick + m_pGunComponent->getFireInterval();
					m_pGunComponent->setFireInterval(0);
				}
				else
				{
					useTick = useTick + m_pGunComponent->getGunUse(itemid);;
				}

				if (m_pGunComponent->canUseGun(itemid, useTick))
				{
					if (gunDef && itemgrid && itemgrid->def && (itemgrid->def->ID == itemid))
					{
						if (gunDef->NeedBullet != 2 && !(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
						{
							if (itemgrid->getUserDataInt())
							{
								itemgrid->setUserDataInt(itemgrid->getUserDataInt() - 1);
								doGunFire(gunDef->BulletID);
							}
						}
						else
						{
							doGunFire(gunDef->BulletID);
						}
						m_pGunComponent->setGunUse(itemid, useTick);
					}
					// 枪械也需要消耗耐久
					
					DoCurToolAtkDuration();
					//}
					return true;
					//bool scripthandled = false;
					//MINIW::ScriptVM::game()->callFunction("Gun_OnUse", "u[ClientPlayer]u[World]iiii>b", this, m_pWorld, 0, 0, 0, 0, &scripthandled);
					//return scripthandled;
				}
				else
				{
					return false;
				}
			}
			else
				return true;
		}
		else if (def->UseTarget == ITEM_USE_CLICKBUTTON)
		{
			if (!def->UseScript.empty() && status == PLAYEROP_STATUS_BEGIN)
			{
				getBody()->playAttack();
				if (ITEM_HOLOGRAPHIC == def->ID
					|| ITEM_SANJIAOMEN == def->ID)
				{
					bool scripthandled = false;
					WCoord tmpPos = getLocoMotion()->getPosition();
					DirectionType tmpface = DIR_POS_Y;
					MINIW::ScriptVM::game()->callFunction(def->UseScript.c_str(), "u[ClientPlayer]u[World]iiii>b", this, m_pWorld, tmpPos.x, tmpPos.y, tmpPos.z, tmpface, &scripthandled/*, &setBlockAllRet*/);

					if (scripthandled)
						useItemOnTrigger(def->ID);
					return false; //这几样东西特殊处理，不需要发送给其他端
				}
				else if (ITEM_STONE_HAMMER <= def->ID && def->ID <= ITEM_TITANIUM_HAMMER)
				{
					// 创造锤 使用时摆放方块
					bool isliquid = def != NULL && def->UseTarget == ITEM_USE_CLICKLIQUID;

					if (g_pPlayerCtrl)
					{
						g_pPlayerCtrl->m_CurMouseX = 0.5f;
						g_pPlayerCtrl->m_CurMouseY = 0.5f;

						bool pickliquid = itemid > 0 && isliquid;
						int  picktype   = g_pPlayerCtrl->doPick(pickliquid, true);

						WCoord        tmpPos  = g_pPlayerCtrl->m_PickResult.block;
						DirectionType tmpface = g_pPlayerCtrl->m_PickResult.face;

						bool scripthandled = false;

						MINIW::ScriptVM::game()->callFunction(def->UseScript, "u[ClientPlayer]u[World]iiii>b", this, m_pWorld, tmpPos.x, tmpPos.y, tmpPos.z, tmpface, &scripthandled /*, &setBlockAllRet*/);
						if (scripthandled)
							useItemOnTrigger(def->ID);
					}
					return false;
				}
				else if (def->ID == ITEM_GLOW_STICK_UNUSED)
                {
                    BackPackGrid* itemgrid = getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
                    if (g_pPlayerCtrl && g_pPlayerCtrl == this && g_pPlayerCtrl->m_CameraModel && g_pPlayerCtrl->getViewMode() == 0)
                    {
                        //播放手部动画-掰荧光棒
                        g_pPlayerCtrl->m_CameraModel->playHandAnim(101157);
                    }
                    if (itemgrid)
                    {
                        itemgrid->addNum(-1);
                        int A = itemgrid->getNum();
                        if (A == 0)
                        {
                            itemgrid->clear();
							itemgrid->setItem(ITEM_GLOW_STICK,1);
                            //刷新手上模型
                            if (g_WorldMgr)
                            {
                                g_WorldMgr->resetAllPlayerHandModel();
                            }
                            if (g_pPlayerCtrl && g_pPlayerCtrl==this)
                            {
                                g_pPlayerCtrl->resetHandModel();
                            }   
                        }
                        else 
                        {
                            getPlayerAttrib()->m_Backpack->addItem(ITEM_GLOW_STICK, 1);
                        }
                        //GameEventQue::GetInstance().postBackpackChange(itemgrid->getIndex());
						 MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
							SetData_Number("grid_index", itemgrid->getIndex());
						if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
							MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
						}
						//成功使用荧光棒返回true，用于同步给客机 by：Jeff
						return true;
                    }
                    return false;
                }
				else if (!m_pWorld->isRemoteMode()
					|| def->ID == ITEM_MAPEDIT || def->ID == ITEM_LETTERS || def->ID == ITEM_INSTRUCTION || def->ID == ITEM_BOOK || def->Type == ITEM_TYPE_PACK
					|| def->ID == ITEM_CUSTOMMODELPACKING_TOOL || def->ID == ITEM_MUSIC_PU || def->ID == ITEM_TREASURE_MAP || ITEM_TERRAIN_MAP == def->ID 
					|| def->ID == ITEM_POLAROID || def->ID == ITEM_POLAROID_PHOTO || def->ID == ITEM_POLAROID_ALBUM || def->ID == ITEM_POLAROID_FRAME 
					|| def->ID == ITEM_RARE_POLAROID || def->ID == ITEM_POLAROID_RARE_ALBUM || def->ID == ITEM_POLAROID_RARE_FRAME || def->ID == ITEM_POLAROID_INNER_FRAME)
				{

					bool scripthandled = false;
					// 					int setBlockAllRet = 0;

					WCoord tmpPos = getLocoMotion()->getPosition();
					DirectionType tmpface = DIR_POS_Y;
					// codeby:liusijia 2022/07/19 记录giftpack道具id 在handlePackGiftItemChg2Host中验证
					if (GetCheatHandler()) GetCheatHandler()->SetLastGiftItem(itemid);

					MINIW::ScriptVM::game()->callFunction(def->UseScript.c_str(), "u[ClientPlayer]u[World]iiii>b", this, m_pWorld, tmpPos.x, tmpPos.y, tmpPos.z, tmpface, &scripthandled/*, &setBlockAllRet*/);

					if (scripthandled)
						useItemOnTrigger(def->ID);
					return scripthandled;
				}
				else return true;
			}
			return false;
		}
		else if (def->UseTarget == ITEM_USE_ADVANCEDDIG)
		{
			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(itemid);
			//2022318 反作弊： 服务端再次判断skillCD,  案例:能量剑 codeby:liushuxin
			if (tooldef && tooldef->Type == 6 && !isSkillCD())
			{
				useItemOnTrigger(def->ID);
				return attackCharge(status);
			}
		} else if (def->UseTarget == ITEM_USE_SHIELD_ITEM) {
            return SheildDefence(def->ID, status);
        }
	}
	return false;
}

bool ClientPlayer::useSpecialItem(int grid_index, int itemId, int num/* =1 */)
{
	int localItemId = getBackPack()->getGridItem(grid_index);
	if (localItemId == 0) return false;

	const ItemDef *def = GetDefManagerProxy()->getItemDef(localItemId);
	if (def == NULL) return false;

	if (m_pWorld == NULL) return false;
	if (!m_pWorld->isRemoteMode())
	{
		if (!m_pWorld->isRemoteMode() && !checkActionAttrState(ENABLE_USEITEM))
		{
			notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14004);
			return false;
		}

		getBackPack()->removeItem(grid_index, 1);
		consumeItemOnTrigger(localItemId, 1);
		auto itemDef = GetDefManagerProxy()->getItemDef(itemId);
		if (itemDef)
		{
			if (itemDef->Type != 6)	//普通物品直接加
			{
				if (localItemId == 12963)		//星星礼盒
				{
					this->getPlayerAttrib()->addExp(500);
				}
				else if (localItemId == 12857 && itemDef->ID == 14001)	// 使用星星福袋返回的随机星星数目
				{
					this->getPlayerAttrib()->addExp(num * 100);
				}
				else
				{
					this->gainItems(itemId, num, 1);
				}
			}
		}

		if (GetClientInfoProxy()->getMultiPlayer() == 0)
		{
			MINIW::ScriptVM::game()->callFunction("SetAccountItemTips", "ii", itemId, num);
		}
		else
		{
			if (hasUIControl())
			{
				MINIW::ScriptVM::game()->callFunction("SetAccountItemTips", "ii", itemId, num);
			}
			else
			{
				PB_SpecialItemUseHC specialItemUseHC;
				specialItemUseHC.set_itemid(itemId);
				specialItemUseHC.set_itemnum(num);

				GetGameNetManagerPtr()->sendToClient(getUin(), PB_SPECIALITEM_USE_HC, specialItemUseHC);
			}
		}
	}

	return true;
}

bool ClientPlayer::openPlotDialogue(ClientMob *mob, int itemid/* =0 */, WCoord pos/* =WCoord(0, 0 ,0)*/, int type/* = 0*/)
{
	if (!mob && itemid == 0) return false;

	if (!m_pWorld->isRemoteMode())
	{
		if (mob)
		{
			//mob->addOpenDialogueUIN(getUin());
			//SandboxContext context(this);
			//context.SetData_Number("yaw", (int)getUin());
			//mob->Event().Emit("addOpenDialogueUIN", context);
			mob->EXEC_USEMODULE(addOpenDialogueUIN, getUin());
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("NPC_talk");
			}
		}

		if (!hasUIControl())
		{
			PB_OpenDialogueHC openDialogueHC;
			if (mob)
			{
				openDialogueHC.set_objid(mob->getObjId());
				RepeatedPtrField<PB_IntertactData>* pdatas = openDialogueHC.mutable_interactdata();
				//mob->getInteractData(pdatas);
				auto plotCom = mob->GetComponent<PlotComponent>();
				if (plotCom)
				{
					plotCom->getInteractData(pdatas);
				}
				//mob->EXEC_USEMODULE(getInteractData, pdatas);
				openDialogueHC.set_itemid(0);
			}
			else
			{
				openDialogueHC.set_objid(0);
				openDialogueHC.set_itemid(itemid);
			}


			openDialogueHC.set_plottype(type);
			PB_Vector3 *openPos = openDialogueHC.mutable_openpos();
			if (openPos)
			{
				openPos->set_x(pos.x);
				openPos->set_y(pos.y);
				openPos->set_z(pos.z);
			}

			if (!GetGameNetManagerPtr()->sendToClient(getUin(), PB_OPENDIALOGUE_HC, openDialogueHC, 0, false))
			{
				return false;
			}
		}
	}

	if (hasUIControl())
	{
		if (mob)
			MINIW::ScriptVM::game()->setUserTypePointer("OpenedDialogueMob", "ClientMob", mob);
		//ge GetGameEventQue().postOpenPlotDialogue(itemid, pos.x, pos.y, pos.z);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).SetData_Number("itemid", itemid).
			SetData_Number("x", pos.x).
			SetData_Number("y", pos.y).
			SetData_Number("z", pos.z);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		{
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_OPEN_DIALOGUE", sandboxContext);
		}
	}

	if (mob)
		m_OpenDialogueMobID = mob->getObjId();

	return true;
}

void ClientPlayer::closePlotDialogue()
{
	if (!m_pWorld->isRemoteMode())
	{
		ClientMob *mob = dynamic_cast<ClientMob *>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_OpenDialogueMobID));
		if (mob)
		{
			//mob->removeOpenDialogueUIN(getUin());
			//SandboxContext context(this);
			//context.SetData_Number("yaw", (int)getUin());
			//mob->Event().Emit("removeOpenDialogueUIN", context);
			mob->EXEC_USEMODULE(removeOpenDialogueUIN, getUin());
		}
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("NPC_talk");
		}
	}

	if (hasUIControl() && m_OpenDialogueMobID > 0)
	{
		MINIW::ScriptVM::game()->setUserTypePointer("OpenedDialogueMob", "ClientMob", NULL);
	}

	m_OpenDialogueMobID = 0;
}

bool ClientPlayer::interactDialogueDirectUse(int type, int interactID, WCoord blockpos/* = WCoord(0, -1, 0)*/)
{
	if (m_pWorld && m_pWorld->isRemoteMode())
		return false;

	if (m_pWorld && m_pWorld->getContainerMgr() && interactID == BLOCK_STARSTATION_TRANSFER_CONSOLE)
	{
		WorldContainer* container = m_pWorld->getContainerMgr()->getContainer(blockpos);
		if (container && container->shieldDialogue())
		{
			return false;
		}
	}

	int num = GetDefManagerProxy()->getNpcPlotConfigurableDefNum();
	for (int i = 0; i < num; i++)
	{
		NpcPlotDef*def = GetDefManagerProxy()->getNpcPlotConfigurableDefByIndex(i);
		if (def && def->UseType == DIRECT_NPCPLPTUSETYPE && def->InteractID == interactID && type == def->InteractType)
		{
			setCurInteractPlotType(type);
			//faceWorldPos(getPosition() + WCoord(0, BLOCK_SIZE / 2, 0), 180.0f, 180.0f);
			return openPlotDialogue(NULL, interactID, blockpos, type);
		}
	}
	return false;
}

void ClientPlayer::removeBackpackItem(int itemid, int num)
{
	BackPack *backpack = getBackPack();
	if (backpack == NULL) return;
	if (num <= 0) return;

	PackContainer *backpackContainer = (PackContainer*)backpack->getContainer(BACKPACK_START_INDEX);
	PackContainer *shortcutContainer = (PackContainer*)backpack->getContainer(getShortcutStartIndex());

	int removenum = 0;
	for (size_t i = 0; i < backpackContainer->m_Grids.size(); i++)
	{
		BackPackGrid &grid = backpackContainer->m_Grids[i];
		if (0 == grid.def) continue;

		if (grid.def->ID == itemid)
		{
			int resultnum = grid.getNum() - num + removenum;
			if (resultnum > 0)									//全部扣除
			{
				removenum = num;
				grid.setNum(resultnum);
				backpack->afterChangeGrid(grid.getIndex());
				break;
			}
			else
			{
				removenum += grid.getNum();
				backpack->removeItem(grid.getIndex(), grid.getNum());

				backpack->afterChangeGrid(grid.getIndex());
			}
		}
	}
	if (removenum < num)
	{
		for (size_t k = 0; k < shortcutContainer->m_Grids.size(); k++)
		{
			BackPackGrid &grid = shortcutContainer->m_Grids[k];
			if (0 == grid.def) continue;

			if (grid.def->ID == itemid)
			{
				int resultnum = grid.getNum() - num + removenum;
				if (resultnum > 0)									//全部扣除
				{
					removenum = num;
					grid.setNum(resultnum);
					backpack->afterChangeGrid(grid.getIndex());
					break;
				}
				else
				{
					removenum += grid.getNum();
					backpack->removeItem(grid.getIndex(), grid.getNum());

					backpack->afterChangeGrid(grid.getIndex());
				}
			}
		}
	}
}

void ClientPlayer::playHurtSound()
{
	// 屏蔽受击声
	if (!canPlayHurtSound())
	{
		return;
	}

	ActorLiving::playHurtSound();
}

void ClientPlayer::playStepSound()
{
	// 屏蔽脚步声
	if (!canPlayStepSound())
	{
		return;
	}

	WCoord blockpos = CoordDivBlock(getLocoMotion()->getPosition() - WCoord(0, 10, 0));
	int blockid = m_pWorld->getBlockID(blockpos);
	Rainbow::FixedString walksound = "";
	if (blockid > 0)
	{
		const BlockDef* blockdef = GetDefManagerProxy()->getBlockDef(blockid);
		if (blockdef)
		{
			walksound = blockdef->WalkSound;
		}

	}
	else if (getLocoMotion()->m_OnGround)  //行走在实体上时，播放行走在石头上的脚步声
	{
		walksound = "blocks.stone";
	}

	if (walksound.IsVaild())
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			std::string strwalkSound = walksound.c_str();
			sound->playSound(strwalkSound.c_str(), GSOUND_WALK);
		}
	}
}


bool ClientPlayer::canPlayStepSound()
{
	// 关闭脚步声处理
	if (g_WorldMgr && g_WorldMgr->isGameMakerRunMode())
	{
		int opId = 0;
		float val = 0.0f;
		g_WorldMgr->getRuleOptionID(GMRULE_PLAYER_STEPSOUND, opId, val);
		if (opId == 2)
		{
			return false;
		}
	}

	return true;
}

bool ClientPlayer::canPlayHurtSound()
{
	// 关闭受击声处理
	if (g_WorldMgr && g_WorldMgr->isGameMakerRunMode())
	{
		int opId = 0;
		float val = 0.0f;
		g_WorldMgr->getRuleOptionID(GMRULE_PLAYER_HURTSOUND, opId, val);
		if (opId == 2)
		{
			return false;
		}
	}

	return true;
}

void ClientPlayer::setGuardSafeTick(int tick)
{
	if (m_nGuardSafeTick < tick)
		m_nGuardSafeTick = tick;
}

void ClientPlayer::throwItem(int itemid, int num)
{
	BackPackGrid grid;
	SetBackPackGrid(grid, itemid, num);
	throwItem(grid);
}

void ClientPlayer::throwItemUserData(int itemid, int num, const char * userData)
{
	BackPackGrid grid;
	SetBackPackGrid(grid, itemid, num);
	grid.setUserdataStr(userData);
	throwItem(grid);
}

void ClientPlayer::throwItem(const BackPackGrid& grid)
{
	if (grid.getItemID() == 0) return;

	Rainbow::Vector3f dir;

	int h = getThrowItemHeight();
	ClientActorMgr* pActorMgr = getActorMgr();
	if (pActorMgr == NULL) return;
	ClientItem* item = pActorMgr->spawnItem(getPosition() + WCoord(0, h, 0), grid);
	if (item == NULL) return;
	if (grid.def && grid.def->iPackID > 0) {
		MINIW::ScriptVM::game()->callFunction("hideGiftPackFrame", "");
	}

	item->setDelayPickTicks(40);
	item->SetItemSpawnType(DISCARDITEM);
	//20211208 codeby:wangyu 创造模式 或扔的道具是喷漆 就设置为可清除
	if ((GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) || grid.getItemID() == ITEM_PAINTTANK) item->m_LiveTicks = 4800;

	bool nodir = false;
	Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
	if (nodir)
	{
		float r = GenRandomFloat() * 50.0f;
		float angle = GenRandomFloat() * 360.0f;
		motion.x = Rainbow::Cos(angle) * r;
		motion.z = Rainbow::Sin(angle) * r;
		motion.y = 20.0f;
	}
	else
	{
		float r = 30.0f;
		dir = getLocoMotion()->getLookDir();

		motion.x = dir.x * r;
		motion.z = dir.z * r;
		motion.y = dir.y * r + 10.0f;

		r = 2.0f * GenRandomFloat();
		float angle = GenRandomFloat() * 360.0f;

		motion.x += r * Cos(angle);
		motion.z += r * Rainbow::Sin(angle);
		motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
	}

	if (item)
	{
		//BackPack::discardItem()里面又会调用'ClientPlayer::throwBackpackItem()',
		// 观察者事件接口:丢弃道具
		ObserverEvent_PlayerItem obevent(getUin(), grid.getItemID(), grid.getNum(), item->getObjId());
		GetObserverEventManager().OnTriggerEvent("Player.DiscardItem", &obevent);
	}
}

void ClientPlayer::throwBackpackItem(int backpack_index, int num)
{
	int itemid = getBackPack()->getGridItem(backpack_index);
	BackPackGrid* pgrid = getBackPack()->index2Grid(backpack_index);
	if (pgrid == NULL || pgrid->getItemID() == 0) return;
	PlayerAttrib* pAttrib = getPlayerAttrib();
	if (!pAttrib || pAttrib->checkIfItemHasAttAction(itemid, PlayerItemAttType::Disable_Throw)) {
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14009);
		return;
	}

	int toolType = getBackPack()->getGridToolType(backpack_index);
	if (toolType == 31) //新鱼竿不可丢
	{
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 81150);
		return;
	}

	int itemnum = pgrid->getNum();
	if (num < 0) num = itemnum;
	if (num > itemnum) num = itemnum;
	if (num == 0) return;

	int durable = pgrid->getDuration();
	BackPackGrid throwgrid(*pgrid);
	throwgrid.setNum(num);
	throwgrid.m_effects = pgrid->m_effects;
	throwItem(throwgrid);

	getBackPack()->removeItem(backpack_index, num);
}

int ClientPlayer::gainItems(int itemid, int num, int prioritytype, bool save)
{
	if (!getWorld()) { return 0; }
	ItemDef* itemDef = GetDefManagerProxy()->getItemDef(itemid);
	if (!itemDef)
		return 0;

	int addnum = 0;
	if (itemDef->IsDefCustomGun || itemDef->IsDefEquip)
	{
		//新枪械和新装备，需要随机词条，单独处理
		addnum = GunSmithMgr::GetInstance().MakeEquipOrGunForPlayerByItemId(itemid, num, this);
		if (addnum < num)
		{
			for (int j = 0; j < num - addnum; ++j)
			{
				int h = getThrowItemHeight();
				ClientItem* item = getActorMgr()->spawnEquipOrGun(getPosition() + WCoord(0, h, 0), itemid);
				if (item == NULL) return -1;

				Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
				Rainbow::Vector3f dir;

				float r = 30.0f;
				PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
				motion.x = dir.x * r;
				motion.z = dir.z * r;
				motion.y = dir.y * r + 10.0f;

				r = 2.0f * GenRandomFloat();
				float angle = GenRandomFloat() * 360.0f;

				motion.x += r * Cos(angle);
				motion.z += r * Rainbow::Sin(angle);
				motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
			}
		}
	}
	else
	{
		addnum = getBackPack()->addItem(itemid, num, prioritytype);
		if (addnum < num)
		{
			int h = getThrowItemHeight();
			ClientItem* item = getActorMgr()->spawnItem(getPosition() + WCoord(0, h, 0), itemid, num - addnum);
			if (item == NULL) return -1;

			Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
			Rainbow::Vector3f dir;

			float r = 30.0f;
			PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
			motion.x = dir.x * r;
			motion.z = dir.z * r;
			motion.y = dir.y * r + 10.0f;

			r = 2.0f * GenRandomFloat();
			float angle = GenRandomFloat() * 360.0f;

			motion.x += r * Cos(angle);
			motion.z += r * Rainbow::Sin(angle);
			motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
		}
	}
#ifdef IWORLD_SERVER_BUILD
	if (save && GetWorldManagerPtr()) {
		bool needSavePlayers = false;
		SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
		if (sandboxResult.IsExecSuccessed())
		{
			needSavePlayers = sandboxResult.GetData_Bool();
		}
		if (needSavePlayers)
			this->saveToFile(0, GetWorldManagerPtr()->m_ChunkIOMgr);
		jsonxx::Object logger_json;
		logger_json << "itemid" << itemid;
		logger_json << "num" << num;
		logger_json << "save" << needSavePlayers;
		Rainbow::GetICloudProxyPtr()->InfoLog(this->getUin(), GetWorldManagerPtr()->getWorldId(), "player_gain_items", logger_json);
	}
#endif
	return addnum;
}

int ClientPlayer::socgainItems(int itemid, int num, int prioritytype, bool save)
{
	GridCopyData grid_copy_data;
	grid_copy_data.resid = itemid;
	grid_copy_data.num = num;
	int addnum = getBackPack()->addItemWithPickUp_bySocGridCopyData(grid_copy_data,false);
	if (addnum < num)
	{
		int h = getThrowItemHeight();
		ClientItem* item = getActorMgr()->spawnItem(getPosition() + WCoord(0, h, 0), itemid, num - addnum);
		if (item == NULL) return -1;

		Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
		Rainbow::Vector3f dir;

		float r = 30.0f;
		PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		motion.x = dir.x * r;
		motion.z = dir.z * r;
		motion.y = dir.y * r + 10.0f;

		r = 2.0f * GenRandomFloat();
		float angle = GenRandomFloat() * 360.0f;

		motion.x += r * Cos(angle);
		motion.z += r * Rainbow::Sin(angle);
		motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
	}

	return addnum;
}

int ClientPlayer::gainItemsUserdata(int itemid, int num, const char *userdata_str, int prioritytype) {

	GridCopyData gridcopydata;
	gridcopydata.resid = itemid;
	gridcopydata.num = num;
	gridcopydata.userdata_str = userdata_str;
	int addnum =  getBackPack()->addItem_byGridCopyData(gridcopydata, 1);
	return addnum;
}

int ClientPlayer::gainItemsByGrid(const BackPackGrid& grid, int prioritytype)
{
	int num = grid.getNum();
	int itemid = grid.getItemID();
	int addnum = getBackPack()->addItem_byGrid(itemid, num, grid, prioritytype);
	if (addnum < num)
	{
		int h = getThrowItemHeight();
		ClientItem* item = getActorMgr()->spawnItem(getPosition() + WCoord(0, h, 0), grid);
		if (item == NULL) return -1;

		Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
		Rainbow::Vector3f dir;

		float r = 30.0f;
		PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		motion.x = dir.x * r;
		motion.z = dir.z * r;
		motion.y = dir.y * r + 10.0f;

		r = 2.0f * GenRandomFloat();
		float angle = GenRandomFloat() * 360.0f;

		motion.x += r * Cos(angle);
		motion.z += r * Rainbow::Sin(angle);
		motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
	}

	return addnum;
}

int ClientPlayer::gainItemsByIndex(int gindex, int num, int priorityType, bool ignoreGodMode/*true*/)
{
	if (ignoreGodMode)
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) return 0;

	auto bp = getBackPack();
	if (bp == nullptr) return 0;

	auto grid = bp->index2Grid(gindex);
	if (grid == nullptr) return 0;

	int itemid = grid->def ? grid->def->ID : 0;
	int tot = grid->getNum(); // 原本有tot个	// 要拿num个
	if (tot < num) num = tot; // 最多拿tot个
	int addnum = getBackPack()->takeItemFrom(gindex, num); // 实际放得下addnum个
	// 从熔炼炉拿出产物
	if (grid->def && itemid > 0 && addnum > 0 && (gindex == FURNACE_START_INDEX + 2 || gindex >= FURNACE_START_INDEX + 5 && gindex <= FURNACE_START_INDEX + 9))
	{
		ObserverEvent obevent;
		obevent.SetData_EventObj(this->getObjId());
		obevent.SetData_Item(itemid, addnum);
		if (grid->def->Type == ITEM_TYPE_BLOCK)
		{
			obevent.SetData_Block(itemid);
		}
		GetObserverEventManager().OnTriggerEvent("Item.Pickup", &obevent);
	}

	// if (addnum < num) // 试图拿num个，放背包addnum个，剩下的扔地上
	// {
	// 	int h = getThrowItemHeight();
	// 	BackPackGrid spawngrid(*grid);
	// 	spawngrid.setNum(num - addnum);
	// 	ClientItem* item = getActorMgr()->spawnItem(getPosition() + WCoord(0, h, 0), spawngrid);
	// 	if (item == NULL) return addnum;

	// 	Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
	// 	Rainbow::Vector3f dir;

	// 	float r = 30.0f;
	// 	PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	// 	motion.x = dir.x * r;
	// 	motion.z = dir.z * r;
	// 	motion.y = dir.y * r + 10.0f;

	// 	r = 2.0f * GenRandomFloat();
	// 	float angle = GenRandomFloat() * 360.0f;

	// 	motion.x += r * Cos(angle);
	// 	motion.z += r * Rainbow::Sin(angle);
	// 	motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
	// }

	return addnum;
}

int CalDropItemCallCount(int rob_enchant_level, float addprob[3])
{
	float r = GenRandomFloat();

	if (rob_enchant_level == 1)
	{
		addprob[0] += 0.16f;
		addprob[1] = -1.0f;
		addprob[2] = -1.0f;
	}
	else if (rob_enchant_level == 2)
	{
		addprob[0] += 0.16f;
		addprob[1] += 0.08f;
		addprob[2] = -1.0f;
	}
	else if (rob_enchant_level == 3)
	{
		addprob[0] += 0.20f;
		addprob[1] += 0.10f;
		addprob[2] += 0.04f;
	}

	if (r < addprob[2])
	{
		return 4;
	}
	else if (r < addprob[1])
	{
		return 3;
	}
	else if (r < addprob[0])
	{
		return 2;
	}

	return 1;
}

void ClientPlayer::doActualAttack(ClientActor* target, int targetIndex)
{
	if (!target)
	{
		return;
	}
	//LOG_INFO("ClientPlayer::doActualAttack(): %d -> %d", getUin(), target->getObjId());
	OneAttackData atkdata;
	// 新伤害计算系统 code-by:liya
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
	{
		if (!getPunchAtkDataNew(atkdata, target, targetIndex)) return;
	}
	else
	{
		if (!getPunchAtkData(atkdata, target)) return;
	}

	int firebufflv = 0;
	bool bNoEffect = false;
	BackPackGrid *grid = getLivingAttrib()->getEquipGrid(EQUIP_WEAPON);
	if (grid && grid->def)
	{
		if (7 == grid->def->Type || 8 == grid->def->Type || 10 == grid->def->Type)
		{//远程武器左键直接攻击不能点燃
			bNoEffect = true;
		}
	}
	int firebuff = bNoEffect ? 0 : getLivingAttrib()->getFireAspect(firebufflv);

	ActorLiving* targetliving = dynamic_cast<ActorLiving*>(target);
	if (targetliving && firebuff > 0 && !target->isBurning())
	{
		auto FireBurnComp = target->sureFireBurnComponent();
		if (FireBurnComp)
		{
			FireBurnComp->setFire(firebuff, firebufflv, -1, getObjId());
		}
	}

	int bleedbufflv = 0;
	int bleedbuff = bNoEffect ? 0 : getLivingAttrib()->getBleedAspect(bleedbufflv);
	if (targetliving && bleedbuff > 0)
	{
		targetliving->getLivingAttrib()->addBuff(bleedbuff, bleedbufflv, -1, 0, getObjId());
	}

	//处理冰霜攻击附魔
	int icebufflv = 0;
	int icebuff = getLivingAttrib()->getIceAspect(icebufflv);
	if (targetliving && icebuff > 0)
	{
		if (icebufflv == 5)
		{
			if (GenRandomInt(100) < 20)
			{
				targetliving->getLivingAttrib()->addBuff(icebuff, icebufflv, -1, 0, getObjId());
			}
			else
			{
				targetliving->getLivingAttrib()->addBuff(icebuff, 4, -1, 0, getObjId());
			}
		}
		else
		{
			targetliving->getLivingAttrib()->addBuff(icebuff, icebufflv, -1, 0, getObjId());
		}
	}

	float addprop[4];
	addprop[0] = getGeniusValue(GENIUS_ROB_MOBDROP, &addprop[1]);
	ClientMob::m_DropItemCallCount = CalDropItemCallCount((int)getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ROB), addprop);
	float fAddRadio = getPlayerAttrib()->getRandomAttValueWithStatus(BuffAttrType::BUFFATTRT_GOODLUCK_HUNTING);
	if (fAddRadio > 0.01f) {
		ClientMob::m_DropItemCallCount = (int)(ClientMob::m_DropItemCallCount * fAddRadio);
	}

	bool damaged = false;
	auto component = target->getAttackedComponent();
	if (component)
	{
		damaged = component->attackedFrom(atkdata, this);
	}
	if (damaged)
	{
		// if (atkdata.knockback > 0)
		// {
		// 	//Rainbow::Vector3f dir = Yaw2FowardDir(m_LocoMotion->m_RotateYaw);
		// 	//target->getLocoMotion()->addMotion(dir.x*knockback*50.0f, 10.0f, dir.z*knockback*50.0f);
		// 	getLocoMotion()->m_Motion.x *= 0.6f;
		// 	getLocoMotion()->m_Motion.z *= 0.6f;

		// }

		if (atkdata.critical)
		{
			ParticlesComponent::playParticles(target, "1003.ent");
		}

		if (atkdata.enchant_atk > 0)
		{
		}
	}

	if (damaged && targetliving)	// 普攻成功了才触发闪电链
	{
		float baseChainDamage = 0;
		int baseChainCnt = (int)getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_LIGHTNING_CHAIN, ATTACK_ALL, ATTACK_TARGET_ALL, &baseChainDamage);	// 附魔闪电链等级
		if (baseChainCnt > 0 && GenRandomInt(2) > 0)	// 50概率
		{
			auto pComponent = targetliving->getLightningChainComponent();
			if (pComponent)
			{
				auto srcObjId = getObjId();
				baseChainCnt--;
				pComponent->addChain(srcObjId, srcObjId, baseChainCnt, baseChainDamage, targetliving->getTeam(), LightningChainComponent::ChainType::RUNE, { srcObjId });
			}
		}
	}

	ClientMob::m_DropItemCallCount = 1;
	if (damaged)
	{
		//装备减去耐久
		int duration = 1;
		BackPackGrid* grid = getLivingAttrib()->getEquipGrid(EQUIP_WEAPON);
		if (grid->def != NULL)
		{
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(grid->def->ID);
			if (tooldef != NULL && tooldef->Duration > 0)
			{
				duration = tooldef->AtkDuration;
			}
		}
		getLivingAttrib()->damageEquipItemWithType(EQUIP_WEAPON, duration);
		getPlayerAttrib()->useStamina(STAMINA_ATTACK);
		//攻击消耗体力加上buff效果   code-by:曹泽港
		float cost = GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_attack;
		cost = cost + m_PlayerAttrib->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_ALL_SPEND_STRENGTH, cost);
		m_PlayerAttrib->addStrength(-cost);
	}
}

void ClientPlayer::doActualRangeAttack(ClientActor *target)
{
	//LOG_INFO("doActualRangeAttack");
	int toolId = getCurToolID();
	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(toolId);
	if (toolId == 0 || tooldef == NULL)
	{
		LOG_WARNING("doActualRangeAttack Error id: %d", toolId);
		return;
	}

	//部分物理道具埋点：弹珠枪、木箱子、桌球、排球、菠萝手雷、罐装手雷
	int phyitems[6] = { 12294,12296,12297,12298,15007,15008 };
	for (int idx = 0; idx < (sizeof(phyitems) / sizeof(*phyitems)); idx++)
		if (toolId == phyitems[idx])
		{
			char sItemID[10];
			sprintf(sItemID, "%d", toolId);
			if (g_pPlayerCtrl && !m_pWorld->isRemoteMode())
			{
				//g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 30002, "", g_pPlayerCtrl->getCurWorldType(), sItemID);
				break;
			}

		}

	getBody()->playAnim(SEQ_TOOL_ATTACK);
	//int pullticks = m_OperateTicks;
	float shotPower;

	//for click to projectile item 
	if (GetDefManagerProxy()->getItemDef(toolId, true)->UseTarget == 3)
	{
		shotPower = 1;
	}
	else
	{
		shotPower = m_RangeAttackPower;
		//shotPower = float(pullticks) / (20.0f * tooldef->AccumulatorTime);
	}

	if (shotPower > 1.0f) shotPower = 1.0f;

	shotPower = (shotPower*shotPower + shotPower * 2.0f) / 3.0f;

	float comparePower = 0.1f;
	if (GetClientInfoProxy()->isPC())
	{
		comparePower = 0.15f;
	}
	else if (GetClientInfoProxy()->isMobile())
	{
		comparePower = 0.1f;
	}
	else if (GetClientInfoProxy()->isPureServer()) //这里要考虑到云服
	{
		comparePower = 0.15f;
	}
	if (!m_pWorld->isRemoteMode() && shotPower >= comparePower)
	{
		rangeAttackWithPower(toolId, shotPower);
	}
}

void ClientPlayer::doActualChargeAttack()
{
	if (isInSpectatorMode())
	{
		return;
	}

	getBody()->playAnim(SEQ_TOOL_ATTACK);
	playToolSound(-1, true);

	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());

    //释放道具技能
    SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_doActualChargeAttack",
        SandboxContext(nullptr)
        .SetData_Userdata("World", "world", m_pWorld)
        .SetData_Userdata("ClientPlayer", "player", this)
    );
	if (!result.IsExecuted() || result.IsFailed())
    {
        playToolEffect(4, true);
        playToolSound(4, false);

        if (tooldef == NULL) return;
        if (tooldef->BodyAtkEffect[0]) 
			playMotion(tooldef->BodyAtkEffect);
		if (tooldef->AtkSound[0])
		{
			auto soundComp = getSoundComponent();
			if (soundComp)
			{
				soundComp->playSound(tooldef->AtkSound, 1.0f, 1.0f, 0);
			}
		}
    }

	std::vector<ClientActor *>actors;

	int duration;
	float stamina;
	if (tooldef->Level == 4)
	{
		if (hasUIControl())
			MINIW::ScriptVM::game()->callFunction("ShowScreenEffect", "i", 2);

		if (m_pWorld->isRemoteMode()) return;

		MINIW::WorldRay ray;
		WCoord center = getPosition() + WCoord(0, BLOCK_SIZE / 2, 0);
		ray.m_Origin = center.toWorldPos();
		ray.m_Dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
		ray.m_Range = 7 * BLOCK_FSIZE;

		IntersectResult result;
		if (m_pWorld->pickGround(ray, &result, PICK_METHOD_SOLID))
		{
			if (ray.m_Range > result.collide_t + BLOCK_FSIZE) ray.m_Range = result.collide_t + BLOCK_FSIZE;
		}

		getFacedActors(actors, ray.m_Dir, (int)ray.m_Range, 120);
		duration = 2;
		stamina = 2.0f;


	}
	else if (tooldef->Level == 5)
	{
		if (hasUIControl())
			MINIW::ScriptVM::game()->callFunction("ShowScreenEffect", "i", 1);

		if (m_pWorld->isRemoteMode()) return;
		WCoord oldpos = getPosition();

		CollideAABB box;
		getCollideBox(box);
		Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
		dir.y = 0.02f;
		WCoord mvec(dir * (6 * BLOCK_FSIZE));
		Rainbow::Vector3f colnormal;
		float t = m_pWorld->moveBox(box, mvec, colnormal);

		getFacedActors(actors, dir, (int)(t*mvec.length()), 80);

		bool effective_attack = false;
		auto RidComp = getRiddenComponent();
		for (size_t i = 0; i < actors.size(); i++)
		{
			ClientActor *target = actors[i];
			//if (target == this || target->getObjId() == m_RidingActor || target->getObjId() == m_RiddenByActor) continue;
			if (target == this || (RidComp && (RidComp->checkRidingByActorObjId(target->getObjId()) || RidComp->checkRiddenByActorObjId(target->getObjId())))) continue;
			if (target == NULL || target->isDead()) continue;
			if (!target->canAttackWithItem()) continue;
			if (!canHurtActor(target)) continue;

			effective_attack = true;
			break;
		}

		if (effective_attack && hasUIControl())
			g_pPlayerCtrl->triggerHeadshotTip();

		WCoord realmove = mvec * t;
		getLocoMotion()->addRealMove(box, realmove);
		getLocoMotion()->m_Motion = Rainbow::Vector3f(0.0f, 0.0f, 0.0f);

		if (!hasUIControl()) {
			syncPos2Client();
			auto functionWrapper = getFuncWrapper();
			if (functionWrapper) functionWrapper->setMustSyncPos(true);
		}
		duration = 5;
		stamina = 3.33f;
	}
	else if (tooldef->Level == 6)
	{
		if (hasUIControl())
			MINIW::ScriptVM::game()->callFunction("ShowScreenEffect", "i", 1);

		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(324);//钛金镰刀技能

		if (tooldef->ID == 12010)//钛金锤技能
			skilldef = GetDefManagerProxy()->getItemSkillDef(325);

		if (skilldef)
		{
			if (skilldef->RangeType == 2) //立方体
			{
				CollideAABB box;
				getCollideBox(box);

				box.expand(skilldef->RangeVal1, skilldef->RangeVal3, skilldef->RangeVal2);
				std::vector<IClientActor*> iactors;
				m_pWorld->getActorsInBox(iactors, box);
				actors.resize(0);
				for (size_t i = 0; i < iactors.size(); i++)
				{
					actors.push_back(static_cast<ClientActor*>(iactors[i]));
				}
			}
			else if (skilldef->RangeType == 3) //圆柱体
			{
				Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
				dir.y = 0.02f;
				
				getLocoMotion()->getFanShapedAreaFacedActors(actors, dir, skilldef->RangeVal1, skilldef->RangeVal3, skilldef->RangeVal2);
			}
		}
		duration = 5;
		stamina = 2.0f;
	}
	else return;

	int ndamaged = 0;
	OneAttackData atkdata;
	float addprop[4];
	addprop[0] = getGeniusValue(GENIUS_ROB_MOBDROP, &addprop[1]);
	ClientMob::m_DropItemCallCount = CalDropItemCallCount((int)getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ROB), addprop);

	float fAddRadio = getPlayerAttrib()->getRandomAttValueWithStatus(BuffAttrType::BUFFATTRT_GOODLUCK_HUNTING);
	if (fAddRadio > 0.01f) {
		ClientMob::m_DropItemCallCount = (int)(ClientMob::m_DropItemCallCount * fAddRadio);
	}

	float fpercent = 0.0f;
	WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
	if (weaponSkinMgr != NULL)
	{
		//皮肤加成 - 蓄力伤害增加X%
		const char* skillType = "WeaponSkin_System_SkillAttack";
		fpercent = weaponSkinMgr->GetSkinAdditionPercent(skillType, this);
	}

	// 区别于普攻的0
	int touReduce = -1;
	const ItemDef* def = GetDefManagerProxy()->getItemDef(getCurToolID());
	if (def)
	{
		for (int i = 0; i < (int)def->SkillID.size(); i++)
		{
			const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
			if (skilldef)
			{
				for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
					// 削韧技能
					if (functiondef->oper_id == 22)
					{
						touReduce = functiondef->func.toughnessReduceFun.touReduce;
					}
				}
			}
		}
	}

	auto RidComp = getRiddenComponent();

	// 范围伤害控制参数（现在是随机的，后续可以根据离玩家距离，对actors进行排序） code-by:liya
	int paramA = GetLuaInterfaceProxy().get_lua_const()->dampingControlA;
	int paramB = GetLuaInterfaceProxy().get_lua_const()->dampingControlB;
	int paramC = 0;
	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientActor *target = actors[i];
		//if (target == this || target->getObjId() == m_RidingActor || target->getObjId() == m_RiddenByActor) continue;
		if (target == this || (RidComp && (RidComp->checkRidingByActorObjId(target->getObjId()) || RidComp->checkRiddenByActorObjId(target->getObjId())))) continue;
		// 新伤害计算系统 code-by:liya
		if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		{
			if (!getPunchAtkDataNew(atkdata, target)) return;
			// 蓄力伤害加成放入蓄力修正中
			if (fpercent > 0.0f)  
				atkdata.charge += fpercent;

			// 范围伤害衰减系数
			if (i >= 1)
			{
				paramC = i + 1;
				if (paramC > 7) paramC = 7;
				atkdata.damping = (float)paramA / (paramB * paramC);
			}
		}
		else
		{
			if (!getPunchAtkData(atkdata, target)) return;

			if (fpercent > 0.0f)  //蓄力伤害加成
				atkdata.atkpoints = atkdata.atkpoints * (1 + fpercent);
		}
		atkdata.touReduce = touReduce;

		if (!canHurtActor(target)) continue;

		if (tooldef->ID == 12010)//钛金锤
		{
			atkdata.knockup = 1.5f;
		}
		else
		{
			atkdata.knockback += 0.5f;
			atkdata.knockup += 0.5f;
		}

		auto component = target->getAttackedComponent();
		if (component)
		{
			if (component->attackedFrom(atkdata, this)) ndamaged++;
		}
	}
	ClientMob::m_DropItemCallCount = 1;

	getLivingAttrib()->damageEquipItemWithType(EQUIP_WEAPON, duration);
	getPlayerAttrib()->useStamina(STAMINA_ATTACK, stamina);

	if (tooldef->SkillCD > 0)
	{
		setSkillCD(tooldef->ID, tooldef->SkillCD);
		syncSkillCD(tooldef->ID, tooldef->SkillCD);
	}
}

void ClientPlayer::rangeAttackWithPower(int toolId, float shotPower)
{
	float pitch = 1.0f / (GenRandomFloat()*0.4f + 1.2f) + shotPower * 0.5f;
	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(toolId);
	if (tooldef == NULL) return;
	if (shotPower > 1.0f) shotPower = 1.0f;
	int enchantArrowFreeValue = (int)getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_FREE);
	bool isEnchantArrowFree = enchantArrowFreeValue > 0;
	bool canpickup = false;
	int consumeid = tooldef->ConsumeID == -1 ? tooldef->ID : tooldef->ConsumeID;

	if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) && !isEnchantArrowFree && tooldef->Type != 24)
	{
		//看数量是否够
		if (getBackPack()->getItemCountInNormalPack(consumeid) < tooldef->ConsumeCount)
		{
			return;
		}
		//从背包减去,直接投掷的物品不在这里消耗， 在PlayerAttrib::onCurToolUsed里面消耗
		if (GetDefManagerProxy()->getItemDef(toolId, true)->UseTarget == ITEM_USE_BOW)
		{
			getBackPack()->removeItemInNormalPack(consumeid, tooldef->ConsumeCount);
			consumeItemOnTrigger(consumeid, tooldef->ConsumeCount);
		}

		if (tooldef->ConsumeCount > 0)
		{
			canpickup = true;
		}
	}

	if (!tooldef->FireSound.empty())
	{
		auto soundComp = getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound(tooldef->FireSound, 1.0f, pitch);
		}
	}

	bool setfire = false;
	int fireLv = 0;
	int fireBuffId = getLivingAttrib()->getFireAspect(fireLv);

	if (fireBuffId > 0) {
		setfire = true;
	}

	if (toolId == ITEM_ARBATOR)
	{
		if (!setfire)
		{
			setfire = getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_EXPLODE) > 0;	// 爆炸箭。。
			fireLv = 1;
		}
		auto* projectile1 = ProjectileFactory::throwItemByActorSpecifyTransform(m_pWorld, this, getLocoMotion()->m_RotateYaw - 6, getLocoMotion()->m_RotationPitch, getEyePosition(), shotPower, consumeid, setfire, canpickup, fireLv);
		auto* projectile2 = ProjectileFactory::throwItemByActor(m_pWorld, this, shotPower, consumeid, setfire, canpickup, fireLv);
		auto* projectile3 = ProjectileFactory::throwItemByActorSpecifyTransform(m_pWorld, this, getLocoMotion()->m_RotateYaw + 6, getLocoMotion()->m_RotationPitch, getEyePosition(), shotPower, consumeid, setfire, canpickup, fireLv);

		if (tooldef->Type == 7)		// 弓类的都记录下符文信息
		{
			BackPackGrid* pgrid = getBackPack()->index2Grid(getCurShortcut() + getShortcutStartIndex());
			projectile1->setProperty_byGrid(pgrid);
			projectile2->setProperty_byGrid(pgrid);
			projectile3->setProperty_byGrid(pgrid);
		}
	}
	else
	{
		//发出投射物
		int projecttileNum = tooldef->ConsumeCount > 0 ? tooldef->ConsumeCount : 1;
		for (int i = 0; i < projecttileNum; i++)
		{
			//ClientActorArrow *arrow = ClientActorArrow::shootArrow(m_pWorld, this, shotPower, ARROW_PICKABLE , setfire);
			BackPackGrid *pgrid = getBackPack()->index2Grid(getCurShortcut() + getShortcutStartIndex());
			ClientActorProjectile *projectile = ProjectileFactory::throwItemByActor(m_pWorld, this, shotPower, consumeid, setfire, canpickup, fireLv);
			if (consumeid == toolId)
				projectile->setProperty_byGrid(pgrid);
			if (consumeid == 12062 && toolId == 12061) {//海洋箭和鲨鱼弓时，海洋箭需要设置符文信息
				projectile->setProperty_byGrid(pgrid);
			}
			//一些特殊道具需要记录数据
			if (pgrid && (consumeid == BLOCK_DRIFTBOTTLE || consumeid == BLOCK_FAR_DRIFTBOTTLE))
			{
				projectile->m_UserDataStr = pgrid->userdata_str;
			}
			if (tooldef->Type == 7)		// 弓类的都记录下符文信息
			{
				projectile->setProperty_byGrid(pgrid);
			}
		}
	}

	if (GetDefManagerProxy()->getItemDef(toolId, true)->UseTarget == ITEM_USE_CHARGETHROW)
	{
		shortcutItemUsed(true);
	}

	//ͨ通过武器发射则消耗耐久
	if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) && (consumeid != toolId || GetDefManagerProxy()->getItemDef(toolId, true)->UseTarget == ITEM_USE_HOOK))
	{
		int dur = -tooldef->AtkDuration;
		if (enchantArrowFreeValue > 0)
		{
			dur = -enchantArrowFreeValue;
		}
		//减武器耐久
		addCurToolDuration(-tooldef->AtkDuration);
	}

	if (tooldef->Type == 24 && !(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) && tooldef->SkillCD > 0) //法杖有cd
	{
		setSkillCD(tooldef->ID, tooldef->SkillCD);
		syncSkillCD(tooldef->ID, tooldef->SkillCD);
	}
}

extern unsigned int GetTeamNameColor(int teamid);

static void ReplacePlayerNameStr(char *content, const char *src, ClientPlayer *self, ClientPlayer *other)
{
	int count = 0;
	while (*src)
	{
		if (*src == '@')
		{
			int teamid = 0;
			const char *pstr = NULL;

			if (strncmp(src, "@self", 5) == 0) { pstr = self->getNickname(); teamid = self->getTeam(); src += 5; }
			else if (strncmp(src, "@other", 6) == 0) { pstr = other->getNickname(); teamid = other->getTeam(); src += 6; }

			if (pstr)
			{
				unsigned int c = GetTeamNameColor(teamid);
				count += sprintf(content + count, "#c%x%s#n", c, pstr);
			}
			else content[count++] = *src++;
		}
		else content[count++] = *src++;
	}
	content[count] = 0;
}

int ClientPlayer::killedByActor(WORLD_ID* pobjid/*=NULL*/)
{
	ClientActor *actor = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(getBeHurtTargetID());
	if (actor == NULL) return 0;

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);

	if (player)
	{
		if (player != this) //自己杀死自己不算分
		{
			player->m_CWKills++;
			player->m_Kills++;
			player->addGameScoreByRule(GMRULE_SCORE_KILLPLAYER);
			player->defeatActorOnTrigger(getUin());
			player->addWeaponSkilledPoint(WEAPON_SKILLED_TYPE_KILLED);

			std::ostringstream oss;
			oss << "" << getPosition().x << "," << getPosition().y << "," << getPosition().z << "";
			std::string location = oss.str();

			GameAnalytics::TrackEvent("player_kill", {
				{"victim_id",getUin()},
				{"victim_name",getNickname()},
				{"killer_id",player->getUin()},
				{"killer_name",player->getNickname()},
				{"weapon_id", player->getCurToolID()},
				{"loc",location}
			});

			//LLDO:击杀展示.
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_KILLPLAYER, getUin(), player->m_CWKills);

			// 返回
			if (pobjid)
				*pobjid = player->getUin();
		}

		if (GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_KILLNOTIFY) > 0)
		{
			char sndname[64];
			char content[256];
			ReplacePlayerNameStr(content, GetDefManagerProxy()->getStringDef(229 + GenRandomInt(3)), player, this);
			jsonxx::Object extend;
			extend << "msgtype" << "killmsg";
			extend << "killer" << player->getUin();
			GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(content, 1, 0, 1, extend.json().c_str());

			m_pWorld->getEffectMgr()->playSound(getPosition(), "pvp.kill", 1.0f, 1.0f, PLAYSND_LONGDIST | PLAYSND_SYNC);

			int id = player->m_CWKills;
			if (id >= 2)
			{
				if (id > 10) id = 10;

				ReplacePlayerNameStr(content, GetDefManagerProxy()->getStringDef(id - 2 + 232), player, this);
				GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(content, 1);
			}
			sprintf(sndname, "pvp.kill_%d", Rainbow::Clamp(id, 1, 6));
			m_pWorld->getEffectMgr()->playSound(getPosition(), sndname, 1.0f, 1.0f, PLAYSND_LONGDIST | PLAYSND_SYNC);

			//终结连杀的提示
			if (m_CWKills >= 3)
			{
				int id = Rainbow::Clamp(m_CWKills, 3, 10);
				ReplacePlayerNameStr(content, GetDefManagerProxy()->getStringDef(243 + id - 3), player, this);
				GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(content, 1);
			}
		}

		return 1;
	}

	ClientMob *mob = dynamic_cast<ClientMob *>(actor);
	if (mob)
	{
		// 返回
		if (pobjid)
			*pobjid = (WORLD_ID)mob->getObjId();

		mob->addGameScoreByRule(GMRULE_SCORE_KILLPLAYER);
		return 2;
	}

	//被怪物发出的投射物打死，也算是被怪物打死
	ClientActorProjectile *projectile = dynamic_cast<ClientActorProjectile *>(actor);
	if (projectile)
	{
		ClientMob *mobActor = dynamic_cast<ClientMob *>(projectile->getShootingActor());
		if (mobActor)
		{
			if (pobjid)
				*pobjid = (WORLD_ID)mobActor->getObjId();

			mobActor->addGameScoreByRule(GMRULE_SCORE_KILLPLAYER);
			return 2;
		}
	}

	return 0;
}

void ClientPlayer::doAttrShapeShiftLeftClick()
{
	if (!m_pWorld && !m_PlayerAttrib) return;
	std::vector<StatusAttInfo> infoList;
	m_PlayerAttrib->getStatusAddAttInfo(BUFFATTRT_ATTR_RIGHT_CLICK, infoList);
	if (infoList.size() > 0)
	{
		for (unsigned int i = 0; i < infoList.size(); i++)
		{
			const StatusAttInfo& info = infoList[i];
			if (info.vValue.size() >= 2)
			{
				if (info.vValue[0].value == 3224) // 海灵守卫普通攻击
				{
					if (!m_pWorld->isRemoteMode())
					{
						auto soundComp = getSoundComponent();
						if (soundComp)
						{
							soundComp->playSound("ent.3224.seaspiritattack1", 1.0f, 0.5f);
						}
					}
				}
			}
		}
	}
}

void ClientPlayer::doAttrShapeShiftRightClick()
{
	if (!m_pWorld && !m_PlayerAttrib) return;
	std::vector<StatusAttInfo> infoList;
	m_PlayerAttrib->getStatusAddAttInfo(BUFFATTRT_ATTR_RIGHT_CLICK, infoList);
	if (m_pWorld->isRemoteMode())
	{
		ProjectileFactory::throwItemByMob(m_pWorld, this, 1.0, ITEM_SPOUT, 0);
		playAnim(SEQ_SANDWORM_MARACAS);
		auto soundComp = getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound("ent.3224.seaspiritattack2", 1.0f, 0.5f);
		}
		jsonxx::Object context;
		context << "uin" << getUin();
		SandBoxManager::getSingleton().sendToHost("PB_ATTR_SHAPE_SHIFT_RIGHT_CLICK_CH", context.bin(), context.binLen());
		return;
	}
	if (infoList.size() > 0)
	{
		for (unsigned int i = 0; i < infoList.size(); i++)
		{
			const StatusAttInfo& info = infoList[i];
			if (info.vValue.size() >= 2)
			{
				if (m_AttrRightClickTick > info.vValue[1].value) // 满足cd
				{
					if (info.vValue[0].value == 3224) // 海灵守卫激光攻击
					{
						if (!m_pWorld->isRemoteMode())
						{
							m_AttrRightClickTick = 0;
							ProjectileFactory::throwItemByMob(m_pWorld, this, 1.0, ITEM_SPOUT, 0);
							playAnim(SEQ_SANDWORM_MARACAS);
							auto soundComp = getSoundComponent();
							if (soundComp)
							{
								soundComp->playSound("ent.3224.seaspiritattack2", 1.0f, 0.5f);
							}
						}
					}
				}
			}
		}
	}
}

void ClientPlayer::attrShapeShiftAttackedFrom(OneAttackData& atkdata, ClientActor* inputattacker)
{
	if (!m_PlayerAttrib)
		return;

	if (inputattacker)
	{
		ClientActorProjectile* actor = dynamic_cast<ClientActorProjectile*>(inputattacker);
		if (actor && actor->m_ProjectileDef && actor->m_ProjectileDef->ID == ITEM_AIR_BALL)// 被空气炮击中
		{
			std::vector<StatusAttInfo> infoList;
			m_PlayerAttrib->getStatusAddAttInfo(BUFFATTRT_ATTR_SHAPESHIFT, infoList);
			if (infoList.size() > 0)
			{
				for (unsigned int i = 0; i < infoList.size(); i++)
				{
					const StatusAttInfo& info = infoList[i];
					if (info.vValue.size() >= 3)
					{
						if (info.vValue[0].value == 3224) // 海灵守卫
						{
							if (m_PlayerAttrib->m_AttrShapeShiftDef)
							{
								atkdata.atkpoints *= 1.2f;
								m_AttrStopRecoverTick = info.vValue[2].value * 20;
								char path[256];
								sprintf(path, "entity/%s/male3.png", m_PlayerAttrib->m_AttrShapeShiftDef->Model.c_str());
								if (getBody())
									getBody()->changeBodyTex(path, "rtexbody");
								if (!m_pWorld->isRemoteMode())
								{
									jsonxx::Object context;
									context << "male" << 3;
									SandBoxManager::getSingleton().sendToClient(getUin(), "PB_ATTR_SHAPE_SHIFT_SYNC", context.bin(), context.binLen());
									return;
								}
							}
						}
					}
				}
			}
		}
	}
}

void  ClientPlayer::AttackFromSharkBite(ClientMob* actor) {

	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", actor->getObjId());
		context << "objid" << objid_str;
		char objid_str2[128];
		sprintf(objid_str2, "%lld", getObjId());
		context << "objid2" << objid_str2;
		context << "data" << actor->getData();
		SandBoxManager::getSingleton().sendToClient(getUin(), "PB_ACTOR_SHARK_BITE_PLAYER_MOVE", context.bin(), context.binLen());
	}

	if (g_pPlayerCtrl && g_pPlayerCtrl->getUin() == getUin() && actor->getData() == 1)
	{
		int viewMode = g_pPlayerCtrl->getViewMode();
		if (viewMode != CameraControlMode::CAMERA_TPS_BACK)
		{
			m_ChangeViewMode = true;
			g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_TPS_BACK);
		}
	}
	else if (g_pPlayerCtrl && g_pPlayerCtrl->getUin() == getUin() && m_ChangeViewMode)
	{
		m_ChangeViewMode = false;
		g_pPlayerCtrl->setViewMode(g_pPlayerCtrl->m_oldViewMode);
	}

	if (actor->getData() == 1 && actor->getBody())
	{
		getLocoMotion()->m_Motion.y = 0;
#ifdef DEDICATED_SERVER
		setPosition(actor->getPosition());
#else
		WCoord bindPos = actor->getBody()->getBindPointPos(206);
		setPosition(bindPos);//挂在鲨鱼嘴里
#endif
		
	}
}

// 处理建筑蓝图放置的函数
bool ClientPlayer::TryPlaceBlueprintBlock(int blueprintId, const WCoord &blockpos, DirectionType targetface, const Rainbow::Vector3f &colpoint)
{
    // 获取建筑蓝图数据
	const ArchitecturalBlueprintCsvDef* blueprintDef = GetDefManagerProxy()->getArchitecturalBlueprintCsvDef(blueprintId);
    //const ArchitecturalBlueprintCsvDef* blueprintDef = ArchitecturalBlueprintCsv::GetInstance()->get(blueprintId);
    if (!blueprintDef) {
        return false;
    }
    
    // 查找目标方块ID和所需材料
    int targetBlockId = 0;
    std::map<int, int> requiredMaterials;
    
    // 检查各种材料类型，获取目标方块ID
	if (blueprintDef->Branch.ProduceItemID > 0) {
		targetBlockId = blueprintDef->Branch.ProduceItemID;
		if (blueprintDef->Branch.ConsumeItemID > 0 && blueprintDef->Branch.Count > 0) {
			requiredMaterials[blueprintDef->Branch.ConsumeItemID] = blueprintDef->Branch.Count;
		}
	}
	else if (blueprintDef->Wood.ProduceItemID > 0) {
        targetBlockId = blueprintDef->Wood.ProduceItemID;
        if (blueprintDef->Wood.ConsumeItemID > 0 && blueprintDef->Wood.Count > 0) {
            requiredMaterials[blueprintDef->Wood.ConsumeItemID] = blueprintDef->Wood.Count;
        }
    }
    else if (blueprintDef->Stone.ProduceItemID > 0) {
        targetBlockId = blueprintDef->Stone.ProduceItemID;
        if (blueprintDef->Stone.ConsumeItemID > 0 && blueprintDef->Stone.Count > 0) {
            requiredMaterials[blueprintDef->Stone.ConsumeItemID] = blueprintDef->Stone.Count;
        }
    }
    else if (blueprintDef->Iron.ProduceItemID > 0) {
        targetBlockId = blueprintDef->Iron.ProduceItemID;
        if (blueprintDef->Iron.ConsumeItemID > 0 && blueprintDef->Iron.Count > 0) {
            requiredMaterials[blueprintDef->Iron.ConsumeItemID] = blueprintDef->Iron.Count;
        }
    }
    else if (blueprintDef->Steel.ProduceItemID > 0) {
        targetBlockId = blueprintDef->Steel.ProduceItemID;
        if (blueprintDef->Steel.ConsumeItemID > 0 && blueprintDef->Steel.Count > 0) {
            requiredMaterials[blueprintDef->Steel.ConsumeItemID] = blueprintDef->Steel.Count;
        }
    }
    
    // 如果没有有效的目标方块ID，返回失败
    if (targetBlockId <= 0 || requiredMaterials.empty()) {
        return false;
    }
	bool bCanBreak = false;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())
		bCanBreak = true;

    // 检查玩家背包中是否有足够的材料
    for (const auto& material : requiredMaterials) {
		if (bCanBreak)
		{
			break;
		}
        int itemId = material.first;
        int count = material.second;
		int haveNum = getBackPack()->getItemCountInNormalPack(itemId);
        if (haveNum < count) {
            // 材料不足，提示玩家
            notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 6019); // "材料不足"的提示ID
            return false;
        }
    }
    
    // 获取当前方块信息用于位置计算
    int blockid = m_pWorld->getBlockID(blockpos);
    BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
    if (!pmtl) {
        return false;
    }
    
    // 使用与原始"摆块"代码相同的位置计算逻辑
    WCoord placepos;
    DirectionType hitface = ReverseDirection(targetface);
    bool isCover = false;
    bool isagain = false;
    bool placeInto = false;
    
    // 确定放置位置
    if (pmtl && pmtl->isReplaceable() && targetBlockId != blockid) {
        placepos = blockpos;
        hitface = DIR_NEG_Y;
    }
    else if (pmtl && pmtl->canPlacedAgain(m_pWorld, targetBlockId, colpoint, blockpos, true, hitface)) {
        placepos = blockpos;
        isagain = true;
        placeInto = true;
    }
    else {
        std::vector<int> sandCoverId = GetLuaInterfaceProxy().get_lua_const()->sandCoverId;
        auto iter = std::find(sandCoverId.begin(), sandCoverId.end(), blockid);
        if (iter != sandCoverId.end() && (targetBlockId == BLOCK_SOLIDSAND || targetBlockId == BLOCK_REDSAND)) {
            placepos = blockpos;
            isCover = true;
        }
        else {
            placepos = NeighborCoord(blockpos, targetface);
            isagain = m_pWorld->getBlockMaterial(placepos)->canPlacedAgain(m_pWorld, targetBlockId, colpoint, blockpos, false, hitface);
            if (!m_pWorld->getBlockMaterial(placepos)->isReplaceable() && !isagain) {
                return false;
            }
        }
    }
    
    // 进行放置逻辑
    getBody()->playAttack();
    
    // 使用计算出的placepos进行方块放置
    bool result = placeBlock(targetBlockId, placepos.x, placepos.y, placepos.z, 
                           hitface, colpoint.x, colpoint.y, colpoint.z, placeInto, isagain);
    
    // 如果放置成功，消耗材料
    if (result) {
        // 消耗材料
        for (const auto& material : requiredMaterials) {
            int itemId = material.first;
            int count = material.second;
            getBackPack()->removeItemInNormalPack(itemId, count);
        }
        
        // 触发相关事件
        MNSandbox::GetGlobalEvent().Emit<>("WorldArchiveMgr_OnBlockChangedByManual");
        addAchievement(1, ACHIEVEMENT_INTERACT_BLOCK, blockid);
        updateTaskSysProcess(TASKSYS_INTERACT_BLOCK, blueprintId, targetBlockId);
		updateTaskSysProcess(TASKSYS_USEBLUEBLUEPRINT, targetBlockId);

        // 记录建筑任务
       // updateTaskSysProcess(GeneralTaskType_UseBuildingItem, blueprintId, 1);
        
        // 建筑建造数据统计埋点
        std::ostringstream oss;
        oss << "" << placepos.x << "," << placepos.y << "," << placepos.z << "";
        std::string location = oss.str();
        
        // 获取服务器信息
        std::string server_id = GetGameInfoProxy()->GetGameVar("serverid");
        std::string server_setting = "";
        int current_player_count = GetWorldManagerPtr()->getAllPlayersNum();
        
        // 获取建筑类型信息
        std::string building_type = "";
        if (blueprintDef) {
            // 根据蓝图ID确定建筑类型
            if (blueprintDef->Branch.ProduceItemID > 0) {
                building_type = "branch";
            } else if (blueprintDef->Wood.ProduceItemID > 0) {
                building_type = "wood";
            } else if (blueprintDef->Stone.ProduceItemID > 0) {
                building_type = "stone";
            } else if (blueprintDef->Iron.ProduceItemID > 0) {
                building_type = "iron";
            } else if (blueprintDef->Steel.ProduceItemID > 0) {
                building_type = "steel";
            }
        }
        
        // 获取手持道具ID
        int inhand_item_id = 0;
        if (getEquipGrid(EQUIP_WEAPON)) {
            inhand_item_id = getEquipGrid(EQUIP_WEAPON)->getItemID();
        }
        
        GameAnalytics::TrackEvent("building_build", {
            {"server_id", GameAnalytics::Value(server_id)},                   // 服务器ID (字符串)
            {"server_setting", GameAnalytics::Value(server_setting)},         // 服务器设置 (字符串)
            {"current_player", GameAnalytics::Value(current_player_count)},   // 服务器当前人数 (数值)
            {"loc", GameAnalytics::Value(location)},                          // 坐标 (字符串)
            {"building_id", GameAnalytics::Value(targetBlockId)},             // 建筑ID (数值)
            {"building_type", GameAnalytics::Value(building_type)},           // 建筑类型 (字符串)
            {"inhand_item_id", GameAnalytics::Value(inhand_item_id)}          // 手持道具ID (数值)
        });
        
        return true;
    }
    
    return false;
}

bool ClientPlayer::UseWater(int status, int itemId, int drinkType)
{
	auto pState = dynamic_cast<DrinkWaterState*>(getActionStatePtr("DrinkWater"));
	if (pState && pState->drinkWater(status, itemId, drinkType))
	{
		if (m_CameraModel && drinkType == 1)
		{
			if (status == 0)
			{
				m_CameraModel->playHandAnim(FPS_DRINK);
			}
			else
			{
				m_CameraModel->playHandAnim(FPS_DRINK);
			}
		}
		return true;
	}
	else
	{
		return false;
	}
}

void ClientPlayer::drinkWaterWithBlock(const WCoord& blockPos, int count, int drinkType, int itemId)
{
	if (!m_pWorld->isRemoteMode())
	{
		ConsumptionWaterBlock(blockPos, count);
		getPlayerAttrib()->drinkWater(count, drinkType, itemId);
		auto soundComp = getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound("misc.drink", 1.0f, 1.0f);
		}
	}
	else
	{
		PB_DrinkWaterCH drinkwaterHC;
		drinkwaterHC.set_x(blockPos.x);
		drinkwaterHC.set_y(blockPos.y);
		drinkwaterHC.set_z(blockPos.z);
		drinkwaterHC.set_type(drinkType);
		drinkwaterHC.set_itemid(itemId);

		GameNetManager::getInstance()->sendToHost(PB_DRINKWATERCH, drinkwaterHC);
	}
}

void ClientPlayer::drinkWaterWithBlock(int x, int y, int z, int count, int drinkType, int itemId)
{
	drinkWaterWithBlock(WCoord(x, y, z), count, drinkType, itemId);
}

void ClientPlayer::fillWaterInBug(const WCoord& blockPos, int gridIdx, int sourceType)
{
	if (!m_pWorld->isRemoteMode())
	{
		BackPackGrid* waterbug = sourceType == 2 ? getBackPack()->index2Grid(gridIdx) : getEquipGrid(EQUIP_WEAPON);
		if (waterbug)
		{
			int amount = waterbug->getMaxWaterVolume() - waterbug->getWaterVolume();
			if (amount > 0)
			{
				ConsumptionWaterBlock(blockPos, amount, false);
				waterbug->addWaterVolume(amount);
				getBackPack()->afterChangeGrid(waterbug->getIndex());
				auto soundComp = getSoundComponent();
				if (soundComp)
				{
					soundComp->playSound("misc.fillwater", 1.0f, 1.0f);
				}
			}
		}
	}
	else
	{
		PB_FillWaterCH fillwaterHC;
		fillwaterHC.set_x(blockPos.x);
		fillwaterHC.set_y(blockPos.y);
		fillwaterHC.set_z(blockPos.z);
		fillwaterHC.set_type(sourceType);
		fillwaterHC.set_grididx(gridIdx);
		GameNetManager::getInstance()->sendToHost(PB_FILLWATERCH, fillwaterHC);
	}
}

void ClientPlayer::activeMachineSource(const WCoord& blockPos)
{
	BlockSocMachineSource* pmtl = dynamic_cast<BlockSocMachineSource*>(m_pWorld->getBlockMaterial(blockPos));
	if (pmtl) pmtl->setActiving(m_pWorld, blockPos, this);
	//containerLinkMachine* pcontainer = dynamic_cast<containerLinkMachine*>(m_pWorld->getContainerMgr()->getContainer(blockPos));
	//if (pcontainer) pcontainer->activating();
}

void ClientPlayer::ConsumptionWaterBlock(const WCoord& blockPos, int& amount, bool isLoadAmount)
{
	auto block = m_pWorld->getBlock(blockPos);
	int blockid = block.getResID();
	if (blockid)
	{
		if (isLoadAmount)
		{
			const FoodDef* def = GetDefManagerProxy()->getFoodDef(blockid);
			if (def)
			{
				//获取修改上面计算值
				amount = def->HealThirst * 10;
			}
		}
		auto pblockmtl = g_BlockMtlMgr.getMaterial(blockid);
		if (pblockmtl)
		{
			if (pblockmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_WaterBlock)
			{
				if (pblockmtl)
				{
					int curCount = pblockmtl->getBlockHP(m_pWorld, blockPos) * 10;
					bool isdamage = curCount <= amount;

					amount = isdamage ? curCount : amount;
					//pblockmtl->DoOnBlockDamaged(m_pWorld, blockPos, this, (amount / 10));
					pblockmtl->onBlockDamaged(m_pWorld, blockPos, this, ATTACK_PUNCH,(amount / 10));
					if (isdamage)
					{
						m_pWorld->playerDestroyBlock(blockPos, BLOCK_MINE_NONE, 0, 0, getUin());
						m_pWorld->notifyBlockSides(blockPos, 0);
					}
					return;
				}
			}
			if (pblockmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_WaterStorage)
			{
				BlockWaterStorage* pwsmtl = static_cast<BlockWaterStorage*>(pblockmtl);
				//if (pwsmtl)
				{
					int curCount = pwsmtl->getCurWaterVolume(m_pWorld, blockPos);
					amount = curCount <= amount ? curCount : amount;
					pwsmtl->addWaterVolume(m_pWorld, blockPos, -amount);
					return;
				}
			}
		}
	}
	amount = 0;
}