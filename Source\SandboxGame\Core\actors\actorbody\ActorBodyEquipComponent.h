#pragma once
#include "ActorBody.h"

class World;
class ActorBody;
class BaseItemMesh;
struct GunDef;
struct EquipmentPartDef;


namespace Rainbow
{
	class Model;
	class Entity;
	class ModelData;
	class MovableObject;
	class GameObject;
};

//自定义装备最多有几个部件 
#define MAX_CUSTOMEQUIP_PART_NUM 3

class ActorBodyEquipComponent
{
	friend class ActorBody;
public:

	ActorBodyEquipComponent(ActorBody* actorBody);
	~ActorBodyEquipComponent();

	static void clearEquipItems(Rainbow::Model* pmodel);
	virtual void setEquipItem(EQUIP_SLOT_TYPE slot, int itemid, int idx = -1);

	void putonCustomEquipPart(EquipmentPartDef* pPartDef, int slot, int nPartIndex);//TODO:自定义装备: 挂载自定义装备部件
	void takeoffCustomEquip(int slot);

	EquipmentPartDef* GetUIEquipmentPartDef();

	void getSkinPart(char* name, EQUIP_SLOT_TYPE slot, int itemid);
	void getTexPath(char* name, EQUIP_SLOT_TYPE slot, int itemid);

	void clearEquipSlot(EQUIP_SLOT_TYPE slot);
	void getEquitMesh(int itemid, int& equipmesh);	// 获取新增野人装备的模型
	void ReleaseUIEquipmentPartDef();

	bool equipSkinWeapon(int itemid, int skinid = 0); //装备武器皮肤

	//为假人做的一个放披风的接口
	void equipPifengSpecial(EQUIP_SLOT_TYPE slot, int itemid);
	bool isCustomEquip(int itemId);//自定义装备
	int getCurShowEquipItemId(EQUIP_SLOT_TYPE slot);
	int getCurShowEquipItemIdx(EQUIP_SLOT_TYPE slot);

	//获取武器的枪口的位置
	bool GetWorldPosFromWeapon(int anchorId, Rainbow::Vector3f& boneWorldPos);

	// 枪械配件模型管理方法
	void UpdateGunComponentModels(int itemid);


private:
	//TODO:自定义装备
	void setCustomEquip(EQUIP_SLOT_TYPE slot, int itemid);
	void releaseCustomEquip();
	bool isNormalEquip(int itemid);
	void putonNormalEquip(int itemid);
	void equipWeaponItem(EQUIP_SLOT_TYPE slot, int itemid, int idx = -1);
	void equipPifengItem(EQUIP_SLOT_TYPE slot, int itemid);
	void equipShoeItem(EQUIP_SLOT_TYPE slot, int itemid, char* skinname, int idx = -1);

	void equipNormalItem(EQUIP_SLOT_TYPE slot, int itemid);

	// 枪械配件模型管理方法
	void ClearGunComponentModels();
	void CreateGunComponentModels();
	void ShowGunComponentModels(bool show);

private:
	ActorBody* m_ActorBody = nullptr;

	Rainbow::Entity* m_DorsumEntity;			//背-渲染对象？
	BaseItemMesh* m_WeaponModel;	//武器模型？
	BaseItemMesh* m_WeaponModel_left;	//左手武器模型
	Rainbow::MovableObject* m_HelmetModel;	//头戴物模型？
	BaseItemMesh* m_EquipsModel[MAX_EQUIP_SLOTS];  // 不包括武器

	Rainbow::Entity* m_CustomEquipPartModel[MAX_EQUIP_SLOTS][MAX_CUSTOMEQUIP_PART_NUM];	//自定义装备
	int m_PartAnchorId[MAX_EQUIP_SLOTS][MAX_CUSTOMEQUIP_PART_NUM];						//对应部件挂载的anchorid
	int m_NormalEquipItemIDs[MAX_EQUIP_SLOTS];											//记录对应格子常规装备的itemID
	int m_NormalEquipItemIdx[MAX_EQUIP_SLOTS];											//记录对应装备所在的装备栏下标
	EquipmentPartDef* m_UIEquipmentPartDef;												//装备部件

	// 枪械配件模型列表
	std::vector<BaseItemMesh*> m_ComponentModels;
	const GunDef* m_GunDef = nullptr;
	int m_CurWeaponId = 0;

};