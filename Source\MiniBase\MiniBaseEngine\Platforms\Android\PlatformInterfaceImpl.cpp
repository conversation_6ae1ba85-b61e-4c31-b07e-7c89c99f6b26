#include "Platforms/PlatformInterface.h"

#include "Graphics/ScreenManager.h"

#include "LegacyMath.h"
#include "OgreStringUtil.h"

#include <ctime>

#include "Platforms/Android/AndroidSystemInfo.h"
#include "Platforms/Android/DVM.h"
#include "Platforms/Android/DVMCalls.h"

#include "Platforms/Android/AppPlayJNI.h"
#include "Platforms/Android/AppPlayBaseActivity.h"
#include "Platforms/Android/GameBaseActivity.h"
//#include "Misc/GameApp.h"

#ifdef IWORLD_UNIVERSE_BUILD 
#include "../../../../External/Game/Firebase/firebase-crashlytics-ndk/include/crashlytics/external/crashlytics.h"
#endif

static MINIW::ImagePickerCallback s_imagePickerCallback = NULL;
static std::string s_imagePickerTargetPath = "";
static MINIW::CameraQRScanerCallback s_cameraQRScannerCallback = NULL;
static MINIW::ContactPersonsCallback s_contactPersonsCallback = NULL;
static MINIW::WatchADCallback s_watchADCallback = NULL;

//----------------------------------------------------------------------------
using String = java::lang::String;

using Activity = android::app::Activity;
using Context = android::content::Context;
//using Toast = android::widget::Toast;
using LocationManager = android::location::LocationManager;
using Location = android::location::Location;
using ConnectivityManager = android::net::ConnectivityManager;
using NetworkInfo = android::net::NetworkInfo;
//using WiFiManager = android::net::wifi::WiFiManager;
using WifiInfo = android::net::wifi::WifiInfo;
using PackageInfo = android::content::pm::PackageInfo;
using DisplayMetrics = android::util::DisplayMetrics;
//----------------------------------------------------------------------------

namespace MINIW
{

	static char* m_pClientVersion = NULL;
	static int m_pClientEnv = 0;
	static statisticsJson m_statisticsJson = NULL;

	inline std::string to_string(const ::java::lang::String& text)
	{
		const char* const str = const_cast<::java::lang::String&>(text).c_str();
		return str ? str : "";
	}

	void SetClientEnv(int env)
	{
		m_pClientEnv = env;
	}

	int GetClientEnv()
	{
		return m_pClientEnv;
	}

	void SetStatisticsJson(statisticsJson reportjson)
	{
		m_statisticsJson = reportjson;
	}

	statisticsJson getStatisticsJson()
	{
		return m_statisticsJson;
	}

	unsigned int GetTimeStamp()
	{
		time_t t;
		time(&t);
		return (unsigned int)t;
	}

	uint64_t GetTimeStampMS()
	{
		uint64_t nTimer = 0;
		struct timeval stNow1;
		gettimeofday(&stNow1, NULL);
		nTimer = stNow1.tv_sec * 1000 + stNow1.tv_usec / 1000;
		return nTimer;
	}

	void SetClientVersion(char* clientVersion)
	{
		m_pClientVersion = clientVersion;
	}

	char* GetClientVersion()
	{
		return m_pClientVersion;
	}

	void ThreadSleep(uint tick)
	{
		timespec ts;
		ts.tv_sec = tick / 1000;
		ts.tv_nsec = int((tick % 1000) * 1000000);
		nanosleep(&ts, NULL);
	}

	void PopMessageBox(const char* content, const char* title)
	{
		SCOPED_JNI();
		Activity& activity = Rainbow::DVM::GetActivity();
		const char* empty = "";
		::java::lang::String _title(title ? title : empty), _content(content ? content : empty);
/*		// corresponding Java code
		new AlertDialog.Builder(context)
				.setTitle(title)
				.setMessage(message)
				.setPositiveButton(android.R.string.yes, null)
//				.setNegativeButton(android.R.string.no, null)
				.setIcon(android.R.drawable.ic_dialog_alert)
				.setCancelable(false)
				.show();
*/
		using CharSequence = ::java::lang::CharSequence;
		using OnClickListener = ::android::content::DialogInterface_OnClickListener;
		android::app::AlertDialog_Builder dialog(activity);
		dialog.SetTitle(_title.operator CharSequence())
			.SetMessage(_content.operator CharSequence())
			.SetPositiveButton(android::R_string::fYes(), OnClickListener(nullptr))
			//			.SetNegativeButton(android::R_string::fNo(), OnClickListener(nullptr))
			.SetIcon(android::R_drawable::fIc_dialog_alert())
			.SetCancelable(false)
			.Show();
	}

	void GetOpenFile(const char* title, const char* format, const char* initDir, char* resultBuffer)
	{
	}

	int GetProcessUsedMemory()
	{
		return 0;
	}

	bool GetMachineLocation(double& longitude, double& latitude)
	{
		longitude = GameBaseActivity::GetLocationLongitude();
		latitude = GameBaseActivity::GetLocationLatitude();
		return true;
	}
	void GetMobileCurLocationState()
	{
		GameBaseActivity::GetCurLocationState();
	}

	int GetNetworkState()
	{
//TODO:CONFIRM
		using namespace Rainbow;
		NetworkReachability netState = DVM::GetInternetReachability();
		//移动互联网
		if (netState == ReachableViaCarrierDataNetwork) {
			return 2;
		}
		//wifi
		if (netState == ReachableViaLocalAreaNetwork) {
			return 1;
		}
		return 0;
		//return AppPlayBaseActivity::GetNetworkState();
	}

	//获取网络信号强度
	int GetNetworkSignal() {
		return GameBaseActivity::GetMobileNetworkSignal();
	}

	void SetScreenBrightness(float bright)
	{
		AppPlayBaseActivity::SetScreenBright(Rainbow::Clamp(bright, 0.0f, 1.0f));
	}

	int GetNetworkCardState()
	{
		return 1;
	}


	void SetApiid(int apiid)
	{
	}


	void BuglyLog(const char* text)
	{
		::java::lang::String _text(text);
		GameBaseActivity::BuglyLog(_text);
	}

	void crashlyticsLog(const char* strlog)
	{
		(void)strlog;
		// 海外用 crashlytics；国内用 bugly 上报，不走这里。
#ifdef IWORLD_UNIVERSE_BUILD 
		firebase::crashlytics::Log(strlog);
#endif
	}

	static float GetDisplayDpi()
	{
/*		// corresponding Java code
		DisplayMetrics dm = context.getResources().getDisplayMetrics();
		return (dm.xdpi + dm.ydpi) * 0.5f;
*/
		SCOPED_JNI();
		using namespace Rainbow;
		Context& context = DVM::GetContext();
		const DisplayMetrics& dm = context.GetResources().GetDisplayMetrics();
		float dpi = (dm.fXdpi() + dm.fYdpi()) * 0.5f;
		return dpi;
	}

	float GetScreenDpi()
	{
		static float dpi = -1;
		if (dpi == -1)
		{
			dpi = GetDisplayDpi();
		}

		return dpi;
	}

	unsigned long int getPthreadSelf()
	{
		return *(unsigned long int*)pthread_self();
	}


	void OnAppsFlyerStatisticsGameEvent(const char* event, const char* paramsName1 /*= ""*/, const char* params1 /*= ""*/, const char* paramsName2 /*= ""*/, const char* params2 /*= ""*/, const char* paramsName3 /*= ""*/, const char* params3 /*= ""*/)
	{
		AppPlayBaseActivity::AppsFlyerStatisticsGameEvent(event, paramsName1, params1, paramsName2, params2, paramsName3, params3);
	}

	void OnStatisticsGameEvent(const char* event, const char* paramsName1, const char* params1, const char* paramsName2, const char* params2, const char* paramsName3, const char* params3)
	{
		//LOG_INFO("Begin OnStatisticsGameEvent: %s", event);
//		AppPlayBaseActivity::StatisticsGameEvent(event, paramsName1, params1, paramsName2, params2, paramsName3, params3);
	}

	void OnStatisticsGameChooseRole(const char* roleName, const char* nickName, int uin)
	{
		//		AppPlayBaseActivity::StatisticsGameChooseRole(roleName, nickName, uin);
	}

	void OnStatisticsGameBuyRole(const char* roleName)
	{
		//		AppPlayBaseActivity::StatisticsGameBuyRole(roleName);
	}

	void OnStatisticsGameRewardMiniCoin(int num, const char* reason)
	{
		//		AppPlayBaseActivity::StatisticsRewardMiniCoin(num, reason);
	}

	void OnStatisticsGamePurchaseMiniCoin(const char* name, int num, float price)
	{
		//		AppPlayBaseActivity::StatisticsPurchaseMiniCoin(name, num, price);
	}

	void OnStatisticsOnChargeRequest(const char* sid, const char* productname, const char* paymentType, float price, int coinnum)
	{
		//		AppPlayBaseActivity::StatisticsOnChargeRequest(sid, productname, paymentType, price, coinnum);
	}

	void OnStatisticsOnChargeSuccess(const char* sid)
	{
		//		AppPlayBaseActivity::StatisticsOnChargeSuccess(sid);
	}

	void OnOmletDeepLink(int uin, const char* roomPw)
	{
		AppPlayBaseActivity::OmletDeepLink(uin, roomPw);
	}

	void MtaTrackEvent(const char* event_id, const char* key, const char* value)
	{
		MtaTrackEventJNI(event_id, key, value);
	}

	void JumpToOppoGameCenter()
	{
		assert(false);
		//		JumpToOppoGameCenterJNI();
	}

	void JumpNetworkDiagnoseActivity()
	{
		assert(false);
		//		JumpNetworkDiagnoseActivityJNI();
	}

	void OnClickCopy(const char* content)
	{
		if (!content)
			return;

		AppPlayBaseActivity::OnClickCopy(content);
	}

	void OnSdkLogin()
	{
		AppPlayBaseActivity::OnSdkLogin();
	}

	void OnSdkSwitch()
	{
		//		AppPlayBaseActivity::SdkSwitch();
	}

	void OnSdkLogout()
	{
		//		AppPlayBaseActivity::SdkLogout();
	}

	void OnSdkForum()
	{
		AppPlayBaseActivity::SdkForum();
	}

	void OnSdkGameCenter()
	{
		AppPlayBaseActivity::SdkForum();
	}

	void OnSdkAccountBinding(int type)
	{
		AppPlayBaseActivity::SdkAccountBinding(type);
	}

	void OnSdkRealNameAuth()
	{
		//		AppPlayBaseActivity::SdkRealNameAuthJNI();
	}

	void OnSdkLogin(int type)
	{
		AppPlayBaseActivity::OnSdkLogin(type);
	}

	bool OnOpenMiniProgram()
	{
		return GameBaseActivity::OnOpenMiniProgram();
	}

	bool OnOpenMiniProgramWithType(int type)
	{
		return GameBaseActivity::OnOpenMiniProgramWithType(type);
	}

	int OnSetGameEnv()
	{
		GameBaseActivity::OnSetGameEnv();
		return 0;
	}

	void OnRespWatchAD(int result)
	{
		LOG_INFO("OnRespWatchAD:%d", result);
		if (s_watchADCallback)
		{
			s_watchADCallback(result);
		}
	}

	void OnRequestReview()
	{

	}

	void SavevideoWithphoto(const char* path)
	{
		LOG_INFO("SavevideoWithphoto %s", path);
		GameBaseActivity::SavevideoWithphoto(path);
	}

	int OnReqSdkAD(const char* type, int adid, WatchADCallback callback, int adindex, int rewardValue)
	{
		LOG_INFO("OnReqSdkAD %s", type);
		s_watchADCallback = callback;
		return AppPlayBaseActivity::OnReqSdkAD(type, adid, adindex, rewardValue);
	}

	void OnPlayAdSuccessCallback(int positionId)
	{
		LOG_INFO("OnPlayAdSuccessCallback positionId = %d", positionId);
		GameBaseActivity::OnPlayAdSuccessCallback(positionId);
	}

	bool OnInitAdvertisementsSDK(int adid)
	{
		return AppPlayBaseActivity::InitAdvertisementsSDK(adid);
	}

	bool OnAdvertisementsLoadStatus(int adid, int position)
	{
		return AppPlayBaseActivity::AdLoadStatus(adid, position);
	}

	void OnLoadSdkAD(int adid, int positionId)
	{
		AppPlayBaseActivity::OnLoadSdkAD(adid, positionId);
	}

	std::string OnGetSdkAdvertisementsInfo(int platformId, int positionId)
	{
		const ::java::lang::String& info = AppPlayBaseActivity::getSdkAdInfo(platformId, positionId);
		return to_string(info);
	}

	void OnSdkAdvertisementsShow(int platformId, int positionId)
	{
		AppPlayBaseActivity::SdkAdShow(platformId, positionId);
	}

	void OnSdkAdvertisementsOnClick(int platformId, int positionId)
	{
		AppPlayBaseActivity::SdkAdOnClick(platformId, positionId);
	}

	void OnScreenCaptureCallback(const char* snapshotPath)
	{
		assert(false);
		//		OnScreenCaptureCallbackJNI(snapshotPath);
	}

	void OnSetSdkRoleInfo(const char* rolename, const char* type, int uin, int coinnum)
	{
		assert(false);
		//		OnSetSdkRoleInfoJNI(rolename, type, uin, coinnum);
	}

	void OnSetSdkFloatMenu(int type)
	{
		assert(false);
		//		OnSetSdkFloatMenuJNI(type);
	}

	bool IsSdkToStartGame()
	{
		assert(false);
		//		return IsSdkToStartGameJNI();
		return false;
	}

	void removeSplashView()
	{
		GameBaseActivity::removeSplashView();
	}
	
	void GameExit(bool restart, const char* pidfile)
	{
		if (restart)
			GameBaseActivity::GameRestart();
		else
		{
			//			Rainbow::GetGameApp().AppExit();
			AppPlayBaseActivity::GameExit();
		}
	}

	void GameVibrate(int val)
	{
		AppPlayBaseActivity::Vibrate(val);
	}

	void GameVibrateWithTimeAndAmplitude(int time, int amplitude)
	{
		//java
		/*Vibrator vibrator = (Vibrator)AppUtils.getGameActivity().getSystemService(Activity.VIBRATOR_SERVICE);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && vibrator.hasAmplitudeControl()) {
			vibrator.vibrate(VibrationEffect.createOneShot(time, amplitude));
		}
		else if (vibrator.hasVibrator()) {
			Log.d(TAG, "the hardware not support amplitudeControl!");
			vibrator.vibrate(time);
		}
		else {
			Log.d(TAG, "the hardware hasn't a vibrator!");
		}*/

		//c++
		SCOPED_JNI();
		Activity& activity = Rainbow::DVM::GetActivity();
		using Vibrator = ::android::os::Vibrator;
		using Build = ::android::os::Build;
		using VibrationEffect = ::android::os::VibrationEffect;
		Vibrator vibrator = (Vibrator) activity.GetSystemService(Activity::fVIBRATOR_SERVICE());
		if (android::os::Build_VERSION::fSDK_INT() >= android::os::Build_VERSION_CODES::fO() && vibrator.HasAmplitudeControl())
		{
			vibrator.Vibrate(VibrationEffect::CreateOneShot(time, amplitude));
			//__android_log_print(ANDROID_LOG_INFO, "MiniCode", "GameVibrateWithTimeAndAmplitude : %d ----------- %d!", time, amplitude);
		}
		else if (vibrator.HasVibrator())
		{
			vibrator.Vibrate(time);
			//__android_log_print(ANDROID_LOG_INFO, "MiniCode", "GameVibrateWithTimeAndAmplitude : the hardware not support amplitudeControl!, time: %d", time);
		}
		else 
		{
			//__android_log_print(ANDROID_LOG_INFO, "MiniCode", "GameVibrateWithTimeAndAmplitude : the hardware hasn't a vibrator!");
		}
	}

	void StopGameVibrate()
	{
		//java
		/*Vibrator vibrator = (Vibrator)AppUtils.getGameActivity().getSystemService(Activity.VIBRATOR_SERVICE);
		vibrator.cancel();*/
		LOG_WARNING("StopGameVibrate start");
		//c++
		SCOPED_JNI();
		Activity& activity = Rainbow::DVM::GetActivity();
		using Vibrator = ::android::os::Vibrator;
		Vibrator vibrator = (Vibrator)activity.GetSystemService(Activity::fVIBRATOR_SERVICE());
		vibrator.Cancel();
		LOG_WARNING("StopGameVibrate end");
		//__android_log_print(ANDROID_LOG_INFO, "MiniCode", "StopGameVibrate : cancel vibrate");
	}

	int  GetMobileLang()
	{
		return GameBaseActivity::getMobileLang();
	}

	void OnOpenNotificationPermission() {
		AppPlayBaseActivity::OnOpenNotificationPermission();
	}
	int OnCheckNotificationPermission() {
		return AppPlayBaseActivity::OnCheckNotificationPermission();
	}

	std::string OnGetAppStartTime()
	{
		::java::lang::String startTime = AppPlayBaseActivity::OnGetAppStartTime();
		return to_string(startTime);
	}
	void OnInitGameDataFinished()
	{
		// NotifyInitGameDataFinished();
		GameBaseActivity::notifyInitGameDataFinished();
	}
	void OnSwitchScreenOrientation(int iScreenType)
	{
		AppPlayBaseActivity::OnSwitchScreenOrientation(iScreenType);
	}
	void OpenMobileWebView(const char* url, int left, int top, int width, int height)
	{
		AppPlayBaseActivity::WindowBrowserOpenWebpage(url, left, top, width, height);
	}
	void CloseMobileWebView()
	{
		AppPlayBaseActivity::WindowBrowserCloseWebpage();
	}
	std::string GetSchemeJson()
	{
		const ::java::lang::String& scheme = GameBaseActivity::GetSchemeJson();
		return to_string(scheme);
	}

	bool PullTPApp(const char* appName, const char* jsonStr)
	{
		return GameBaseActivity::PullTPApp(appName, jsonStr);
	}

	void OpenSchemes(const char* url)
	{

	}


	void GameDnsIps(const char* domain)
	{
		::java::lang::String _domain(domain);
		AppPlayBaseActivity::GameIPDomain(_domain);
	}

	std::string GetCountry()
	{
		return android::systeminfo::GetCountry();
	}

	std::string GetOperatorAndNetworkType()
	{
		return "";
	}

	std::string GetTraceRouteInfo(const char* ip)
	{
		return "";
		//return GetTraceRouteInfoJNI(ip);
	}

	std::string GetAccount()
	{
		return "";
	}

	std::string GetDeviceToken()
	{
		return "";
	}

	std::string GetDeviceId()
	{
		SCOPED_JNI();
		// Cache the value for the following invokings, which will not change since the App started
		java::lang::String deviceId = GameBaseActivity::GetDToken();
		const char* text = deviceId.c_str();
		if (text)
			return text;
		ErrorStringMsg("GameBaseActivity GetDeviceId() return null");
		return "";

		// std::string sdeviceid = "";
		// XMLNode node = Root::getSingleton().m_Config.getRootNode().getOrCreateChild("GameData").getOrCreateChild("Device");
		// if(node.hasAttrib("id2"))
		// {
		// 	sdeviceid = std::string(node.attribToString("id2"));	
		// 	//LOG_INFO("GetDeviceToken1:%s",sdtoken.c_str());		
		// }
		// else
		// {
		// 	sdeviceid = getDTokenJNI();		
		// 	node.setAttribStr("id2", sdeviceid.c_str());
		// 	//LOG_INFO("GetDeviceToken2:%s",sdtoken.c_str());
		// }
		// //LOG_INFO("GetDeviceToken3:%s",sdtoken.c_str());
		// return sdeviceid;
	}

	std::string OnGetClipBoard()
	{
		const ::java::lang::String& text = GameBaseActivity::OnGetClipBoard();
		return to_string(text);
	}

	void SaveAccount(const char* jsonChar)
	{

	}

	void Logout()
	{
		GameBaseActivity::LogoutAccount();
	}

	void onLoginCallBack(int type, const char* uin)
	{
		GameBaseActivity::LoginCallBack(type, uin);
	}

	std::string GetSHA512String(const char* str)
	{
		return "";
	}

	std::string GetShouQParams()
	{
		return "";
	}

	std::string GetDeviceModel()
	{
#if 0
		return android::systeminfo::Model();
#else
		const ::java::lang::String& model = android::os::Build::fMODEL();
		return to_string(model);
#endif
	}

	std::string GetMobilePhoneInfo()
	{
		const ::java::lang::String& info = GameBaseActivity::GetMobilePhoneInfo();
		return to_string(info);
	}

	void ScanImage(const char* imagePath)
	{
		if (!imagePath)
			return;

		GameBaseActivity::ScanImage(imagePath);
	}

	int GameHasTPPay()
	{
		return AppPlayBaseActivity::HasTPPay();
	}

	void GameMoreGame()
	{
		assert(false);
		//		MoreGameJNI();
	}

	void GameStartUpdate()
	{
		AppPlayBaseActivity::StartUpdate();
	}

	void TakeARAvatar(const char* imageFilename, const int photographMode, const bool clearOld)
	{
		AppPlayBaseActivity::takeARCameraAvatar(imageFilename, photographMode, clearOld);
	}

	bool ShowCameraQRScannerforARSkin()
	{
		return showCameraQRScannerForSkinJNI();
	}

	void GameSetAccount(int uin, const char* nickname)
	{
		AppPlayBaseActivity::SetAccount(uin, nickname);
	}

	void SetCrashReportUserId(const char* userId)
	{
		AppPlayBaseActivity::setCrashReportUserId(userId);
	}

	bool HasBuiltWithARM64()
	{
		return AppPlayBaseActivity::hasBuiltWithARM64();
	}

	bool CanShowARCameraBackground()
	{
		return GameBaseActivity::CanShowARCameraBackground();
	}

	void GamePay(const char* productName, float amount, const char* productId, int payType, int orderId, const char* sId/* ="" */)
	{
		AppPlayBaseActivity::Pay(productName, amount, productId, payType, orderId, sId);
	}

	void SetPayExtendParams(int i, char* buf, int bufsize)
	{
		buf[0] = '\0';

		const char* data = nullptr; assert(false);// SetPayExtendParamsJNI(i);
		if (data && data[0])
		{
			LOG_INFO("SetPayExtendParams[%d]: '%s'", i, data);

			int dataLen = strlen(data);

			if (dataLen <= bufsize - 1)
			{
				strcpy(buf, data);
			}
			else
			{
				assert(0 && "data too long");
				strncpy(buf, data, bufsize - 1);
				buf[bufsize - 1] = '\0';
			}
		}
	}

	void WindowBrowserOpenWebpage(const char* url, int left, int top, int width, int height)
	{
		AppPlayBaseActivity::WindowBrowserOpenWebpage(url, left, top, width, height);
	}

	void WindowBrowserCloseWebpage()
	{
		AppPlayBaseActivity::WindowBrowserCloseWebpage();
	}

	void WindowBrowserShowWebpage()
	{
		AppPlayBaseActivity::WindowBrowserShowWebpage();
	}

	void WindowBrowserHideWebpage()
	{
		AppPlayBaseActivity::WindowBrowserHideWebpage();
	}

	void BrowserShowWebpage(const char* url, int type)
	{
		LOG_INFO("BrowserShowWebpage: '%s' type: %d", url, type);
		AppPlayBaseActivity::BrowserShowWebpage(url, type);
	}

	void OpenWebView(const char* url, int type, const char* extend)
    {
        AppPlayBaseActivity::OpenWebView(url, type, extend);
    }

	void StartOnlineShare(const char* jsonStr, const char* imagePath, const char* url, const char* title, const char* content)
	{
		GameBaseActivity::StartOnlineShare(jsonStr, imagePath, url, title, content);
	}

	void StartMiniwShare(const char* platformName, const char* imagePath, const char* url, const char* title, const char* content)
	{
		GameBaseActivity::StartMiniwShare(platformName, imagePath, url, title, content);
	}

	void ShareToQQ(const char* imgpath, const char* url, const char* title, const char* text)
	{
		AppPlayBaseActivity::ShareToQQ(imgpath, url, title, text);
	}

	bool OpenQQBuLuo()
	{
		return AppPlayBaseActivity::OpenQQBuLuo();
	}

	bool OpenQQVip(int type, int months)
	{
		return AppPlayBaseActivity::OpenQQVip(type, months);
	}

	std::string GetQQUserInfo()
	{
		// return GetQQUserInfoJNI();
		return "";
	}

	std::string GetPackageName()
	{
		::java::lang::String packageName = GameBaseActivity::GetPackageName();
		return packageName.c_str();
	}

	void AddQQFriend(const char* openid, const char* label, const char* message)
	{
		AppPlayBaseActivity::AddQQFriend(openid, label, message);
	}

	bool CheckQQLogin(bool dologin)
	{
		return AppPlayBaseActivity::CheckQQLogin(dologin);
	}

	bool IsAppExist(const char* name)
	{
		return GameBaseActivity::IsAppExist(name);
	}

	bool IsAppInstall(const char* platformName)
	{
		return GameBaseActivity::IsShareAppInstalled(platformName);
	}

	bool ShowImagePicker(const char* targetPath, ImagePickerCallback callback, int type, bool crop, int x, int y)
	{
		LOG_INFO("ShowImagePicker %s", targetPath);
		s_imagePickerCallback = callback;
		s_imagePickerTargetPath = targetPath;
		return GameBaseActivity::ShowImagePicker(targetPath, type, crop, x, y);
	}
	void onImagePicked(int err)
	{
		if (s_imagePickerCallback)
		{
			s_imagePickerCallback(err == 0);
		}
	}

	void QueryContactPersons(ContactPersonsCallback callback)
	{
		s_contactPersonsCallback = callback;
		int num = GameBaseActivity::QueryContactPersons();
		OnContactPersons(num);
	}
	void OnContactPersons(int num)
	{
		s_contactPersonsCallback(num);
	}
	ContactPerson GetContactPerson(int index)
	{
		ContactPerson cp;
		cp.name = GameBaseActivity::GetContactPersonName(index);
		cp.phone = GameBaseActivity::GetContactPersonPhone(index);
		return cp;
	}

	bool ShowCameraQRScanner(CameraQRScanerCallback callback)
	{
		s_cameraQRScannerCallback = callback;

		return GameBaseActivity::ShowCameraQRScanner();
	}

	bool LoadSystemSoLib()
	{
		return false;
	}

	void AndroidDeleteFile(const char* path)
	{
		assert(false);
		//DeleteFileJNI(path);
	}

	void onCameraQRScanned(int result, const std::string& scaned_string)
	{
		if (s_cameraQRScannerCallback)
			s_cameraQRScannerCallback(result, scaned_string);
	}

	bool SendTextMessage(const std::string& phoneNumber, const std::string& message)
	{
		return GameBaseActivity::SendTextMessage(phoneNumber.c_str(), message.c_str());
	}

	bool CheckHasPermission(DevicePermission perm)
	{
		return GameBaseActivity::CheckHasPermission((int)perm);
	}

	void RequestPermission(DevicePermission perm)
	{
		GameBaseActivity::RequestPermission((int)perm);
	}

	void RequestPermissionNoUI(DevicePermission perm)
	{
		GameBaseActivity::RequestPermissionNoUI((int)perm);
	}

	void OpenSystemSetting()
	{
		GameBaseActivity::OpenSystemSetting();
	}

	void JumpPolicyPage(const char* type)
	{
		if (!type)
			return;

		GameBaseActivity::JumpPolicyPage(type);
	}

	void ShowPrivacyUpdateDialog(const char* version)
	{
		if (!version)
			return;

		GameBaseActivity::ShowPrivacyUpdateDialog(version);
	}

	int GetSpecialReviewMode()
	{
		return 0;
	}

	void developerCertificationToMinibox(const char* str)
	{
		if (!str)
			return;

		GameBaseActivity::DeveloperCertificationToMinibox(str);
	}

	int getSoundState()
	{
		return GameBaseActivity::getSoundPermissionState();
	}
	void setSoundState(int state)
	{
		GameBaseActivity::setSoundPermissionState(state);
	}

	bool OnShowFullScreenVideo(const char* path)
	{
		return OnShowFullScreenVideoJNI(path);
	}

	void OnCloseFullScreenVideo()
	{
		OnCloseFullScreenVideoJNI();
	}

	std::string GetIDFA()
	{
		static std::string sAndroidAdid;
		if (sAndroidAdid.empty())
			sAndroidAdid = to_string(GameBaseActivity::getDAdid());
		
		return sAndroidAdid;
	}

	std::string GetIDFVInfo()
	{
		return "";
	}

	std::string GetIDFV()
	{
		return "";
	}

	void OpenAdTrackingSetting()
	{

	}

	//打开三方客服
	void ShowSDKConversation(int level, int showType)
	{
		GameBaseActivity::ShowConversationNative(level, showType);
	}

	//更新三方客服sdk信息
	void UpdateSdkInfo()
	{
		GameBaseActivity::UpdateSdkUserInfo();
	}

	void setRequestedOrientation(int orientation)
	{
		using namespace Rainbow;
		ScreenManagerPlatform& manager = GetScreenManager();
		AppScreenOrientation engineOrientation = kAutoRotation;
		// keep in sync with @ScreenOrientation in ActivityInfo.java
		enum ActivityScreenOrientation: int
		{
//			UNSPECIFIED       = -1,
			LANDSCAPE         = 0,
			PORTRAIT          = 1,
//			USER              = 2,
//			BEHIND            = 3,
//			SENSOR            = 4,
//			NOSENSOR          = 5,
			SENSOR_LANDSCAPE  = 6,
			SENSOR_PORTRAIT   = 7,
			REVERSE_LANDSCAPE = 8,
			REVERSE_PORTRAIT  = 9,
			FULL_SENSOR       = 10,
//			USER_LANDSCAPE    = 11,
//			USER_PORTRAIT     = 12,
//			FULL_USER         = 13,
//			LOCKED            = 14,
		};
		
		bool portrait = false;
		bool landscape = false;
		bool portrait_r = false;
		bool landscape_r = false;
		switch(orientation)
		{
		case LANDSCAPE:         landscape = true; engineOrientation = kLandscapeLeft; break;
		case PORTRAIT:          portrait = true;  engineOrientation = kPortrait;      break;
		default:
		case SENSOR_LANDSCAPE:  landscape = landscape_r = true;  break;
		case SENSOR_PORTRAIT:   portrait  = portrait_r  = true;  break;
		case REVERSE_LANDSCAPE: landscape_r = true; engineOrientation = kLandscapeRight;     break;
		case REVERSE_PORTRAIT:  portrait_r = true;  engineOrientation = kPortraitUpsideDown; break;
		case FULL_SENSOR:       portrait = landscape = portrait_r = landscape_r = true;      break;
		}
		
		manager.SetIsOrientationEnabled(kAutorotateToPortrait, portrait);
		manager.SetIsOrientationEnabled(kAutorotateToPortraitUpsideDown, portrait_r);
		manager.SetIsOrientationEnabled(kAutorotateToLandscapeLeft, landscape);
		manager.SetIsOrientationEnabled(kAutorotateToLandscapeRight, landscape_r);
		manager.RequestOrientation(engineOrientation);
//		manager.SetScreenOrientationAsync(engineOrientation);
		manager.SetScreenOrientation(engineOrientation);
	}

	bool CheckAppExist(const char* pkgname)
	{
		return false;
	}

	bool CheckAppInstall(const char* platformName)
	{
		return false;
	}

	std::string GetCountryFromIpAddress(const char* ipAddress)
	{
		return std::string("");
	}

	int PatchVersion()
	{
		return 0;
	}

	std::string ShowAudioPicker(std::string path, int type)
	{
		return std::string("");
	}

	int ShowImagePickerEx(std::string path, int type, bool crop, int x, int y)
	{
		return 0;
	}

	int GetTimeZoneStamp()
	{
		// 获取当前时间
		std::time_t now = std::time(nullptr);
		
		// 获取UTC时间结构
		std::tm* utc_tm = std::gmtime(&now);
		if (!utc_tm) return 0;
		
		// 获取本地时间结构
		std::tm* local_tm = std::localtime(&now);
		if (!local_tm) return 0;
		
		// 计算时区偏移（小时）
		std::time_t utc_time = std::mktime(utc_tm);
		std::time_t local_time = std::mktime(local_tm);
		
		if (utc_time == -1 || local_time == -1) return 0;
		
		// 返回时区偏移小时数（与Windows版本保持一致）
		return static_cast<int>((local_time - utc_time) / 3600);
	}

	void FirebaseAnalyticsEvent(const char* event, const char* paramJson)
	{

	}
	/*
		int getFontSizeAccordingHeightJni(int height)
		{
			return 0;
		}

		std::string getStringWithEllipsisJni(const char* text, float width, float fontSize)
		{
			return "";
		}
	*/
	static std::string flyerUid;
	static std::string adCampaignInfo;
	static std::string deviceRegisterInfo;
	std::string GetFlyerUID()
	{
		if (flyerUid.empty())
			flyerUid = to_string(GameBaseActivity::GetFlyerUID());

		return flyerUid;
	}

	std::string GetDeviceStorageInfo()
	{
		return to_string(GameBaseActivity::GetDeviceStorageInfo());
	}

	std::string GetAdCampaignInfo()
	{
		if (adCampaignInfo.empty())
			adCampaignInfo = to_string(GameBaseActivity::GetAdCampaignInfo());
		
		return adCampaignInfo;
	}

	std::string GetDeviceRegisterInfo()
	{
		if(deviceRegisterInfo.empty())
		{
			deviceRegisterInfo = to_string(GameBaseActivity::GetDeviceRegisterInfo());
		}
		return deviceRegisterInfo; 
	}

	std::string fetchLaunchIp()
	{
		return to_string(GameBaseActivity::fetchLaunchIp());
	}

	int checkFirstLaunch()
	{
		return GameBaseActivity::checkFirstLaunch();
	}

	long fetchFirstLaunchTimeStamp()
	{
		return GameBaseActivity::fetchFirstLaunchTimeStamp();
	}

	int fetchApn()
	{
		return GameBaseActivity::fetchApn();
	}
	
	void OnEnterGameCallback(int uin, bool isAdult)
	{
		GameBaseActivity::OnEnterGameCallback(uin, isAdult);
	}

	void SetCrashUserValue(const char* key, const char* value)
	{
		GameBaseActivity::SetCrashUserValue(key, value);
	}

	std::string CallNativeFeatureQuery(int type, const char* params)
	{
		::java::lang::String str = AppPlayBaseActivity::CallNativeFeatureQuery(type, params);
		if (str.EmptyOrNull())
			return std::string("");

		return str.c_str();
	}
	
	bool CallNativeView(int type, const char* params)
	{
		return AppPlayBaseActivity::CallNativeView(type, params);
	}

	bool CallNativeFeature(int type, const char* params)
	{
		return AppPlayBaseActivity::CallNativeFeature(type, params);
	}

	void CheckSupportWXGameLive(int uin)
	{
		GameBaseActivity::CheckSupportWXGameLive(uin);
	}

	void LoadWXGameLiveView()
	{
		GameBaseActivity::LoadWXGameLiveView();
	}
	
	void setUserDataDir(const char* datas)
	{
		AppPlayBaseActivity::setUserDataDir(datas);
	}

	std::string getUserManualData()
	{
		::java::lang::String str = AppPlayBaseActivity::getUserManualData();
		if (str.EmptyOrNull())
			return std::string("");

		return str.c_str();
	}

	void saveKVForSP(const char* key, const char* value)
	{
		AppPlayBaseActivity::saveKVForSP(key, value);
	}

	std::string getValueForSP(const char* key)
	{
		::java::lang::String str = AppPlayBaseActivity::getValueForSP(key);
		if (str.EmptyOrNull())
			return std::string("");

		return str.c_str();
	}

	void responseLuaWithCallback(const char *funcName, const char *sessionId, const char *responseJson)
	{
		::java::lang::String _jstrFuncName(funcName);
		::java::lang::String _jstrSessionId(sessionId);
		::java::lang::String _jstrResponseJson(responseJson);
		AppPlayBaseActivity::responseLuaWithCallback(_jstrFuncName, _jstrSessionId, _jstrResponseJson);
	}

    void setCrashTag(const char *tagKey, const char *tagValue)
	{
		GameBaseActivity::setCrashTag(tagKey, tagValue);
	}

    void rmCrashTag(const char *tagKey)
	{
		GameBaseActivity::rmCrashTag(tagKey);
	}

	std::string CallNativeFileSelector(const char* params)
	{
		::java::lang::String str = AppPlayBaseActivity::CallNativeFileSelector(params);
		if (str.EmptyOrNull())
			return std::string("");

		return str.c_str();
	}

	void CallNativeFileCopy(const char* params)
	{
		AppPlayBaseActivity::CallNativeFileCopy(params);
	}
}
