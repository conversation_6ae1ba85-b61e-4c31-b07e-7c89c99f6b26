#include "ClientActorManager.h"
#include "ClientItem.h"
#include "ActorNpc.h"
#include "PlayerControl.h"
#include "ActorBoss.h"
#include "ClientActorFuncWrapper.h"
#include "GameMode.h"
#include "Ecosystem.h"
#include "ChunkGenerator.h"
#include "ClientInfoProxy.h"
#include "ActorBall.h"
#include "ActorDesertBusinessman.h"
#include "DesertTradeCaravanMgr.h"

#include "BiomeRegionGenConfig.h"
#include "ActorPackHorse.h"
#include "ActorVehicleAssemble.h"
#include "ClientActorProjectile.h"
#include "ActorChunkPos.h"
#include "GameInfoProxy.h"
#include "LuaInterfaceProxy.h"
#include "SandboxGlobalNotify.h"
#include "MapInfoRefreshCenter.h"
#include "ClientAquaticMob.h"
#include "ClientAvatarSummonMob.h"
#include "SleepState.h"
#include "IMiniDeveloperProxy.h"
#include "TemperatureManager.h"
#include "Optick/optick.h"
#include "SandboxIdDef.h"
#include "special_blockid.h"
#include "BlockMaterialMgr.h"
#include "Optick/optick.h"
#include "ActorVehicleAssemble.h"
#include "VehicleWorld.h"
#include "ClientAquaticComponent.h"
#include "PlayerAttrib.h"
#include "SpaceActorManager.h"
#include "Gizmo/GizmoManager.h"
#include "CameraManager.h"
#include "Gizmo/GizmoRenderer.h"
#include "Gizmo/GizmoUtil.h"
#include "GameCamera.h"
#include "Input/InputManager.h"
#include "chunk.h"
#include "Geometry/Intersection.h"
#include "WorldRender.h"
#include "OgreEntity.h"
#include "Core/GameObject.h"
#include "BlockScene.h"
#include "Core/GameScene.h"
#include "ModelItemMesh.h"
#include "WorldManager.h"
#include "ActorDragon.h"
#include "ClientVacantBoss.h"
#include "ActorFlySnakeGod.h"
#include "ActorGiant.h"
#include "ActorHalfGiant.h"
#include "ActorDesertVillager.h"
#include "BlockBed.h"
#include "ActorFisherMan.h"
#include "ActorFishingVillager.h"
#include "EcosysBigBuildBuilder.h"
#include "ActorPatrolMob.h"
#include "ActorIslandBusinessman.h"
#include "ActorVillager.h"
#include "BlockVillageTotemIce.h"
#include "BlockVillagerFlagBuilding.h"
#include "container_villageflagbuilding.h"
#include "container_villagetotemice.h"
#include "voxelmodel.h"
#include "ActorFrostWyrm.h"
#include "ActorBasketBall.h"
#include "ActorTravelingTrader.h"
#include "ClientItem.h"
#include "BluePrintMgr.h"
#include "Plugin.h"
#include "UGCActorBatchManager.h"
#include "BlockMesh.h"
#include "SectionMesh.h"
#include "Components/MeshRenderer.h"
#include "SceneEditorMeshGen.h"
#include "RiddenComponent.h"
#include "TemperatureManager.h"
#include "CombatCheckExtend.h"
#include "ImageMesh.h"
#include "ModPackMgr.h"
#include "UgcAssetMgr.h"
#include "GunGridDataComponent.h"
#include "Debug/DebugMgr.h"
#include "GunSmithMgr.h"
#include "CityConfig.h"
#include "ICloudProxy.h"
#include "ItemUseComponent.h"
#include "Graphics/ScreenManager.h"
#include "DebugDataMgr.h"
#include "MiniShared/utils/json.hpp"
#include "Core/terrgen/EcosysBuildHelp.h"
#include "Play/player/AINpc.h"
#include "IClientGameManagerInterface.h"
#include "ClientAccountInfo.h"
#include "Core/actors/helper/ActorBodySequence.h"
#include "Core/worldData/WorldManager.h"
#include "Play/gamenet/GameNetManager.h"
#include <unordered_map>
#include <cmath>
#include <set>
#include <random>

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

#define MAX_PLAYER_UIN 0x100000000LL
#define MOB_SPAWN_DIST 2   //怪物出生离最近玩家距离

#define CLIENTACTORMGR_DEBUG_LOG 1
#if CLIENTACTORMGR_DEBUG_LOG
#define CLIENTACTORMGR_LOG(...) \
    do { \
        WarningStringMsg("[ClientActorMgr] " __VA_ARGS__); \
    } while(0)
#else
#define CLIENTACTORMGR_LOG(...)
#endif

inline void CalCollideBox(CollideAABB& box, const MonsterDef* def, const WCoord& pos)
{
	if (def == NULL)
		return;

	box.dim = WCoord(def->Width * def->ModelScale, def->Height * def->ModelScale, def->Width * def->ModelScale);
	box.pos = pos - WCoord(box.dim.x / 2, 0, box.dim.z / 2);
}

unsigned int ComputeWCoordHash(const WCoord& pos) 
{
	unsigned int x = static_cast<unsigned int>(pos.x);
	unsigned int y = static_cast<unsigned int>(pos.y);
	unsigned int z = static_cast<unsigned int>(pos.z);
	return ((x + 31) * 31 + z) * 31 + y;
}

ClientActorMgr::ClientActorMgr(World* pworld) : m_World(pworld), m_GenMobChunks(577), m_spawnSharkPoint(0)
{
	Vector3f center(0.0f);
	if (g_pPlayerCtrl)
		center = g_pPlayerCtrl->getPosition().toWorldPos().toVector3();
	m_ActorMGT = ENG_NEW(ActorMGT)(center, 512.0f * 16.0f * 100.0f);
	/* 默认事件 */
	m_World->Event().CreateEventDispatcher("OnActorEnter");
	m_World->Event().CreateEventDispatcher("OnActorLeave");
	m_SpaceManager = ENG_NEW(SpaceActorManager)(pworld);

	memset(m_LiveMobNum, 0, sizeof(m_LiveMobNum));
	m_LiveMobNumByID.clear();

	for (int i = 0; i < MAX_MOBTYPE; i++)
	{
		m_GenAccumTicks[i] = -400;
	}
	m_GenAccumTicks[0] = -100;
	m_lastMobGenTick = 0;

	setMobGen(true, true);
	m_vTeamMobs.clear();

	GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaSelfUpdate");
	spawnSharkEvent();
	UGCActorBatchManager::GetInstance().OnEnterWorld(m_World->getCurMapID());

	parseNpcList(); //避免每次解析gamevar "npc_list"

    if (!m_World->isRemoteMode())
    {
        char path[256] = { 0 };
        std::string rootDir = Rainbow::GetFileManager().GetWritePathRoot();
        sprintf(path, "%s/data/w%lld/soc_mob_presets.json", rootDir.c_str(), m_World->getOWID());
        m_mobPresetConfigPath = path;
        CLIENTACTORMGR_LOG("mobPresetConfigPath: %s", m_mobPresetConfigPath.c_str());

        initMobPresetsInfo();
    }	

	m_nGameDay = GetWorldManagerPtr()->getWorldTimeDay();
    // 初始化怪物预设位置管理器
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	int maxMobPresetCount = 500;
	if (pLuaConst) {
		maxMobPresetCount = pLuaConst->K_MAX_MOB_COUNT;
		// 上浮5%的数量，用于解决随机预设点不能100%有效的问题
		maxMobPresetCount = static_cast<int>(maxMobPresetCount * 1.05f);
	}

	ChunkGenerator* provider = m_World->getChunkProvider();
	ChunkIndex startci, endci;
	int startX = -2000, endX = 2000;
	int startZ = -2000, endZ = 2000;
	if (provider) {
		provider->GetPlaneRange(m_World, startci, endci);
		const int EDGE_MARGIN = 3;
		startX = (startci.x + EDGE_MARGIN) * 16;
		endX = (endci.x - EDGE_MARGIN) * 16;
		startZ = (startci.z + EDGE_MARGIN) * 16;
		endZ = (endci.z - EDGE_MARGIN) * 16;
	}

    m_mobPresetPositionMgr.Init(startX, endX, startZ, endZ, maxMobPresetCount);
    m_mobPresetDebugInitialized = false;
    m_mobPresetDebugEnabled = false; // 默认关闭调试线条显示
    m_totalPersonalSpawnCount = 0; // 初始化personal spawn计数器
    m_totalPersonalAINPCCount = 0; // 初始化AINPC计数器
    CLIENTACTORMGR_LOG("MobPresetPostionMgr initialized with %d points, startX: %d, endX: %d, startZ: %d, endZ: %d", maxMobPresetCount, startX, endX, startZ, endZ);

#ifdef IWORLD_DEV_BUILD
    GlobalCallbacks::Get().m_PreRenderCallbacks.Register<ClientActorMgr>(&ClientActorMgr::DrawTopView, this);
#endif
}

ClientActorMgr::~ClientActorMgr()
{
#ifdef IWORLD_DEV_BUILD
    GlobalCallbacks::Get().m_PreRenderCallbacks.Unregister<ClientActorMgr>(&ClientActorMgr::DrawTopView, this);
#endif
	// 清理怪物预设位置调试线条
	m_mobPresetPositionMgr.ClearDebugLines();
	
	ENG_DELETE(m_SpaceManager);
	ENG_DELETE(m_ActorMGT);
	UGCActorBatchManager::GetInstance().OnLeaveWorld();
	UGCActorBatchManager::Destroy();
}

#define INVALID_ACTOR_CONTINUE(pActor)\
	if (!ClientActor::IsValid(pActor)) {\
		Assert(false);\
		ErrorStringMsg("invalid_actor_ptr:%d",__LINE__); \
		continue;\
	}

void ClientActorMgr::reset()
{
	OPTICK_EVENT();
	for (auto iter = m_Players.begin(); iter != m_Players.end(); ++iter)
	{
		MINIW::ScriptVM::game()->callFunction("PlayerExitMapNotification", "i", (*iter)->getObjId());
	}
	m_World->GetWorldScene()->UnbindAllObjs();

	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		INVALID_ACTOR_CONTINUE(actor);
		actor->leaveWorld(false);
		actor->release();
		iter->second = NULL;
	}
	m_LiveActors.clear();

	for (auto iter = m_EmptyActors.begin(); iter != m_EmptyActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		INVALID_ACTOR_CONTINUE(actor);
		actor->leaveWorld(false);
		actor->release();
		iter->second = NULL;
	}
	m_EmptyActors.clear();

	m_Index.clear();
	//m_UidIndex.clear();
	for (size_t i = 0; i < m_Players.size(); i++)
	{
		INVALID_ACTOR_CONTINUE(m_Players[i]);
		MINIW::ScriptVM::game()->callFunction("PlayerExitMapNotification", "i", m_Players[i]->getObjId());
		m_Players[i]->leaveWorld(false);
		m_Players[i]->release();
	}
	m_Players.clear();

	for (size_t i = 0; i < m_Bosses.size(); i++)
	{
		INVALID_ACTOR_CONTINUE(m_Players[i]);
		m_Bosses[i]->leaveWorld(false);
		m_Bosses[i]->release();
	}
	m_Bosses.clear();

	assert(m_LiveActors.empty());
	assert(m_Players.empty());
	assert(m_Bosses.empty());
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("refresh_map", MNSandbox::SandboxContext(nullptr).SetData_Number("type", MRT_Boss));
}

void ClientActorMgr::onMobDead(ClientMob* mob)
{
	if (!mob) return;

	unsigned int posKey = mob->getMobPresetPosKey();
	CLIENTACTORMGR_LOG("onMobDead: mob %d, poskey: %d", mob->getDef()->ID, posKey);
	
	// 检查是否为personal spawn的mob，如果是则减少计数并从列表移除
	if (posKey == 999999 && !m_World->isRemoteMode()) {
		// 只在服务器端减少计数
		if (m_totalPersonalSpawnCount > 0) {
			m_totalPersonalSpawnCount--;
			CLIENTACTORMGR_LOG("Personal spawn mob died, decreased count to: %d (mob: %d)", 
				m_totalPersonalSpawnCount, mob->getDef()->ID);
		} else {
			CLIENTACTORMGR_LOG("WARNING: Personal spawn mob died but count was already 0! (mob: %d)", mob->getDef()->ID);
		}
		
		// 从创建者的列表中移除
		WORLD_ID masterObjId = mob->getMasterObjId();
		if (masterObjId > 0) {
			// 遍历玩家列表查找创建者
			for (auto player : m_Players) {
				if (player && player->getObjId() == masterObjId) {
					auto it = std::find(player->m_personalSpawnMobs.begin(), player->m_personalSpawnMobs.end(), mob->getObjId());
					if (it != player->m_personalSpawnMobs.end()) {
						player->m_personalSpawnMobs.erase(it);
					}
					break;
				}
			}
		}
	}
	
	// 处理预设刷新系统（有posKey的mob）
	if (posKey != 0) {
		auto it = m_MobPresetGenerated.find(posKey);
		if (it != m_MobPresetGenerated.end()) {
			MobPresetSpawnInfo& info = it->second;
			info.liftState |= (1 << 1); // 设置死亡标记
		}
		
		// 根据posKey找到对应的预设位置并释放
		releasePresetPositionByPosKey(posKey);
	}
	
	// 处理随机刷新系统（没有posKey但使用了预设位置的mob）
	// mob刷新的时候记录一个spawn位置，死亡的时候直接使用该位置，而不是当前位置，因为mob会移动
	WCoord mobPos = mob->getSpawnPoint();
	releasePresetPositionByCoord(mobPos);
}

void ClientActorMgr::releasePresetPositionByPosKey(unsigned int posKey)
{
	// 通过posKey在预设配置中找到对应的坐标
	for (const auto& chunkMap : m_mMobPresetMap) {
		for (const auto& preset : chunkMap.second) {
			if (preset.second.posKey == posKey) {
				releasePresetPosition(preset.second.pos);
				return;
			}
		}
	}
	CLIENTACTORMGR_LOG("Failed to find preset position for posKey: %d", posKey);
}

void ClientActorMgr::releasePresetPositionByCoord(const WCoord& mobPos)
{	
	int bestIndex = -1;
	const auto& positions = m_mobPresetPositionMgr.GetAllPositions();
	for (size_t i = 0; i < positions.size(); i++) {
		if (positions[i].isUsed) { // 只检查已使用的位置
			WCoord pos = positions[i].pos;
			if (pos.x == mobPos.x && pos.z == mobPos.z) {
				bestIndex = static_cast<int>(i);
				break;
			}
		}
	}
	
	if (bestIndex >= 0) {
		m_mobPresetPositionMgr.SetPositionUsed(bestIndex, false, -1);
		CLIENTACTORMGR_LOG("releasePresetPositionByCoord: Pos: %s", mobPos.__tostring().c_str());
	}
}

void ClientActorMgr::releasePresetPosition(const WCoord& pos)
{
	int index = m_mobPresetPositionMgr.FindPositionIndex(pos);
	if (index >= 0) {
		m_mobPresetPositionMgr.SetPositionUsed(index, false, -1);
		CLIENTACTORMGR_LOG("Released preset position index %d: (%d, %d, %d)", 
		                  index, pos.x, pos.y, pos.z);
	} else {
		CLIENTACTORMGR_LOG("Failed to find preset position index for: (%d, %d, %d)", 
		                  pos.x, pos.y, pos.z);
	}
}

void ClientActorMgr::onChunkLoad(Chunk* pchunk)
{
	OPTICK_EVENT();
	refeshMobWithChunk(pchunk);
}

void ClientActorMgr::onChunkUnload(Chunk* pchunk)
{
	OPTICK_EVENT();
}

void ClientActorMgr::refeshMobWithChunk(Chunk* pchunk)
{
	int cx = pchunk->getOrigin().x >> 4;
	int cz = pchunk->getOrigin().z >> 4;

	int minx = pchunk->getOrigin().x;
	int maxx = minx + 16;
	int minz = pchunk->getOrigin().z;
	int maxz = minz + 16;

	//int gameDay = m_nGameDay;//GetWorldManagerPtr()->getWorldTimeDay();
	auto listIter = m_mMobPresetMap.find(ChunkIndex(cx, cz));
	if (listIter != m_mMobPresetMap.end())
	{
		for (const auto& mobPreset : listIter->second)
		{
			WCoord p = mobPreset.second.pos;
			unsigned int posKey = ComputeWCoordHash(p);

			auto findPosKeyMob = [posKey](ClientActor* pActor) -> bool
				{
					ClientMob* pMob = dynamic_cast<ClientMob*>(pActor);
					if (pMob && pMob->getMobPresetPosKey() == posKey)
						return true;
					return false;
				};

			auto it = m_MobPresetGenerated.find(posKey);
			if (it != m_MobPresetGenerated.end()) {
				MobPresetSpawnInfo& info = it->second;
				if (((info.liftState & 1) == 0) || //完全没有生成过
					(/*(info.liftState & (1 << 1)) == 1 && */info.createDay != m_nGameDay))//或生成了，已经死亡，然后时间过了一天后
				{
					auto pmob = FindActor(findPosKeyMob);//是否死亡不使用info中的值。因为不实时
					//info.createDay = m_nGameDay;
					//if (pmob)
					//{
					//	pmob->setNeedClear();
					//}
					if (!pmob) info.createDay = m_nGameDay;
					else continue;
				}
				else
				{
					continue;
				}
			}
			else continue;

			//WCoord p2 = CoordDivBlock(p);
			//if (p2.x >= minx && p2.x <= maxx && p2.z >= minz && p2.z <= maxz) 
			{
				auto replaceInfo = GetDefManagerProxy()->getBuildReplaceDef(mobPreset.second.blockId);
				if (replaceInfo) {
					ChunkRandGen randGen;
					randGen.setSeed64(m_World->getChunkSeed(cx, cz));
					int mobId = EcosysBuildHelp::randReplaceId(replaceInfo, randGen);
					if (mobId > 0) {
						const MonsterDef* mobDef = GetDefManagerProxy()->getMonsterDef(mobId);
						spawnMobDelay(mobPreset.second.pos, 5, mobId, false, true, mobDef->Type, NULL, -1, NULL, posKey);
						m_MobPresetGenerated[posKey].liftState = 1;//新生成设置为生成了没有死亡
						CLIENTACTORMGR_LOG("onChunkLoad: preset mob %d spawned. pos: (%d, %d, %d), poskey: %d", mobId, p.x, p.y, p.z, posKey);
					}
				}
			}
		}
	}
}

void ClientActorMgr::resetByToggleGameMakerMode()
{
	auto tempActors = m_LiveActors;
	for (auto iter = tempActors.begin(); iter != tempActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		m_World->GetWorldScene()->UnbindLegacyActor(actor);
	}
	assert(m_LiveActors.empty());

	auto tempBosses = m_Bosses;
	for (auto iter = tempBosses.begin(); iter != tempBosses.end(); ++iter)
	{
		m_World->GetWorldScene()->UnbindLegacyActor(*iter);
	}
	assert(m_Bosses.empty());
	//切换游戏模式时，boss重置一下 code by : keguanqiang
	WorldMapData* mapdata = GetWorldManagerPtr()->getMapData(MAPID_LIEYANSTAR);
	if (mapdata)
		mapdata->bosses.clear();

}

void ClientActorMgr::reLoadMobAI()
{
	auto tempActors = m_LiveActors;
	for (auto iter = tempActors.begin(); iter != tempActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		if (actor->getObjType() == OBJ_TYPE_MONSTER)
		{
			ClientMob* mob = dynamic_cast<ClientMob*>(actor);
			if (mob)
			{
				mob->reloadAI();
			}
		}
	}

}

void ClientActorMgr::InsertLiveActors(ClientActor* actor)
{
	if (ActorBoss* boss = actor->ToCast<ActorBoss>())
	{
		if (boss->getObjId() == 0)
		{
			SetBossId(boss);
		}
		m_Bosses.push_back(boss);

		boss->enterWorld(m_World);
	}
	else if (ClientPlayer* player = actor->ToCast<ClientPlayer>())
	{
		GetICloudProxyPtr()->SimpleSLOG("InsertLiveActors %d ", player->getUin());
		m_Players.push_back(player);
		m_Index[player->getUin()] = player;
		//m_UidIndex[player->getUid()] = player;

		player->enterWorld(m_World);
	}
	else if (actor->IsEmpty())
	{
		if (!m_World->isRemoteMode())
		{
			SetActorId(actor, actor->getObjId());
		}
		m_EmptyActors[actor->getObjId()] = actor;
	}
	else
	{
		if (!m_World->isRemoteMode())
		{
			SetActorId(actor, actor->getObjId());
		}
		m_LiveActors[actor->getObjId()] = actor;

		actor->enterWorld(m_World);
	}
}

void ClientActorMgr::EraseLiveActors(ClientActor* actor, bool keepinchunk)
{
	// 离开世界
	actor->leaveWorld(keepinchunk);

	if (ActorBoss* boss = actor->ToCast<ActorBoss>())
	{
		for (auto iter = m_Bosses.begin(); iter != m_Bosses.end(); ++iter)
		{
			if (boss == *iter)
			{
				m_Bosses.erase(iter);
				MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("refresh_map", MNSandbox::SandboxContext(nullptr).SetData_Number("type", MRT_Boss));
				break;
			}
		}
	}
	else if (ClientPlayer* player = actor->ToCast<ClientPlayer>())
	{
		for (auto iter = m_Players.begin(); iter != m_Players.end(); ++iter)
		{
			if (player == *iter)
			{
				GetICloudProxyPtr()->SimpleSLOG("EraseLiveActors %d ", player->getUin());
				m_Index.erase(player->getUin());
				//m_UidIndex.erase(player->getUid());
				m_Players.erase(iter);
				break;
			}
		}
	}
	else if (actor->IsEmpty())
	{
		m_EmptyActors.erase(actor->getObjId());
	}
	else
	{
		//LOG_INFO("EraseLiveActors ID:%lld", actor->getObjId());
		m_LiveActors.erase(actor->getObjId());
	}

	actor->release(); // 引用计数减1
}

bool ClientActorMgr::addActorByChunk(IClientActor* iactor)
{
	OPTICK_EVENT();
	if (iactor == NULL) return false;
	auto actor = iactor->GetActor();
	//if(m_LiveActors.find(actor->getObjId()) != m_LiveActors.end())
	if (m_World->GetWorldScene()->GetSceneObjById(actor->GetNodeid()))
	{
		if (actor->IsObject())
		{
			// 客机实体没有在chunk 重发的情况下删除, 这里存在是正常情况
			return true;
		}
		return false;
	}

	m_World->GetWorldScene()->BindLegacyActor(actor);

	actor->addRef();
	//m_LiveActors[actor->getObjId()] = actor;
	//actor->enterWorld(m_World);
	actor->update(0); //更新骨骼动画绑定点等

	return true;
}

void ClientActorMgr::removeActorByChunk(IClientActor* iactor, bool keep_inchunk/*=false*/)
{
	if (iactor->getObjType() == OBJ_TYPE_GAMEOBJECT)
	{
		DeleteLargeGameObject(iactor->ToCast<ClientActor>());
	}
	m_World->GetWorldScene()->UnbindLegacyActor(iactor, keep_inchunk);
	//actor->leaveWorld(true);
	//m_LiveActors.erase(actor->getObjId());
	//actor->release();
}

void ClientActorMgr::despawnActor(MNSandbox::AutoRef<ClientActor> actor, bool isTeleport/*=false*/)
{
	if (!actor)
	{
		SANDBOX_ASSERT(false);
		return;
	}
	//actor->leaveWorld(false);
	
	// 检查是否为personal spawn的mob，如果是则减少计数并从列表移除
	ClientMob* mob = dynamic_cast<ClientMob*>(actor.get());
	if (mob && !m_World->isRemoteMode()) {
		unsigned int posKey = mob->getMobPresetPosKey();
		
		if (posKey == 999999) {
			if (m_totalPersonalSpawnCount > 0) {
				m_totalPersonalSpawnCount--;
				CLIENTACTORMGR_LOG("Personal spawn mob despawned, decreased count to: %d (mob: %d)", 
					m_totalPersonalSpawnCount, mob->getDef()->ID);
			} else {
				CLIENTACTORMGR_LOG("WARNING: Personal spawn mob despawned but count was already 0! (mob: %d)", mob->getDef()->ID);
			}
			
			// 从创建者的列表中移除
			WORLD_ID masterObjId = mob->getMasterObjId();
			if (masterObjId > 0) {
				// 遍历玩家列表查找创建者
				for (auto player : m_Players) {
					if (player && player->getObjId() == masterObjId) {
						auto it = std::find(player->m_personalSpawnMobs.begin(), player->m_personalSpawnMobs.end(), mob->getObjId());
						if (it != player->m_personalSpawnMobs.end()) {
							player->m_personalSpawnMobs.erase(it);
						}
						break;
					}
				}
			}
		}
	}

	ClientPlayer* player = nullptr;
	if (actor->getObjId() < MAX_PLAYER_UIN)
	{
		player = actor->ToCast<ClientPlayer>();// dynamic_cast<ClientPlayer *>(actor);
		assert(player);
		std::vector<ClientPlayer*>::iterator iter = m_Players.begin();
		for (; iter != m_Players.end(); iter++)
		{
			if (*iter == player)
			{
				MINIW::ScriptVM::game()->callFunction("PlayerExitMapNotification", "i", player->getObjId());
				//m_Index.erase(player->getUin());离开会在同步移除
				//player->release();
				//m_Players.erase(iter);
				break;
			}
		}
	}
	else
	{
		//m_LiveActors.erase(actor->getObjId());
		//actor->release();
	}

	m_World->GetWorldScene()->UnbindLegacyActor(actor);
	if (player)
		MNSandbox::GlobalNotify::GetInstance().m_PlayerLeaveScene.Emit(player->getUin(), player->StaticToCast<SandboxNode>(), isTeleport);
}

void ClientActorMgr::reTrackActor()
{
	for (size_t i = 0; i < m_Players.size(); i++)
	{
		m_World->untrackActor(m_Players[i]);
		m_World->trackActor(m_Players[i]);
	}

	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		m_World->untrackActor(iter->second);
		m_World->trackActor(iter->second);
	}

	for (size_t i = 0; i < m_Bosses.size(); i++)
	{
		m_World->untrackActor(m_Bosses[i]);
		m_World->trackActor(m_Bosses[i]);
	}
}

void ClientActorMgr::clearTrackActor()
{
	for (size_t i = 0; i < m_Players.size(); i++)
	{
		m_World->untrackActor(m_Players[i]);
	}

	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		m_World->untrackActor(iter->second);
	}

	for (size_t i = 0; i < m_Bosses.size(); i++)
	{
		m_World->untrackActor(m_Bosses[i]);
	}

}
void ClientActorMgr::tickOneActorPublic(ClientActor* actor, bool force_tick)
{
	tickOneActor(actor, force_tick);
}

void ClientActorMgr::tickOneActor(ClientActor* actor, bool force_tick)
{
	if (actor == NULL) return;

	//WCoord blockpos = CoordDivBlock(actor->getPosition());
	//const int R = 16;

//	if (m_World && m_World->isRemoteMode())
		//force_tick = true;

	//if(force_tick || m_World->checkChunksExist(WCoord(blockpos.x-R,0, blockpos.z-R), WCoord(blockpos.x+R,0,blockpos.z+R)))
	{
		//if(!force_tick && actor->m_isInChunk)
		{
			actor->m_LiveTicks++;
#ifndef USE_SPACE_TICK
			actor->tick();
#endif
		}
		auto actorChunkPos = actor->getChunkPosComponent();
		if (actorChunkPos)
		{
			actorChunkPos->onAfterTick();
		}
	}
}

void ClientActorMgr::tickOnePlayer(ClientPlayer* player)
{
	if (!player|| !m_World) return;

	player->m_LiveTicks++;
#ifndef USE_SPACE_TICK
	player->tick();
#endif

	auto actorChunkPos = player->getChunkPosComponent();
	if (actorChunkPos) {
		actorChunkPos->onAfterTick();
	}
}

void ClientActorMgr::prepareTick()
{
	OPTICK_EVENT();
#ifdef USE_SPACE_TICK
	m_SpaceManager->PrepareTick();
#else
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		if (iter->second != NULL) {
			ActorLocoMotion* locmove = iter->second->getLocoMotion();
			if (locmove) locmove->prepareTick();
		}
	}

	for (size_t i = 0; i < m_Players.size(); i++)
	{
		ActorLocoMotion* locmove = m_Players[i]->getLocoMotion();
		if (locmove) locmove->prepareTick();
	}

	for (size_t i = 0; i < m_Bosses.size(); i++)
	{
		ActorLocoMotion* locmove = m_Bosses[i]->getLocoMotion();
		if (locmove) locmove->prepareTick();
	}
#endif

	//教育版配角
#ifdef MODULE_FUNCTION_ENABLE_MINICODE
	//发送演员的准备tick事件
	MNSandbox::GetGlobalEvent().Emit<>("MiniCodeManager_prepareTick");
#endif
}

void ClientActorMgr::tick()
{
	OPTICK_EVENT();
    METRIC_PROFILER("ClientActorMgr::tick");
	SANDBOXPROFILING_FUNC("ClientActorMgr::tick")
	m_gameobjDelay = 0;
    
	Rainbow::GetDebugMgr().SetCheckActorRelease(true);
	m_SpaceManager->Tick();
    Rainbow::GetDebugMgr().SetCheckActorRelease(false);

	//unsigned int t1 = Rainbow::Timer::getSystemTick();
#ifdef IWORLD_SERVER_BUILD

	checkMobGen();
	// m_LiveActors 在tickOneActor中，可能会减少成员，不能直接遍历
	static std::vector<ClientActor*> actors;
	actors.clear();
	for (const auto& pa : m_LiveActors)
	{
		actors.push_back(pa.second);
	}

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientActor* actor = actors[i];
		if (actor == NULL) continue;
		tickOneActor(actor, false);

		WORLD_ID objid = actor->getObjId();
		if (actor->checkNeedClear()) {
			//std::map<WORLD_ID, ClientActor *>::iterator iter = m_LiveActors.find(objid);
			//assert(iter != m_LiveActors.end());
			//if (iter != m_LiveActors.end()) m_LiveActors.erase(iter);
		}
	}

	// tickplayers 在tickOneActor中，可能会减少成员，不能直接遍历
	static std::vector<ClientPlayer*> tickplayers;
	tickplayers.clear();
	tickplayers = m_Players;
	std::vector<ClientPlayer*> willDeleteAINPCs;
	for (size_t i = 0; i < tickplayers.size(); i++)
	{
		if (!tickplayers[i]) continue;
		tickOnePlayer(tickplayers[i]);

		if (GetWorldManagerPtr() && !m_World->isRemoteMode()) {
			uint64_t ts = GetWorldManagerPtr()->getCurrentTimeStamp();
			if (ts - m_lastMobGenTick > 1000) {
				m_lastMobGenTick = ts;
				
				// For NPC
				checkPlayerPersonalSpawn(tickplayers[i]);
	
				// For AI-NPC
				checkPlayerAINPCSpawn(tickplayers[i]);

				cleanupIdlePersonalAINPCs(tickplayers[i], willDeleteAINPCs);
			}
		}		

		//t31 = Rainbow::Timer::getSystemTick();
		if (MNSandbox::Config::GetSingleton().IsOpenMiniCraftRender())
			tickplayers[i]->updateChunkView();
	}

	if (willDeleteAINPCs.size() > 0) {
		for (auto ainpc : willDeleteAINPCs) {
			EraseLiveActors(ainpc, false);
		}
	}

	
	// 沙漠探寻绿洲
	// desertExploreChunkView(tickplayers);
	
	static const std::string aat("AfterActorTick");
	SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit(aat);
	size_t i = 0;
	while (i < m_Bosses.size())
	{
		ActorBoss* actor = m_Bosses[i];
		if (actor == NULL) continue;
		tickOneActor(actor, false);
		actor->updateChunkView();
		if (actor->checkNeedClear()) {
			//m_Bosses.erase(m_Bosses.begin() + i);
		}
		else {
			i++;
		}
	}

	i = 0;
	while (i < m_SpawnMobDelayParams.size())
	{
		SpawnMobDelayParam& param = m_SpawnMobDelayParams[i];
		if (param.m_delaySpawnTicks > 0) 
			param.m_delaySpawnTicks--;

		if (param.m_delaySpawnTicks == 0)
		{
			ClientMob* mob = nullptr;
			if (param.m_createFunc) {
				param.m_createFunc(param.m_pos, param.m_monsterid, param.m_mobtype_check, param.m_mob_check, param.m_yaw, param.m_pfunc);
			} else {
				CLIENTACTORMGR_LOG("spawnMob outter: mobid=%d, position=%s, posKey = %d", param.m_monsterid, param.m_pos.__tostring().c_str(), param.m_PresetPosKey);
				mob = spawnMob(param.m_pos, param.m_monsterid, param.m_mobtype_check, param.m_mob_check, param.m_yaw, param.m_mobtype, "", false, param.m_PresetPosKey);
				if (mob){
					mob->setSpawnPoint(param.m_pos);
					mob->setMobPresetPosKey(param.m_PresetPosKey);
				}
				if (param.m_pfunc) {
					param.m_pfunc(mob);
				}
			}

			m_SpawnMobDelayParams.erase(m_SpawnMobDelayParams.begin() + i);
		}
		else i++;
	}
#else
	// 家园地图--暂时屏蔽刷怪和各种商人
	bool isEnterHomeLand = (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD);
	if (!isEnterHomeLand)
	{
		checkMobGen();
	}
	//unsigned int t2 = Rainbow::Timer::getSystemTick();

	do
	{
		SANDBOXPROFILING_FUNC("Tick_All_Actor");
		OPTICK_EVENT("Tick_All_Actor");

		// m_LiveActors 在tickOneActor中，可能会减少成员，不能直接遍历
		static std::vector<ClientActor*> actors(500);
		actors.clear();
#if USE_OPTICK
		const int MaxActorType = 256;
		static unsigned int actorTypes[MaxActorType];
		memset(actorTypes, 0, sizeof(unsigned int) * MaxActorType);
#endif
		auto iter = m_LiveActors.begin();
		auto over = m_LiveActors.end();
		while (iter != over)
		{
			actors.push_back(iter->second);
#if USE_OPTICK
			auto at = iter->second->getObjType();
			if (at < MaxActorType)
				++actorTypes[at];
#endif
			++iter;
		}
		tickActors(actors);
		OPTICK_TAG("actors", actors.size());
#if USE_OPTICK
		if (actors.size() > 0) {
			std::ostringstream oss;
			bool bFirst = true;
			for (int i = 0; i < MaxActorType; i++) {
				if (actorTypes[i] > 0)
				{
					oss << i << ":" << actorTypes[i] << "|";
				}
			}
			OPTICK_TAG("types", oss.str().c_str());
		}
#endif
	} while (false);

	//unsigned int t3 = Rainbow::Timer::getSystemTick();
	//unsigned int t31 = Rainbow::Timer::getSystemTick();

	do
	{
		OPTICK_EVENT("Tick_All_Player");
		SANDBOXPROFILING_FUNC("Tick_All_Player");
		// tickplayers 在tickOneActor中，可能会减少成员，不能直接遍历
		std::vector<ClientPlayer*>tickplayers(m_Players);
		for (size_t i = 0; i < tickplayers.size(); i++)
		{
			if (tickplayers[i] == NULL) continue;
			tickOneActor(tickplayers[i], false);

			//t31 = Rainbow::Timer::getSystemTick();
			tickplayers[i]->updateChunkView();
		}
		
		//沙漠探寻绿洲
		// desertExploreChunkView(tickplayers);
		
		OPTICK_TAG("players", tickplayers.size());
	} while (false);

	//unsigned int t4 = Rainbow::Timer::getSystemTick();

	{
		OPTICK_EVENT("AfterActorTick");
		SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("AfterActorTick");
	}
	do
	{
		OPTICK_EVENT("Tick_All_Boss");
		SANDBOXPROFILING_FUNC("Tick_All_Boss");
		size_t i = 0;
		while (i < m_Bosses.size())
		{
			ActorBoss* actor = m_Bosses[i];
			if (actor == NULL) continue;
			tickOneActor(actor, false);
			actor->updateChunkView();
			if (actor->checkNeedClear()) {
				//工具1711修改
				//m_Bosses.erase(m_Bosses.begin() + i);
			}
			else {
				i++;
			}
		}
		OPTICK_TAG("bosses", i);
	} while (false);

	//延迟Spawn
	do
	{
		OPTICK_EVENT("DelaySpawnMob");
		size_t i = 0;
		// 收集下所有的objId, 一次发送
		std::vector<WORLD_ID> mIdArr;
		mIdArr.reserve(m_SpawnMobDelayParams.size());
		while (i < m_SpawnMobDelayParams.size())
		{
			SpawnMobDelayParam& param = m_SpawnMobDelayParams[i];
			if (param.m_delaySpawnTicks > 0) param.m_delaySpawnTicks--;
			if (param.m_delaySpawnTicks == 0)
			{
				ClientMob* mob = nullptr;
				if (param.m_createFunc)
				{
					param.m_createFunc(param.m_pos, param.m_monsterid, param.m_mobtype_check, param.m_mob_check, param.m_yaw, param.m_pfunc);
				}
				else
				{
					// if (param.m_monsterid > MonsterIdStart && param.m_monsterid <= MonsterIdEnd && ModPackMgr::GetInstancePtr())
					// {
					// 	mob = spawnMobByPrefabCheck(param.m_pos.x, param.m_pos.y, param.m_pos.z, ModPackMgr::GetInstancePtr()->GetResIdByCfgId(CustomModType::Mod_Monster, param.m_monsterid), -1, true, true, param.m_monsterid);
					// }
					// else
					// {
					// 	mob = spawnMob(param.m_pos, param.m_monsterid, param.m_mobtype_check, param.m_mob_check, param.m_yaw, param.m_PresetPosKey);
					// }

					CLIENTACTORMGR_LOG("spawnMob outter: mobid=%d, position=%s, posKey = %d", param.m_monsterid, param.m_pos.__tostring().c_str(), param.m_PresetPosKey);
					mob = spawnMob(param.m_pos, param.m_monsterid, param.m_mobtype_check, param.m_mob_check, param.m_yaw, param.m_PresetPosKey);
					if (mob){
						mob->setMobPresetPosKey(param.m_PresetPosKey);
					}

					if (param.m_pfunc) {
						param.m_pfunc(mob);
					}

					if (mob)
					{
						mIdArr.push_back(mob->getObjId());
					}
				}

				m_SpawnMobDelayParams.erase(m_SpawnMobDelayParams.begin() + i);
			}
			else i++;
		}

		//发送消息
		{
			jsonxx::Array arrJson;
			std::for_each(mIdArr.begin(), mIdArr.end(), [&arrJson] (WORLD_ID id){
				arrJson << id;
			});
			if (arrJson.size() > 0)
			{
				ObserverEvent obevent;
				obevent.SetData_CustomStr(arrJson.json());
				GetObserverEventManager().OnTriggerEvent("Biome.ActorSpawn", &obevent);
			}
		}
		OPTICK_TAG("delaymob", i);
	} while (false);
#endif
	refreshBuildData();
#ifndef IWORLD_SERVER_BUILD
	if (UGCActorBatchManager::GetInstance().IsActorBatchEnable())
		BatchActor();
#endif

	//根据主角位置，刷新整个八叉树的根节点位置
	//m_ActorMGT生命周期和mgr一致，不用检查null
	if (g_pPlayerCtrl)
	{
		OPTICK_EVENT("OctreeApplyOffset");
		//测试offset
		auto centerPos = g_pPlayerCtrl->getPosition().toWorldPos().toVector3();
		//如果偏移超过整个八叉树边长75%，那么八叉树整体位移一次
		auto rootAABB = m_ActorMGT->GetRootBounds();
		auto offset = centerPos - rootAABB.m_Center;
		auto bounds = rootAABB.m_Extent * 3.0f / 4.0f;
		if (abs(offset.x) > bounds.x
			|| abs(offset.y) > bounds.y
			|| abs(offset.z) > bounds.z) {
			m_ActorMGT->ApplyOffset(offset);
		}
	}	
}

void ClientActorMgr::tickBlockLine()
{
	std::vector<ClientPlayer*>tickplayers(m_Players);
	for (size_t i = 0; i < tickplayers.size(); i++)
	{
		if (tickplayers[i] == NULL) continue;
		PlayerControl* pPlayerControl = dynamic_cast<PlayerControl*>(tickplayers[i]);
		if (pPlayerControl == NULL) continue;
		pPlayerControl->tickBlockLine();
	}
}

void ClientActorMgr::tickActorLine()
{
	std::vector<ClientPlayer*>tickplayers(m_Players);
	for (size_t i = 0; i < tickplayers.size(); i++)
	{
		if (tickplayers[i] == NULL) continue;
		PlayerControl* pPlayerControl = dynamic_cast<PlayerControl*>(tickplayers[i]);
		if (pPlayerControl == NULL) continue;
		pPlayerControl->tickActorLine();
	}
}

void ClientActorMgr::tickActors(std::vector<ClientActor*>& actors)
{
	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientActor* actor = actors[i];
		if (actor == NULL)
			continue;

		tickOneActor(actor, false);

		//WORLD_ID objid = actor->getObjId();
		if (actor->checkNeedClear())
		{
			//工具版本1711修改
			//std::map<WORLD_ID, ClientActor *>::iterator iter = m_LiveActors.find(objid);
			//assert(iter != m_LiveActors.end());
			//if (iter != m_LiveActors.end())
			//{
			//	m_LiveActors.erase(iter);
			//}
		}
	}
}

void ClientActorMgr::update(float dtime)
{
	OPTICK_EVENT();
	SANDBOXPROFILING_FUNC("ClientActorMgr::update");
	METRIC_PROFILER("ClientActorMgr::update");

#ifdef USE_SPACE_TICK
    Rainbow::GetDebugMgr().SetCheckActorRelease(true);
	m_SpaceManager->Update(dtime);
    Rainbow::GetDebugMgr().SetCheckActorRelease(false);
#else
	//PROFINY_NAMED_SCOPE("ClientActorMgr::Update")
	{
		OPTICK_EVENT("actor update");
		for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
		{
			ClientActor* actor = iter->second;
			INVALID_ACTOR_CONTINUE(actor);
			if (actor == NULL) continue;
			actor->update(dtime);
		}
		OPTICK_TAG("actors", m_LiveActors.size());
	}

	{
		OPTICK_EVENT("player update");
		for (size_t i = 0; i < m_Players.size(); i++)
		{
			INVALID_ACTOR_CONTINUE(m_Players[i]);
			m_Players[i]->update(dtime);
		}
		OPTICK_TAG("player", m_Players.size());
	}
	{
		OPTICK_EVENT("boss update");
		for (size_t i = 0; i < m_Bosses.size(); i++)
		{
			INVALID_ACTOR_CONTINUE(m_Bosses[i]);
			m_Bosses[i]->update(dtime);
		}
		OPTICK_TAG("boss", m_Bosses.size());
	}
#endif

	// 【重要】在有渲染能力的端（单机或联机客户端）绘制调试线条，纯服务器端不需要
	// 判断标准：有渲染器 = 需要调试功能（单机模式或联机客户端）
	// 无渲染器 = 纯服务器端，不需要调试功能
	bool hasRenderer = (m_World->getRender() != nullptr);
	bool isRemote = m_World->isRemoteMode();
	
	if (hasRenderer)
	{
		// 只有在启用调试线条时才绘制
		if (m_mobPresetDebugEnabled)
		{
			// 初始化并绘制怪物预设位置调试线条
			if (!m_mobPresetDebugInitialized)
			{
				// 区分单机和联机客户端模式
				const char* modeStr = m_World->isRemoteMode() ? "CLIENT" : "STANDALONE";
				CLIENTACTORMGR_LOG("%s: Initializing MobPresetPositionMgr debug lines...", modeStr);
				m_mobPresetPositionMgr.DrawDebugLines(m_World, true);
				m_mobPresetDebugInitialized = true;
				CLIENTACTORMGR_LOG("%s: MobPresetPositionMgr debug lines initialized", modeStr);
			}
			m_mobPresetPositionMgr.Draw(m_World);
		}
		else
		{
			// 如果调试线条被禁用，清除已绘制的线条
			if (m_mobPresetDebugInitialized)
			{
				const char* modeStr = m_World->isRemoteMode() ? "CLIENT" : "STANDALONE";
				CLIENTACTORMGR_LOG("%s: Clearing MobPresetPositionMgr debug lines...", modeStr);
				m_mobPresetPositionMgr.ClearDebugLines();
				m_mobPresetDebugInitialized = false;
				CLIENTACTORMGR_LOG("%s: MobPresetPositionMgr debug lines cleared", modeStr);
			}
		}
	}
	else
	{
		// 纯服务器端不绘制调试线，只记录一次日志
		if (!m_mobPresetDebugInitialized)
		{
			CLIENTACTORMGR_LOG("SERVER: Skipping debug line drawing (no renderer available)");
			m_mobPresetDebugInitialized = true;
		}
	}
}

void ClientActorMgr::updateExtras(float dtime)
{
	OPTICK_EVENT();
	//PROFINY_NAMED_SCOPE("ClientActorMgr::updateActorExtras")
	if (GetWorldManagerPtr() == NULL) return;
	WCoord center = GetWorldManagerPtr()->m_RenderEyePos;
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		if (actor == NULL) continue;

		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		if (vehicle == NULL) continue;
		vehicle->getVehicleWorld()->getContainerMgr()->updateDisplay(dtime);
	}
}

void ClientActorMgr::clearMobs(MOB_TYPE mobtype)
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(iter->second);
		if (mob && mob->getDef()->Type == mobtype) mob->setNeedClear();
	}
}

void ClientActorMgr::clearMobs(int objtype)
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		if (iter->second->getObjType() == objtype)
			iter->second->setNeedClear();
	}
}

void ClientActorMgr::clearCustomMobs()
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		std::string sType;
		std::string sID;
		if (actor)
		{
			//string tmp = actor->getFacade();
			std::string tmp;
			auto functionWrapper = actor->getFuncWrapper();
			if (functionWrapper)
			{
				tmp = functionWrapper->getActorFacade();
			}
			else
			{
				continue;
			}

			int pos = (int)tmp.find('_');
			if (pos > 0 && pos < (int)tmp.size())
			{
				sType = tmp.substr(0, pos);
				sID = tmp.substr(pos + 1);
			}
			else
			{
				continue;
			}
			if (sType == "custom" || sType == "fullycustom")
			{
				actor->setNeedClear();
			}
			else
				continue;

		}

	}
}


void ClientActorMgr::clearActorsWithId(int actorid, bool bKill)
{
	std::vector<ClientActor*> vClearList;
	vClearList.clear();

	for (auto it = m_LiveActors.begin(); it != m_LiveActors.end(); it++) {
		if (it->second->getDefID() == actorid) {
			if (bKill)
				it->second->kill();
			else
				vClearList.push_back(it->second);
		}
	}

	if (vClearList.size() == 0) { return; }
	for (unsigned int i = 0; i < vClearList.size(); i++) {
		despawnActor(vClearList[i]);
	}
}

int ClientActorMgr::getTeamAllMobs(int teamid)
{
	m_vTeamMobs.clear();

	ClientMob* pMob;
	for (auto it = m_LiveActors.begin(); it != m_LiveActors.end(); it++) {
		pMob = dynamic_cast<ClientMob*>(it->second);
		if (pMob && pMob->getTeam() == teamid) {
			m_vTeamMobs.push_back(pMob->getObjId());
		}
	}

	return (int)m_vTeamMobs.size();
}

long long ClientActorMgr::getTeamMobByIndex(int idx)
{
	if (idx < (int)m_vTeamMobs.size()) {
		return m_vTeamMobs[idx];
	}

	return -1;
}

void ClientActorMgr::clearPhysxObj()
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		PhysicsLocoMotion* loc = dynamic_cast<PhysicsLocoMotion*>(iter->second->getLocoMotion());
		if (loc && loc->m_hasPhysActor)
		{
			iter->second->setNeedClear();
		}
		else
		{
			ActorBall* pBall = dynamic_cast<ActorBall*>(iter->second);
			if (pBall && pBall->isPhysics())
				iter->second->setNeedClear();
		}
	}
}

void ClientActorMgr::clearMobs()
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		if (actor) actor->setNeedClear();
	}
}

void ClientActorMgr::SetActorId(ClientActor* actor, long long objid/*=0*/)
{
	if (m_World && !m_World->isRemoteMode())
	{
		if (objid > 0)
			actor->SetObjId(objid);
		else {
			long long nextObjId = ClientActor::GenNextObjId();
			// CLIENTACTORMGR_LOG("SetActorId: objid=%lld, nextObjId=%lld", objid, nextObjId);
			actor->SetObjId(nextObjId);
		}
	}

	//疑似发生原因当地图崩溃的时候，全局数据存储和怪物数据存储会存在不一致,全局数据NextObjId会有落后
	//assert(m_LiveActors.find(actor->getObjId()) == m_LiveActors.end()); 
	while (1)
	{
		if (m_LiveActors.find(actor->getObjId()) != m_LiveActors.end())
		{
			ClientActor::ResetObjId(ClientActor::GenNextObjId() + 10);
			actor->SetObjId(ClientActor::GenNextObjId());
		}
		else
		{
			break;
		}
	}

}

void ClientActorMgr::SetBossId(ActorBoss* boss)
{
	if (!m_World->isRemoteMode())
	{
		boss->SetObjId(ClientActor::GenNextObjId());
	}
}

void ClientActorMgr::spawnActor(IClientActor* actor, long long objid)
{
	OPTICK_EVENT();

	SetActorId(static_cast<ClientActor*>(actor), objid);
	CLIENTACTORMGR_LOG("spawnActor: objid=%lld, defid=%d, position=%s", objid, actor->getDefID(), actor->getPosition().__tostring().c_str());
	
	ClientMob* mob = dynamic_cast<ClientMob*>(actor);
	if (mob) {
		unsigned int posKey = mob->getMobPresetPosKey();
		
		CLIENTACTORMGR_LOG("SpawnActor on %s: mobid=%d, posKey=%u, checking for personal spawn marker", m_World->isRemoteMode() ? "CLIENT" : "SERVER", mob->getDefID(), posKey);
		
		if (posKey == 999999) {
			addPersonalSpawnMarkers(mob);
			CLIENTACTORMGR_LOG("Found personal spawn mob via posKey on %s: mobid=%d, applying color marker", m_World->isRemoteMode() ? "CLIENT" : "SERVER", mob->getDefID());
		}
	}
	
	m_World->GetWorldScene()->BindLegacyActor(actor);
}

void ClientActorMgr::spawnBoss(ActorBoss* boss)
{
	//if(!m_World->isRemoteMode())
	//{
	//	boss->setObjId(ClientActor::genNextObjId());
	//}
	//boss->enterWorld(m_World);
	//m_Bosses.push_back(boss);

	SetBossId(boss);
	m_World->GetWorldScene()->BindLegacyActor(boss);
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("refresh_map", MNSandbox::SandboxContext(nullptr).SetData_Number("type", MRT_Boss));
}

void ClientActorMgr::spawnPlayerAddRef(ClientPlayer* player, bool isTeleport/*= false*/)
{
	if (!player) return;
#ifdef _DEBUG
	for (size_t i = 0; i < m_Players.size(); i++)
	{
		if (m_Players[i] == player || m_Players[i]->getUin() == player->getUin())
		{
			assert(0);
		}
	}
#endif

	bool isLocalPlayer = (player == g_pPlayerCtrl); // 本地玩家

#ifdef SDB_MULTI_SCENE_OPEN
	m_World->GetWorldScene()->BindLegacyActor(player);
	MNSandbox::GlobalNotify::GetInstance().m_PlayerEnterScene.Emit(player->getUin(), player->StaticToCast<SandboxNode>(), isLocalPlayer, m_World->getCurMapID(), isTeleport);
#else
	MNSandbox::GlobalNotify::GetInstance().m_PlayerEnterScene.Emit(player->getUin(), player->StaticToCast<SandboxNode>(), isLocalPlayer, m_World->getCurMapID(), isTeleport);
	m_World->GetWorldScene()->BindLegacyActor(player);
#endif


	//player->enterWorld(m_World);
	//m_Players.push_back(player);
	//m_Index[player->getUin()] = player;
	//如果有变身效果就不用初始化Avatar模型

	if (!(player->getLivingAttrib() && player->getLivingAttrib()->hasStatusEffect(STATUS_EFFECT_MODELCHANGE)))
	{
		bool changemodel = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->HasChangeModel(player->getUin());
		if (!changemodel)
		{
			player->initAvatar();
			player->initCusMotion();	//自定义动作处理
		}
	}
	player->addRef();
}

void ClientActorMgr::spawnActor(ClientActor* actor, const WCoord& worldpos, float yaw, float pitch, long long objid)
{
	OPTICK_EVENT();
	actor->getLocoMotion()->gotoPosition(worldpos, yaw, pitch);
	spawnActor(actor, objid);
	CLIENTACTORMGR_LOG("spawnActor: objid=%lld, defid=%d, position=%s", objid, actor->getDefID(), worldpos.__tostring().c_str());

	// 投掷物被创建
	if (actor->IsTriggerProjectile())
	{
		// 开发者事件
		if (m_World && !m_World->isRemoteMode())
		{
			ClientActor* shooter = actor->getShootingActor();
			WCoord objpos = actor->getPosition();
			objpos = CoordDivBlock(objpos);
			int itemid = actor->GetItemId();

			ClientPlayer* pPlayer = shooter ? dynamic_cast<ClientPlayer*>(shooter) : NULL;
			if (pPlayer)
			{
				// 观察者事件接口
				ObserverEvent obevent;
				obevent.SetData_EventObj(pPlayer->getObjId());
				obevent.SetData_ToObj(actor->getObjId());
				obevent.SetData_Item(itemid);
				obevent.SetData_Position(objpos.x, objpos.y, objpos.z);
				obevent.SetData_HelperObjid(pPlayer->getObjId());
				GetObserverEventManager().OnTriggerEvent("Missile.Create", &obevent);
			}
			else
			{
				long long eventobjid = (shooter && shooter->IsTriggerCreature()) ? shooter->getObjId() : 0;

				// 观察者事件接口
				ObserverEvent obevent;
				obevent.SetData_EventObj(eventobjid);
				obevent.SetData_ToObj(actor->getObjId());
				obevent.SetData_Item(itemid);
				obevent.SetData_Position(objpos.x, objpos.y, objpos.z);
				obevent.SetData_HelperObjid(eventobjid);
				if (eventobjid > 0)
				{
					obevent.SetData_Actor(shooter->getDefID());
				}
				GetObserverEventManager().OnTriggerEvent("Missile.Create", &obevent);
			}
		}
	}
}

ClientItem* ClientActorMgr::spawnItem(const WCoord& worldpos, int itemid, int num, int protodata, long long objid/* = 0*/)
{
	BackPackGrid grid;
	SetBackPackGrid(grid, itemid, num);
	return spawnItem(worldpos, grid, protodata, objid);
}

//生成新枪械或者新装备
ClientItem* ClientActorMgr::spawnEquipOrGun(const WCoord& worldpos, int itemid, int protodata, long long objid)
{
	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def)
		return NULL;


	if (def->IsDefCustomGun || def->IsDefEquip)
	{
		BackPackGrid grid;
		bool ret = GunSmithMgr::GetInstance().MakeEquipOrGunForGrid(itemid, grid);
		if (ret)
			return spawnItem(worldpos, grid, protodata, objid);
	}
	return NULL;
}

long long ClientActorMgr::spawnEquipOrGun(int x, int y, int z, int itemid)
{
	WCoord pos = WCoord(x, y, z) * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2);
	ClientItem* pItem = spawnEquipOrGun(pos, itemid);
	return pItem ? (long long)pItem->getObjId() : 0;
}

ClientItem* ClientActorMgr::spawnSpecialLetterItem(const WCoord& worldpos, int itemid, int num, int protodata, std::string letterStr)
{
	BackPackGrid grid;
	SetBackPackGrid(grid, itemid, num);

	std::string title = "";
	std::string context = "";
	std::string authorName = "";
	std::string serviceID;
	int canSave = 1;
	jsonxx::Object txtObj;
	if (txtObj.parse(letterStr))
	{
		if (txtObj.has<jsonxx::String>("title"))
		{
			title = txtObj.get<jsonxx::String>("title");
		}
		if (txtObj.has<jsonxx::String>("context"))
		{
			context = txtObj.get<jsonxx::String>("context");
		}
		if (txtObj.has<jsonxx::String>("authorName"))
		{
			authorName = txtObj.get<jsonxx::String>("authorName");
		}
		if (txtObj.has<jsonxx::String>("serviceID"))
		{
			serviceID = txtObj.get<jsonxx::String>("serviceID");
		}
		if (txtObj.has<jsonxx::Number>("canSave"))
		{
			canSave = (int)txtObj.get<jsonxx::Number>("canSave");
		}
	}
	jsonxx::Object lettersObj;
	lettersObj << "uin" << g_pPlayerCtrl->getUin();
	lettersObj << "authorname" << authorName;
	lettersObj << "title" << title;
	lettersObj << "context" << context;
	lettersObj << "serviceID" << serviceID;
	lettersObj << "canSave" << canSave;
	grid.userdata_str = lettersObj.json();

	return spawnItem(worldpos, grid, protodata);
}

namespace AntiSetting {
	extern void recordMapItem(int64_t objid, int item_id, int num);
}

ClientItem* ClientActorMgr::spawnItem(const WCoord& worldpos, const BackPackGrid& grid, int protodata, long long objid/* = 0*/)
{
	OPTICK_EVENT();
	if (!GetWorldManagerPtr()->getCanDropItem())
		return NULL;

	ClientItem* item = SANDBOX_NEW(ClientItem, grid);

	ActorLocoMotion* locmove = item->getLocoMotion();
	locmove->gotoPosition(worldpos, 0, 0);
	locmove->m_RotateYaw = GenRandomFloat() * 360.0f;
	// locmove->m_Motion.x = GenRandomFloat() * 20.0f - 10.0f;
	// locmove->m_Motion.y = 20.0f;
	// locmove->m_Motion.z = GenRandomFloat() * 20.0f - 10.0f;

	item->setProtoData(protodata);

	spawnActor(item, objid);
	AntiSetting::recordMapItem(item->getObjId(), grid.getItemID(), grid.getNum());
	return item;
}

static bool CanMobTypeSpawnOnPos(WorldProxy* pworld, MOB_TYPE mobtype, const WCoord& pos, bool isAmphibious)
{
	if (mobtype == MOB_WATER)
	{
		bool tmp1 = pworld->isBlockLiquid(pos);
		bool tmp2 = !pworld->isBlockNormalCube(TopCoord(pos));
		bool checkWater = tmp1 && tmp2;
		return checkWater;
	}
	else if (mobtype == MOB_FLY)
	{
		bool tmp1 = pworld->isAirBlock(pos);
		bool tmp2 = !pworld->isBlockNormalCube(TopCoord(pos));
		bool checkAir = tmp1 && tmp2;
		return checkAir;
	}
	else
	{
		WCoord downpos = DownCoord(pos);
		if (!pworld->doesBlockHaveSolidTopSurface(downpos))
		{
			if (isAmphibious)//水陆两栖
			{
				bool tmp1 = pworld->isBlockLiquid(pos);
				bool tmp2 = !pworld->isBlockNormalCube(TopCoord(pos));
				bool checkWater = tmp1 && tmp2;
				return checkWater;
			}
			return false;
		}
		int downid = pworld->getBlockID(downpos);
		if (downid == BLOCK_BEDROCK) return false;

		if (mobtype == MOB_HOSTILE && GetWorldManagerPtr() && GetWorldManagerPtr()->m_DragonStatuePoint.y >= 0)
		{
			WCoord dp = pos - GetWorldManagerPtr()->m_DragonStatuePoint;
			int w = 16;
			if (Abs(dp.x) < w && Abs(dp.z) < w) return false;
		}

		return !pworld->isBlockNormalCube(pos) && !pworld->isBlockLiquid(pos) && !pworld->isBlockNormalCube(TopCoord(pos));
	}
}

static bool CanMobTypeSpawnOnPos(World* pworld, MOB_TYPE mobtype, const WCoord& pos, bool isAmphibious = false)
{
	MainWorldProxy proxy(pworld);
	return CanMobTypeSpawnOnPos(&proxy, mobtype, pos, isAmphibious);
}

static bool CanSpawnMob3413(World* pworld, const WCoord& blockpos)
{
	WCoord c1 = BlockDivSection(blockpos);
	WCoord s = BlockDivSection(GetWorldManagerPtr()->getSpawnPointEx(pworld));

	if (c1.x >= s.x - 2 && c1.x <= s.x + 2) return false;
	if (c1.z >= s.z - 2 && c1.z <= s.z + 2) return false;
	return true;
}

bool CanMonbSpawnHere(const MonsterDef* def, World* pworld, const WCoord& pos)
{
	WCoord blockpos = CoordDivBlock(pos);
	if (pworld->getBlockSunIllum(blockpos) < def->SpawnSunLight)
	{
		CLIENTACTORMGR_LOG("CanMonbSpawnHere false: getBlockSunIllum value:%d, SpawnSunLight:%d position x:%d y:%d z:%d", pworld->getBlockSunIllum(blockpos), def->SpawnSunLight, pos.x, pos.y, pos.z);
		return false;
	}

	if (pworld->getBlockLightValue(blockpos) > def->SpawnMaxLight)
	{
		CLIENTACTORMGR_LOG("CanMonbSpawnHere false: getBlockLightValue value:%d, SpawnMaxLight:%d position x:%d y:%d z:%d", pworld->getBlockLightValue(blockpos), def->SpawnMaxLight, pos.x, pos.y, pos.z);
		return false;
	}

	// 生物生成时间判断
	int curHours = pworld->getHours();
	int limitMinT = def->SpawnMinTime;
	int limitMaxT = def->SpawnMaxTime;

	// 两个时间一样，代表无限制
	if (limitMinT != limitMaxT)
	{
		if (limitMinT > limitMaxT)
		{
			if (curHours >= limitMaxT && curHours < limitMinT)
			{
				CLIENTACTORMGR_LOG("CanMonbSpawnHere false: curHours:%d, limitMaxT:%d, limitMinT:%d", curHours, limitMaxT, limitMinT);
				return false;
			}
		}
		else
		{
			if (!(curHours >= limitMinT && curHours < limitMaxT))
			{
				CLIENTACTORMGR_LOG("CanMonbSpawnHere false: curHours:%d, limitMaxT:%d, limitMinT:%d", curHours, limitMaxT, limitMinT);
				return false;
			}
		}
	}

	// 生物生成温度判断
	int curPosTemperature = dynamic_cast<TemperatureManager*>(g_WorldMgr->getTemperatureMgr())->GetPositionTemperature(pworld, blockpos);
	if (curPosTemperature < def->SpawnMinTemperature || (def->SpawnMaxTemperature > 0 && curPosTemperature > def->SpawnMaxTemperature))
	{
		CLIENTACTORMGR_LOG("CanMonbSpawnHere false: curPosTemperature:%d, SpawnMinTemperature:%d, SpawnMaxTemperature:%d", curPosTemperature, def->SpawnMinTemperature, def->SpawnMaxTemperature);
		return false;
	}

	// 生物生成高度判断
	int curPosHeight = blockpos.y;
	if (curPosHeight < def->SpawnMinHeight || (def->SpawnMaxHeight > 0 && curPosHeight > def->SpawnMaxHeight))
	{
		CLIENTACTORMGR_LOG("CanMonbSpawnHere false: curPosHeight:%d, SpawnMinHeight:%d, SpawnMaxHeight:%d", curPosHeight, def->SpawnMinHeight, def->SpawnMaxHeight);
		return false;
	}

	if (!pworld->getChunkProvider()->canMobSpawnHere(def, pworld, blockpos)) {
		CLIENTACTORMGR_LOG("CanMonbSpawnHere false: canMobSpawnHere return false, defid=%d, position x:%d y:%d z:%d", def->ID, blockpos.x, blockpos.y, blockpos.z);
		return false;
	}

	// if (def->Type == MOB_PASSIVE)
	// {
	// 	// 动物只能刷在草块和冻土上
	// 	if (pworld->getBlockID(DownCoord(blockpos)) != BLOCK_GRASS &&
	// 		pworld->getBlockID(DownCoord(blockpos)) != BLOCK_DIRT_FREEZE) {
	// 		CLIENTACTORMGR_LOG("CanMonbSpawnHere false: animal should spawn on grass or freeze dirt, defid=%d, position x:%d y:%d z:%d", def->ID, blockpos.x, blockpos.y, blockpos.z);
	// 		return false;
	// 	}
	// }

	// if (def->ID == 3413 && !CanSpawnMob3413(pworld, blockpos)) return false;//迅猛龙特殊处理

	CollideAABB box;
	CalCollideBox(box, def, pos);

	if (def->Type != MOB_WATER && pworld->isAnyLiquid(box.minPos(), box.maxPos())) {
		CLIENTACTORMGR_LOG("CanMonbSpawnHere false: gen MOB NON_WATER return false, defid=%d, position x:%d y:%d z:%d", def->ID, blockpos.x, blockpos.y, blockpos.z);
		return false;
	}

	if (def->Type == MOB_WATER && !pworld->isAnyLiquid(box.minPos(), box.maxPos())) {
		CLIENTACTORMGR_LOG("CanMonbSpawnHere false: gen MOB_WATER return false, defid=%d, position x:%d y:%d z:%d", def->ID, blockpos.x, blockpos.y, blockpos.z);
		return false;
	}

	//bool ret = pworld->checkNoCollisionBoundBox(box, NULL);
	//if (!ret) {
	//	CLIENTACTORMGR_LOG("CanMonbSpawnHere false: checkNoCollisionBoundBox return false, defid=%d, position x:%d y:%d z:%d", def->ID, blockpos.x, blockpos.y, blockpos.z);
	//}
	return true;
}

ClientAvatarSummonMob* ClientActorMgr::spawnMob(const WCoord& pos, int monsterid)
{
	bool canSpawn = true;
	MINIW::ScriptVM::game()->callFunction("CanSpawnMob", "iu[World]iii>b", monsterid, m_World, pos.x, pos.y, pos.z, &canSpawn);

	if (!canSpawn)
		return NULL;

	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(monsterid);
	ClientAvatarSummonMob* mon = ClientAvatarSummonMob::createFromDef(monsterid);
	if (NULL == mon)
		return NULL;

	mon->getLocoMotion()->gotoPosition(pos, float(GenRandomInt(360)), 0);

	spawnActor(mon);

	return mon;
}

void ClientActorMgr::parseNpcList()
{
	const char* nt = GetGameInfoProxy()->GetGameVar("npc_list");
	jsonxx::Array ntObject;
	ntObject.parse(nt);
	for (unsigned int i = 0; i < (unsigned int)ntObject.size(); i++)
	{
		m_NpcList.push_back((int)ntObject.get<jsonxx::Number>(i));
	}

	for (int aNpc : m_NpcList)
	{
		if (aNpc == 4)
		{
			m_canSpawn3507 = true;
		}
		else if (aNpc == 5)
		{
			m_canSpawn3508 = true;
		}
		else if (aNpc == 6)
		{
			m_canSpawn3120 = true;
		}
	}

	return;
}

ClientMob* ClientActorMgr::spawnMob(const WCoord& pos, int monsterid, bool mobtype_check, bool mob_check, float yaw/* =-1 */, int mobtype/* = 0*/, std::string monsterName/* = ""*/, bool trigger, unsigned int presetPosKey/* = 0*/)
{
	OPTICK_EVENT();
	//bool canSpawn3507 = m_canSpawn3507;
	//bool canSpawn3508 = m_canSpawn3508;
	//bool canSpawn3120 = m_canSpawn3120;

	////只有过年的时候刷浮球
	//if (monsterid == 3507 && !canSpawn3507)
	//{
	//	return NULL;
	//}


	////只有周年庆的时候刷奶嘴怪
	//if (monsterid == 3508 && !canSpawn3508)
	//{
	//	return NULL;
	//}

	////只有五一的时候刷恶霸
	//if (monsterid == 3120 && !canSpawn3120)
	//{
	//	return NULL;
	//}
	CLIENTACTORMGR_LOG("spawnMob: mobid=%d, position=%s, posKey = %d begin", monsterid, pos.__tostring().c_str(), presetPosKey);

	CityConfig* cityConfig = CityConfig::getSingletonPtr();
	if (cityConfig &&
		cityConfig->getOtherConfig().notGenMonsterId.size()>0 &&
	 	cityConfig->getOtherConfig().notGenMonsterId.find(monsterid) != cityConfig->getOtherConfig().notGenMonsterId.end())
	{
		CLIENTACTORMGR_LOG("spawnMob %d failed, config not gen monster", monsterid);
		return NULL;//配置不生成生物
	}

	bool canSpawn = true;
	MINIW::ScriptVM::game()->callFunction("CanSpawnMob", "iu[World]iii>b", monsterid, m_World, pos.x, pos.y, pos.z, &canSpawn);

	if (!canSpawn)
	{
		CLIENTACTORMGR_LOG("spawnMob %d failed, canSpawn return NULL", monsterid);
		return NULL;
	}

	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(monsterid);
	if (mobtype_check)
	{
		WCoord blockpos = CoordDivBlock(pos);
		if (def == NULL)
		{
			CLIENTACTORMGR_LOG("spawnMob %d failed, def == NULL", monsterid);
			return NULL;
		}
		bool isAmphibious = false;
		if (def->ID == 3621)//螃蟹属于陆地水里都可以放置生成的生物，在这里加个判断，后面再加类型
		{
			isAmphibious = true;
		}
		if (!CanMobTypeSpawnOnPos(m_World, (MOB_TYPE)def->Type, blockpos, isAmphibious))
		{
			CLIENTACTORMGR_LOG("spawnMob %d failed, CanMobTypeSpawnOnPos return NULL", monsterid);
			return NULL;
		}
	}
	if (mob_check)
	{
		if (def == NULL) {
			CLIENTACTORMGR_LOG("spawnMob %d failed, def == NULL", monsterid);
			return NULL;
		}
		if (!CanMonbSpawnHere(def, m_World, pos)) {
			CLIENTACTORMGR_LOG("spawnMob %d failed, CanMonbSpawnHere return NULL, pos=%s", monsterid, pos.__tostring().c_str());
			return NULL;
		}
	}

	ClientMob* mon = ClientMob::createFromDef(monsterid, mobtype, trigger);
	if (NULL == mon)
	{
		CLIENTACTORMGR_LOG("spawnMob %d failed, ClientMob::createFromDef return NULL", monsterid);
		return NULL;
	}

	if (yaw < 0)
		mon->getLocoMotion()->gotoPosition(pos, float(GenRandomInt(360)), 0);
	else
		mon->getLocoMotion()->gotoPosition(pos, yaw, 0);

	mon->setmodName(monsterName);
	mon->setSpawnPoint(pos);

	// 注意：客户端模式下刷新的NPC没有有效objId，无法交互
	// 如需客户端刷新，应通过网络同步获取服务器分配的objId
	spawnActor(mon);

	if (def && def->Type == 7)
		m_NpcActorsWID[monsterid] = mon->getObjId();

	CLIENTACTORMGR_LOG("spawnMob %d success. total mobs:%d", monsterid, m_LiveMobNum[def->Type]);
	return mon;
}

ClientMob* ClientActorMgr::spawnMobByPrefab(int x, int y, int z, const std::string& monsterName)
{
	auto pActor = UgcAssetMgr::GetInstancePtr()->CreateMobByPrefab(monsterName);
	auto pMob = dynamic_cast<ClientMob*>(pActor);
	if (nullptr != pMob)
	{
		pMob->setPosition(WCoord(x, y, z));
		pMob->SetWorldPosition((float)x, (float)y, (float)z);
		pMob->setSpawnPoint(WCoord(x, y, z));
		pMob->setFaceYaw(float(GenRandomInt(360)), true);
	}
	return pMob;
}
ClientMob* ClientActorMgr::spawnMobByPrefab(int x, int y, int z, const std::string& monsterName, float yaw, float pitch)
{
	auto pActor = UgcAssetMgr::GetInstancePtr()->CreateMobByPrefab(monsterName);
	auto pMob = dynamic_cast<ClientMob*>(pActor);
	if (nullptr != pMob)
	{
		pMob->getLocoMotion()->gotoPosition(WCoord(x,y,z), yaw, pitch);
		pMob->setSpawnPoint(WCoord(x, y, z));
	}
	return pMob;
}

ClientMob* ClientActorMgr::spawnMobByPrefabCheck(int x, int y, int z, const std::string& monsterName, int yaw, bool mobtype_check, bool mob_check, int monsterId)
{
	if (!UgcAssetMgr::GetInstancePtr() || !ModPackMgr::GetInstancePtr()) return NULL;
	if (monsterId <= 0)
	{
		monsterId = ModPackMgr::GetInstancePtr()->GetAllocatedId(monsterName);
		if (monsterId <= 0) return NULL;
	}
	WCoord pos(x, y, z);
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(monsterId);
	if (mobtype_check)
	{
		WCoord blockpos = CoordDivBlock(pos);
		if (def == NULL)
			return NULL;
		bool isAmphibious = false;
		if (def->ID == 3621)//螃蟹属于陆地水里都可以放置生成的生物，在这里加个判断，后面再加类型
		{
			isAmphibious = true;
		}
		if (!CanMobTypeSpawnOnPos(m_World, (MOB_TYPE)def->Type, blockpos, isAmphibious))
		{
			return NULL;
		}
	}
	if (mob_check)
	{
		if (def == NULL)
			return NULL;
		if (!CanMonbSpawnHere(def, m_World, pos)) return NULL;
	}
	//20210812: 新增是否触发触发器参数 codeby:wangshuai
	ClientMob* mon = dynamic_cast<ClientMob*>(UgcAssetMgr::GetInstancePtr()->CreateMobByPrefab(monsterName));
	if (NULL == mon)
		return NULL;

	if (yaw < 0)
		mon->getLocoMotion()->gotoPosition(pos, float(GenRandomInt(360)), 0);
	else
		mon->getLocoMotion()->gotoPosition(pos, yaw, 0);
	if (def && def->Type == 7)
		m_NpcActorsWID[monsterId] = mon->getObjId();
	return mon;
}

void ClientActorMgr::spawnMobDelay(const WCoord& pos, int delay_ticks, int monsterid, bool mobtype_check, bool mob_check, unsigned char mob_type, SpawnMobDelayCallback pfunc, float yaw/* =-1 */, SpawnMobCallback pfCreate, unsigned int presetPosKey)
{
	assert(delay_ticks >= 0);

	//CLIENTACTORMGR_LOG("spawnMobDelay: delay_ticks=%d, monsterid=%d, mobtype_check=%d, mob_check=%d, yaw=%f, pfCreate=%p",
	//	delay_ticks, monsterid, mobtype_check, mob_check, yaw, pfCreate);
	m_SpawnMobDelayParams.push_back(SpawnMobDelayParam(pos, delay_ticks, monsterid, mobtype_check, mob_check, mob_type, pfunc, yaw, pfCreate, presetPosKey));
}

void ClientActorMgr::addMobSpawnNum(int mobtype, int num)
{
	if (mobtype < MAX_MOBTYPE && mobtype >= 0)
	{
		CLIENTACTORMGR_LOG("addMobSpawnNum: mobtype=%d, num=%d, liveMobNum=%d", mobtype, num, m_LiveMobNum[mobtype]);
		m_LiveMobNum[mobtype] += num;
	}
}

void ClientActorMgr::addMobSpawnNumByID(int mobId, int num)
{
	if (mobId <= 0)
	{
		return;
	}
	if (m_LiveMobNumByID.find(mobId) == m_LiveMobNumByID.end())
	{
		CLIENTACTORMGR_LOG("addMobSpawnNumByID: SET mobId=%d, num=%d, liveMobNumByID=%d", mobId, num, m_LiveMobNumByID[mobId]);
		m_LiveMobNumByID[mobId] = num;
	}
	else
	{
		CLIENTACTORMGR_LOG("addMobSpawnNumByID: mobId=%d, num=%d, liveMobNumByID=%d", mobId, num, m_LiveMobNumByID[mobId]);
		m_LiveMobNumByID[mobId] += num;
	}
}

ClientPlayer* ClientActorMgr::findPlayerByUin(int uin)
{
	auto iter = m_Index.find(uin);
	if (iter != m_Index.end())
	{
		return iter->second.get();
	}

	return NULL;
}

//ClientPlayer* ClientActorMgr::findPlayerByUid(const std::string& uid)
//{
//	auto iter = m_UidIndex.find(uid);
//	if (iter != m_UidIndex.end())
//	{
//		return iter->second.get();
//	}
//
//	return NULL;
//}

bool ClientActorMgr::areAllPlayersAsleep()
{
	std::vector<ClientPlayer*>::iterator iter = m_Players.begin();
	if (iter == m_Players.end())//特判
	{
		return false;
	}
	for (; iter != m_Players.end(); iter++)
	{
		ClientPlayer* player = *iter;
		auto sleepState = dynamic_cast<SleepState*>(player->getActionStatePtr("Sleep"));
		if (sleepState && (!sleepState->isSleeping() || sleepState->getSleepingTimer() < 100))
		{
			return false;
		}
	}

	return true;
}

//获取当前睡觉玩家数量
int ClientActorMgr::sleepingMembers()
{
	int num = 0;
	std::vector<ClientPlayer*>::iterator iter = m_Players.begin();
	for (; iter != m_Players.end(); iter++)
	{
		ClientPlayer* player = *iter;
		if (player->isSleeping()) num++;
	}
	return num;
}


void ClientActorMgr::wakeAllPlayers()
{
	std::vector<ClientPlayer*>::iterator iter = m_Players.begin();
	for (; iter != m_Players.end(); iter++)
	{
		ClientPlayer* player = *iter;
		if (player->isSleeping())
		{
			WorldCanvas::whenPlayerWakeup(player);
			player->wakeUp(false, false, true);
			player->updateTaskSysProcess(TASKSYS_SKIP_NIGHT);
			//todo huangfubin
			if (GetLuaInterfaceProxyPtr()->shouldUseNewHpRule())
			{
				WCoord pos = player->getPosition();
				int biomeId = player->getWorld()->getBiomeType(CoordDivBlock(pos.x), CoordDivBlock(pos.z));

				LivingAttrib* attrib = player->getLivingAttrib();
				if (attrib)
				{

					auto pSleepState = dynamic_cast<SleepState*>(player->getLocoCurActionStatePtr("Sleep"));
					float SleepRecoveryFactor = 1.0f;
					if (pSleepState)
					{
						int sleepTime = pSleepState->m_sleepRealHour;
						std::vector<std::array<int, 3>> SleepConfig = GetLuaInterfaceProxy().get_lua_const()->sleepConfig;
						for (size_t i = 0; i < SleepConfig.size(); i++)
						{
							std::array<int, 3> arr = SleepConfig[i];

							int StartTime = arr[0];
							int EndTime = arr[1];
							int Precent = arr[2];
							if (StartTime <= sleepTime && sleepTime < EndTime)
							{
								int Random = rand() % 3;
								int Offset = Random - 1;
								SleepRecoveryFactor = (Precent * 1.0 / 100) + (Offset / 10.0);
								PlayerAttrib* playerAttrib = player->getPlayerAttrib();
								if (playerAttrib)
								{
									float st = playerAttrib->getStrength();
									if (st <= 15)
									{
										st = 20.0;
									}
									else
									{
										st += 10 * SleepRecoveryFactor;
									}
									playerAttrib->setStrength(st);
								}
							}
						}
					}
					std::vector<int> SleepDebuff{ 107001,108001 };
					int buffid = GetDefManagerProxy()->getCurBiomeBuffFixed(biomeId, SleepRecoveryFactor, SleepDebuff);
					attrib->addBuff(buffid / 1000, buffid % 1000);

				}
			}
			else {
				LivingAttrib* attrib = player->getLivingAttrib();
				if (attrib)
				{
					attrib->addBuff(81, 1);
				}
			}
		}
	}
}

bool ClientActorMgr::areAllPlayersSkipNight()
{
	std::vector<ClientPlayer*>::iterator iter = m_Players.begin();
	for (; iter != m_Players.end(); iter++)
	{
		ClientPlayer* player = *iter;
		if (!player->isCoconutSkipNight() || player->getCoconutTimer() < 100) return false;
	}

	return true;
}

void ClientActorMgr::wakeSkipNightPlayers()
{
	std::vector<ClientPlayer*>::iterator iter = m_Players.begin();

	for (; iter != m_Players.end(); iter++)
	{
		ClientPlayer* player = *iter;
		if (player->isSleeping())
		{
			WorldCanvas::whenPlayerWakeup(player);
			player->wakeUp(false, false, true);
			player->updateTaskSysProcess(TASKSYS_SKIP_NIGHT);
		}
		//增加一个buf
		if (player->isCoconutHit())
		{
			LivingAttrib* attrib = player->getLivingAttrib();
			if (attrib)
			{
				attrib->addBuff(107, 1);
			}
		}
		player->updateCoconutSkipNight();
	}
}

void ClientActorMgr::broadcastGameInfo(int infotype, int id, int num, const char* name)
{
	std::vector<ClientPlayer*>::iterator iter = m_Players.begin();
	for (; iter != m_Players.end(); iter++)
	{
		ClientPlayer* player = *iter;
		player->notifyGameInfo2Self(infotype, id, num, name);
	}
}

ClientPlayer* ClientActorMgr::getOccupiedPlayer(const WCoord& blockpos, int flag)
{
	for (int i = 0; i < getNumPlayer(); i++)
	{
		ClientPlayer* pp = getIthPlayer(i);

		//校验ACTORFLAG_SLEEP不一定睡觉也可能躺床上
		if (pp->getFlagBit(flag) || ((flag == ACTORFLAG_SLEEP) && (pp->getFlagBit(flag) || pp->isRestInBed())) || pp->isSittingInStarStationCabin())
		{
			if (blockpos == CoordDivBlock(pp->getPosition())) return pp;
		}

		if (pp->isSittingInPianoChair())
		{
			return pp;
		}
	}
	return NULL;
}

ClientPlayer* ClientActorMgr::getPlayerByPos(const WCoord& blockpos)
{
	for (int i = 0; i < getNumPlayer(); i++)
	{
		ClientPlayer* pp = getIthPlayer(i);
		if (pp && blockpos == CoordDivBlock(pp->getPosition()))
			return pp;
	}

	return NULL;
}

ClientMob* ClientActorMgr::findMobByWID(long long wid)
{
	auto iter = m_LiveActors.find(wid);
	if (iter != m_LiveActors.end())
	{
		return dynamic_cast<ClientMob*>(iter->second);
	}
	return NULL;
}

ActorHorse* ClientActorMgr::findActorHorsebByWID(long long wid)
{
	auto iter = m_LiveActors.find(wid);
	if (iter != m_LiveActors.end())
	{
		return dynamic_cast<ActorHorse*>(iter->second);
	}
	return NULL;
}

ClientMob* ClientActorMgr::findMobByServerID(const std::string& serverid)
{
	if (serverid.empty())
	{
		return NULL;
	}
	auto iter = m_LiveActors.begin();
	for (; iter != m_LiveActors.end(); iter++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(iter->second);
		if (mob)
		{
			if (serverid.compare(mob->getHomeLandServerId()) == 0)
			{
				return mob;
			}
		}
	}
	return NULL;
}
ClientActor* ClientActorMgr::findActorByWID(long long wid)
{
	if (wid < MAX_PLAYER_UIN)
	{
		return findPlayerByUin((int)wid);
	}
	else
	{
		auto iter = m_LiveActors.find(wid);
		if (iter != m_LiveActors.end())
			return iter->second;

		auto iter1 = m_EmptyActors.find(wid);
		if (iter1 != m_EmptyActors.end())
			return iter1->second;

		for (size_t i = 0; i < m_Bosses.size(); i++)
		{
			if (m_Bosses[i]->getObjId() == wid) return m_Bosses[i];
		}
	}
	return NULL;
}

bool ClientActorMgr::iIsActorExist(IClientActor* actor)
{
	return isActorExist(actor ? actor->GetActor() : nullptr);
}

bool ClientActorMgr::isActorExist(long long objId) {
	return findActorByWID(objId) != nullptr;
}

bool ClientActorMgr::isActorExist(ClientActor* actor)
{
	//从逻辑上来，m_LiveActors中用于索引的是ClientActor的m_ObjId，而不是_ID，这里的用_ID查找是有问题的
	//修改为findActorByWID
	// by David, 2024/5/30
	if (actor == nullptr) return false;
	return findActorByWID(actor->getObjId()) != nullptr;

	//用map直接找
	//auto iter = m_LiveActors.find(actor->_ID);
	//return iter != m_LiveActors.end();

	// 	auto iter = m_LiveActors.begin();
	// 	while (iter != m_LiveActors.end())
	// 	{
	// 		if (iter->second == actor)
	// 		{
	// 			return true;
	// 		}
	// 		iter++;
	// 	}
	// 	return false;
}

static void GetOnePlayerSpawnChunks(GenMobTable& indices, ClientPlayer* player, World* pworld)
{
	WCoord center = player->getPosition();
	int cx = CoordDivSection(center.x);
	int cz = CoordDivSection(center.z);

	short range = 3;
#ifdef DEDICATED_SERVER
	range = 3;
#endif
	for (int z = -range; z <= range; z++)
	{
		for (int x = -range; x <= range; x++)
		{
			Chunk* pchunk = pworld->getChunkBySCoord(x + cx, z + cz);
			//if (pchunk && (!pworld->hasSky() || pchunk->canGenerateMob()))
			if (pchunk)
			{
//#ifdef DEDICATED_SERVER
//				bool onedge = false;
//#else
//				bool onedge = x == -range || x == range || z == -range || z == range;
//#endif

				indices.insert(ChunkIndex(x + cx, z + cz), true);
			}
		}
	}
}

void ClientActorMgr::getMobSpawnChunks(GenMobTable& indices)
{
	indices.clear();
	for (size_t i = 0; i < m_Players.size(); i++)
	{
		GetOnePlayerSpawnChunks(indices, m_Players[i], m_World);
	}
}

static ClientMob* GetNearbyMob(World* pworld, const WCoord& center, int range_xz, int range_y, int mobresid)
{
	CollideAABB box;
	box.pos = center * BLOCK_SIZE;
	box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	box.expand(range_xz * BLOCK_SIZE, range_y * BLOCK_SIZE, range_xz * BLOCK_SIZE);

	std::vector<IClientActor*>actors;
	pworld->getActorsInBox(actors, box);
	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(actors[i]);
		if (mob && mob->getDef()->ID == mobresid)
		{
			return mob;
		}
	}

	return NULL;
}

static int GetNearbyMobCount(World* pworld, const WCoord& center, int range_xz, int range_y, int mobresid) {
	CollideAABB box;
	box.pos = center * BLOCK_SIZE;
	box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	box.expand(range_xz * BLOCK_SIZE, range_y * BLOCK_SIZE, range_xz * BLOCK_SIZE);

    std::vector<IClientActor*>actors;
    pworld->getActorsInBox(actors, box);
    int count = 0;
    for (size_t i = 0; i < actors.size(); i++) {
        ClientMob* mob = dynamic_cast<ClientMob*>(actors[i]);
        if (mob && mob->getDef()->ID == mobresid) {
            count++;
        }
    }

	return count;
}

// 统计玩家周围所有mob的总数量（用于个人刷新系统）
static int GetNearbyAllMobCount(World* pworld, const WCoord& center, int range_xz, int range_y) {

	WCoord worldCenter = center * BLOCK_SIZE + WCoord(BLOCK_SIZE/2, 0, BLOCK_SIZE/2);
	WCoord boxSize = WCoord(range_xz * 2 * BLOCK_SIZE, range_y * 2 * BLOCK_SIZE, range_xz * 2 * BLOCK_SIZE);
	
	CollideAABB box;
	box.pos = worldCenter - boxSize / 2;
	box.dim = boxSize;

	std::vector<IClientActor*> actors;
	pworld->getActorsInBox(actors, box);
	int count = 0;
	for (size_t i = 0; i < actors.size(); i++) {
		ClientMob* mob = dynamic_cast<ClientMob*>(actors[i]);
		if (mob) {
			count++;
		}
	}
	
	CLIENTACTORMGR_LOG("GetNearbyAllMobCount: center=(%d,%d,%d), range=%d, found %d mobs", center.x, center.y, center.z, range_xz, count);
	return count;
}



bool ClientActorMgr::performWorldGenSpawnOne(Ecosystem* biome, int ox, int oz, int rangex, int rangez, ChunkRandGen& randgen, MOB_TYPE mobtype)
{
	OPTICK_EVENT();
	auto funcIsSpawnUnderground = [](int mobid, const MonsterDef* def, int y, int& outMinH, int& outMaxH, std::vector<int>& outValidBlockids) -> bool {
		if (mobid == 3881) // 焱焱蟹，目前没有单独的字段控制生物地下生成
		{
			int miny = 40; // 生物生成的最低高度
			outMinH = 3; // 生物生成的地下深度，最小深度
			outMaxH = 10; // 生物生成的地下深度，最大深度

			if (BiomeRegionGenConfig::GetInstancePtr())
			{
				BiomeRegionGenConfig::GetInstance().GetInt(BIOME_VOLCANO, "MOB3881_SpawnHeightLimit", miny);
				BiomeRegionGenConfig::GetInstance().GetNumberArray(BIOME_VOLCANO, "MOB3881_SpawnUndergroundDepth", outMinH, outMaxH);
			}
			if (y <= miny)
				return false;

			outMinH = Min(y - miny, outMinH);
			outMaxH = Min(y - miny, outMaxH);

			outValidBlockids.push_back(BLOCK_SULPHURROCK); // 生物生成的方块，需要被挖空
			outValidBlockids.push_back(BLOCK_STONE); // 生物生成的方块，需要被挖空
			return true;
		}
		return false;
	};
	auto funcFindSpawnUndergroundPos = [&](int x, int y, int z, int minoH, int maxoH, const std::vector<int>& validBlocks) -> int {
		// 地下生物随机地面往下生成
		// 优先找空的方块生成，如果没有空的方块，则随机找位置破坏方块生成
		std::vector<int> oyAir, oyValidBlock;
		int blockidTemp = 0;
		for (int oy = minoH; oy <= maxoH; oy++)
		{
			blockidTemp = m_World->getBlockID(WCoord(x, y - oy, z));
			if (blockidTemp == BLOCK_AIR)
			{
				oyAir.push_back(oy);
			}
			else if (std::find(validBlocks.begin(), validBlocks.end(), blockidTemp) != validBlocks.end())
			{
				oyValidBlock.push_back(oy);
			}
		}
		if (oyAir.size() > 1)
		{
			return oyAir.at(randgen.nextInt(oyAir.size() - 1) + 1);
		}
		else if (!oyAir.empty())
		{
			int oy = oyAir.front();
			if (m_World->getBlockID(TopCoord(WCoord(x, y - oy, z))) != BLOCK_AIR) // 生成普通怪物需要两格空间
			{
				m_World->setBlockAll(TopCoord(WCoord(x, y - oy, z)), BLOCK_AIR, 0);
			}
			return oy;
		}
		else if (!oyValidBlock.empty())
		{
			int oy;
			if (oyValidBlock.size() > 1)
			{
				oy = oyValidBlock.at(randgen.nextInt(oyValidBlock.size() - 1) + 1);
			}
			else
			{
				oy = oyValidBlock.front();
			}

			m_World->setBlockAll((WCoord(x, y - oy, z)), BLOCK_AIR, 0);
			if (m_World->getBlockID(TopCoord(WCoord(x, y - oy, z))) != BLOCK_AIR) // 生成普通怪物需要两格空间
			{
				m_World->setBlockAll(TopCoord(WCoord(x, y - oy, z)), BLOCK_AIR, 0);
			}
			return oy;
		}
		return -1;
	};

	int x = ox + randgen.nextInt(rangex);
	int z = oz + randgen.nextInt(rangez);
	int savex = x;
	int savez = z;
	bool specialBiome = false;
	int specialBiomeHeight = 0;
	if (biome->getAirlandBiome())
	{
		specialBiome = true;
		//这里加个概率计算, 来确认是在平原还是空岛上生成对应怪物
		if (randgen.nextInt(10) > 6)
		{
			specialBiomeHeight = 9500;
		}
	}
	int mobid = biome->getSpawnMobs(mobtype, -1, specialBiomeHeight);
	if (mobid < 0) return true;
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(mobid);
	int mingroupcount = def->PackNum;
	int maxgroupcount = def->PackNum;

	int numpack = mingroupcount + randgen.nextInt(1 + maxgroupcount - mingroupcount);
	int undergroundMinH = 0, undergroundMaxH = 0;
	std::vector<int> validBlockIDs;

	//我们收集下所有的objId, 一次发送
	std::vector<WORLD_ID> mIdArr;
	for (int i = 0; i < numpack; ++i)
	{
		bool spawned = false;

		for (int j = 0; !spawned && j < 4; ++j)
		{
			int y = 0;
			bool findTopPos = false;

			if (mobid == 3107)
			{
				y = m_World->getMiddleSolidOrLiquidBlock(x, z);
				findTopPos = true;
			}
			else if (mobid == 3103) //重力巫师出生高度由配置项控制
			{
				y = GenRandomInt(def->SpawnMinHeight, def->SpawnMaxHeight);
			}
			else if (specialBiome)
			{
				// 特殊地形，空岛和平原的y高度取值限制不同
				// 对应的位置上 不一定有可站立方块
				if (specialBiomeHeight > 0)
				{
					y = m_World->getLimitHeight(x, z, 90, 0);
				}
				else
				{
					y = m_World->getLimitHeight(x, z, 0, 80);
				}

				if (y <= 0)
				{
					continue;
				}
			}
			else
			{
				y = m_World->getTopSolidOrLiquidBlock(x, z);
			}

			if (def->Type == MOB_WATER) //如果是水生生物获取水下的一个随机位置
			{
				int topY = m_World->getWaterSurfaceBlock(x, z);
				if (topY != -1 && topY > y)
				{
					y += randgen.nextInt(topY - y);
				}
			}
			else if (funcIsSpawnUnderground(mobid, def, y, undergroundMinH, undergroundMaxH, validBlockIDs)) // 地下生物
			{
				int oy = funcFindSpawnUndergroundPos(x, y, z, undergroundMinH, undergroundMaxH, validBlockIDs);
				if (oy < 0)
					continue;

				y -= oy;
			}

			WCoord blockpos(x, y, z);
			if (mobid == 3413 && CanSpawnMob3413(m_World, blockpos) || mobid != 3413)
			{
				ClientMob* mob = NULL;
				if (mobid == 3107)//蝙蝠集中生成
				{
					int count = 3;
					int loopS = 10;
					while (loopS--)
					{
						WCoord tmpPos = blockpos;
						tmpPos = CoordDivBlock(blockpos + WCoord(GenRandomInt(-1, 1), GenRandomInt(-1, 1), GenRandomInt(-1, 1)));
						mob = spawnMob(BlockBottomCenter(blockpos), mobid, true, false);
						if (mob)
						{
							if (findTopPos)
								mob->setReverse(true);
							count--;
						}
						if (count == 0) break;
					}
				}
				else
				{
					
					if (mobid > MonsterIdStart && mobid <= MonsterIdEnd && ModPackMgr::GetInstancePtr())
					{
						auto pos = BlockBottomCenter(blockpos);
						mob = spawnMobByPrefabCheck(pos.x, pos.y, pos.z, ModPackMgr::GetInstancePtr()->GetResIdByCfgId(CustomModType::Mod_Monster, mobid), -1, true, false, mobid);
					}
					else
					{
						mob = spawnMob(BlockBottomCenter(blockpos), mobid, true, false);
					}
				}

				if (mob)
				{
					if (findTopPos)
						mob->setReverse(true);
					spawned = true;
					mIdArr.push_back(mob->getObjId());
					// 萌眼星新增尖叫鸡物种 有概率生成不同颜色 code-by:lizb
					if (mobid == 3424 && m_World->getCurMapID() == MAPID_MENGYANSTAR)
					{
						if (GenRandomInt(1, 100) <= 30) //先设置为30%的概率
						{
							int mobColor = 0;
							MINIW::ScriptVM::game()->callFunction("GetRandomColor", ">i", &mobColor);
							mob->setColor(mobColor);
						}
					}
				}
			}

			x += randgen.nextInt(5) - randgen.nextInt(5);
			z += randgen.nextInt(5) - randgen.nextInt(5);

			while (x < ox || x >= ox + rangex || z < oz || z >= oz + rangez)
			{
				x = savex + randgen.nextInt(5) - randgen.nextInt(5);
				z = savez + randgen.nextInt(5) - randgen.nextInt(5);
			}
		}
	}
	//发送消息
	{
		jsonxx::Array arrJson;
		std::for_each(mIdArr.begin(), mIdArr.end(), [&arrJson](WORLD_ID id) {
			arrJson << id;
			});
		if (arrJson.size() > 0)
		{
			ObserverEvent obevent;
			obevent.SetData_CustomStr(arrJson.json());
			GetObserverEventManager().OnTriggerEvent("Biome.ActorSpawn", &obevent);
		}
	}
	return false;
}

void ClientActorMgr::setMobGen(bool hostile, bool animal)
{
	memset(m_MobGen, 0, sizeof(m_MobGen));
	m_NpcGen = false;

	//商人npc
	if (m_World->getOWID() == NEWBIEWORLDID || (m_World->isRemoteMode() && ROOM_SERVER_RENT != GetGameInfoProxy()->GetRoomHostType())) return;

	if (hostile)
	{
		m_MobGen[MOB_HOSTILE] = true;
	}

	if (animal)
	{
		m_MobGen[MOB_PASSIVE] = true;
		m_MobGen[MOB_RARE] = true;
		m_MobGen[MOB_WATER] = false;
		m_MobGen[MOB_FLY] = false;
		m_MobGen[MOB_TRIXENIE] = true;
		m_MobGen[MOB_VILLAGER] = true;
		m_MobGen[MOB_NPC] = true;
		m_NpcGen = true;
	}
}

bool ClientActorMgr::getMobGen(MOB_TYPE mobtype)
{
	return m_MobGen[mobtype];
}

WCoord ClientActorMgr::getNpcPositionById(int id)
{
	auto iter = m_NpcActorsWID.find(id);
	if (iter != m_NpcActorsWID.end())
	{
		auto actor = findActorByWID(iter->second);
		if (actor)
			return actor->getLocoMotion()->getPosition();
	}

	return WCoord(0, 0, 0);
}

bool ClientActorMgr::isMobGen(MOB_TYPE mobtype)
{
	int rulegen = -1;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr) 
		rulegen = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_MOBGEN);

	if (rulegen < 0) return m_MobGen[mobtype];
	else return rulegen > 0;
}

void ClientActorMgr::performWorldGenSpawning(Ecosystem* biome, int ox, int oz, int rangex, int rangez, ChunkRandGen& randgen)
{
	OPTICK_EVENT();

	// if (m_quotaSystemEnabled)
	// {
	// 	performWorldGenSpawningByQuota(biome, ox, oz, rangex, rangez, randgen);
	// 	return;
	// }

	// bool genpassive = isMobGen(MOB_PASSIVE);
	// bool genrare = isMobGen(MOB_RARE);
	// bool trixenie = isMobGen(MOB_TRIXENIE);

	// while (genpassive && randgen.getFloat() < 0.1f)
	// {
	// 	if (performWorldGenSpawnOne(biome, ox, oz, rangex, rangez, randgen, MOB_PASSIVE)) break;
	// }

	// while (genpassive && randgen.getFloat() < 0.12f)
	// {
	// 	if (performWorldGenSpawnOne(biome, ox, oz, rangex, rangez, randgen, MOB_FLY)) break;
	// }

	// while (genpassive && randgen.getFloat() < 0.12f)
	// {
	// 	if (performWorldGenSpawnOne(biome, ox, oz, rangex, rangez, randgen, MOB_WATER)) break;
	// }

	// while (trixenie && randgen.getFloat() < 0.01f)
	// {
	// 	if (performWorldGenSpawnOne(biome, ox, oz, rangex, rangez, randgen, MOB_TRIXENIE)) break;
	// }

	// if (genrare && randgen.getFloat() < 0.05f)
	// {
	// 	performWorldGenSpawnOne(biome, ox, oz, rangex, rangez, randgen, MOB_RARE);
	// }
}

void ClientActorMgr::checkMobGen()
{
	OPTICK_EVENT();

	// 客户端模式下禁用本地刷新，避免创建无效NPC
	if (m_World && m_World->isRemoteMode())
	{
		return;
	}
	
	// 创建地图模式下不刷新
	bool bCreateMap = m_World->isSOCCreateMap();
	if (bCreateMap)
	{
		return;
	}

	bool updateChunks = false;
	int TICKS_PER_SECOND = 20;
	static int GenTicks[MAX_MOBTYPE] = { 20 * TICKS_PER_SECOND, 10 * TICKS_PER_SECOND, 20 * TICKS_PER_SECOND, 1200, 1200, 1200, 1200, 1200, 1200, 1200, 1200, 1200 };
	{
		OPTICK_EVENT("SpawnMobs");
		for (int i = 0; i < MAX_MOBTYPE; i++)
		{
			if (!isMobGen((MOB_TYPE)i)) continue;

			m_GenAccumTicks[i]++;
			if (m_GenAccumTicks[i] >= GenTicks[i])
			{
				m_GenAccumTicks[i] = 0;
				if (!updateChunks)
				{
					updateChunks = true;
					getMobSpawnChunks(m_GenMobChunks);
				}
				trySpawnMobs(m_World, (MOB_TYPE)i);
			}
		}
	}

	if (m_NpcGen)
	{
		OPTICK_EVENT("SpawnNpc");
		//节日NPC管理 1=元旦 2=圣诞节 3=国庆 4=过年 5=周年庆 6=五一节日 7=六一

		for (int aNpc : m_NpcList)
		{
			int npcid = 0;
			int torchid = BLOCK_TORCH;
			if (aNpc != 8)
			{
				if (aNpc == 1)
				{
					npcid = 3014;
				}
				else if (aNpc == 2)
				{
					npcid = 3013;
					torchid = 244;
				}
				else if (aNpc == 4)
				{
					npcid = 3015;
				}
				else if (aNpc == 5)
				{
					npcid = 3016;
					torchid = 980;
				}
				else if (aNpc == 7)
				{
					npcid = 3017;
				}
				//else if (aNpc == 9)// 心愿商人移除了
				//{
				//	npcid = 3020;
				//}
				if (npcid > 0)
				{
					if (!updateChunks)
					{
						updateChunks = true;
						getMobSpawnChunks(m_GenMobChunks);
					}
					//其他类型商人
					trySpawnTrader(npcid, torchid, true);
				}
			}
			else
			{
				if (!updateChunks)
				{
					updateChunks = true;
					getMobSpawnChunks(m_GenMobChunks);
				}
				//如果是爱心大使的开关
				trySpawnLoveAmbassador();
			}
		}
	}
}

static void TryAddTorch(World* pworld, const WCoord& center, int torchid)
{
	int count = 0;
	WCoord pos;
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(torchid);

	for (int i = 0; i < 20; i++)
	{
		pos.x = center.x + GenRandomInt(-4, 4);
		pos.z = center.z + GenRandomInt(-4, 4);
		pos.y = center.y + GenRandomInt(0, 1);

		if (pos.x == center.x && pos.z == center.z) continue;

		int y = 0;
		for (; y < def->Height; y++)
		{
			BlockMaterial* pmtl = pworld->getBlockMaterial(pos + WCoord(0, y, 0));
			if (!pmtl->isReplaceable()) break;
		}
		if (y < def->Height) continue;

		if (!pworld->doesBlockHaveSolidTopSurface(DownCoord(pos))) continue;

		if (def->Height == 1)
		{
			pworld->setBlockAll(pos, torchid, DIR_NEG_Y, 2);
			count++;
		}
		else
		{
			pworld->setBlockAll(pos, torchid, DIR_NEG_Y, 2);
			pworld->setBlockAll(TopCoord(pos), torchid, DIR_NEG_Y | 8, 2);
			count += 2;
		}

		if (count >= 2) break;
	}
}

static void TryAddAttachment(World* pworld, const WCoord& center, int attachmentId)
{
	int count = 0;
	WCoord pos;
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(attachmentId);

	for (int i = -1; i < 2; i++)
	{
		for (int j = -1; j < 2; j++)
		{
			pos.x = center.x + i;
			pos.z = center.z + j;
			pos.y = center.y;

			if (pos.x == center.x && pos.z == center.z) continue;

			int y = 0;
			for (; y < def->Height; y++)
			{
				BlockMaterial* pmtl = pworld->getBlockMaterial(pos + WCoord(0, y, 0));
				if (!pmtl->isReplaceable()) break;
			}
			if (y < def->Height) continue;

			if (!pworld->doesBlockHaveSolidTopSurface(DownCoord(pos))) continue;

			if (def->Height == 1)
			{
				pworld->setBlockAll(pos, attachmentId, DIR_NEG_Y, 2);
				count++;
			}
			else
			{
				pworld->setBlockAll(pos, attachmentId, DIR_NEG_Y, 2);
				pworld->setBlockAll(TopCoord(pos), attachmentId, DIR_NEG_Y | 8, 2);
				count += 2;
			}

			//if(count >= 2) break;
		}
	}
}

static bool IsSpecialNight(World* pworld, bool isholiday = false)
{
	if (pworld->getCurMapID() != 0) return false;

	if (!GetWorldManagerPtr()) return false;

	int day4tick = TICKS_ONEDAY;
	if (!isholiday) day4tick *= 4;

	int tick = GetWorldManagerPtr()->getDayNightTime() % day4tick;
	if (tick < day4tick - TICKS_ONEDAY / 2) return false;  //第4天晚上出现
	else return true;
}

void ClientActorMgr::updateLoveAmbassadorIcon(int mobId, int itemId, int itemCount, int animId, bool isSync)
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(iter->second);
		if (mob && mob->getDef() && mob->getDef()->ID == mobId)
		{
			if (!isSync)
			{
				mob->updateActorDisplayName(mob->getDef()->ID, itemId, itemCount, animId);
			}
			else
			{
				mob->syscActorDisplayNameToClient(false);
			}
			break;
		}

	}
}

void ClientActorMgr::trySpawnLoveAmbassador()
{
	//配置部分
	int loveAmbassadorId = 3021;      //ID
	int spawnTime = 0;	           //出生时间
	int liveTime = 0;			   //存活时间
	int attachmentId = 0;	       //伴随出现的附属品ID,从指定ID列表中随机获取
	int testId = 0;
	char tmpBuf1[64];
	sprintf(tmpBuf1, "F%d_GetInfo", loveAmbassadorId);
	MINIW::ScriptVM::game()->callFunction(tmpBuf1, "i>iiii", testId, &loveAmbassadorId, &spawnTime, &liveTime, &attachmentId);
	spawnTime = (spawnTime - 6) * 1000;  //转换为世界时间的9点

	if (GetWorldManagerPtr() == NULL) return;
	//获取当前时刻
	//int worldTime = GetWorldManagerPtr()->getWorldTime();
	//int day = worldTime / TICKS_ONEDAY;
	//int dayTime = worldTime - day * TICKS_ONEDAY;
	int dayTime = GetWorldManagerPtr()->getTimeInDay();

	//如果爱心大使在消失时间段内还未清理数据，清理一次
	if (dayTime >= spawnTime + liveTime * 1000 || dayTime <= spawnTime)
	{
		if (GetWorldManagerPtr()->getLoveAmbassadorSpawnTime() > 0)
		{
			GetWorldManagerPtr()->setLoveAmbassadorSpawnTime(0);
		}
	}

	//爱心大使不允许同时存在两个
	if (GetWorldManagerPtr()->getLoveAmbassadorSpawnTime() > 0)
	{
		return;
	}

	//只出现在冒险模式
	if (m_World->getCurMapID() != 0 || GetWorldManagerPtr()->isGodMode())
	{
		return;
	}

	//玩家和地图必须存在
	if (g_pPlayerCtrl == NULL || g_pPlayerCtrl->getCurMapID() != 0)
	{
		return;
	}

	//18点到8点之间不生成
	if (dayTime >= spawnTime + liveTime * 1000 || dayTime <= spawnTime)
	{
		return;
	}

	//当前时刻到了爱心大使出现的时刻
	if (dayTime >= spawnTime)
	{
		//玩家当前位置
		WCoord center = CoordDivBlock(g_pPlayerCtrl->getPosition());
		//爱心大使的定义信息
		const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(loveAmbassadorId);
		//尝试召唤

		GenMobTable::Element* iter = m_GenMobChunks.iterate(NULL);
		for (; iter != NULL; iter = m_GenMobChunks.iterate(iter))
		{
			if (!iter->value) continue;

			Chunk* pchunk = m_World->getChunk(iter->key);

			assert(pchunk);

			//在玩家周围召唤
			WCoord spawncenter;
			spawncenter.x = GenRandomInt(CHUNK_BLOCK_X);
			spawncenter.z = GenRandomInt(CHUNK_BLOCK_Z);
			int maxy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM - 1;
			spawncenter.y = GenRandomInt(maxy);
			spawncenter += pchunk->m_Origin;

			const int MIN_DIST = 3 * 3;
			const int MAX_DIST = 9 * 9;
			int d = spawncenter.squareDistanceTo(center);
			if (d >= MIN_DIST && d <= MAX_DIST)
			{
				//距离合适
				if (m_World->getBlockID(spawncenter) == 0 && m_World->doesBlockHaveSolidTopSurface(DownCoord(spawncenter)) && m_World->getBlockID(TopCoord(spawncenter)) == 0)
				{
					//没有方块占坑
					CollideAABB box;
					WCoord pos = BlockBottomCenter(spawncenter);
					CalCollideBox(box, def, pos);
					if (m_World->checkNoActorCollision(box, NULL))
					{
						//设置爱心大使的出生时间 
						GetWorldManagerPtr()->setLoveAmbassadorSpawnTime(GetWorldManagerPtr()->getWorldTime());
						//没有生物占坑,可以召唤了
						ClientMob* mob = spawnMob(pos, loveAmbassadorId, false, false);
						//添加附属品
						TryAddAttachment(m_World, spawncenter, attachmentId);

						return;
					}
				}
			}
		}
	}
}

void ClientActorMgr::trySpawnTrader(int id, int torchid, bool isholiday)
{
	if (GetWorldManagerPtr() == NULL) return;
	if (m_World->getCurMapID() != 0 || GetWorldManagerPtr()->isGodMode()) return;
	if (g_pPlayerCtrl == NULL || g_pPlayerCtrl->getCurMapID() != 0) return;
	if (GetWorldManagerPtr() == NULL) return;
	int wtime = GetWorldManagerPtr()->getWorldTime();
	if (isholiday)
	{
		if (GetWorldManagerPtr()->getNpcSpawnTime() == 0)
		{
			if (wtime < TICKS_ONEDAY / 2 + 20 * 10) return;
		}
		else if (wtime < GetWorldManagerPtr()->getNpcSpawnTime() + TICKS_ONEDAY + 20 * 10) return; //间隔一天
	}
	else if (GetWorldManagerPtr()->getWorldTime() < GetWorldManagerPtr()->getNpcSpawnTime() + TOURTRADER_LIVETIME + 20 * 60) return;

	if (!IsSpecialNight(m_World, isholiday)) return;

	WCoord center = CoordDivBlock(g_pPlayerCtrl->getPosition());
	const int MIN_DIST = 3 * 3;
	const int MAX_DIST = 9 * 9;

	GenMobTable::Element* iter = m_GenMobChunks.iterate(NULL);
	for (; iter != NULL; iter = m_GenMobChunks.iterate(iter))
	{
		if (!iter->value) continue;
		Chunk* pchunk = m_World->getChunk(iter->key);
		assert(pchunk);
		if (pchunk == NULL) continue;
		WCoord spawncenter;
		spawncenter.x = GenRandomInt(CHUNK_BLOCK_X);
		spawncenter.z = GenRandomInt(CHUNK_BLOCK_Z);
		int maxy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM - 1;
		spawncenter.y = GenRandomInt(maxy);

		spawncenter += pchunk->m_Origin;
		int d = spawncenter.squareDistanceTo(center);
		if (d >= MIN_DIST && d <= MAX_DIST)
		{
			if (m_World->getBlockID(spawncenter) == 0 && m_World->doesBlockHaveSolidTopSurface(DownCoord(spawncenter)) && m_World->getBlockID(TopCoord(spawncenter)) == 0)
			{
				CollideAABB box;
				WCoord pos = BlockBottomCenter(spawncenter);
				const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(id);
				if (!def)
				{
					SANDBOX_ASSERT(false);
					continue;
				}
				CalCollideBox(box, def, pos);
				if (m_World->checkNoActorCollision(box, NULL))
				{
					GetWorldManagerPtr()->setNpcSpawnTime();

					ClientMob* mob = spawnMob(pos, id, false, false);
					if (mob && id >= 3010 && id <= 3020 && GetWorldManagerPtr()->m_TraderCertainID > 0)
					{
						ActorTrader* trader = dynamic_cast<ActorTrader*>(mob);
						if (trader)
							trader->SetOneGrid(0, GetWorldManagerPtr()->m_TraderCertainID);
						GetWorldManagerPtr()->m_TraderCertainID = 0;
					}

					//游商在存档内要唯一出现
					if (mob && (id == 3010 || id == 3020))
					{
						std::vector<ClientActor*> vActors;
						std::map<WORLD_ID, ClientActor*>::iterator iter;
						for (iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
						{
							if (iter->second && iter->first != mob->getObjId() && (iter->second->getDefID() == 3010 || iter->second->getDefID() == 3020))
							{
								vActors.push_back(iter->second);
							}
						}
						if (vActors.size() > 0)
						{
							for (unsigned int i = 0; i < vActors.size(); i++) {
								despawnActor(vActors[i]);
							}
							vActors.clear();
						}
					}

					TryAddTorch(m_World, spawncenter, torchid);

					int n = GetWorldManagerPtr()->getDayNightTime() / (TICKS_ONEDAY * 4);
					broadcastGameInfo(PLAYER_NOTIFYINFO_TIPS, (n % 2) == 1 ? 116 : 117);
					return;
				}
			}
		}
	}
}


void ClientActorMgr::trySpawnMobs(World* pworld, MOB_TYPE mobtype)
{
	MonsterSpawnDef* def = GetDefManagerProxy()->getMonsterSpawnDef(mobtype);
	if (!def)
	{
		CLIENTACTORMGR_LOG("trySpawnMobs: def is null, mobtype=%d", mobtype);
		return;
	}

	const float maxMobChunkSize = (7 * 7) * 100;
	int genMobChunkSize = m_GenMobChunks.size();
	float ratio = (float)genMobChunkSize / maxMobChunkSize;
	int maxmob = def->max_num * ratio;
	if (maxmob >= def->max_num && def->max_num > 0) {
		maxmob = def->max_num;
		// CLIENTACTORMGR_LOG("trySpawnMobs: maxmob >= def->max_num, mobtype=%d, maxmob=%d, def->max_num=%d", mobtype, maxmob, def->max_num);
	}

	// CLIENTACTORMGR_LOG("trySpawnMobs: genMobChunkSize=%d, ratio=%f, mobtype=%d, maxmob=%d, def->max_num=%d, liveMobNum=%d", genMobChunkSize, ratio, mobtype, maxmob, def->max_num, m_LiveMobNum[mobtype]);

	int canCreateCount = maxmob - m_LiveMobNum[mobtype];
	if (canCreateCount <= 0)
	{
		// CLIENTACTORMGR_LOG("trySpawnMobs: canCreateCount <= 0, mobtype=%d, maxmob=%d, liveMobNum=%d", mobtype, maxmob, m_LiveMobNum[mobtype]);
		return;
	}

	GenMobTable::Element* iter = m_GenMobChunks.iterate(NULL);
	for (; iter != NULL; iter = m_GenMobChunks.iterate(iter))
	{
		if (!iter->value) continue;

		Chunk* pchunk = pworld->getChunk(iter->key);

		assert(pchunk);
		if (pchunk == NULL) continue;

		WCoord spawncenter;
		spawncenter.x = GenRandomInt(CHUNK_BLOCK_X);
		spawncenter.z = GenRandomInt(CHUNK_BLOCK_Z);
		int maxy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM - 1;
		spawncenter.y = GenRandomInt(maxy);

		spawncenter += pchunk->m_Origin;

		int blockid = pworld->getBlockID(spawncenter);
		if (mobtype == MOB_WATER)
		{
			if (!BlockMaterialMgr::isWater(blockid))
			{
				continue;
			}
		}
		else
		{
			if (blockid != 0 && blockid != BLOCK_SNOWPANE) continue;
		}

		m_CurSpawnMobID = -1;
		m_SpawnedMobNumOnce = 0;
		for (int itry = 0; itry < 3; itry++)
		{
			if (!spawnMobPackInChunk(pworld, mobtype, spawncenter)) break;
		}
		canCreateCount -= m_SpawnedMobNumOnce;
		if (canCreateCount <= 0)
			break;
	}
}

bool ClientActorMgr::spawnMobPackInChunk(World* pworld, MOB_TYPE mobtype, const WCoord& spawncenter)
{
	OPTICK_EVENT();
	
	// 【关键修改】只在服务器端执行mob刷新逻辑
	// isRemoteMode() == true 表示客户端，== false 表示服务器端
	if (pworld->isRemoteMode()) {
		CLIENTACTORMGR_LOG("Client side detected (isRemoteMode=true), skipping mob spawn");
		return false; // 客户端不执行刷新
	}
	
	// CLIENTACTORMGR_LOG("Server side detected (isRemoteMode=false), proceeding with mob spawn");
	
	WCoord spawnpos = spawncenter;
	Chunk* pchunk = pworld->getChunk(spawncenter);

	// 计算chunk坐标 (chunk大小是16x16)
	// 使用向下取整确保负坐标也能正确计算
	int chunkX = (int)floor(spawncenter.x / 16.0);
	int chunkZ = (int)floor(spawncenter.z / 16.0);
	
	// 首先检查是否还有可用的预设点
	int availableCount = getAvailablePresetPositionCount();
	int totalCount = getTotalPresetPositionCount();
	
	if (availableCount == 0) {
		// 没有可用的预设点，停止刷新
		CLIENTACTORMGR_LOG("No available preset positions left (0/%d), stopping spawn in chunk (%d, %d)", 
		                  totalCount, chunkX, chunkZ);
		return false; // 停止刷新
	}
	
	// 检查该chunk内是否有预设刷新点
	WCoord presetPoint = getPresetPointInChunk(chunkX, chunkZ);
	bool usingPresetPoint = false;
	WCoord originalPresetPoint; // 保存原始预设点坐标用于标记
	
	if (presetPoint.y != -999) {
		// 找到了chunk内的预设刷新点，使用它
		originalPresetPoint = presetPoint; // 保存原始预设点坐标用于标记
		spawnpos = presetPoint;
		usingPresetPoint = true;
		
		// 调整Y坐标到地形表面
		int surfaceY = pworld->getTopSolidOrLiquidBlock(spawnpos.x, spawnpos.z);
		if (surfaceY > 0) {
			spawnpos.y = surfaceY;
		} else {
			// 当前预设点没有找到地形表面，在该点周围3格范围内找到一个可用的预设点
			const int checkRange = 4;
			for (int dx = -checkRange; dx <= checkRange; dx++) {
				for (int dz = -checkRange; dz <= checkRange; dz++) {
					WCoord newPresetPoint = presetPoint + WCoord(dx, 0, dz);
					int surfaceY = pworld->getTopSolidOrLiquidBlock(newPresetPoint.x, newPresetPoint.z);
					if (surfaceY > 0) {
						originalPresetPoint = newPresetPoint;
						spawnpos = newPresetPoint;
						usingPresetPoint = true;
						break;
					}
				}

				if (usingPresetPoint) {
					break;
				}
			}
		}
		
		CLIENTACTORMGR_LOG("Found preset point in chunk (%d, %d): original(%d, %d, %d) -> adjusted(%d, %d, %d), available positions: %d/%d", 
		                  chunkX, chunkZ, originalPresetPoint.x, originalPresetPoint.y, originalPresetPoint.z, 
		                  spawnpos.x, spawnpos.y, spawnpos.z, availableCount, totalCount);
	} else {
		// // 该chunk内没有预设刷新点，尝试查找附近的可用预设点
		// WCoord nearbyPresetPoint = getPresetSpawnPosition(spawncenter, 96); // 在96格范围内查找
		// if (nearbyPresetPoint.y != -999) {
		// 	// 找到了附近的可用预设点
		// 	originalPresetPoint = nearbyPresetPoint; // 保存原始预设点坐标用于标记
		// 	spawnpos = nearbyPresetPoint;
		// 	usingPresetPoint = true;
			
		// 	// 调整Y坐标到地形表面
		// 	int surfaceY = pworld->getTopSolidOrLiquidBlock(spawnpos.x, spawnpos.z);
		// 	if (surfaceY > 0) {
		// 		spawnpos.y = surfaceY;
		// 	}
			
		// 	CLIENTACTORMGR_LOG("No preset point in chunk (%d, %d), using nearby preset point: original(%d, %d, %d) -> adjusted(%d, %d, %d), available positions: %d/%d", 
		// 	                  chunkX, chunkZ, originalPresetPoint.x, originalPresetPoint.y, originalPresetPoint.z, 
		// 	                  spawnpos.x, spawnpos.y, spawnpos.z, availableCount, totalCount);
		// } else {
		// 	// 附近没有预设点，尝试获取任何可用的预设点
		// 	WCoord randomPresetPoint = m_mobPresetPositionMgr.GetRandomAvailablePosition();
		// 	if (randomPresetPoint.y != -999) {
		// 		// 找到了可用的预设点（可能在很远的地方）
		// 		originalPresetPoint = randomPresetPoint; // 保存原始预设点坐标用于标记
		// 		spawnpos = randomPresetPoint;
		// 		usingPresetPoint = true;
				
		// 		// 调整Y坐标到地形表面
		// 		int surfaceY = pworld->getTopSolidOrLiquidBlock(spawnpos.x, spawnpos.z);
		// 		if (surfaceY > 0) {
		// 			spawnpos.y = surfaceY;
		// 		}
				
		// 		CLIENTACTORMGR_LOG("No preset point in chunk (%d, %d) or nearby, using random preset point: original(%d, %d, %d) -> adjusted(%d, %d, %d), available positions: %d/%d", 
		// 		                  chunkX, chunkZ, originalPresetPoint.x, originalPresetPoint.y, originalPresetPoint.z, 
		// 		                  spawnpos.x, spawnpos.y, spawnpos.z, availableCount, totalCount);
		// 	} else {
		// 		// 没有任何可用的预设点，停止刷新
		// 		CLIENTACTORMGR_LOG("No preset point found in chunk (%d, %d) or anywhere, stopping spawn, available positions: %d/%d", 
		// 		                  chunkX, chunkZ, availableCount, totalCount);
		// 		return false; // 停止刷新
		// 	}
		// }
	}

	// 使用预设点进行刷新，不再使用随机位置逻辑
	int count = 0;
	int delay_tick = (int)m_SpawnMobDelayParams.size();
	
	CLIENTACTORMGR_LOG("Starting spawn attempt at preset position (%d, %d, %d) for mobtype %d", 
	                  spawnpos.x, spawnpos.y, spawnpos.z, mobtype);
	
	while (count < 1)
	{
		// 直接使用预设点位置，不再随机调整
		if (!CanMobTypeSpawnOnPos(pworld, mobtype, spawnpos))
		{
			CLIENTACTORMGR_LOG("CanMobTypeSpawnOnPos failed for position (%d, %d, %d), mobtype %d, attempt %d", 
			                  spawnpos.x, spawnpos.y, spawnpos.z, mobtype, count + 1);
			count++;
			continue;
		}
		
		CLIENTACTORMGR_LOG("CanMobTypeSpawnOnPos passed for position (%d, %d, %d)", 
		                  spawnpos.x, spawnpos.y, spawnpos.z);

		WCoord pos = spawnpos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2);

		int dist = minDistToPlayer(pos, NULL, false);
		int spawnDist = MOB_SPAWN_DIST;
		if (m_World->getTerrainType() == TERRAIN_EMPTY_FLAT)
		{
			spawnDist = 3;
		}
		
		CLIENTACTORMGR_LOG("Distance check: dist=%d, spawnDist=%d, maxDist=%d, worldPos=(%d, %d, %d)", 
		                  dist, spawnDist * BLOCK_SIZE, MOB_DESPAWN_MAXDIST * BLOCK_SIZE, pos.x, pos.y, pos.z);

		if (dist > spawnDist * BLOCK_SIZE && dist < MOB_DESPAWN_MAXDIST * BLOCK_SIZE)
		{
			CLIENTACTORMGR_LOG("Distance check passed");
			WCoord specialpoint(0, -1, 0);
			if (pworld->getCurMapID() == 0) specialpoint = GetWorldManagerPtr()->getSpawnPointEx(pworld);
			else specialpoint = pworld->getPortalPoint();

			bool do_create = true;
			if (specialpoint.y >= 0 && spawnpos.squareDistanceTo(specialpoint) < spawnDist * spawnDist)
				do_create = false;

			if (do_create)
			{
				if (m_CurSpawnMobID < 0)
				{
					Ecosystem* biome = pworld->getBiomeGen(spawnpos.x, spawnpos.z);
					if (biome == NULL) {
						CLIENTACTORMGR_LOG("Failed to get biome for position (%d, %d, %d)", spawnpos.x, spawnpos.y, spawnpos.z);
						continue;
					}
					
					CLIENTACTORMGR_LOG("Got biome for position (%d, %d, %d), trying to get spawn mobs for mobtype %d", 
					                  spawnpos.x, spawnpos.y, spawnpos.z, mobtype);
					
					if (IsSpecialNight(m_World))
						m_CurSpawnMobID = biome->getSpawnMobs(mobtype, 3501, pos.y);
					else
						m_CurSpawnMobID = biome->getSpawnMobs(mobtype, -1, pos.y);

					if (m_CurSpawnMobID < 0)
					{
						CLIENTACTORMGR_LOG("Failed to get spawn mob ID from biome, mobtype %d returned %d", mobtype, m_CurSpawnMobID);
						count++;
						continue;
					}
					
					CLIENTACTORMGR_LOG("Got spawn mob ID: %d for mobtype %d", m_CurSpawnMobID, mobtype);
				}

				if (!canSpawnMobByProbability(mobtype))
				{
					CLIENTACTORMGR_LOG("canSpawnMobByProbability failed for mobtype %d", mobtype);
					continue;;
				}
				
				CLIENTACTORMGR_LOG("canSpawnMobByProbability passed for mobtype %d", mobtype);

				const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(m_CurSpawnMobID);
				if (def && def->NumLimit > 0)
				{
					int totalNum = getDelaySpawnMobCount(m_CurSpawnMobID);
					if (totalNum >= def->NumLimit) return false;

					if (m_LiveMobNumByID.find(m_CurSpawnMobID) != m_LiveMobNumByID.end())
					{
						totalNum += m_LiveMobNumByID[def->ID];
						if (totalNum >= def->NumLimit) return false;
					}
				}
				if (m_CurSpawnMobID == 3107)
				{
					//spawnMobDelay(pos, (delay_tick++) / 2, m_CurSpawnMobID, false, true, NULL, -1.0f,
					//	[this](const WCoord& pos, int monsterid, bool mobtype_check, bool mob_check, float yaw, SpawnMobDelayCallback pfunc) {
					//	(void)mobtype_check;
					//(void)mob_check;
					//(void)yaw;
					//(void)pfunc;
					//this->spawnMobTogether(m_World, pos, monsterid, 3);
					//});
				}
				else {
					CLIENTACTORMGR_LOG("Spawning mob %d at position (%d, %d, %d) with delay %d", m_CurSpawnMobID, pos.x, pos.y, pos.z, (delay_tick++) / 2);
					
					spawnMobDelay(pos, (delay_tick++) / 2, m_CurSpawnMobID, false, true, def->Type);
					m_SpawnedMobNumOnce++;
					
					// 只有在使用预设点时才标记为已使用
					if (usingPresetPoint) {
						// 使用预设点的原始坐标来标记（包含原始Y=-1），而不是调整后的坐标
						int gameday = GetWorldManagerPtr()->getWorldTimeDay();
						markPresetPositionUsed(originalPresetPoint, gameday);
						
						// 输出更新后的可用位置数量
						int availableCount = getAvailablePresetPositionCount();
						int totalCount = getTotalPresetPositionCount();
						CLIENTACTORMGR_LOG("Marked preset position as used at (%d, %d, %d), remaining available positions: %d/%d", 
						                  originalPresetPoint.x, originalPresetPoint.y, originalPresetPoint.z, availableCount, totalCount);
					}
				}

				int maxpack = GetDefManagerProxy()->getMonsterDef(m_CurSpawnMobID)->PackNum;
				if (m_SpawnedMobNumOnce >= maxpack) return false;
			}
		}
		else {
			CLIENTACTORMGR_LOG("Distance check failed: dist=%d, required range: %d < dist < %d", 
			                  dist, spawnDist * BLOCK_SIZE, MOB_DESPAWN_MAXDIST * BLOCK_SIZE);
		}

		count++;
	}

	return true;
}

void ClientActorMgr::spawnMobTogether(World* pworld, const WCoord& pos, int spawnMobID, int count)
{
	int mob_count = GetNearbyMobCount(pworld, CoordDivBlock(pos), 16, 2, spawnMobID);
	int count_to_create = count - mob_count;
	if (count_to_create <= 0) {
		return;
	}
	ClientMob* mob_temp = nullptr;
	mob_temp = spawnMob(pos, spawnMobID, false, true);
	if (mob_temp == NULL) {
		return;
	}
	m_SpawnedMobNumOnce++;
	count_to_create--;
	if (count_to_create <= 0) {
		return;
	}

	WCoord mob_temp_pos = mob_temp->getPosition();
	WCoord pos_temp = pos;
	for (int i = 0; i < 3; i++) {
		for (int j = 0; j < 3; j++) {
			for (int k = 0; k < 3; k++) {
				pos_temp = mob_temp_pos + WCoord(-100 + i * 100, 0 + k * 100, -100 + j * 100);
				if (spawnMob(pos_temp, spawnMobID, false, true)) {
					m_SpawnedMobNumOnce++;
					count_to_create--;
					if (count_to_create <= 0)
						return;
				}
			}
		}
	}
}

bool ClientActorMgr::checkMobStandPoint(const MonsterDef* mondef, Chunk* pchunk, const WCoord& pos)
{
	Block pblock = pchunk->getBlock(pos);
	if (mondef->Type == MOB_WATER)
	{
		if (!pblock.isWater()) return false;
	}
	else if (mondef->Type == MOB_FLY)
	{
		if (!pblock.isAir()) return false;
	}
	else
	{
		if (pos.y <= 0) return false;

		Block pdownblock = pchunk->getBlock(pos.x, pos.y - 1, pos.z);
		if (!pdownblock.moveCollide()) return false;

		if (pblock.getResID() != 0) return false;

		int h = (int)ceil(mondef->Height * mondef->ModelScale / 100.0f);
		for (int i = 1; i <= h; i++)
		{
			if (pos.y + i < CHUNK_BLOCK_Y && pchunk->getBlock(pos.x, pos.y + i, pos.z).getResID() != 0) return false;
		}
	}

	return true;
}

int ClientActorMgr::minDistToPlayer(const WCoord& worldpos, ClientPlayer** retplayer, bool live_players)
{
	ClientPlayer* player = NULL;
	int mindist = Rainbow::MAX_INT;
	for (size_t i = 0; i < m_Players.size(); i++)
	{
		if (live_players && m_Players[i]->isDead())
		{
			continue;
		}

		WCoord pos = m_Players[i]->getLocoMotion()->getPosition();

		pos -= worldpos;
		int dist = int(pos.length());
		if (dist < mindist)
		{
			mindist = dist;
			player = m_Players[i];
		}
	}

	if (retplayer) *retplayer = player;
	return mindist;
}

static bool DefaultSelectActorFunc(ClientActor* actor, void* userdata)
{
	return actor && actor->alive();
}

//默认选择方式的selectNearPlayer函数
ClientPlayer* ClientActorMgr::selectNearPlayerDefault(const WCoord& pos, int range)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	ClientPlayer* pTarget = NULL;
	float fDist = 99999999.0f;

	for (size_t i = 0; i < m_Players.size(); i++)
	{
		ClientPlayer* player = m_Players[i];
		WCoord vec = player->getLocoMotion()->getPosition() - pos;

		float dist = vec.length();
		if (dist < range && dist < fDist && DefaultSelectActorFunc(player, NULL))
		{
			fDist = dist;
			pTarget = player;
		}
	}

	return pTarget;
}

ClientPlayer* ClientActorMgr::selectNearPlayer(const WCoord& pos, int range, SelectActorFunc pfunc, void* userdata)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	ClientPlayer* pTarget = NULL;
	float fDist = 99999999.0f;
	if (pfunc == NULL) pfunc = DefaultSelectActorFunc;

	for (size_t i = 0; i < m_Players.size(); i++)
	{
		ClientPlayer* player = m_Players[i];
		if (player->getLocoMotion())
		{
			WCoord vec = player->getLocoMotion()->getPosition() - pos;

			float dist = vec.length();
			if (dist < range && dist < fDist && pfunc(player, userdata))
			{
				fDist = dist;
				pTarget = player;
			}
		}
	}

	return pTarget;
}

ClientPlayer* ClientActorMgr::selectRandomPlayer()
{
	std::vector<ClientPlayer*>players;
	players.reserve(m_Players.size());

	for (size_t i = 0; i < m_Players.size(); i++)
	{
		ClientPlayer* player = m_Players[i];
		if (player->isDead())
		{
			continue;
		}

		players.push_back(player);
	}

	if (players.empty()) return NULL;

	int i = GenRandomInt(players.size());
	return players[i];
}

void ClientActorMgr::selectNearAllPlayers(std::vector<ClientPlayer*>& players, const WCoord& pos, int range, SelectActorFunc pfunc, void* userdata)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	if (pfunc == NULL) pfunc = DefaultSelectActorFunc;
#ifdef NEW_WORLD_QUERY
	selectNearActors(pos, range, [&userdata, &pfunc](IClientActor* actor) {
		return (actor->getObjType() == OBJ_TYPE_ROLE) && pfunc(actor->ToCast<ClientActor>(), userdata);
	},
		[&players](IClientActor* actor) {
		ClientPlayer* player = static_cast<ClientPlayer*>(actor);
	Assert(actor->IsKindOf<ClientPlayer>());
	players.push_back(player);
	});
#else
	for (size_t i = 0; i < m_Players.size(); i++)
	{
		ClientPlayer* player = m_Players[i];
		WCoord vec = player->getLocoMotion()->getPosition() - pos;

		float dist = vec.length();
		if (dist < range && pfunc(player, userdata))
		{
			players.push_back(player);
		}
	}
#endif
}

static bool selectMobFunc(ClientActor* actor, void* userdata)
{
	ClientMob* mob = dynamic_cast<ClientMob*>(actor);
	if (mob && !mob->isDead())
	{
		return true;
	}
	else
	{
		return false;
	}
}

void ClientActorMgr::selectNearAllIClientMobs(std::vector<IClientMob*>& imobs, const WCoord& pos, int range, SelectMobCallback callbacktype, void* data)
{
	std::vector<ClientMob*> mobs;
	switch (callbacktype)
	{
	case NONE_CALLBACK:
		selectNearAllMobs(mobs, pos, range, NULL, data);
		break;
	case NORMAL_MOB:
		selectNearAllMobs(mobs, pos, range, selectMobFunc, data);
		break;
	default:
		break;
	}
	for (int i = 0; i < mobs.size(); i++)
	{
		imobs.push_back(mobs[i]);
	}
}
void ClientActorMgr::selectNearAllMobs(std::vector<ClientMob*>& mobs, const WCoord& pos, int range, SelectActorFunc pfunc, void* userdata)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	if (pfunc == NULL) pfunc = DefaultSelectActorFunc;
#ifdef NEW_WORLD_QUERY
	selectNearActors(pos, range, [&userdata, &pfunc](IClientActor* actor) {
		return (dynamic_cast<ClientMob*>(actor) != nullptr) && pfunc(actor->ToCast<ClientActor>(), userdata);
	},
		[&mobs](IClientActor* actor) {
		ClientMob* mob = static_cast<ClientMob*>(actor);
	Assert(actor->IsKindOf<ClientMob>());
	mobs.push_back(mob);
	});
#else
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(iter->second);
		if (mob)
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;

			float dist = vec.length();
			if (dist < range && pfunc(mob, data))
			{
				mobs.push_back(mob);
			}
		}
	}
#endif
}

void ClientActorMgr::selectNearAllMobs(std::vector<long long>& mobObjIds, const WCoord& pos, int range, SelectActorFunc pfunc, void* userdata)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	if (pfunc == NULL) pfunc = DefaultSelectActorFunc;

#ifdef NEW_WORLD_QUERY
	selectNearActors(pos, range, [&userdata, &pfunc](IClientActor* actor) {
		return (dynamic_cast<ClientMob*>(actor) != nullptr) && pfunc(actor->ToCast<ClientActor>(), userdata);
	},
		[&mobObjIds](IClientActor* actor) {
		ClientMob* mob = static_cast<ClientMob*>(actor);
	Assert(actor->IsKindOf<ClientMob>());
	mobObjIds.push_back(mob->getObjId());
	});
#else
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(iter->second);
		if (mob)
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;

			float dist = vec.length();
			if (dist < range && pfunc(mob, data))
			{
				mobObjIds.push_back(mob->getObjId());
			}
		}
	}
#endif
}

void ClientActorMgr::selectNearAllLivings(std::vector<ActorLiving*>& livings, const WCoord& pos, int range, SelectActorFunc pfunc, void* userdata)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	if (pfunc == NULL) pfunc = DefaultSelectActorFunc;
#ifdef NEW_WORLD_QUERY
	selectNearActors(pos, range, [&userdata, &pfunc](IClientActor* actor) {
		return (dynamic_cast<ActorLiving*>(actor) != nullptr) && pfunc(actor->ToCast<ClientActor>(), userdata);
	},
		[&livings](IClientActor* actor) {
		ActorLiving* living = static_cast<ActorLiving*>(actor);
	Assert(actor->IsKindOf<ActorLiving>());
	livings.push_back(living);
	});
#else
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ActorLiving* mob = dynamic_cast<ActorLiving*>(iter->second);
		if (mob)
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;

			float dist = vec.length();
			if (dist < range && pfunc(mob, userdata))
			{
				livings.push_back(mob);
			}
		}
	}
	for (size_t i = 0; i < m_Players.size(); i++)
	{
		ActorLiving* player = m_Players[i];
		WCoord vec = player->getLocoMotion()->getPosition() - pos;

		float dist = vec.length();
		if (dist < range && pfunc(player, userdata))
		{
			livings.push_back(player);
		}
	}
#endif
}

void ClientActorMgr::selectNearAllItems(std::vector<ClientItem*>& items, const WCoord& pos, int range, SelectActorFunc pfunc, void* data)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	if (pfunc == nullptr) pfunc = DefaultSelectActorFunc;
#ifdef NEW_WORLD_QUERY
	selectNearActors(pos, range, [&data, &pfunc](IClientActor* actor) {
		return (dynamic_cast<ClientItem*>(actor) != nullptr) && pfunc(actor->ToCast<ClientActor>(), data);
	},
		[&items](IClientActor* actor) {
		ClientItem* item = static_cast<ClientItem*>(actor);
	Assert(actor->IsKindOf<ClientItem>());
	items.push_back(item);
	});
#else
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientItem* item = dynamic_cast<ClientItem*>(iter->second);
		if (item)
		{
			WCoord vec = item->getLocoMotion()->getPosition() - pos;
			float dist = vec.length();
			if (dist < range && pfunc(item, data))
			{
				items.push_back(item);
			}
		}
	}
#endif
}

void ClientActorMgr::selectNearAllVehicleActors(std::vector<ActorVehicleAssemble*>& actors, const WCoord& pos, int range, SelectActorFunc pfunc, void* userdata)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	if (pfunc == NULL) pfunc = DefaultSelectActorFunc;
#ifdef NEW_WORLD_QUERY
	//八叉树里包含所有actor，不需要再单独遍历player
	selectNearActors(pos, range, [&userdata, &pfunc](IClientActor* actor) {
		return (actor->getObjType() == OBJ_TYPE_VEHICLE) && (dynamic_cast<ActorVehicleAssemble*>(actor) != nullptr) && pfunc(actor->ToCast<ClientActor>(), userdata);
	},
		[&actors](IClientActor* actor) {
		ActorVehicleAssemble* vehic = static_cast<ActorVehicleAssemble*>(actor);
	Assert(actor->IsKindOf<ActorVehicleAssemble>());
	actors.push_back(vehic);
	});
#else
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ActorVehicleAssemble* mob = dynamic_cast<ActorVehicleAssemble*>(iter->second);
		if (mob)
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;

			float dist = vec.length();
			if (dist < range && pfunc(mob, data))
			{
				actors.push_back(mob);
			}
		}
	}
#endif
}

ClientActor* ClientActorMgr::FindActor(std::function<bool(ClientActor*)> pSelectFunc)
{
	OPTICK_EVENT();
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++) {
		if (pSelectFunc(iter->second)) {
			return iter->second;
		}
	}
	return nullptr;
}

void ClientActorMgr::FindActors(std::vector<ClientActor*>& pVector, std::function<bool(ClientActor*)> pSelectFunc)
{
	OPTICK_EVENT();
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++) {
		if (pSelectFunc(iter->second)) {
			pVector.push_back(iter->second);
		}
	}
}

void ClientActorMgr::ReloadActor(int defid, int iObjType /* = 0 */)
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end();)
	{
		ClientActor* actor = iter->second;
		if (actor)
		{
			if (actor->getDefID() == defid)
			{
				if (iObjType == OBJ_TYPE_MONSTER)
				{
					WCoord pos = actor->getPosition();
					long long nObjId = actor->getObjId();
					actor->leaveWorld(false);
					actor->release();

					ClientMob* mob = ClientMob::createFromDef(defid, 0, false);

					if (mob)
					{
						mob->SetObjId(nObjId);
						mob->getLocoMotion()->gotoPosition(pos, 0, 0);
						mob->enterWorld(m_World);
						m_LiveActors[nObjId] = mob;
						mob->playSaySound();
					}
					else
					{
						iter = m_LiveActors.erase(iter);
						continue;
					}
				}
				else if (iObjType == OBJ_TYPE_DROPITEM)
				{
					ClientItem* pItem = dynamic_cast<ClientItem*>(actor);
					if (pItem)
					{
						pItem->updateRenderObjs();
					}
				}
			}
		}

		iter++;
	}
	if (m_World && m_World->IsUGCEditMode())
	{
		SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("BlockOrActorReload", SandboxContext(nullptr));
	}
}

void ClientActorMgr::spawnActor(ClientActor* actor, int x, int y, int z, float yaw, float pitch)
{
	spawnActor(actor, WCoord(x, y, z), yaw, pitch);
}

ClientMob* ClientActorMgr::spawnMonster(int x, int y, int z, int monsterid, float yaw/* =-1 */, int mobtype/* = 0*/, std::string monsterName/* = ""*/, bool mobType_check /*= true*/)
{
	ClientMob* mob = NULL;
	if (1 <= mobtype)
	{
		mob = spawnMob(WCoord(x, y, z), monsterid, false, false, yaw, mobtype, monsterName);
	}
	else
		mob = spawnMob(WCoord(x, y, z), monsterid, mobType_check, false, yaw, mobtype, monsterName);

	if (mob)
	{
		CLIENTACTORMGR_LOG("spawnMonster: mobid=%d, type=%d, position=%s", monsterid, mobtype, WCoord(x, y, z).__tostring().c_str());
		mob->playSaySound();
		//mob->setPersistance(true);
		mob->setSpawnPoint(WCoord(x, y, z));
		return mob;
	}
	else return NULL;
}

ClientMob* ClientActorMgr::spawnMonsterEx(int x, int y, int z, int monsterid, bool trigger)
{
	ClientMob* mob = spawnMob(WCoord(x, y, z), monsterid, false, false, -1, 0, "", trigger);
	if (mob)
	{
		CLIENTACTORMGR_LOG("spawnMonsterEx: mobid=%d, position=%s", monsterid, WCoord(x, y, z).__tostring().c_str());
		mob->playSaySound();
		//mob->setPersistance(true);
		mob->setSpawnPoint(WCoord(x, y, z));
		return mob;
	}
	else return NULL;
}

void ClientActorMgr::showDialogPlotNpc(int npcid, bool state)
{
	auto iter = m_NpcActorsWID.find(npcid);
	if (iter != m_NpcActorsWID.end())
	{
		ActorNpc* npc = dynamic_cast<ActorNpc*>(findActorByWID(iter->second));
		npc->showDialog(state);
	}
}

void ClientActorMgr::throwItemMotion(int posX, int posY, int posZ, int itemId, int dirX, int dirY, int dirZ, int shooterObjIdHigh, int shooterObjIdLow, int num/* =1 */)
{
	ClientItem* item = spawnItem(WCoord(posX, posY, posZ), itemId, num);
	if (item)
	{
		Rainbow::Vector3f motion = Rainbow::Vector3f((float)dirX, (float)dirY, (float)dirZ) * 30.0f;
		motion.y += 20.0f;

		motion.x += GenGaussian() * 0.75f * 6.0f;
		motion.z += GenGaussian() * 0.75f * 6.0f;
		motion.y += GenGaussian() * 0.75f * 6.0f;

		item->getLocoMotion()->m_Motion = motion;
		long long t = shooterObjIdHigh;
		long long shooterObjId = t << 32 | shooterObjIdLow;
		item->setMasterObjId(shooterObjId);
	}
}

int ClientActorMgr::getNumPlayer()
{
	return (int)m_Players.size();
}

int ClientActorMgr::getActorCount()
{
	return (int)m_LiveActors.size();
}

ClientPlayer* ClientActorMgr::getIthPlayer(int i)
{
	return m_Players[i];
}

void ClientActorMgr::resetRound()
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		actor->resetRound();
	}
}

void ClientActorMgr::resetActorsByCustomModel(int id)
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		if (iter->second->getObjType() == OBJ_TYPE_THROWABLE && iter->second->GetItemId() == id)
		{
			auto actorProjectile = dynamic_cast<ClientActorProjectile*>(iter->second);
			if (actorProjectile)
			{
				actorProjectile->createPhysActor(id);
				actorProjectile->resetModel();
			}
		}
	}
}

void ClientActorMgr::resetActorsByFullyCustomModel(std::string skey)
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		int objtype = iter->second->getObjType();

		if (OBJ_TYPE_DROPITEM == objtype) //掉落物
		{
			ClientItem* clitem = dynamic_cast<ClientItem*>(iter->second);
			if (clitem)
			{
				std::string actorSkey = clitem->getFullyCustomModelKey();
				if (!actorSkey.empty() && actorSkey == skey)
				{
					clitem->updateBodyByFullyCustomModel();
				}
			}
		}
		else
		{
			ClientMob* mob = dynamic_cast<ClientMob*>(iter->second);
			if (mob)
			{
				std::string actorSkey = mob->getFullyCustomModelKey();
				if (!actorSkey.empty() && actorSkey == skey)
				{
					mob->updateBodyByFullyCustomModel();
				}
			}
		}
	}

	for (size_t i = 0; i < m_Players.size(); i++)
	{
		std::string actorSkey = m_Players[i]->getFullyCustomModelKey();
		if (!actorSkey.empty() && actorSkey == skey)
		{
			m_Players[i]->updateBodyByFullyCustomModel();
		}
		else
		{
			std::string strCustomModel = m_Players[i]->getCustomModel();
			if (strCustomModel.size() > 0)
			{
				m_Players[i]->updateBodyByFullyCustomModel();
			}
		}
	}
}

void ClientActorMgr::resetActorsByImportModel()
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		iter->second->updateBodyByImportModel();
	}

	for (size_t i = 0; i < m_Players.size(); i++)
	{
		m_Players[i]->updateBodyByImportModel();
	}
}


bool ClientActorMgr::existMobNearPos(const WCoord& pos, int range, WORLD_ID objId)
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
#ifdef NEW_WORLD_QUERY
	bool exist = false;
	selectNearActors(pos, range, [](IClientActor* actor) {
		return (dynamic_cast<ClientMob*>(actor) != nullptr);
	},
		[&exist, &objId](IClientActor* actor) {
		if (actor->getObjId() == objId)
		{
			exist |= true;
		}
	});
	return exist;
#else
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(iter->second);
		if (mob)
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;

			float dist = vec.length();
			WORLD_ID mobObjId = mob->getObjId();
			if (dist < range && mobObjId == objId)
			{
				return true;
			}
		}

	}

	return false;
#endif
}

bool ClientActorMgr::changeAllTamedToWild(long long playerId, int mobDefId, int wildDefId)
{
	if (playerId == 0 || mobDefId == 0) return false;

	ClientMob* pTempMob = NULL;
	for (auto it = m_LiveActors.begin(); it != m_LiveActors.end(); it++) {
		pTempMob = dynamic_cast<ClientMob*>(it->second);
		if (pTempMob && pTempMob->getDefID() == mobDefId) {
			if (pTempMob->getTamedOwnerID() == playerId)
			{
				pTempMob->setTamedOwnerUin(0);
				if (wildDefId > 0) pTempMob->mobToWild(wildDefId);
			}
		}
	}

	return true;
}

void ClientActorMgr::getAllLiveActors(std::vector<ClientActor*>& actors)
{
	for (std::map<WORLD_ID, ClientActor*>::iterator iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		actors.push_back(iter->second);
	}
}

void ClientActorMgr::getAllEmptyActors(std::vector<ClientActor*>& actors)
{
	for (std::map<WORLD_ID, ClientActor*>::iterator iter = m_EmptyActors.begin(); iter != m_EmptyActors.end(); iter++)
	{
		actors.push_back(iter->second);
	}
}

void ClientActorMgr::FreeOldAllActors()
{
	//for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	//{
	//	ClientActor* actor = iter->second;
	//	//todo huangfubin
	//	//if(actor->IsInScene())
	//		actor->leaveWorld(false);
	//	actor->release();
	//	iter->second = NULL;
	//}
	//m_LiveActors.clear();
	auto tempActors = m_LiveActors;
	for (auto iter = tempActors.begin(); iter != tempActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		m_World->GetWorldScene()->UnbindLegacyActor(actor);
	}
	assert(m_LiveActors.empty());
}

void ClientActorMgr::emptyActorChange(WORLD_ID id, bool flag)
{
	if (!flag)
	{
		auto iter = m_EmptyActors.find(id);
		if (iter != m_EmptyActors.end())
		{
			ClientActor* actor = iter->second;
			bool newFlag = true;
			if (actor && actor->getCurMapID() != NULL_MAPID)
			{
				actor->leaveWorld(false);
				newFlag = false;
			}
			m_EmptyActors.erase(id);
			if (actor)
			{
				if (newFlag)
				{
					spawnActor(actor, actor->getObjId());
				}
				else
				{
					InsertLiveActors(actor);
				}
			}
		}
	}
	else
	{
		ClientActor* actor = findActorByWID(id);
		if (actor)
		{
			//GetActorMGT()->RemoveNode(actor);
			if (actor->getChunkPosComponent())
			{
				actor->getChunkPosComponent()->onLeaveWorld(false);
				actor->RemoveComponent(actor->getChunkPosComponent());
			}
			m_LiveActors.erase(actor->getObjId());

			actor->setWorld(m_World);
			InsertLiveActors(actor);

			actor->OnEnterChildCull();
		}
	}
}

void ClientActorMgr::UpdateAllActorsVisiableDistance()
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		ActorBody* actorBody = actor->getBody();
		if (actorBody)
		{
			actorBody->UpdateVisiableDistance();
		}
	}
}

//20210908 codeby：chenwei 查找周边指定距离内的玩家
std::vector<int> ClientActorMgr::findPlayersByRange(int range) //20210916 codeby:chenwei修改容器类型
{
	OPTICK_EVENT();
	OPTICK_TAG("range", range);
	std::vector<int> uin_list;

	for (size_t i = 0; i < m_Players.size(); i++)
	{
		ClientPlayer* player = m_Players[i];
		if (player->isDead() || player->getUin() == g_pPlayerCtrl->getUin())
		{
			continue;
		}

		// 距离
		WCoord originPos = g_pPlayerCtrl->getPosition();
		WCoord targetPos = player->getPosition();
		float dis = originPos.distanceTo(targetPos); //20210908 codeby：chenwei 计算玩家间距

		// 射线检测，防止中间有其他障碍物，出发点：P1脚底->P2眼睛
		MINIW::WorldRay ray;
		ray.m_Origin = g_pPlayerCtrl->getPosition().toWorldPos(); //20210908 codeby：chenwei 设置射线起始点
		ray.m_Dir = player->getEyePosition().toWorldPos().toVector3() - g_pPlayerCtrl->getPosition().toWorldPos().toVector3();//20210908 codeby��chenwei ���㷽��
		ray.m_Range = ray.m_Dir.Length();
		ray.m_Dir /= ray.m_Range;


		// 范围内并且无其他东西阻挡
		IntersectResult inter_result;
		//2021-09-17 codeby:chenwei 指定格子内并且没有受block阻挡的玩家 //2021-10-20 codeby:wangyu 增加过滤方块类型
		if (!m_World->pickGround(ray, &inter_result, PICK_METHOD_SOLID) && dis <= BLOCK_SIZE * range)
		{
			uin_list.push_back(player->getUin());
		}
	}

	return uin_list; //20210916 codeby:chenwei 修改返回容器类型
}

bool ClientActorMgr::IsSandwormExist()
{
	for (auto iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(iter->second);
		if (mob)
		{
			if (mob->getDefID() == 3825)
			{
				return true;
			}
		}
	}
	return false;
}

bool ClientActorMgr::spawnDesertTradeCaravan(long long uin, WCoord explorePos, long long saveTime)
{
	if (!GetDesertTradeCaravanMgr().isShouldCreateNewTrade(explorePos))
	{
		return false;
	}
	ConstAtLua* luaConst = GetLuaInterfaceProxy().get_lua_const();
	if (!luaConst)
	{
		return false;
	}
	//12个格子
	const char dir[12][2] = { {-1, -1}, {-1, 0}, {-1, 1}, {0, -1,}, {0, 0}, {0, 1}, {1, -1}, {1, 0}, {1, 1}, {2, -1}, {2, 0,}, {2, 1} };
	ClientPlayer* player = findPlayerByUin(uin);
	if (player == NULL)
	{
		return false;
	}
	World* world = player->getWorld();
	if (world == NULL)
	{
		return false;
	}
	if (world->getCurMapID() != 0)
	{
		return false;
	}
	auto mapData = g_WorldMgr->getMapData(0, false);
	if (mapData != NULL)
	{
		auto& chunks = mapData->specialChunk[SpecialEcosysType_DesertVillage];
		for (auto p : chunks)
		{
			WCoord centerPos(p.index.x * SECTION_BLOCK_DIM + SECTION_BLOCK_DIM / 2, 0, p.index.z * SECTION_BLOCK_DIM + SECTION_BLOCK_DIM / 2);
			if (!GetDesertTradeCaravanMgr().checkPos(centerPos, explorePos))
			{
				return false;
			}
		}
	}
	WCoord pos = player->getPosition();
	WCoord originBlockPos = CoordDivBlock(pos);
	WCoord blockPos = originBlockPos;
	int camelNum = 3;
	//骆驼数量50%为4个
	if (GenRandomInt(1, 10) <= 5)
	{
		camelNum = 4;
	}
	float yaw = player->getLocoMotion()->m_RotateYaw;
	int findx = -sinf(yaw) >= 0 ? 1 : -1;
	int findz = -cos(yaw) >= 0 ? 1 : -1;
	int tryTime = 5;
	bool find = false;
	std::vector<WCoord> mobPos(12, WCoord(0, 0, 0));
	WCoord curPos;
	for (int i = 0; i < tryTime; i++)
	{
		//在玩家前面确定一个随机位置
		blockPos.x += GenRandomInt(luaConst->desertTradeCaravanSpawnMinx, luaConst->desertTradeCaravanSpawnMaxx) * findx;
		blockPos.z += GenRandomInt(luaConst->desertTradeCaravanSpawnMinz, luaConst->desertTradeCaravanSpawnMaxz) * findz;
		for (int j = 0; j < 12; j++)
		{
			curPos.x = blockPos.x + dir[j][0];
			curPos.z = blockPos.z + dir[j][1];
			int curY = world->getTopHeight(curPos.x, curPos.z);
			curPos.y = curY;
			//在这一格y轴上尝试, 这里可以优化
			int k = -1;
			for (; k < 3; k++)
			{
				curPos.y = curY + k;
				int blockid = m_World->getBlockID(curPos);
				if (IsWoodBlockID(blockid))
				{
					break;
				}
				if (IsLeavesBlockID(blockid))
				{
					break;
				}
				if (blockid == BLOCK_WIND_EROSIONS_STONE)
				{
					break;
				}
				if (CanMobTypeSpawnOnPos(m_World, MOB_NPC, curPos))
				{
					mobPos[j] = curPos;
					if (j == 8)
					{
						find = true;
					}
					break;
				}
			}
			if (k >= 3)
			{
				break;
			}
		}
		if (find)
		{
			break;
		}
		blockPos = originBlockPos;
	}

	if (!find)
	{
		return false;
	}
	ActorDesertBusInessMan* mob;
	ActorDesertBusInessManGuard* guard;
	ActorPackHorse* camel;
	int posNum = 0;
	int mobid = 3210;
	int leader = 0;
	ActorDesertBusInessMan* leaderMob = NULL;
	const int containerType[4] = { 801, 979, 974, 969 };
	for (int i = 0; i < 3; i++)
	{
		mob = dynamic_cast<ActorDesertBusInessMan*>(spawnMob(BlockCenterCoord(mobPos[posNum++]), mobid++, false, false));
		if (mob != NULL)
		{
			mob->getLocoMotion()->m_RotateYaw = yaw;
			if (leader == i)
			{
				mob->setExploreType(ActorDesertBusInessManExplore_Oasis);
				leaderMob = mob;
			}
			else
			{
				mob->setExploreType(ActorDesertBusInessManExplore_Follow);
				if (leaderMob != NULL)
					mob->setFollowObjId(leaderMob->getObjId());
			}
			mob->setExplorePos(explorePos);
		}
		guard = dynamic_cast<ActorDesertBusInessManGuard*>(spawnMob(BlockCenterCoord(mobPos[posNum++]), 3213, false, false));
		if (guard != NULL && mob != NULL)
		{
			guard->getLocoMotion()->m_RotateYaw = yaw;
			guard->setFollowObjId(mob->getObjId(), true);
			guard->setGatherPos(explorePos);
			mob->addGuard(guard->getObjId());
		}
		camel = dynamic_cast<ActorPackHorse*>(spawnMob(BlockCenterCoord(mobPos[posNum++]), 3823, false, false));
		if (camel != NULL && mob != NULL)
		{
			camel->setMasterObjId(mob->getObjId());
			mob->setPickingCamel(camel->getObjId());
			int type = GenRandomInt(0, 3);
			camel->equipPack(containerType[type], 0);
			camel->equipPack(containerType[type], 1);
			camel->loadPack();
			camel->setFollowType(ActorHorseFollowDesertBussinessMan_Explore);
			camel->setGatherPos(explorePos);
		}
	}
	//最后一个生成的是特殊商人,有一个交易用的骆驼
	if (mob != NULL)
	{
		camel = dynamic_cast<ActorPackHorse*>(spawnMob(BlockCenterCoord(mobPos[posNum++]), 3823, false, false));
		if (camel)
		{
			camel->setMasterObjId(mob->getObjId());
			mob->setDealCamel(camel->getObjId());
			camel->setFollowType(ActorHorseFollowDesertBussinessMan_Explore);
			camel->setGatherPos(explorePos);
		}
	}
	GetDesertTradeCaravanMgr().addNewTradeCaravan(explorePos, saveTime);
	return true;
}

void ClientActorMgr::setShowEffectState(bool isOpen)
{
	for (std::map<WORLD_ID, ClientActor*>::iterator iter = m_EmptyActors.begin(); iter != m_EmptyActors.end(); iter++)
	{
		iter->second->SetVisible(isOpen);
	}
}

void ClientActorMgr::BatchActor()
{
	if (UGCActorBatchManager::GetInstance().IsBatching())
		return;

	int nEntityCount = 0;
	int nStaticEntityCount = 0;
	std::vector<ClientActor*> actors;
	//actors.reserve(m_LiveActors.size());
	for (std::map<WORLD_ID, ClientActor*>::iterator iter = m_LiveActors.begin(); iter != m_LiveActors.end(); iter++)
	{
		ClientActor* actor = iter->second;
		if (actor)
		{
			/*
			if (actor->CanBatch())
			{
				actors.emplace_back(iter->second);
			}
			else
			{
				actor->EnableMeshRender(true);
			}
			*/
			AddBatchActor(actors, actor, nEntityCount, nStaticEntityCount);
		}
	}

	UGCActorBatchManager::GetInstance().SetEntityCount(nEntityCount);

	if (UGCActorBatchManager::GetInstance().IsNeedReBatch(m_World->getCurMapID(), this, actors))
	{
		UGCActorBatchManager::GetInstance().BatchActors(actors);
	}
}

void ClientActorMgr::AddBatchActor(vector<ClientActor*>& batchActor, ClientActor* pActor, int& nEntityCount, int& nStaticEntityCount)
{
	if (!pActor || !pActor->IsObject())
		return;

	nEntityCount += 1;

	if (pActor->CanBatch())
	{
		nStaticEntityCount += 1;
		batchActor.emplace_back(pActor);
	}
	else
	{
		if (pActor->IsLogicVisible())
			pActor->EnableMeshRender(true);
	}

	if (pActor->GetChildrenCount() == 0)
		return;

	std::vector<AutoRef<SandboxNode>> children = pActor->GetAllChildren();

	for (auto& child : children)
	{
		if (!child->IsKindOf<ClientActor>())
			continue;

		ClientActor* pChildActor = child->ToCast<ClientActor>();
		if (!pChildActor || !pChildActor->IsObject())
			continue;

		AddBatchActor(batchActor, pChildActor, nEntityCount, nStaticEntityCount);
	}
}

int ClientActorMgr::GetActorUpdateStep(long long objid)
{
	return m_SpaceManager->GetActorUpdateStep(objid);
}

int ClientActorMgr::GetActorUpdateStep()
{
	return m_SpaceManager->GetActorUpdateStep();
}

void ClientActorMgr::desertExploreChunkView(std::vector<ClientPlayer*>& players)
{
	if (!GetDesertTradeCaravanMgr().shouldExplore())
	{
		return;
	}
	GetDesertTradeCaravanMgr().setShouldExplore(false);
	//做个筛选
	std::vector<ClientPlayer*> filter;
	for (auto p : players)
	{
		if (p != NULL && !p->isDead())
		{
			World* world = p->getWorld();
			if (world != NULL && world->getCurMapID() == 0)
			{
				WCoord pos = CoordDivBlock(p->getPosition());
				BIOME_TYPE type = world->getBiomeType(pos.x, pos.z);
				if (type == BIOME_DESERT)
				{
					filter.push_back(p);
				}
			}
		}
	}
	int size = filter.size();
	if (size <= 0)
	{
		return;
	}
	int select = 0;
	if (size == 1)
	{
		select = 0;
	}
	else
	{
		select = GenRandomInt(1, size) - 1;
	}
	if (select < 0 || select >= size)
	{
		return;
	}
	ConstAtLua* p = GetLuaInterfaceProxy().get_lua_const();
	if (p)
	{
		filter[select]->exploreChunkView(p->playerExploreRange * SECTION_BLOCK_DIM, p->playerExploreFindBiome);
	}
}

void ClientActorMgr::spawnSharkEvent() {

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("spawnSharkPoint");
	m_spawnSharkPointCallback = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("spawnSharkPoint", nullptr, [&](SandboxContext context) -> SandboxResult {
		int point = (int)context.GetData_Number("point");
	m_spawnSharkPoint += point;
	size_t spawnSharkPoint = m_Players.size() == 0 ? 1 * 1000 : m_Players.size() * 1000;
	bool genpassive = isMobGen(MOB_PASSIVE);
	if (m_spawnSharkPoint > spawnSharkPoint && genpassive) //累计1000点生一条鲨鱼
	{
		m_spawnSharkPoint = m_spawnSharkPoint - spawnSharkPoint;
		std::vector<ClientPlayer*> players;
		size_t num = m_Players.size();
		for (size_t i = 0; i < num; i++)
		{
			WCoord pos = m_Players[i]->getPosition() / BLOCK_SIZE;
			BIOME_TYPE biome_type = m_World->getBiomeType(pos.x, pos.z);
			if (biome_type == BIOME_OCEAN || biome_type == BIOME_DEEPOCEAN)
			{
				players.push_back(m_Players[i]);
			}
		}
		size_t num1 = players.size();
		if (num1 >= 1)
		{
			const double pi = 3.1415926;
			int index = GenRandomInt(0, num1 - 1);
			int dist = GenRandomInt(3000, 4800);
			int y = GenRandomInt(4200, 6200);
			WCoord pos = players[index]->getPosition();
			int waterSurfaceY = m_World->getWaterSurfaceBlock(pos.x / BLOCK_SIZE, pos.z / BLOCK_SIZE);

			if (waterSurfaceY != -1)
				pos.y = (waterSurfaceY - 2) * BLOCK_SIZE;
			else
				pos.y = y;

			int unitAngle = 60;
			for (size_t i = 1; i <= 6; i++) {
				int angle = GenRandomInt(unitAngle * (i - 1), unitAngle * i);
				pos.z += dist * std::cos(angle * (pi / 180));
				pos.x += dist * std::sin(angle * (pi / 180));
				int blockId = m_World->getBlockID(pos / BLOCK_SIZE);
				if (BlockMaterialMgr::isWater(blockId))//(blockId == BLOCK_FLOW_WATER || blockId == BLOCK_STILL_WATER)
				{
					ClientMob* clienMob = spawnMob(pos, 3628, false, false);
					ClientAquaticMob* shark = dynamic_cast<ClientAquaticMob*>(clienMob);
					auto pClientAquaticComponent = clienMob->getClientAquaticComponent();

					if (shark)
					{
						shark->shark_source = 1;
					}
					else if (pClientAquaticComponent)
					{
						pClientAquaticComponent->shark_source = 1;
					}

					clienMob->setPosition(pos);
					MINIW::ScriptVM::game()->callFunction("SharkSpawnReport", "i", 1);//鲨鱼生成埋点上报
					break;
				}
			}
		}
	}
	return MNSandbox::SandboxResult(nullptr, true);
	});
}

void ClientActorMgr::searchLiveActors(ClientActorSearcher* seacher)
{
	for (auto it = m_LiveActors.begin(); it != m_LiveActors.end(); ++it)
	{
		if (!seacher->look(it->second))
			break;
	}
}

bool ClientActorMgr::canSpawnMobByProbability(MOB_TYPE mobtype)
{
	if (mobtype == MOB_WATER)
	{
		if (GenRandomInt(100) >= GetLuaInterfaceProxy().get_lua_const()->spawnWaterMobPercent)
		{
			return false;
		}
	}
	else if (mobtype == MOB_FLY)
	{
		if (GenRandomInt(100) >= GetLuaInterfaceProxy().get_lua_const()->spawnFlyMobPercent)
		{
			return false;
		}
	}

	return true;
}

int ClientActorMgr::getDelaySpawnMobCount(int monsterid)
{
	int delayCount = 0;

	size_t delaySize = m_SpawnMobDelayParams.size();
	for (size_t index = 0; index < delaySize; index++)
	{
		const SpawnMobDelayParam& param = m_SpawnMobDelayParams[index];
		if (param.m_monsterid == monsterid) delayCount++;
	}
	return delayCount;
}

int ClientActorMgr::getDelaySpawnMobCountByType(MOB_TYPE mobtype)
{
	int delayCount = 0;

	size_t delaySize = m_SpawnMobDelayParams.size();
	for (size_t index = 0; index < delaySize; index++)
	{
		const SpawnMobDelayParam& param = m_SpawnMobDelayParams[index];
		const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(param.m_monsterid);
		
		if (def && def->Type == mobtype) 
		{
			delayCount++;
		}
	}
	return delayCount;
}

int ClientActorMgr::isPickBlockMeshActor(ClientActor& actor, const Rainbow::Ray& ray, float mindist, float* pt)
{
	ActorBody* pActorBody = actor.getBody();
	if (!pActorBody)
		return -1;
	Entity* pEntity = pActorBody->getEntity();
	if (!pEntity || pEntity->GetBindObjectCount() == 0)
		return -1;

	int extraData = pActorBody->getExtraData();
	if (extraData <= 0)
		return -1;

	int itemId = ClientItem::getRealModelItemId(extraData);
	const ItemDef* pItemdef = GetDefManagerProxy()->getItemDef(itemId);
	if (!pItemdef)
		return -1;
	//用于BlockItem
	BlockMesh* pBlockMesh = dynamic_cast<BlockMesh*>(pEntity->GetBindObject(0));
	if (!pBlockMesh || !pBlockMesh->getMesh() || !pBlockMesh->GetTransform())
		return -1;

	AABB meshBound = actor.GetAABB();
	if (!IntersectRayAABB(ray, meshBound))
		return 0;

	const BlockDef* pBlockdef = GetDefManagerProxy()->getBlockDef(itemId);
	//这里的判断逻辑和创建逻辑对等
	bool isBlockModel = pBlockdef && (pItemdef->MeshType == ICON_GEN_MESH || pItemdef->MeshType == OMOD_GEN_MESH || pItemdef->MeshType == BLOCK_GEN_MESH);
	isBlockModel = isBlockModel || (pItemdef->MeshType == BLOCK_GEN_MESH || pItemdef->MeshType == 3);

	float dist = MAX_FLOAT;

	if (isBlockModel)
	{
		Transform* pTrans = pBlockMesh->GetTransform();
		Vector3f worldPos = pTrans->GetWorldPosition();
		Vector3f worldScale = pTrans->GetWorldScale();
		Quaternionf worldRot = pTrans->GetWorldRotation();

		Matrix4x4f worldMat = Matrix4x4f::identity;
		makeSRTMatrix(worldMat, worldScale, worldRot, worldPos);
		worldMat.Invert_Full();
		Rainbow::Ray tempRay = ray;
		tempRay.m_Origin = worldMat.MultiplyPoint3(tempRay.m_Origin);
		tempRay.m_Direction = worldMat.MultiplyVector3(tempRay.m_Direction);

		//实体的中点加上Entity设置的局部位置
		Vector3f offset = Vector3f(50, 0, 50) + pTrans->GetLocalPosition();

		SectionMesh* pSectionMesh = pBlockMesh->getMesh();
		if (!pSectionMesh)
			return 0;

		//是否选中网格
		bool isPickBlockMesh = false;
		for (int meshIndex = 0; meshIndex < pSectionMesh->GetSubMeshCount(); meshIndex++)
		{
			SectionSubMesh* pSectionSubMesh = pSectionMesh->GetSubMeshAt(meshIndex);
			for (int meshInfoIndex = 0; meshInfoIndex < pSectionSubMesh->GetMeshInfoCount(); meshInfoIndex++)
			{
				SectionSubMesh::MeshInfo* pMeshInfo = pSectionSubMesh->GetMeshInfo(meshInfoIndex);
				const IndexFormat& indexFormat = pMeshInfo->m_Mesh->GetIndexFormat();
				UInt32 indexSize = GetMeshIndexSize(indexFormat);
				//如果顶点的类型是32位的暂不处理
				if (indexSize != 2)
					continue;
				size_t indexCount = pMeshInfo->m_Mesh->GetTotalIndexCount();
				UInt16* indexDataBuf = (UInt16*)pMeshInfo->m_Mesh->GetIndexBuffer().data();
				size_t vertexCount = pMeshInfo->m_Mesh->GetVertexCount();
				StrideIterator<Vector3f> vertexBegin = pMeshInfo->m_Mesh->GetVertexBegin();

				for (int indiceIndex = 0; indiceIndex < indexCount; indiceIndex += 3)
				{
					const UInt16& triangleIndice0 = indexDataBuf[indiceIndex + 0];
					const UInt16& triangleIndice1 = indexDataBuf[indiceIndex + 1];
					const UInt16& triangleIndice2 = indexDataBuf[indiceIndex + 2];
					const Vector3f& triangleVert0 = *(vertexBegin + triangleIndice0);
					const Vector3f& triangleVert1 = *(vertexBegin + triangleIndice1);
					const Vector3f& triangleVert2 = *(vertexBegin + triangleIndice2);
					Vector3f trianglePos0 = triangleVert0 - offset;
					Vector3f trianglePos1 = triangleVert1 - offset;
					Vector3f trianglePos2 = triangleVert2 - offset;

					if (!IntersectRayTriangle(tempRay, trianglePos0, trianglePos1, trianglePos2, &dist))
						continue;
					if (dist < mindist)
					{
						isPickBlockMesh = true;
						mindist = dist;
						if (pt) *pt = dist;
					}
				}
			}
		}
		if (isPickBlockMesh)
			return 1;
		else
			return 0;
	}
	else if (pItemdef->MeshType == OMOD_GEN_MESH || pItemdef->MeshType == CUSTOM_GEN_MESH || pItemdef->MeshType == FULLY_CUSTOM_GEN_MESH || pItemdef->MeshType == IMPORT_MODEL_GEN_MESH)
	{
		//非方块的情况暂时没有用到，先不处理
	}
	else if (pItemdef->MeshType == VEHICLE_GEN_MESH)
	{
		//这种暂时没有用到，先不处理
	}
	else
	{
		//这种暂时没有用到，先不处理
	}
	return -1;
}

ClientActor* ClientActorMgr::pickActorByOctTree(Rainbow::Ray ray, float* pt, float range, ActorExcludes& excludes, AABB& aabbBox, BlockScene* pPrefabScene, bool* isNewModel)
{
	/*auto gameCam = GetCameraManager().getGameCamera();
	if (gameCam == nullptr) return nullptr;
	auto cam = gameCam->getEngineCamera();

	Rainbow::CullPlanes currentCameraCullPlanes;
	const Rainbow::Matrix4x4f& world2Cam = cam->GetWorldToCameraMatrix();
	Rainbow::Matrix4x4f viewProj = Matrix4x4Mul(world2Cam, cam->GetProjectionMatrix());
	currentCameraCullPlanes.CreateFromMatrix(viewProj);*/

	if (!m_ActorMGT) return nullptr;

	float mindist = range;
	ClientActor* pickActor = nullptr;
	m_ActorMGT->QueryAllNodes([&](IClientActor* iactor) {

		auto actor = iactor->ToCast<ClientActor>();
		if (excludes.inExcludes(actor)) return true;
	ActorBody* pActorBody = actor->getBody();
	if (pPrefabScene)
	{
		Rainbow::GameObject* pGameObject = nullptr;

		if (pActorBody)
		{
			Entity* pEntity = pActorBody->getEntity();
			if (!pEntity)
				return true;

			pGameObject = pEntity->GetGameObject();
			if (!pGameObject)
				return true;
		}
		else
		{
			pGameObject = actor->GetGameObject();
			if (!pGameObject)
				pGameObject = actor->GetMeshRenderGO();

			if (!pGameObject)
				return true;
		}
		
		GameScene* pGameScene = pGameObject->GetScene();
		if (!pGameScene)
			return true;

		if (pGameScene->GetGameSceneId() != pPrefabScene->GetGameSceneId())
			return true;
	}

	float dist = MAX_FLOAT;
	bool haveMesh = false;
	bool haveImage = false;

	if (pActorBody)
	{
		Entity* entity = pActorBody->getEntity();
		if (entity)
		{
			Model* model = entity->GetMainModel();
			if (!model && entity->GetBindObjectCount() > 0)
			{
				auto bindobj = entity->GetBindObject(0);
				ModelItemMesh* pModelMesh = dynamic_cast<ModelItemMesh*>(bindobj);
				if (pModelMesh)
				{
					model = pModelMesh->GetModel();
				}

				ImageMesh* pImageMesh = dynamic_cast<ImageMesh*>(bindobj);
				if (pImageMesh)
				{
					haveImage = true;
				}
			}

			if (model && model->IsKindOf<ModelLegacy>())
			{
				ModelLegacy* modellegacy = static_cast<ModelLegacy*>(model);
				ClientMob* pClientMob = dynamic_cast<ClientMob*>(actor);
				//如果是微缩的完全自定义模型无法使用精确点击就直接使用hitCollideBox
				if (pClientMob && pClientMob->getDef()->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
				{
					std::vector<TypeCollideAABB> boxs;
					if (actor->getILocoMotion())
						actor->getILocoMotion()->getMultiTypeCollidBoxs(boxs);
					for (auto& typebox : boxs)
					{
						Rainbow::AABB collideBoxAABB = typebox.box.ToAABB();
						IntersectRayAABB(ray, collideBoxAABB, &dist);
						if (dist < mindist)
						{
							mindist = dist;
							pickActor = actor;
							if (pt) *pt = dist;

							aabbBox = collideBoxAABB;
						}
					}
				}
				else if (modellegacy != nullptr)
				{
					Matrix4x4f tm = modellegacy->GetWorldMatrix();
					Matrix4x4f InvertTm = tm.GetInvert_Full();
					Vector3f dir = ray.m_Direction;
					haveMesh = true;
					if (modellegacy->IntersectRayStaticMesh(Rainbow::INTERSECT_PICK, ray, 0, &dist))
					{
						dir = InvertTm.MultiplyVector3(dir);
						dir.Normalize();
						Vector3f rangePoint = dir * dist;
						rangePoint = tm.MultiplyVector3(rangePoint);
						dist = rangePoint.Length();
						if (dist < mindist)
						{
							mindist = dist;
							pickActor = actor;
							if (pt) *pt = dist;

							aabbBox = modellegacy->GetWorldBounds();
						}
					}
				}
			}else if((model && model->IsKindOf<ModelNew>()))
			{
				/*if (actor->canBeCollidedWith())
				{*/
					std::vector<TypeCollideAABB> boxs;
					if (actor->getILocoMotion())
						actor->getILocoMotion()->getMultiTypeCollidBoxs(boxs);
					for (auto& typebox : boxs)
					{
						Rainbow::AABB collideBoxAABB = typebox.box.ToAABB();
						IntersectRayAABB(ray, collideBoxAABB, &dist);
						if (dist < mindist)
						{
							mindist = dist;
							pickActor = actor;
							if (pt) *pt = dist;

							aabbBox = collideBoxAABB;
							if (isNewModel)
							{
								*isNewModel = true;
							}
							break;
						}
					}
				/*}*/
			}
			else
			{
				int ret = isPickBlockMeshActor(*actor, ray, mindist, pt);
				if (ret >= 0)
				{
					aabbBox = actor->GetAABB();
					haveMesh = true;
					if (ret > 0)  pickActor = actor;
				}
			}
		}
	}
	else
	{
		Rainbow::MeshRenderer* render = actor->GetMeshRender();
		if (render)
		{
			haveMesh = true;
			if (render) {
				Rainbow::SharePtr< Rainbow::Mesh> mesh = render->GetSharedMesh();
				if (mesh) {
					Vector3f worldPos = actor->GetWorldPosition();
					Vector3f worldScale = actor->GetWorldScale();
					Quaternionf worldRot = actor->GetWorldRotation();
					Matrix4x4f worldMat = Matrix4x4f::identity;
					Rainbow::Ray tempRay = ray;
					makeSRTMatrix(worldMat, worldScale, worldRot, worldPos);
					worldMat.Invert_Full();
					tempRay.m_Origin = worldMat.MultiplyPoint3(tempRay.m_Origin);
					tempRay.m_Direction = worldMat.MultiplyVector3(tempRay.m_Direction);
					float dist = 0;
					if (SceneEditorMeshGen::GetInstance().IntersectRayMesh(tempRay, mesh.Get(), 25600, &dist)) {
						if (dist < mindist)
						{
							mindist = dist;
							pickActor = actor;
							if (pt) *pt = dist;

							aabbBox = actor->GetAABB();
						}
					}
				}
			}
		}
	}

	bool isHasSpecialCom = actor->getSoundComponent() || actor->getEffectComponent();
	if ((!haveMesh && isHasSpecialCom) || haveImage)
	{
		AABB aabb = actor->GetAABB();
		IntersectRayAABB(ray, aabb, &dist);

		if (dist < mindist)
		{
			mindist = dist;
			pickActor = actor;
			if (pt) *pt = dist;
			aabbBox = aabb;
		}
	}

	return true;
	});

	return pickActor;
}

ClientActor* ClientActorMgr::pickActorPhysic(float* pt, const Rainbow::Vector2f& mousePosition)
{
	auto gameCam = CameraManager::GetInstance().getGameCamera();
	if (!gameCam) return nullptr;
	auto cam = gameCam->getEngineCamera();
	auto camPos = gameCam->getPosition();
	Rainbow::CullPlanes currentCameraCullPlanes;
	const Rainbow::Matrix4x4f& world2Cam = cam->GetWorldToCameraMatrix();
	Rainbow::Matrix4x4f viewProj = Matrix4x4Mul(world2Cam, cam->GetProjectionMatrix());
	currentCameraCullPlanes.CreateFromMatrix(viewProj);

	if (!m_ActorMGT || !m_World) return nullptr;

	Rainbow::RaycastHit hitInfo;
	Rainbow::Ray ray = cam->ScreenPointToRay(mousePosition);

	bool ret = m_World->m_PhysScene->RayCast(camPos.toVector3(), ray.m_Direction, 6000, hitInfo, 0xffffffff, false);
	if (!ret || !hitInfo.collider || !hitInfo.collider->GetUserData())
	{
		return nullptr;
	}

	ClientActor* pickActor = dynamic_cast<ClientActor*>((ClientActor*)hitInfo.collider->GetUserData());
	if (pickActor && pt)
	{
		*pt = hitInfo.distance;
	}

	return pickActor;
}

std::string hoverClientActorClass;
int         hoverClientActorDistance;
void ClientActorMgr::OnDrawGizmo()
{
#if GIZMO_DRAW_ENGABLE
	if (m_ActorMGT == nullptr || g_pPlayerCtrl == nullptr)return;
	GetGizmoManager().SetGizmoEnable(true);
	auto gameCam = CameraManager::GetInstance().getGameCamera();
	if (gameCam == nullptr)return;
	OPTICK_EVENT();
	GizmosNS::SetGizmoMatrix(Matrix4x4f::identity);

	//只渲染视锥体内的
	auto cam = gameCam->getEngineCamera();
	auto camPos = gameCam->getPosition();
	CullPlanes currentCameraCullPlanes;
	const Matrix4x4f& world2Cam = cam->GetWorldToCameraMatrix();
	Matrix4x4f viewProj = Matrix4x4Mul(world2Cam, cam->GetProjectionMatrix());
	currentCameraCullPlanes.CreateFromMatrix(viewProj);

	GizmosNS::SetGizmoColor(ColorRGBAf(1.0, 0, 0.0f));
	{
		OPTICK_EVENT("DrawActorGizmo");

		int actor = 0;
		Rainbow::RectInt playerAOI;
		auto& playerPos = g_pPlayerCtrl->getPosition();
		playerAOI.width = 9 * CHUNK_SIZE_X;
		playerAOI.height = 9 * CHUNK_SIZE_Z;
		playerAOI.SetCenterPos(playerPos.x, playerPos.z);
		m_ActorMGT->QueryWithinFrustum(currentCameraCullPlanes.Planes, false, [&playerAOI, &actor](IClientActor* iobj) {
			auto obj = iobj->ToCast<ClientActor>();
			if (obj == g_pPlayerCtrl) {
				GizmosNS::SetGizmoColor(ColorRGBAf(1.0f, 1.0f, 1.0f));
			}
			else
			{
				//如果对象在4个chunk范围内（player的aoi内，换个颜色）
				auto& objPos = obj->getPosition();
				if (playerAOI.Contains(objPos.x, objPos.z)) {
					GizmosNS::SetGizmoColor(ColorRGBAf(128.0f / 255.0f, 0, 1.0f));
				}
				else {
					GizmosNS::SetGizmoColor(ColorRGBAf(0, 0, 1.0f));
				}
			}
		actor++;
		auto aabb = obj->GetAABB();
		GizmosNS::DrawWireCube(aabb.m_Center, aabb.m_Extent * 2.0f, false);
		});
		OPTICK_TAG("num", actor);
	}

	//hover + distance display
	{
		auto& mousePos = GetInputManager().GetMousePosition();
		Rainbow::Ray ray = cam->ScreenPointToRay(mousePos);
		//hover效果
		GizmosNS::SetGizmoColor(ColorRGBAf(1.0f, 0.5f, 0.5f));
		ClientActor* testObj = nullptr;
		m_ActorMGT->QueryWithRay(ray, [this, &testObj](IClientActor* obj, float t0, float t1) {
			if (obj == g_pPlayerCtrl) {
				return true;
			}
		auto aabb = obj->GetAABB();
		GizmosNS::DrawWireCube(aabb.m_Center, aabb.m_Extent * 2.5f);
		testObj = obj ? obj->GetActor() : nullptr;

		//distance show
		GetDebugInfo().distance = (int)Rainbow::Distance(aabb.GetCenter(), g_pPlayerCtrl->getPosition().toVector3());
		// 			WCoord p(aabb.GetCenter()+Vector3f(0.0f, aabb.GetExtent().y + 10.0f,0.0f));
		// 			WCoord eye = GetWorldManagerPtr()->m_RenderEyePos;
		// 			auto dis = Rainbow::Distance(aabb.GetCenter(), eye.toWorldPos().toVector3());
		// 			LogStringMsg("obj[%p][%d,%d][%f,%f,%f-%f,%f,%f] distance: %.1f", obj, 
		// 				obj->GetMGTHandle().NodeIndex(),obj->GetMGTHandle().ElementIndex(), 
		// 				aabb.m_Center.x, aabb.m_Center.y, aabb.m_Center.z, 
		// 				aabb.m_Extent.x, aabb.m_Extent.y, aabb.m_Extent.z, 
		// 				dis);

					//创建一个距离的3d文字
		// 			bool bExist = false;
		// 			aabb.Offset(aabb.GetExtent().y);
		// 			this->m_SceneMGT->QueryFirstNodes(aabb, [&bExist](SceneMGTNode* obj) {
		// 				bExist = dynamic_cast<EffectText3D*>(obj) != nullptr;
		// 				return !bExist;
		// 				});
		// 			if (!bExist)
		// 			{
		// 				char buffer[50]; // 设定足够大的缓冲区
		// 				snprintf(buffer, 50, "%.1f", dis);
		// 				this->getEffectMgr()->playText3DEffect(buffer, p, 100);
		// 				LogStringMsg("obj[%p] distance: %.1f", obj, dis);
		// 			}
					//不再继续查找
		return false;
		});
		if (testObj != nullptr) {
			const std::type_info& typeInfo = typeid(*testObj);
			GetDebugInfo().hoverName = typeInfo.name();
		}
		else {
			GetDebugInfo().hoverName = nullptr;
		}
		//debug，再查找一次
// 		if (testObj) {
// 			bool bFound = false;
// 			Rainbow::AABB testAABB(pos, BLOCK_FSIZE / 2.0f);
// 
// 			m_ActorMGT->QueryNodes(testAABB, [&bFound, &testObj](ClientActor* obj) {
// 				bFound |= testObj == obj;
// 				}
// 			);
// 			if (bFound)
// 			{
// 				GizmosNS::SetGizmoColor(ColorRGBAf(0.0f, 1.0f, 0.0f));
// 			}
// 			else {
// 				GizmosNS::SetGizmoColor(ColorRGBAf(1.0f, 0.0f, 0.0f));
// 			}
// 			GizmosNS::DrawWireCube(testAABB.m_Center, testAABB.m_Extent * 2.1f);
// 			//LogStringMsg("==> found obj: %d", bFound);
// 		}

	}

	GizmosNS::SetGizmoColor(ColorRGBAf(0, 0, 1.0f));
	{
		OPTICK_EVENT("DrawTreeNodes");
		//自定义的树遍历，用bitarray存储节点可见性
		int PartiallyVisTreeNode = 0;
		int FullVisTreeNode = 0;
		math::float4 outPlanesSOA[8];
		PrepareOptimizedPlanes(currentCameraCullPlanes.Planes, 6, outPlanesSOA);
		ActorMGT::BitArray visibleNodes((unsigned int)m_ActorMGT->GetTreeNodes() * 2, 0);
		m_ActorMGT->QueryTreeNodes([&visibleNodes, &outPlanesSOA, this](ActorMGT::NodeIndex parentNodeIndex, ActorMGT::NodeIndex nodeIndex, const AABB& nodeAABB, int depth) {
			return m_ActorMGT->GetNodesVisibilityOptimized(visibleNodes, parentNodeIndex, nodeIndex, nodeAABB, outPlanesSOA);
		},
			[&FullVisTreeNode, &PartiallyVisTreeNode, &visibleNodes, this](ActorMGT::NodeIndex parentNodeIndex, ActorMGT::NodeIndex nodeIndex, const AABB& aabb, int depth) {
			if (visibleNodes.test(nodeIndex * 2 + 1)) {
				FullVisTreeNode++;
			}
			else if (visibleNodes.test(nodeIndex * 2)) {
				PartiallyVisTreeNode++;
			}

		//float tintVal = float(depth) / DefaultOctreeSemantics::MaxNodeDepth; // Will eventually get values > 1. Color 

		float tintVal = (float)m_ActorMGT->GetElementsForNode(nodeIndex).size() / (float)DefaultOctreeSemantics::MaxElementsPerLeaf;
		//rounds to 1 automatically
		//float tintVal = 0;
		GizmosNS::SetGizmoColor(ColorRGBAf(tintVal, 0, 1.0f - tintVal));
		bool enableDepth = true;
		if (g_pPlayerCtrl) {
			auto handle = g_pPlayerCtrl->GetMGTHandle();
			if (m_ActorMGT->IsValidElementId(handle)) {
				if (handle.NodeIndex() == nodeIndex) {
					GizmosNS::SetGizmoColor(ColorRGBAf(0, 1.0f, 1.0f));
					enableDepth = false;
				}
			}
		}

		GizmosNS::DrawWireCube(aabb.m_Center, aabb.m_Extent * 2.0f, enableDepth);
		});
		OPTICK_TAG("PartiallyVis", PartiallyVisTreeNode);
		OPTICK_TAG("FullVis", FullVisTreeNode);
	}

	if (GetInputManager().GetKeyDown(SDLK_KP_MULTIPLY)) {
		m_ActorMGT->DumpStats();
	}
	// 渲染一下相机视锥
	//cam->OnEditorGizmoDraw();
// 	GizmosNS::SetGizmoColor(ColorRGBAf::white);
// 	GetGizmoManager().SetGizmoEnable(false);
#endif
}

void ClientActorMgr::resetLiveMobAttr()
{
	for (auto it = m_LiveActors.begin(); it != m_LiveActors.end(); ++it)
	{
		ClientMob *mob = dynamic_cast<ClientMob*>(it->second);
		
		if (mob)
		{
			MobAttrib* mobAttr = dynamic_cast<MobAttrib*>(mob->getAttrib());

			if (mobAttr)
			{
				mobAttr->resetDiffModeAttr();
				mob->setHPProgressDirty();
			}
		}
		
	}
}

void AddBossesToMapData(WorldMapData* mapdata, const WorldBossData& boss)
{
	for (size_t i = 0; i < mapdata->bosses.size(); i++)
	{
		if (mapdata->bosses[i].defid == boss.defid)
		{
			mapdata->bosses[i] = boss;
			return;
		}
	}
	mapdata->bosses.push_back(boss);
}

void ClientActorMgr::AddBossToMapData(WorldMapData* destdata)
{
	for (size_t i = 0; i < getNumBoss(); i++)
	{
		ActorBoss* boss = getBoss(i);
		if (boss == NULL) continue;
		WorldBossData bossdata;
		boss->saveBossData(&bossdata);

		AddBossesToMapData(destdata, bossdata);
	}
}

void ClientActorMgr::spawnPlayer(IClientPlayer* iplayer)
{
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(iplayer);
	spawnPlayerAddRef(player);
}

IClientMob* ClientActorMgr::CreateMobFromDef(int monsterid, int mobtype/* = 0*/, bool trigger/* = true*/, bool init/* = true*/)
{
	return ClientMob::createFromDef(monsterid, mobtype, trigger, init);
}


IClientMob* ClientActorMgr::iSpawnMob(const WCoord& pos, int monsterid, bool mobtype_check, bool mob_check, float yaw/* = -1*/, int mobtype/* = 0*/, std::string monsterName/* = ""*/, bool trigger /*= true*/)
{
	return dynamic_cast<IClientMob*>(spawnMob(pos, monsterid, mobtype_check, mob_check, yaw, mobtype, monsterName, trigger));
}

void ClientActorMgr::HandleDesertVillageBuildingNpc(std::vector<DesertVillageNpcInfo>& npcInfo, int mtype, const WCoord& villagePos, const WCoord& startPos)
{
	if (mtype == DesrtVillageBuildIngType_GatherPlace)
	{
		WCoord curPos;
		WCoord guardPos;
		WCoord camelPos;
		std::vector<WCoord> tradeBedPos;
		std::vector<WCoord> tradeWorkPos;
		std::vector<WCoord> guardeWorkPos;
		std::vector<float> workDir({ 180.f, -90.f, -90.f, -90.f });
		tradeBedPos.push_back(WCoord(9, 0, 4));
		tradeBedPos.push_back(WCoord(6, 0, 7));
		tradeBedPos.push_back(WCoord(6, 0, 9));
		tradeBedPos.push_back(WCoord(9, 0, 12));
		tradeWorkPos.push_back(WCoord(9, 0, 5));
		tradeWorkPos.push_back(WCoord(7, 0, 7));
		tradeWorkPos.push_back(WCoord(7, 0, 9));
		tradeWorkPos.push_back(WCoord(10, 0, 12));
		guardeWorkPos.push_back(WCoord(7, 0, 3));
		guardeWorkPos.push_back(WCoord(5, 0, 5));
		guardeWorkPos.push_back(WCoord(6, 0, 12));
		guardeWorkPos.push_back(WCoord(8, 0, 14));
		int curWorkIndex = 0;
		const int containerType[5] = { 1180,1181, 979, 974, 969 }; //圆角版本没有小箱子，改为横箱子和竖箱子 by：Jeff
		for (int i = 0; i < npcInfo.size() && curWorkIndex < tradeWorkPos.size(); )
		{
			int defid = npcInfo[i].defId;
			curPos.x = startPos.x + tradeWorkPos[curWorkIndex].x;
			curPos.z = startPos.z + tradeWorkPos[curWorkIndex].z;
			curPos.y = startPos.y + 1;
			ActorDesertBusInessMan* trade = dynamic_cast<ActorDesertBusInessMan*>(spawnMob(BlockBottomCenter(curPos), defid, false, false));
			if (!trade)
			{
				i++;
				assert(0);
				continue;
			}
			auto bedPos = tradeBedPos[curWorkIndex] + startPos + WCoord(0, 1, 0);
			if (defid == 3210 || defid == 3211 || defid == 3212)
			{
				//这里直接绑定，不要刷出来AI绑定,不然比较乱
				trade->setBedBindPos(bedPos);
				trade->setWorkPos(curPos);
				trade->setWorkYaw(workDir[curWorkIndex]);
				trade->initAIWorkPos();
				BedLogicHandle::setBindActor(m_World, bedPos, trade->getObjId()); //床标记绑定
				if (i + 1 < npcInfo.size() && npcInfo[i + 1].defId == 3213)
				{
					guardPos.x = startPos.x + guardeWorkPos[curWorkIndex].x;
					guardPos.z = startPos.z + guardeWorkPos[curWorkIndex].z;
					guardPos.y = startPos.y + 1;
					ActorDesertBusInessManGuard* guard = dynamic_cast<ActorDesertBusInessManGuard*>(spawnMob(BlockBottomCenter(guardPos), 3213, false, false));
					if (!guard)
					{
						assert(0);
					}
					else
					{
						trade->addGuard(guard->getObjId());
						trade->setExplorePos(curPos);
						guard->setFollowObjId(trade->getObjId());
						guard->setGatherPos(guardPos);
						guard->setWorkPos(guardPos);
						guard->setNightWorkPos(guardPos);
						guard->setWorkYaw(workDir[curWorkIndex]);
						guard->initAIWorkPos();
					}
					i++;
				}
				if (i + 1 < npcInfo.size() && npcInfo[i + 1].defId == 3823)
				{
					camelPos.x = npcInfo[i + 1].x;
					camelPos.z = npcInfo[i + 1].z;
					camelPos.y = startPos.y + 1;
					ActorPackHorse* camel = dynamic_cast<ActorPackHorse*>(spawnMob(BlockBottomCenter(camelPos), 3823, false, false));
					if (!camel)
					{
						assert(0);
					}
					else
					{
						camel->setMasterObjId(trade->getObjId());
						//camel->setAIActive(false);
						trade->setPickingCamel(camel->getObjId());
						int type = GenRandomInt(0, 4);
						camel->equipPack(containerType[type], 1);
						camel->equipPack(containerType[type], 0);
						camel->loadPack();
						camel->setGatherPos(camelPos);
						camel->getLocoMotion()->m_RotateYaw = 0;
					}
					i++;
				}
				//特殊商人还有一个可以出售的骆驼
				if (defid == 3212 && (i + 1 < npcInfo.size() && npcInfo[i + 1].defId == 3823))
				{
					camelPos.x = npcInfo[i + 1].x;
					camelPos.z = npcInfo[i + 1].z;
					camelPos.y = startPos.y + 1;
					ActorPackHorse* camel = dynamic_cast<ActorPackHorse*>(spawnMob(BlockBottomCenter(camelPos), 3823, false, false));
					if (!camel)
					{
						assert(0);
					}
					else
					{
						camel->setMasterObjId(trade->getObjId());
						//camel->setAIActive(false);
						camel->getLocoMotion()->m_RotateYaw = 0;
						trade->setDealCamel(camel->getObjId());
						camel->setGatherPos(camelPos);
					}
					i++;
				}
			}
			i++;
			curWorkIndex++;
		}
	}
	else
	{
		WCoord curPos;
		for (auto& info : npcInfo)
		{
			curPos.x = info.x + startPos.x;
			curPos.z = info.z + startPos.z;
			curPos.y = startPos.y + 1;
			ActorDesertVillager* mob = dynamic_cast<ActorDesertVillager*>(spawnMob(BlockBottomCenter(curPos), info.defId, false, false));
			if (!mob)
			{
				assert(0);
				continue;
			}
			mob->setVillagePoint(villagePos);
		}
	}
}

void ClientActorMgr::HandleFishingVillageNpc(const  WCoord& fishingmanPos, const  WCoord& wharfPos,
	const  WCoord& newWharfPos, std::map<WCoord, int>& spawnPos)
{
	ActorFisherMan* mob = dynamic_cast<ActorFisherMan*>(spawnMob(BlockCenterCoord(WCoord(fishingmanPos.x, fishingmanPos.y + 2, fishingmanPos.z)), 3229, false, false));
	if (mob)
	{
		mob->setVillagePoint(fishingmanPos);
		mob->setWharfPoint(wharfPos);
	}

	int defid[3][2] = {
		{3214, 3215},
		{3216, 3217},
		{3218, 3219}
	};
	WCoord offestPos[3] = { {0, 0, 0}, {1, 0, 0}, {0, 0, 1} };

	for (auto& blockinfo : spawnPos)
	{
		WCoord tmpPos(blockinfo.first.x, blockinfo.first.y, blockinfo.first.z);
		for (int j = 0; j < 3; j++)
		{
			WCoord pos = tmpPos + offestPos[j];
			int index = GenRandomInt(1, 100) <= 50 ? 0 : 1;
			int id = defid[j][index];
			ActorFishingVillager* mob = dynamic_cast<ActorFishingVillager*>(spawnMob(BlockCenterCoord(WCoord(pos.x, pos.y + 2, pos.z)), id, false, false));
			if (mob)
			{
				mob->setVillagePoint(pos);
				mob->setWharfPoint(newWharfPos);
			}
		}
	}
}

void ClientActorMgr::HandleIslandBuildNpc( int type, const  WCoord& startPos )
{
	if (type == (int)BigBuildCreater::BigBuildsId::IslandBuildCoralIsLandTreasureBox)
	{
		WCoord pos[5] = { {13, 1, 11}, {11, 1, 19}, {8, 1, 31}, {4, 1, 30}, {12, 1, 6} };
		int num = GenRandomInt(3, 5);
		//int num = 1;
		WCoord curPos;
		int defid;
		for (int i = 0; i < num; i++)
		{
			defid = GenRandomInt(1, 100) > 50 ? 3220 : 3221;
			curPos = startPos + pos[i];
			ActorPatrolMob* mob = dynamic_cast<ActorPatrolMob*>(spawnMob(BlockBottomCenter(curPos), defid, false, false));
			if (mob)
			{
				mob->setPatrolPos(curPos);
				mob->initAIWorkPos();
			}
		}

	}
	else if (type == (int)BigBuildCreater::BigBuildsId::IslandBuildIsLandPirateShop)
	{
		WCoord pos = startPos + WCoord(14, 4, 9);
		ActorIslandBusInessMan* mob = dynamic_cast<ActorIslandBusInessMan*>(spawnMob(BlockBottomCenter(pos), 3222, false, false));
		if (mob)
		{
			mob->setSalePos(pos);
			mob->initAIWorkPos();
		}
	}
	else if (type == (int)BigBuildCreater::BigBuildsId::IslandBuildIsLandShop)
	{
		WCoord pos = startPos + WCoord(11, 6, 13);
		ActorIslandBusInessMan* mob = dynamic_cast<ActorIslandBusInessMan*>(spawnMob(BlockBottomCenter(pos), 3223, false, false));
		if (mob)
		{
			mob->setSalePos(pos);
			mob->initAIWorkPos();
		}
	}
	else if (type == (int)BigBuildCreater::BigBuildsId::IsLandBuildDesertTreasureBox2)
	{
		WCoord pos[5] = { {12, 2, 5}, {7, 2, 11}, {6, 2, 17}, {0, 1, 13}, {15, 1, 2} };
		int num = GenRandomInt(3, 5);
		//int num = 1;
		WCoord curPos;
		int defid;
		for (int i = 0; i < num; i++)
		{
			defid = GenRandomInt(1, 100) > 50 ? 3220 : 3221;
			curPos = startPos + pos[i];
			ActorPatrolMob* mob = dynamic_cast<ActorPatrolMob*>(spawnMob(BlockBottomCenter(curPos), defid, false, false));
			if (mob)
			{
				mob->setPatrolPos(curPos);
				mob->initAIWorkPos();
			}
		}
	}
	//else if (type == (int)BigBuildCreater::BigBuildsId::IslandBuildOceanTreasureBox1)
	//{
	//	WCoord pos[5] = { {3, 5, 4}, {3, 5, 10}, {9, 5, 11}, {12, 4, 13}, {13, 5, 3} };
	//	int num = GenRandomInt(3, 5);
	//	//int num = 1;
	//	WCoord curPos;
	//	for (int i = 0; i < num; i++)
	//	{
	//		curPos = m_startPos + pos[i];
	//		pworld->getActorMgr()->spawnMob(BlockBottomCenter(curPos), 3224, false, false);
	//	}
	//}
	else if (type == (int)BigBuildCreater::BigBuildsId::IslandBuildOceanTreasureBox2)
	{
		bool genhostile = getMobGen(MOB_HOSTILE);
		if (genhostile)
		{
			WCoord pos[5] = { {6, 2, 1}, {5, 2, 3}, {14, 2, 6}, {9, 2, 15}, {5, 2, 15} };
			int num = GenRandomInt(3, 5);
			//int num = 1;
			WCoord curPos;
			for (int i = 0; i < num; i++)
			{
				curPos = startPos + pos[i];
				spawnMob(BlockBottomCenter(curPos), 3224, false, false);
			}
		}
	}
	//else if (type == (int)BigBuildCreater::BigBuildsId::IslandBuildOceanTreasureBox3)
	//{
	//	WCoord pos[5] = { {7, 8, 6}, {6, 8, 5}, {8, 7, 4}, {10, 6, 10}, {15, 2, 8} };
	//	int num = GenRandomInt(3, 5);
	//	//int num = 1;
	//	WCoord curPos;
	//	for (int i = 0; i < num; i++)
	//	{
	//		curPos = startPos + pos[i];
	//		spawnMob(BlockBottomCenter(curPos), 3224, false, false);
	//	}
	//}
}

void ClientActorMgr::HandleIcePlantBuildNpc(int type, const  WCoord& startPos)
{
	auto SpawnVillager = [this](World* pworld, int village_uid, int monsterid, WCoord pos)
	{
		pos.y += 3;
		auto mob = dynamic_cast<ActorVillager*>(spawnMob(BlockBottomCenter(pos), monsterid, false, false));
		if (!mob)
		{
			assert(0);
		}
		if (mob) {
			mob->setWorkBindPos(pos);
			mob->setTamedOwnerID(village_uid);
			GetWorldManagerPtr()->getWorldInfoManager()->addNewVillager(village_uid, mob->getObjId());

			std::vector<WCoord> WildTotem = GetWorldManagerPtr()->getWorldInfoManager()->getAllVillageTotems(0);
			if (WildTotem.size() > 0)
			{
				for (size_t i = 0; i < WildTotem.size(); i++)
				{
					if (pos.distanceTo(WildTotem[i]) <= 64)
					{
						BlockVillageTotemIce::AddVillage(pworld, WildTotem[i], mob->getObjId());
					}
				}
			}
		}
		return mob;
	};

	//MapDataBuildType_IceBuildCenter = 1,		//冰原建筑中心
	//MapDataBuildType_IceBuildLoggCamp,		//伐木场
	//MapDataBuildType_IceBuildHunt,			//狩猎小屋
	//MapDataBuildType_IceBuildResidence,     //住宅小屋
	//MapDataBuildType_IceBuildVillageAltar,  //村庄祭坛
	//MapDataBuildType_IceBuildTower,			//箭塔
	//MapDataBuildType_IceBuildLoggerPoint,   //伐木指示点
//村庄信息
	int village_uid = 0;
	if ((int)MapDataBuildType_IceBuildHunt == type)//狩猎小屋
	{
		for (int i = 0; i < 2; i++)
		{
			SpawnVillager(m_World, village_uid, 3236, startPos);
		}
	}
	else if ((int)MapDataBuildType_IceBuildResidence == type)//住宅小屋
	{
		vector<ActorVillager*> VillageVector;
		VillageVector.clear();
		for (int i = 0; i < 2; i++)
		{
			ActorVillager* mob = SpawnVillager(m_World, village_uid, 3238, startPos);
			VillageVector.push_back(mob);
		}
		WCoord pos = startPos;
		WCoord offset(15, 1, 15);
		WCoord containerPos = BlockVillagerFlagBuilding::GetContainerPos(m_World, pos + offset);
		VillageFlagBuildingContainer* container = dynamic_cast<VillageFlagBuildingContainer*>(m_World->getContainerMgr()->getContainer(containerPos));
		if (container == NULL)
		{
			container = ENG_NEW(VillageFlagBuildingContainer)(containerPos);
			m_World->getContainerMgr()->spawnContainer(container);
		}
		for (size_t i = 0; i < VillageVector.size(); i++)
		{
			VillageVector[i]->setWorkBindPos(containerPos);
		}
		if (container)
		{
			int bluePrintID = GenRandomInt(11104, 11109);
			std::string userDataStr = "";
			BluePrint* blueprint = nullptr;
			BluePrintMgr* bluePrintModule = GET_SUB_SYSTEM(BluePrintMgr);
			if (g_WorldMgr && bluePrintModule)
			{
				blueprint = bluePrintModule->getBuilderBP(bluePrintID);
				if (blueprint == nullptr)
				{
					return;
				}
				blueprint->getGridUserdataStr(userDataStr);
			}
			else
			{
				return;
			}
			BluePrint* BluePrintObj = container->SetContainerData(userDataStr, 0);
			if (!BluePrintObj)
			{
				return;
			}
			container->SetBluePrint(BluePrintObj);
			WCoord startPos = BlockVillagerFlagBuilding::GetContainerPos(m_World, pos + offset);
			WCoord dim = container->GetDim();
			int rotateType = container->ChangeDim(1, startPos, dim);
			container->SetDim(dim);
			container->SetStartPos(startPos);
			container->SetRotateType(rotateType);
			std::vector<PreBlocksData> PreBlocks;
			BluePrintObj->preBuildBluePrint(m_World, startPos, PreBlocks, rotateType, true);
			container->SetPreBlocks(PreBlocks);
			container->AttatchMesh(PreBlocks);
			container->InitBuildBlocksData(m_World, startPos, rotateType);
			container->SetLoaded(true);
			container->SetStartBuild(true);
			container->SetGrid(true);
			GetWorldManagerPtr()->getWorldInfoManager()->addNewVillageFlag(0, 150001, BlockVillagerFlagBuilding::GetContainerPos(m_World, pos + offset));
		}
	}
	else if ((int)MapDataBuildType_IceBuildLoggerPoint == type)//伐木指示点
	{
		for (int i = 0; i < 2; i++)
		{
			SpawnVillager(m_World, village_uid, 3237, startPos);
		}
	}
	else if ((int)MapDataBuildType_IceBuildVillageAltar == type)//村庄祭坛
	{
		//int village_uid = 0;
		//GetWorldManagerPtr()->getWorldInfoManager()->addNewVillageTotem(village_uid, m_startPos);
		//GetWorldManagerPtr()->getWorldInfoManager()->setVillagePoint(village_uid, m_startPos);
		for (int i = 0; i < 2; i++)
		{
			SpawnVillager(m_World, village_uid, 3239, startPos);
			//SpawnVillager(pworld, village_uid, 3235, m_startPos);
		}
		WCoord VillageTotemOffset = { 8,3,8 };
		VillageTotemIceContainer* container = ENG_NEW(VillageTotemIceContainer)(BlockVillageTotemIce::GetRealTotemIceContainer(m_World, startPos + VillageTotemOffset));
		m_World->getContainerMgr()->spawnContainer(container);
		container->UpdateEffect(m_World);

	}
}

void ClientActorMgr::HandleIceAltarBuildNpc(int type, const  WCoord& startPos, VoxelModel* BigBuildVM)
{
	if (type == (int)MapDataBuildType_IceBuildAltar)
	{
		//创建的BOSS。
		//
		int mobId = 3517;
		auto grid = startPos + BigBuildVM->getDim() / 2;
		auto pos = BlockBottomCenter(grid);
		auto mob = dynamic_cast<ActorFrostWyrm*>(spawnMob(pos, mobId, false, false));
		if (mob)
		{
			//这里需要加2。
			int x = (int)startPos.x / CHUNK_BLOCK_X + 2;
			int z = (int)startPos.z / CHUNK_BLOCK_Z + 2;
			mob->setChunkIndex(ChunkIndex(x, z));

			int posy = m_World->getTopHeight(startPos.x, startPos.z);
			mob->setSpawnPoint(WCoord(pos.x, posy, pos.z));
		}
	}
}

bool ClientActorMgr::HandleEcosysUnitDesertVillageNpc(std::vector<IClientActor*>& nearMobs, const WCoord& gatherPos, const WCoord& gatherCenterPos)
{
	bool needSpawnBussinessMan = true;
	if (nearMobs.size() > 0)
	{
		ActorDesertBusInessMan* mob = NULL;
		ActorDesertBusInessManGuard* guard = NULL;
		ActorPackHorse* horse = NULL;
		for (int i = 0; i < nearMobs.size(); i++)
		{
			if (nearMobs[i]->getDefID() == 3823)
			{
				horse = dynamic_cast<ActorPackHorse*>(nearMobs[i]);
				if (horse != NULL && !GetDesertTradeCaravanMgr().checkPos(horse->getGatherPos(), gatherPos))
				{
					horse->setFollowType(ActorHorseFollowDesertBussinessMan_None);
					horse->setGatherPos(WCoord(0, 0, 0));
				}
			}
			else if (nearMobs[i]->getObjType() == OBJ_TYPE_DESERTBUSINESSMANGUARD)
			{
				guard = dynamic_cast<ActorDesertBusInessManGuard*>(nearMobs[i]);
				if (guard != NULL && !GetDesertTradeCaravanMgr().checkPos(guard->getGatherPos(), gatherPos))
				{
					guard->setBehaviorType(ActorDesertBusInessManGuardBehaviorType_Follow);
					if (!guard->isAvoidIngStand())
					{
						guard->ResetBTree();
					}
				}
			}
			else if (nearMobs[i]->getObjType() == OBJ_TYPE_DESERTBUSINESSMAN)
			{
				//附近一般只会有一个商队
				mob = dynamic_cast<ActorDesertBusInessMan*>(nearMobs[i]);
				if (mob != NULL && !GetDesertTradeCaravanMgr().checkPos(mob->getExplorePos(), gatherPos))
				{
					needSpawnBussinessMan = false;
					if (mob->isLeader())
					{
						GetDesertTradeCaravanMgr().removeTradeCaravan(mob->getExplorePos(), false);
						mob->setExplorePos(gatherCenterPos);
						mob->setExploreType(ActorDesertBusInessManExplore_NeedToVillageOnce, false);
						if (!mob->isAvoidIngStand())
						{
							mob->ResetBTree();
						}
					}
					else
					{
						mob->setExploreType(ActorDesertBusInessManExplore_FollowToVillageOnce, false);
						if (!mob->isAvoidIngStand())
						{
							mob->ResetBTree();
						}
					}
				}
			}
		}
	}

	return needSpawnBussinessMan;
	
	//for (int i = 0; i < 9; i++)
	//{
	//	int j = 0;
	//	curTrunkx = trunkx + dir[i][0];
	//	curTrunkz = trunkz + dir[i][1];
	//	for (; j < 9; j++)
	//	{
	//		ck = wrold->getChunkBySCoord(curTrunkx + dir[j][0], curTrunkz + dir[j][1]);
	//		if (ck == NULL)
	//		{
	//			break;
	//		}
	//	}
	//	//周围trunk都存在, 就采样
	//	if (j >= 9)
	//	{
	//		int blockCenterX = curTrunkx * SECTION_BLOCK_DIM + SECTION_BLOCK_DIM / 2;
	//		int blockCenterZ = curTrunkz * SECTION_BLOCK_DIM + SECTION_BLOCK_DIM / 2;
	//		int curBlockX = 0;
	//		int curBlockZ = 0;
	//		int biomeid = 0;
	//		int m = 0;
	//		bool have = false;
	//		WCoord havePos;
	//		int startblockX = 0;
	//		int startblockZ = 0;
	//		int startTrunkX = 0;
	//		int startTrunkZ = 0;
	//		for (; m < 9; m++)
	//		{
	//			curBlockX = blockCenterX + sampling[m][0];
	//			curBlockZ = blockCenterZ + sampling[m][1];
	//			startTrunkX = curTrunkx + dir[m][0];
	//			startTrunkZ = curTrunkz + dir[m][1];
	//			startblockX = startTrunkX * SECTION_BLOCK_DIM;
	//			startblockZ = startTrunkZ * SECTION_BLOCK_DIM;
	//			curChunk = wrold->getChunkBySCoord(startTrunkX, startTrunkZ);
	//			biomeid = curChunk->getBiomeID(curBlockX - startblockX, curBlockZ - startblockZ);
	//			if (biomeid == BIOME_DESERT_LAKE || biomeid == BIOME_DESERT_LAKE)
	//			{
	//				break;
	//			}
	//			if (biomeid == BIOME_DESERT_OASIS /*&& sampling[m][0] == 0 && sampling[m][1] == 0*/)
	//			{
	//				have = true;
	//				havePos.x = curBlockX;
	//				havePos.y = 0;
	//				havePos.z = curBlockZ;
	//			}
	//		}
	//		if (m >= 9 && have)
	//		{
	//			WCoord spawnPos(blockCenterX, pos.y, blockCenterZ);
	//			WCoord endPos;
	//			//if (m_Modelgen->addToWorld(pworld, randgen, calculateVillagePos(spawnPos, pworld), false))
	//			//{
	//			//	/*for (int f = 0; f < 9; f++)
	//			//	{
	//			//		curChunk = wrold->getChunkBySCoord(curTrunkx + dir[f][0], curTrunkz + dir[f][1]);
	//			//	}*/
	//			//	chunks.push_back(ChunkIndex(curTrunkx, curTrunkz));

	//			//	endPos.x = blockCenterX - 13;
	//			//	endPos.z = blockCenterZ - 19;
	//			//	endPos.y = pworld->getTopHeight(blockCenterX -13, blockCenterZ - 19) + 1;
	//			//	auto p = GetDesertTradeCaravanMgr().findVillageCaravan(havePos);
	//			//	if (p != NULL)
	//			//	{
	//			//		p->workPos = endPos;
	//			//	}
	//			//	std::vector<IClientActor*> nearMobs;
	//			//	wrold->findNearActors(nearMobs, WCoord(blockCenterX * BLOCK_SIZE, 0, blockCenterZ * BLOCK_SIZE), 2, OBJ_TYPE_DESERTBUSINESSMAN);
	//			//	bool needSpawnBussinessMan = true;
	//			//	if (nearMobs.size() > 0)
	//			//	{
	//			//		ActorDesertBusInessMan* mob = NULL;
	//			//		for (int i = 0; i < nearMobs.size(); i++)
	//			//		{
	//			//			//附近一般只会有一个商队
	//			//			mob = (ActorDesertBusInessMan*)(nearMobs[i]);
	//			//			if (!GetDesertTradeCaravanMgr().checkPos(mob->getExplorePos(), havePos))
	//			//			{
	//			//				needSpawnBussinessMan = false;
	//			//				if (mob->isLeader())
	//			//				{
	//			//					GetDesertTradeCaravanMgr().removeTradeCaravan(mob->getExplorePos(), false);
	//			//				}
	//			//				mob->setExplorePos(endPos);
	//			//				mob->setExploreType(ActorDesertBusInessManExplore_NeedToVillageOnce, false);
	//			//				mob->ResetBTree();
	//			//			}
	//			//		}
	//			//	}
	//			//	spawnNpc(spawnPos, pworld, needSpawnBussinessMan);
	//			//	return true;
	//			//}
	//		}
	//	}
	//}
}

void ClientActorMgr::HandleBlockDragonEggBoss(int resid, int curtool)
{
	if (getNumBoss() == 0)
	{
		int mid = 0;
		int dlgid = 0;
		int bossid = -1;

		if (resid == BLOCK_ANCIENT_EGG && curtool == ITEM_ANCIENT_HORN)
		{
			mid = 1;
			dlgid = 1000;
			bossid = 3502;
		}
		else if (resid == BLOCK_LAVA_STONE || (resid == BLOCK_ANCIENT_EGG && curtool == ITEM_LAVA_HORN))
		{
			mid = 3;
			dlgid = 2016;
			bossid = 3503;
		}
		else if (resid == BLOCK_CHAOS_STONE || (resid == BLOCK_ANCIENT_EGG && curtool == ITEM_CHAOS_HORN))
		{
			mid = 5;
			dlgid = 2019;
			bossid = 3504;

		}

		if (bossid > 0)
		{
			ActorBoss* boss = dynamic_cast<ActorBoss*>(m_World->getChunkProvider()->createBoss(bossid));
			if (boss)
			{
				boss->addMissionFlags(1 << (mid - 1));
				//ge GetGameEventQue().postMissionComplete(mid);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("id", mid);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_MISSION_COMPLETE", sandboxContext);
				//ge GetGameEventQue().postGameDialogue(dlgid);
				const char* info = GetDefManagerProxy()->getStringDef(dlgid);
				MNSandbox::SandboxContext sandboxContext1 = MNSandbox::SandboxContext(nullptr).
					SetData_String("info", info);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_GAME_DIALOGUE", sandboxContext1);
			}
		}
	}
}

bool ClientActorMgr::IsActorHorse(IClientActor* actor)
{
	return dynamic_cast<ActorHorse*>(actor) ? true : false;
}
bool ClientActorMgr::IsActorBasketBall(IClientActor* actor)
{
	return dynamic_cast<ActorBasketBall*>(actor) ? true : false;
}
void ClientActorMgr::SpawnPlayerAddRef(IClientPlayer* player, bool isTeleport/* = false*/)
{
	spawnPlayerAddRef(static_cast<ClientPlayer*>(player), isTeleport);
}
void ClientActorMgr::BlockBedForPlayer(const  WCoord& blockpos)
{
	ClientPlayer* player = getOccupiedPlayer(blockpos, ACTORFLAG_SLEEP);
	if (!player)
	{
		return;
	}
	if (player && m_World->getBiomeType(blockpos.x, blockpos.z) == BIOME_DESERT)
	{
		//起床
		player->wakeUp(true, true, false);
		player->getLivingAttrib()->addBuff(VIGILANCE_BUFF, 1);
	}
}
void ClientActorMgr::BlockBedBindActor(const  WCoord& blockpos, WORLD_ID bindactor, WorldBed* container)
{
	ClientActor* actor = findActorByWID(bindactor);
	if (actor && actor->IsKindOf<ActorTravelingTrader>())
		container->setBedStatus(ENUM_BED_STATUS_TRADER_IN_HOME);
	else
		container->setBedStatus(ENUM_BED_STATUS_NORMAL);

	//世界也记一份
	if (GetWorldManagerPtr())
	{
		ActorVillager* mob = dynamic_cast<ActorVillager*>(actor);
		if (mob && mob->getTamedOwnerID())
		{
			GetWorldManagerPtr()->getWorldInfoManager()->changeVillageBedRelationship(mob->getTamedOwnerID(), bindactor, blockpos, ENUM_BED_STATUS_NORMAL);
		}
	}
}
void ClientActorMgr::BlockBedBodyShow(const  WCoord& blockpos, bool occupied)
{
	//为了避免部分大的模型穿透,睡觉的时候 直接隐藏
	auto actor = getOccupiedPlayer(blockpos, ACTORFLAG_SLEEP);
	if (actor && actor->getBody())
	{
		actor->getBody()->show(!occupied);
	}
}
IClientItem* ClientActorMgr::SpawnIClientItem(const WCoord& worldpos, int itemid, int num, int protodata/* = 0*/, long long objid/* = 0*/)
{
	return spawnItem(worldpos, itemid, num, protodata, objid);
}

IClientItem* ClientActorMgr::SpawnIClientItem(const WCoord& worldpos, const BackPackGrid& grid, int protodata/* = 0*/, long long objid/* = 0*/)
{
	return spawnItem(worldpos, grid, protodata, objid);
}
bool ClientActorMgr::HandleBlockButtonActorCollide(int blockid, const WCoord& blockpos, int& contactActorID, bool includeVehicle, CollisionDetect& cd)
{
	CollideAABB box;
	if (m_World->isVehicleWorld())
	{
		//载具上无法检测包围盒内的生物，只能记录最后一次发生触碰的生物，用于判断这个生物离开
		if (contactActorID != 0)
		{
			ClientActor* actor = findActorByWID(contactActorID);
			if (actor)
			{
				VehicleWorld* pVehicleWorld = dynamic_cast<VehicleWorld*>(m_World);
				if (pVehicleWorld)
				{
					ActorVehicleAssemble* pActorVehicleAssemble = pVehicleWorld->getActorVehicleAssemble();
					if (pActorVehicleAssemble)
					{
						int blockid, x, y, z;
						actor->getLocoMotion()->getCollideBox(box);
						box.expand(-1, -1, -1);

						bool bRes = pActorVehicleAssemble->intersectBox(box, blockid, x, y, z);
						if (bRes)
						{
							blockid = pVehicleWorld->getBlockID(x, y, z);
							if (blockpos == WCoord(x, y, z))
								return true;
							else
							{
								contactActorID = 0;
								return false;
							}


						}
						else
						{
							contactActorID = 0;
							return false;
						}
					}
				}
			}
			else
			{
				contactActorID = 0;
				return false;
			}
		}
		return false;
	}
	else
	{
		

		CollideAABB box;
		box.pos = cd.getCollideMin();
		box.dim = cd.getCollideMax() - box.pos;

		std::vector<IClientActor*>actors;
		// 这里获取的actor 要包含物理机械ActorVehicleAssemble
		m_World->getActorsInBox(actors, box, 1000, includeVehicle);

		for (size_t i = 0; i < actors.size(); i++)
		{
			// 这里判断是否为物理actor
			if (actors[i]->getILocoMotion())
			{
				ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actors[i]);

				if (vehicle)
				{
					MINIW::WorldRay ray;
					Rainbow::WorldPos pos;

					int blockdata = m_World->getBlockData(blockpos);
					int dir_value = blockdata & 3;
					// 正北
					int offsetX = BLOCK_SIZE / 2;
					int offsetZ = BLOCK_SIZE / 2;
					WCoord coord = BlockCenterCoord(blockpos);
					pos = coord.toWorldPos();
					if (dir_value == 2)
					{
						coord.z -= BLOCK_SIZE / 2;
						ray.m_Dir.x = 0.0f;
						ray.m_Dir.y = 0.0f;
						ray.m_Dir.z = 1.0f;
						offsetZ = BLOCK_SIZE;
					}
					else if (dir_value == 3)
					{
						coord.z += BLOCK_SIZE / 2;

						ray.m_Dir.x = 0.0f;
						ray.m_Dir.y = 0.0f;
						ray.m_Dir.z = -1.0f;
						offsetX = BLOCK_SIZE;
					}
					else if (dir_value == 1)
					{
						coord.x += BLOCK_SIZE / 2;
						ray.m_Dir.x = -1.0f;
						ray.m_Dir.y = 0.0f;
						ray.m_Dir.z = 0.0f;
						offsetZ = BLOCK_SIZE;
					}
					else if (dir_value == 0)
					{
						coord.x -= BLOCK_SIZE / 2;
						ray.m_Dir.x = 1.0f;
						ray.m_Dir.y = 0.0f;
						ray.m_Dir.z = 0.0f;

						offsetX = BLOCK_SIZE;
					}
					else
					{
						coord.z -= BLOCK_SIZE / 2;
						ray.m_Dir.x = 0.0f;
						ray.m_Dir.y = 0.0f;
						ray.m_Dir.z = 1.0f;
						offsetZ = BLOCK_SIZE;
					}
					ray.m_Origin = pos;
					ray.m_Range = 5 * BLOCK_FSIZE;
					int blockID = 0;
					float t = 0;
					int x = -1, y = -1, z = -1;
					if (vehicle->intersect(ray, t, blockID, x, y, z))
					{
						WCoord vehicleContact = vehicle->convertWcoord(WCoord(x, y, z));
						bool isIn = true;
						if (vehicleContact.x < (blockpos.x * BLOCK_SIZE - 2) || vehicleContact.x >(blockpos.x * BLOCK_SIZE + offsetX)
							|| vehicleContact.y < (blockpos.y * BLOCK_SIZE - 2) || vehicleContact.y >(blockpos.y * BLOCK_SIZE + BLOCK_SIZE / 2)
							|| vehicleContact.z < (blockpos.z * BLOCK_SIZE - 2) || vehicleContact.z >(blockpos.z * BLOCK_SIZE + offsetZ))
						{
							isIn = false;
						}
						if ((BLOCK_FRONT_LEFT_WHEEL == blockID ||
							BLOCK_FRONT_RIGHT_WHEEL == blockID ||
							BLOCK_REAR_LEFT_WHEEL == blockID ||
							BLOCK_REAR_RIGHT_WHEEL == blockID) && t < 100)
						{
							return true;
						}
						else if (blockID > 0 && isIn)
						{
							return true;
						}
					}
				}
				else
				{
					PhysicsLocoMotion* physxMotion = dynamic_cast<PhysicsLocoMotion*>(actors[i]->getILocoMotion());
					if (physxMotion && physxMotion->m_PhysActor)
					{
						return true;
					}
					int actortype = actors[i]->getObjType();
					if (actortype == OBJ_TYPE_MONSTER || actortype == OBJ_TYPE_ROLE || actortype == OBJ_TYPE_MINECART || actortype == OBJ_TYPE_ARROW || actortype == OBJ_TYPE_COBBLE)
					{
						return true;
					}
				}
			}
		}
	}
	return false;
}

void ClientActorMgr::CheckLargeGameObject(ClientActor* pActor)
{
	DeleteLargeGameObject(pActor);

	Rainbow::AABB aabbMerge = pActor->GetMergeAABB();
	pActor->SetMergeAABB(aabbMerge);

	Vector3f& extent = aabbMerge.GetExtent();
	if (extent.x < CHUNK_SIZE_X / 4 && extent.z < CHUNK_SIZE_Z / 4)
	{
		return;
	}

	Vector3f& center = aabbMerge.GetCenter();
	int startX = CoordDivSection(center.x - extent.x);
	int endX = CoordDivSection(center.x + extent.x);
	int startZ = CoordDivSection(center.z - extent.z);
	int endZ = CoordDivSection(center.z + extent.z);

	std::set<CHUNK_INDEX> chunkList;
	for (int x = startX; x <= endX; x++)
	{
		for (int z = startZ; z <= endZ; z++)
		{
			chunkList.insert(ChunkIndex(x, z));
		}
	}
	if (chunkList.size() <= 1)
	{
		return;
	}

	WORLD_ID objID = pActor->getObjId();

	m_largeActorChunk[objID] = chunkList;
	for (auto chunkIdx : chunkList)
	{
		auto iter = m_chunkLargeActor.find(chunkIdx);
		if (iter != m_chunkLargeActor.end())
		{
			iter->second.insert(objID);
		}
		else
		{
			m_chunkLargeActor[chunkIdx].insert(objID);
		}
	}

	return;
}

void ClientActorMgr::DeleteLargeGameObject(ClientActor* pActor)
{
	WORLD_ID objID = pActor->getObjId();

	auto iter = m_largeActorChunk.find(objID);
	if (iter != m_largeActorChunk.end())
	{
		std::vector<CHUNK_INDEX> deleteChunkList;
		std::set<CHUNK_INDEX>& chunkList = iter->second;
		for (auto chunkIdx : chunkList)
		{
			auto iter1 = m_chunkLargeActor.find(chunkIdx);
			if (iter1 != m_chunkLargeActor.end())
			{
				std::set <WORLD_ID>& actorList = iter1->second;
				auto iter2 = actorList.find(objID);
				if (iter2 != actorList.end())
				{
					actorList.erase(iter2);

					if (actorList.size() == 0)
					{
						deleteChunkList.push_back(chunkIdx);
					}
				}
			}
		}

		for (auto chunkIdx : deleteChunkList)
		{
			m_chunkLargeActor.erase(chunkIdx);
		}

		m_largeActorChunk.erase(iter);
	}

	return;
}

bool ClientActorMgr::CanRemoveChunk(CHUNK_INDEX& chunkIdx)
{
	bool ret = true;
	auto iter = m_chunkLargeActor.find(chunkIdx);
	if (iter != m_chunkLargeActor.end())
	{
		std::set <WORLD_ID>& actorList = iter->second;
		for (auto objID : actorList)
		{
			auto iter1 = m_largeActorChunk.find(objID);
			if (iter1 != m_largeActorChunk.end())
			{
				std::set<CHUNK_INDEX>& chunkList = iter1->second;
				for (auto chunkIdx1 : chunkList)
				{
					if (m_World && !m_World->isPrepareRemoveChunk(chunkIdx1))
					{
						ret = false;
						break;
					}
				}
			}

			if (!ret) break;
		}
	}

	return ret;
}

bool ClientActorMgr::addMobPreset(int blockId, int X, int Y, int Z)
{
    MobPresetInfo mob;
	mob.mobId = 0;
	mob.pos.x = X;
	mob.pos.y = Y;
	mob.pos.z = Z;
    mob.blockId = blockId;
	mob.posKey = ComputeWCoordHash(mob.pos);
	ChunkIndex cidx(CoordDivBlock(mob.pos.x) >> 4, CoordDivBlock(mob.pos.z) >> 4);
	auto& plist = m_mMobPresetMap[cidx];
	plist[mob.pos] = mob;
	return true;
}

bool ClientActorMgr::saveMobPresetsToJson(const std::string& filePath)
{
    std::string targetPath = filePath.empty() ? m_mobPresetConfigPath : filePath;
    if (targetPath.empty()) {
        CLIENTACTORMGR_LOG("No file path specified for saving mob preset config");
        return false;
    }
    
    try {
        nlohmann::json jsonData;
        nlohmann::json mobsArray = nlohmann::json::array();
	
        int mobPresetCount = 0;
        for (const auto& plist : m_mMobPresetMap) {
			for (const auto& mob : plist.second) {
				nlohmann::json mobJson;
				mobJson["blockId"] = mob.second.blockId;
				mobJson["X"] = mob.second.pos.x;
				mobJson["Y"] = mob.second.pos.y;
				mobJson["Z"] = mob.second.pos.z;

				mobsArray.push_back(mobJson);
				mobPresetCount++;
			}
        }
        
        jsonData["mobs"] = mobsArray;
        
        std::string jsonStr = jsonData.dump(4);
        
        bool success = Rainbow::GetFileManager().SaveFile(targetPath, jsonStr.c_str(), jsonStr.size());
        if (!success) {
            CLIENTACTORMGR_LOG("Failed to save file: %s", targetPath.c_str());
            return false;
        }
        
        CLIENTACTORMGR_LOG("Saved %d mob presets to %s", mobPresetCount, targetPath.c_str());
        return true;
    }
    catch (const std::exception& e) {
        CLIENTACTORMGR_LOG("Error saving mob preset config: %s", e.what());
        return false;
    }
}

bool ClientActorMgr::initMobPresetsInfo()
{
    dynamic_array<UInt8> fileData;
    bool fileExists = Rainbow::GetFileManager().LoadFullPathFileAsBinary(m_mobPresetConfigPath.c_str(), fileData);
    
    if (!fileExists || fileData.empty()) {
        CLIENTACTORMGR_LOG("Failed to open mob preset config file: %s", m_mobPresetConfigPath.c_str());
        return false;
    }
    
    try {
        std::string jsonStr(reinterpret_cast<const char*>(fileData.data()), fileData.size());
        nlohmann::json jsonData = nlohmann::json::parse(jsonStr);
        
        int mobPresetCount = 0;
        if (jsonData.contains("mobs") && jsonData["mobs"].is_array()) {
            
            for (const auto& item : jsonData["mobs"]) {
                MobPresetInfo mob;
                mob.blockId = item.contains("blockId") ? item["blockId"].get<int>() : 0;
				mob.mobId = 0;
                mob.pos.x = item.contains("X") ? item["X"].get<int>() : 0;
                mob.pos.y = item.contains("Y") ? item["Y"].get<int>() : 0;
                mob.pos.z = item.contains("Z") ? item["Z"].get<int>() : 0;
				mob.posKey = ComputeWCoordHash(mob.pos);
				ChunkIndex cidx(CoordDivBlock(mob.pos.x) >> 4, CoordDivBlock(mob.pos.z) >> 4);
				auto& plist = m_mMobPresetMap[cidx];
				plist[mob.pos] = mob;
				
				// init spawn info
				MobPresetSpawnInfo spawnInfo;
				spawnInfo.presetPosKey = mob.posKey;
				spawnInfo.liftState = 0;
				spawnInfo.createDay = 0;
				m_MobPresetGenerated[mob.posKey] = spawnInfo;
                // CHESTMGR_LOG("Loaded building chest at position %d, %d, %d, blockId: %d", chest.pos.x, chest.pos.y, chest.pos.z, chest.blockId);
            }
        }
        
        return true;
    }
    catch (const std::exception& e) {
        CLIENTACTORMGR_LOG("Error parsing mob preset config file: %s", e.what());
        return false;
    }
}

IClientPlayer* ClientActorMgr::iGetIthPlayer(int i)
{
	return getIthPlayer(i);
}

IClientPlayer* ClientActorMgr::iFindPlayerByUin(int uin)
{
	return findPlayerByUin(uin);
}

//IClientPlayer* ClientActorMgr::iFindPlayerByUid(const std::string& uid)
//{
//	return findPlayerByUid(uid);
//}

IClientPlayer* ClientActorMgr::IGetOccupiedPlayer(const WCoord& blockpos, int flag)
{
	return getOccupiedPlayer(blockpos, flag);
}

IClientPlayer* ClientActorMgr::selectNearIPlayer(const WCoord& pos, int range)
{
	return selectNearPlayer(pos, range);
}

bool ClientActorMgr::SelectNearActors(BoundaryBoxGeometry& box, int maxRange, IClientActor* actorMain)
{
	ClientActor* horse = NULL;
	bool find = false;
	if (actorMain && actorMain->GetActor()->getRiddenComponent())
	{
		horse = actorMain->GetActor()->getRiddenComponent()->getRidingActor();
	}
	selectNearActors(box.center(), maxRange, [&actorMain, &horse](ClientActor* actor) {
		return actor != actorMain && actor != horse;
		}, [&find, &box](ClientActor* actor) {
			if (!find && CombatCheckExtend::checkActorBoxCollide(actor, box))
			{
				find = true;
			}
		});
	return find;
}

void ClientActorMgr::SelectNearbyActor(BoundaryBoxGeometry& box, int maxRange, std::vector<IClientActor*>& actors, IClientActor* actorMain)
{
	ClientActor* horse = NULL;
	if (actorMain && actorMain->GetActor()->getRiddenComponent())
	{
		horse = actorMain->GetActor()->getRiddenComponent()->getRidingActor();
	}
	selectNearActors(box.center(), maxRange, [&actorMain, &horse](ClientActor* actor) {
		return actor != actorMain && actor != horse;
		}, [&actors, &box](ClientActor* actor) {
			if (CombatCheckExtend::checkActorBoxCollide(actor, box))
			{
				actors.emplace_back(actor);
			}
		});
}
void ClientActorMgr::SelectNeighborMobs(const BoundaryGeometryHolder& holder, const WCoord& centerPos, int exrange, std::vector<IClientActor*>& mobs, IClientActor* actor)
{
	ClientActor* horse = NULL;
	if (actor && actor->GetActor()->getRiddenComponent())
	{
		horse = actor->GetActor()->getRiddenComponent()->getRidingActor();
	}
	selectNearActors(centerPos, exrange, [&actor, &horse](ClientActor* tactor) {
		return tactor != actor && tactor != horse;
		}, [&mobs, &holder](ClientActor* actor) {
			if (CombatCheckExtend::checkActorBoxCollide(actor, holder))
			{
				mobs.emplace_back(actor);
			}
		});
}
void ClientActorMgr::DespawnClientActor(IClientActor* actor, bool isTeleport/* = false*/)
{
	despawnActor(static_cast<ClientActor*>(actor), isTeleport);
}

ClientActorMgr* ClientActorMgr::ToCastMgr()
{
	return this;
}

IClientMob* ClientActorMgr::iFindMobByServerID(const std::string& serverid)
{
	return findMobByServerID(serverid);
}
IClientMob* ClientActorMgr::iFindMobByWID(long long wid)
{
	return dynamic_cast<IClientMob*>(findMobByWID(wid));
}
IClientActor* ClientActorMgr::iFindActorByWID(long long wid) 
{
	return findActorByWID(wid);
}
#ifdef IWORLD_DEV_BUILD
const int CANVASSIZE = 512;
const int MAXCHUNK = 32;
inline int worldX2canvas(float x, const Rainbow::RectInt& canvas) {
    return canvas.x + canvas.width / 2 + float(x / CHUNK_SIZE_X) * (CANVASSIZE / MAXCHUNK);
}
inline int worldZ2canvas(float z, const Rainbow::RectInt& canvas) {
    return canvas.y + canvas.height / 2 + float(z / CHUNK_SIZE_Z) * (CANVASSIZE / MAXCHUNK);
}
inline void world2canvas(float x_world, float z_world, int& out_x, int& out_y, const Rainbow::RectInt& canvas) {
    out_x = worldX2canvas(x_world, canvas);
    out_y = worldZ2canvas(z_world, canvas);
}
inline Rainbow::RectInt world2canvas(float rect_upperleft_x_world, float rect_upperleft_z_world, float w_world, float h_world, const Rainbow::RectInt& canvas) {
    int x, y;
    world2canvas(rect_upperleft_x_world, rect_upperleft_z_world, x, y, canvas);
    int w = float(w_world / CHUNK_SIZE_X) * (CANVASSIZE / MAXCHUNK);
    int h = float(h_world / CHUNK_SIZE_Z) * (CANVASSIZE / MAXCHUNK);
    return Rainbow::RectInt(x, y, w, h);
}
//inline void drawChunkArea(Rainbow::UILib::UIRenderer& display, const ChunkPos& chunkPos, const Rainbow::Vector3f& worldCenter, const Rainbow::RectInt& displayArea, const Rainbow::ColorRGBA32& clr) {
//    display.DrawRect(world2canvas(chunkPos.x * CHUNK_SIZE_X - worldCenter.x,
//                                  chunkPos.z * CHUNK_SIZE_Z - worldCenter.z,
//                                  CHUNK_SIZE_X,
//                                  CHUNK_SIZE_Z,
//                                  displayArea), clr);
//}
inline float CoordDivSection(float x) {
    float n = x / CHUNK_SIZE_X;
    return (x - n * CHUNK_SIZE_X) < 0 ? n - 1 : n;
}

void drawCamera(Rainbow::UILib::UIRenderer& display, const Rainbow::Vector3f& centerPos, const Rainbow::Vector3f& camPos, const Rainbow::Vector3f& camDir, float fov, const Rainbow::RectInt& cliprect, const Rainbow::ColorRGBA32& clr) {
    float x1, y1, x2, y2;
    x1 = 0;
    y1 = 0;
    x2 = camDir.x * 50;
    y2 = camDir.z * 50;
    int showposX, showposZ;

    world2canvas(x1, y1, showposX, showposZ, cliprect);
    float ox = camPos.x - centerPos.x;
    float oz = camPos.z - centerPos.z;
    display.DrawLine(showposX + ox, showposZ + oz, showposX + x2 + ox, showposZ + y2 + oz, clr);

    float halfFovRad = fov * 0.5f * 3.14159f / 180.0f;
    float dx1 = camDir.x * cos(halfFovRad) - camDir.z * sin(halfFovRad);
    float dz1 = camDir.x * sin(halfFovRad) + camDir.z * cos(halfFovRad);
    float dx2 = camDir.x * cos(-halfFovRad) - camDir.z * sin(-halfFovRad);
    float dz2 = camDir.x * sin(-halfFovRad) + camDir.z * cos(-halfFovRad);

    display.DrawLine(showposX + ox, showposZ + oz, showposX + ox + dx1 * 100, showposZ + oz + dz1 * 100, clr);
    display.DrawLine(showposX + ox, showposZ + oz, showposX + ox + dx2 * 100, showposZ + oz + dz2 * 100, clr);
}

bool ShowTopViewInfo = true;
bool ShowTopViewAABB = true;
void ClientActorMgr::DrawTopView() {
    if (!GetDebugDataMgr().isEnableRender()) {
        return;
    }

    if (!g_pPlayerCtrl) {
        return;
    }
    if (g_pPlayerCtrl->getWorld() != m_World) {
        return;
    }

    auto sw = GetScreenManager().GetWidth();
    auto sh = GetScreenManager().GetHeight();
    //添加一些debug信息，在top view中画出所有actor的位置和当前相机的frustum
    auto& display = UILib::UIRenderer::GetInstance();

    Rainbow::RectInt cliprect(sw / 2 - CANVASSIZE / 2, sh / 2 - CANVASSIZE / 2, CANVASSIZE, CANVASSIZE);
    //display.PushClipRect(cliprect);
    SharePtr<MaterialInstance>& mat = GetDebugDataMgr().getActorTopViewMat();
    if (!mat) {
        mat = display.CreateInstance(UILib::UIDrawFlag::UI_DRAWFLAG_NONE, kBlendModeTranslucent);
    }
    display.BeginDraw(mat, display.GetNullTexture());
    display.DrawRectWireframe(cliprect, ColorRGBA32(255, 255, 255, 255));

    WPreciseCoord center = g_pPlayerCtrl ? g_pPlayerCtrl->getPosition().toVector3() : Rainbow::Vector3f::zero;

    //绘制刷怪区域
    float old_aoi_w = 17 * CHUNK_SIZE_X;
    float old_aoi_h = 17 * CHUNK_SIZE_Z;
    auto aoiOldRect = world2canvas(-old_aoi_w / 2.0f, -old_aoi_h / 2.0f, old_aoi_w, old_aoi_h, cliprect);
    display.DrawRectWireframe(aoiOldRect, ColorRGBA32::green);

    if (g_pPlayerCtrl) {
        float old_aoi_w = g_pPlayerCtrl->getCurViewRange() * 2 * CHUNK_SIZE_X;
        float old_aoi_h = g_pPlayerCtrl->getCurViewRange() * 2 * CHUNK_SIZE_Z;
        auto aoiOldRect = world2canvas(-old_aoi_w / 2.0f, -old_aoi_h / 2.0f, old_aoi_w, old_aoi_h, cliprect);
        display.DrawRectWireframe(aoiOldRect, ColorRGBA32::yellow);
    }

    //普通生物
    auto iter = m_LiveActors.begin();
    auto over = m_LiveActors.end();
    while (iter != over) {
        auto actor = iter->second;
        if (actor) {
            auto pos = actor->getPosition().toWorldPos().toVector3();
            // 转为屏幕坐标,假设屏幕大小为512x512, 最大显示16x16 chunk, 每个chunk 512/32 = 16 pixel
            int showposX, showposZ;
            world2canvas(pos.x - center.x, pos.z - center.z, showposX, showposZ, cliprect);
            Rainbow::ColorRGBA32 clr = Rainbow::ColorRGBA32::red;

            display.DrawRect(Rainbow::RectInt(showposX - 2, showposZ - 2, 4, 4), clr);
            if (ShowTopViewAABB) {
                auto aabb = actor->GetAABB();
                auto aabbRect = world2canvas(aabb.m_Center.x - aabb.m_Extent.x - center.x, aabb.m_Center.z - aabb.m_Extent.z - center.z, aabb.m_Extent.x * 2, aabb.m_Extent.z * 2, cliprect);
                display.DrawRectWireframe(aabbRect, Rainbow::ColorRGBA32::silver);
            }
        }
        ++iter;
    }

    //绘制player位置
    // 转为屏幕坐标,假设屏幕大小为512x512, 最大显示16x16 chunk, 每个chunk 512/32 = 16 pixel
    for (auto& player : m_Players) {
        if (player) {
            auto pos = player->getPosition().toWorldPos().toVector3();
            int showposX, showposZ;
            world2canvas(pos.x - center.x, pos.z - center.z, showposX, showposZ, cliprect);
            display.DrawRect(Rainbow::RectInt(showposX - 2, showposZ - 2, 4, 4), Rainbow::ColorRGBA32::red);
        }
    }
    //绘制boss
    for (auto& boss : m_Bosses) {
        if (boss) {
            auto pos = boss->getPosition().toWorldPos().toVector3();
            int showposX, showposZ;
            world2canvas(pos.x - center.x, pos.z - center.z, showposX, showposZ, cliprect);
            display.DrawRect(Rainbow::RectInt(showposX - 3, showposZ - 3, 6, 6), Rainbow::ColorRGBA32::orange);
        }
    }

    //绘制camera朝向
    {
        if (g_pPlayerCtrl->m_pCamera && g_pPlayerCtrl->m_pCamera->getEngineCamera()) {
            drawCamera(display, center.toVector3(), center.toVector3(), g_pPlayerCtrl->m_pCamera->getEngineCamera()->GetForward(), g_pPlayerCtrl->m_pCamera->getEngineCamera()->GetVerticalFieldOfView(), cliprect, Rainbow::ColorRGBA32::red);
        }
    }

    display.EndDraw();
}
#endif

WCoord ClientActorMgr::getPresetSpawnPosition(const WCoord& centerPos, int maxDistance)
{
    // 首先尝试重置过期的位置（假设每天重置）这里可以获取当前游戏天数，暂时使用一个默认值
    int currentGameDay = 0; // TODO: 从游戏世界获取当前天数
    m_mobPresetPositionMgr.ResetExpiredPositions(currentGameDay, 1);
    
    WCoord presetPos = m_mobPresetPositionMgr.GetNearestAvailablePosition(centerPos, maxDistance);
    
    	if (presetPos.y == -999) {
		CLIENTACTORMGR_LOG("No nearby preset position found within distance %d of center: (%d, %d, %d)", 
		                  maxDistance, centerPos.x, centerPos.y, centerPos.z);
	} else {
		CLIENTACTORMGR_LOG("Found preset position: (%d, %d, %d) for center: (%d, %d, %d)", 
		                  presetPos.x, presetPos.y, presetPos.z, centerPos.x, centerPos.y, centerPos.z);
	}
    
    return presetPos;
}

void ClientActorMgr::markPresetPositionUsed(const WCoord& pos, int gameDay)
{
    int index = m_mobPresetPositionMgr.FindPositionIndex(pos);
    if (index >= 0) {
        m_mobPresetPositionMgr.SetPositionUsed(index, true, gameDay);
        CLIENTACTORMGR_LOG("Marked preset position index %d as used: (%d, %d, %d)", 
                          index, pos.x, pos.y, pos.z);
    } else {
        CLIENTACTORMGR_LOG("Failed to find preset position index for: (%d, %d, %d)", 
                          pos.x, pos.y, pos.z);
    }
}

WCoord ClientActorMgr::getPresetPointInChunk(int chunkX, int chunkZ)
{
    WCoord presetPoint = m_mobPresetPositionMgr.GetPresetPointInChunk(chunkX, chunkZ);
    
    if (presetPoint.y != -999) {
        // CLIENTACTORMGR_LOG("Found preset point (%d, %d, %d) in chunk (%d, %d)", presetPoint.x, presetPoint.y, presetPoint.z, chunkX, chunkZ);
    } else {
        CLIENTACTORMGR_LOG("No preset point found in chunk (%d, %d)", chunkX, chunkZ);
    }
    
    return presetPoint;
}

int ClientActorMgr::getAvailablePresetPositionCount() const
{
    return m_mobPresetPositionMgr.GetAvailablePositionCount();
}

int ClientActorMgr::getTotalPresetPositionCount() const
{
    return m_mobPresetPositionMgr.GetTotalPositionCount();
}

void ClientActorMgr::toggleMobPresetDebugLines()
{
    m_mobPresetDebugEnabled = !m_mobPresetDebugEnabled;
    CLIENTACTORMGR_LOG("MobPresetDebugLines toggled: %s", m_mobPresetDebugEnabled ? "ENABLED" : "DISABLED");
}

void ClientActorMgr::setMobPresetDebugEnabled(bool enabled)
{
    if (m_mobPresetDebugEnabled != enabled)
    {
        m_mobPresetDebugEnabled = enabled;
        CLIENTACTORMGR_LOG("MobPresetDebugLines set to: %s", enabled ? "ENABLED" : "DISABLED");
    }
}

bool ClientActorMgr::isMobPresetDebugEnabled() const
{
    return m_mobPresetDebugEnabled;
}
const int MAX_CHUNKS_PER_FRAME = 5;
void ClientActorMgr::refreshBuildData()
{
	m_nGameDay = GetWorldManagerPtr()->getWorldTimeDay();
	int refreshCount = 0;
	for (auto player : m_Players)
	{
		WCoord playerPos = CoordDivBlock(player->getPosition());
		ChunkIndex playerChunkIdx(playerPos.x >> 4, playerPos.z >> 4);
		auto pChunk = m_World->getChunkBySCoord(playerChunkIdx.x, playerChunkIdx.z);
		if (refreshCount < MAX_CHUNKS_PER_FRAME && pChunk && pChunk->getGameDay() != m_nGameDay)
		{
			refreshCount++;
			refeshMobWithChunk(pChunk);
			ChestManager* chestMgr = m_World->GetChestMgr();
			chestMgr->trySpawnChestsInChunk(playerChunkIdx.x, playerChunkIdx.z);
			pChunk->setGameDay(m_nGameDay);
		}
	}
}

void ClientActorMgr::checkPlayerPersonalSpawn(ClientPlayer* player)
{
	// 机器人不刷
	if (player->isRobot()) return;

	// 确保只在服务器端执行
	if (!player || m_World->isRemoteMode()) {
		return;
	}

	// 创建地图模式下不刷新
	bool bCreateMap = m_World->isSOCCreateMap();
	if (bCreateMap) {
		return;
	}

	// 家园地图暂时屏蔽刷怪
	bool isEnterHomeLand = (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD);
	if (isEnterHomeLand) {
		return;
	}

	// 安全区不刷
	bool isInProtectedZone = m_World->IsProtectedZone(player->getPosition() / BLOCK_SIZE);
	if (isInProtectedZone) {
		// CLIENTACTORMGR_LOG("Player [%d] is in protected zone, skip personal spawn", player->getUin());
		// reset timer
		player->m_personalSpawnTimerPassive = 0;
		player->m_personalSpawnTimerHostile = 0;

		return;
	}

	bool passiveReady = false;
	bool hostileReady = false;
	bool shouldSpawn = false;

	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	
	// 检查被动mob timer
	player->m_personalSpawnTimerPassive++;
	if (player->m_personalSpawnTimerPassive >= pLuaConst->K_PERSONAL_SPAWN_INTERVAL_ANIMAL) {
		player->m_personalSpawnTimerPassive = 0;
		passiveReady = true;

		WCoord playerPos = CoordDivBlock(player->getPosition());
		int nearbyMobCount = GetNearbyAllMobCount(m_World, playerPos, pLuaConst->K_PERSONAL_SPAWN_RANGE_MAX, 5);
		
		int currentPlayerMobCount = (int)player->m_personalSpawnMobs.size();
		shouldSpawn = nearbyMobCount < pLuaConst->K_PERSONAL_SPAWN_MIN_MOBCOUNT_THRESHOLD && 
		              currentPlayerMobCount < pLuaConst->K_PERSONAL_SPAWN_MOB_COUNT + 1;
		int totalAllPlayersMobCount = 0;
		for (auto p : m_Players) {
			if (p) {
				totalAllPlayersMobCount += (int)p->m_personalSpawnMobs.size();
			}
		}
		
		CLIENTACTORMGR_LOG("Player personal spawn timer [passive] ready: nearby=%d, threshold=%d, player_mobs=%d, total_server_mobs=%d, spawn_counter=%d", 
			nearbyMobCount, pLuaConst->K_PERSONAL_SPAWN_MIN_MOBCOUNT_THRESHOLD, currentPlayerMobCount, totalAllPlayersMobCount, m_totalPersonalSpawnCount);
	}

	// 检查敌对mob timer  
	player->m_personalSpawnTimerHostile++;
	if (player->m_personalSpawnTimerHostile >= pLuaConst->K_PERSONAL_SPAWN_INTERVAL_HOSTILE) {
		player->m_personalSpawnTimerHostile = 0;
		hostileReady = true;

		WCoord playerPos = CoordDivBlock(player->getPosition());
		int nearbyMobCount = GetNearbyAllMobCount(m_World, playerPos, pLuaConst->K_PERSONAL_SPAWN_RANGE_MAX, 5);
		
		// 统计当前玩家和全服personal spawn mob数量
		int currentPlayerMobCount = (int)player->m_personalSpawnMobs.size();
		shouldSpawn = nearbyMobCount < pLuaConst->K_PERSONAL_SPAWN_MIN_MOBCOUNT_THRESHOLD && currentPlayerMobCount < pLuaConst->K_PERSONAL_SPAWN_MOB_COUNT + 1;
		int totalAllPlayersMobCount = 0;
		for (auto p : m_Players) {
			if (p) {
				totalAllPlayersMobCount += (int)p->m_personalSpawnMobs.size();
			}
		}
		
		CLIENTACTORMGR_LOG("Player personal spawn timer [hostile] ready: nearby=%d, threshold=%d, player_mobs=%d, total_server_mobs=%d, spawn_counter=%d", 
			nearbyMobCount, pLuaConst->K_PERSONAL_SPAWN_MIN_MOBCOUNT_THRESHOLD, currentPlayerMobCount, totalAllPlayersMobCount, m_totalPersonalSpawnCount);
	}
	
	// 优先级 hostile > passive
	if (shouldSpawn) {
		if (hostileReady) {
			performPlayerPersonalSpawn(player, MOB_HOSTILE);
		} else if (passiveReady) {
			performPlayerPersonalSpawn(player, MOB_PASSIVE);
		}
	}
}

void ClientActorMgr::performPlayerPersonalSpawn(ClientPlayer* player, MOB_TYPE mob_type)
{
	if (!player || m_World->isRemoteMode()) {
		return;
	}

	WCoord playerPos = CoordDivBlock(player->getPosition());
	
	// 统计刷新前的mob数量
	int beforePlayerMobCount = (int)player->m_personalSpawnMobs.size();
	int beforeTotalAllPlayersMobCount = 0;
	for (auto p : m_Players) {
		if (p) {
			beforeTotalAllPlayersMobCount += (int)p->m_personalSpawnMobs.size();
		}
	}

	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	
	CLIENTACTORMGR_LOG("Personal spawn starting for player at (%d, %d, %d): before_player_mobs=%d, before_total_server_mobs=%d, spawn_counter=%d", 
		playerPos.x, playerPos.y, playerPos.z, beforePlayerMobCount, beforeTotalAllPlayersMobCount, m_totalPersonalSpawnCount);
	
	// 刷新前先清理当前玩家之前的非战斗personal spawn mob
	cleanupIdlePersonalSpawnMobs(player, playerPos, pLuaConst->K_PERSONAL_SPAWN_RANGE_MAX);
	
	int successfulSpawns = 0;
	for (int i = 0; i < pLuaConst->K_PERSONAL_SPAWN_MOB_COUNT && successfulSpawns < pLuaConst->K_PERSONAL_SPAWN_MOB_COUNT; i++) {
		// 在15-30格环形区域内选择刷新点
		float angle = GenRandomFloat() * 2.0f * 3.14159f;
		int distance = GenRandomInt(pLuaConst->K_PERSONAL_SPAWN_RANGE_MIN, pLuaConst->K_PERSONAL_SPAWN_RANGE_MAX);
		
		WCoord spawnPos;
		spawnPos.x = playerPos.x + (int)(cos(angle) * distance);
		spawnPos.z = playerPos.z + (int)(sin(angle) * distance);
		spawnPos.y = m_World->getTopSolidOrLiquidBlock(spawnPos.x, spawnPos.z);
		
		if (spawnPos.y <= 0) {
			CLIENTACTORMGR_LOG("Personal mob: invalid spawn position Y=%d, skipping", spawnPos.y);
			continue;
		}
		
		Chunk* chunk = m_World->getChunk(spawnPos);
		if (chunk) {
			Ecosystem* biome = m_World->getBiomeGen(spawnPos.x, spawnPos.z);
			if (biome) {
				ClientMob* personalMob = nullptr;
				bool spawnSuccess = false;

				personalMob = trySpawnSingleMob(spawnPos, biome, mob_type);
				if (personalMob) {
					spawnSuccess = true;
				}
				
				if (personalMob) {
					addPersonalSpawnMarkers(personalMob);
					
					// 为了确保客户端也能看到颜色，给mob添加一个特殊标记
					// 客户端可以通过这个标记识别并设置颜色
					if (!m_World->isRemoteMode()) {
						// 服务器端：使用特殊的posKey标记personal spawn
						personalMob->setMobPresetPosKey(999999); 
						
						// 设置创建者为当前玩家，用于后续清理时识别
						personalMob->setMasterObjId(player->getObjId());
						
						// 添加到玩家的personal spawn mob列表
						player->m_personalSpawnMobs.push_back(personalMob->getObjId());
						
						m_totalPersonalSpawnCount++;
					}
					
					successfulSpawns++;
					CLIENTACTORMGR_LOG("Personal spawn successful at (%d, %d, %d), total personal spawns: %d, mob_type: %d", 
						spawnPos.x, spawnPos.y, spawnPos.z, m_totalPersonalSpawnCount, mob_type);
				} else {
					CLIENTACTORMGR_LOG("Personal spawn failed at (%d, %d, %d)", spawnPos.x, spawnPos.y, spawnPos.z);
				}
			}
		}
	}
	
	// 统计刷新完成后的mob数量
	int currentPlayerMobCount = (int)player->m_personalSpawnMobs.size();
	int totalAllPlayersMobCount = 0;
	for (auto p : m_Players) {
		if (p) {
			totalAllPlayersMobCount += (int)p->m_personalSpawnMobs.size();
		}
	}
	
	CLIENTACTORMGR_LOG("Personal spawn completed: %d/%d successful spawns, player_mobs=%d, total_server_mobs=%d, spawn_counter=%d", 
		successfulSpawns, pLuaConst->K_PERSONAL_SPAWN_MOB_COUNT, currentPlayerMobCount, totalAllPlayersMobCount, m_totalPersonalSpawnCount);
}

ClientMob* ClientActorMgr::trySpawnSingleMob(const WCoord& spawnPos, Ecosystem* biome, MOB_TYPE mobtype)
{
	if (!biome) return nullptr;
	
	int mobID = -1;
	if (IsSpecialNight(m_World))
		mobID = biome->getSpawnMobs(mobtype, 3501, spawnPos.y);
	else
		mobID = biome->getSpawnMobs(mobtype, -1, spawnPos.y);
	
	if (mobID < 0) {
		CLIENTACTORMGR_LOG("Personal spawn: Failed to get mob ID from biome, mobtype=%d", mobtype);
		return nullptr;
	}
	
	WCoord worldPos = spawnPos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2);
	ClientMob* mob = spawnMob(worldPos, mobID, true, true); // mobtype_check=true, mob_check=true
	
	if (mob) {
		CLIENTACTORMGR_LOG("Personal spawn: Successfully spawned mobID=%d at (%d, %d, %d)", mobID, spawnPos.x, spawnPos.y, spawnPos.z);
	} else {
		CLIENTACTORMGR_LOG("Personal spawn: Failed to spawn mobID=%d at (%d, %d, %d)", mobID, spawnPos.x, spawnPos.y, spawnPos.z);
	}
	
	return mob;
}

void ClientActorMgr::addPersonalSpawnMarkers(ClientMob* mob)
{

}

void ClientActorMgr::cleanupIdlePersonalSpawnMobs(ClientPlayer* player, const WCoord& center, int range)
{
	if (m_World->isRemoteMode() || !player) {
		return; // 只在服务器端执行清理
	}
	
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	WORLD_ID playerObjId = player->getObjId();
	int beforeCleanupCount = (int)player->m_personalSpawnMobs.size();
	int cleanedCount = 0;
	
	CLIENTACTORMGR_LOG("Personal spawn cleanup starting for player %lld: before_cleanup_list_size=%d", playerObjId, beforeCleanupCount);
	
	for (int i = (int)player->m_personalSpawnMobs.size() - 1; i >= 0; i--) {
		WORLD_ID mobObjId = player->m_personalSpawnMobs[i];
		ClientMob* mob = dynamic_cast<ClientMob*>(findActorByWID(mobObjId));
		
		if (!mob) {
			player->m_personalSpawnMobs.erase(player->m_personalSpawnMobs.begin() + i);
			continue;
		}
		
		WCoord mobPos = CoordDivBlock(mob->getPosition());
		float distance = center.distanceTo(mobPos);
		
		bool shouldCleanup = false;
		bool inCombat = false;

		// 检查是否有攻击目标，有攻击目标不删
		if (mob->getHasAttackTarget()) {
			inCombat = true;
		}

		if (!inCombat && distance >= pLuaConst->K_PERSONAL_SPAWN_RANGE_MAX * 1.2) {
			shouldCleanup = true;
		}

		
		// 检查马匹是否正在被骑乘，被骑乘的马匹不能删
		bool isHorse = false;
		bool isBeingRidden = false;		
		isHorse = GetDefManagerProxy()->getHorseDef(mob->getDef()->ID) != nullptr;
		if (isHorse) {
			mob->getRiddenActor(isBeingRidden);
		}
		if (isHorse && isBeingRidden) {
			shouldCleanup = false;
			CLIENTACTORMGR_LOG("Skipping cleanup for ridden horse (player %lld): mobid=%d, distance=%.1f, position=(%d,%d,%d)", 
				playerObjId, mob->getDef()->ID, distance, mobPos.x, mobPos.y, mobPos.z);
		}
		
		if (shouldCleanup) {
			CLIENTACTORMGR_LOG("Cleaning up personal spawn mob (player %lld): mobid=%d, distance=%.1f, position=(%d,%d,%d)", 
				playerObjId, mob->getDef()->ID, distance, mobPos.x, mobPos.y, mobPos.z);
			
			player->m_personalSpawnMobs.erase(player->m_personalSpawnMobs.begin() + i);
			
			despawnActor(mob, false);
			cleanedCount++;
		}
	}
	
	int afterCleanupCount = (int)player->m_personalSpawnMobs.size();
	
	if (cleanedCount > 0 || beforeCleanupCount != afterCleanupCount) {
		CLIENTACTORMGR_LOG("Personal spawn cleanup completed for player %lld: removed %d mobs, before=%d, after=%d, spawn_counter=%d", 
			playerObjId, cleanedCount, beforeCleanupCount, afterCleanupCount, m_totalPersonalSpawnCount);
	}
}

// AINPC个人刷新系统实现

// AINPC随机名字集合
static const std::vector<std::string> AINPC_NAMES = {
	"Alex", "Jordan", "Casey", "Riley", "Taylor", "Morgan", "Avery", "Quinn", 
	"Sage", "River", "Sky", "Robin", "Drew", "Phoenix", "Dallas", "Reese",
	"Blake", "Charlie", "Devon", "Finley", "Hayden", "Kai", "Lane", "Mason",
	"Nova", "Ocean", "Parker", "Rowan", "Eden", "Storm", "Vale", "Winter",
	"雨萱", "晓晨", "思雨", "梦琪", "雨薇", "语嫣", "诗涵", "梦洁",
	"雅芙", "雨婷", "惠茜", "漫妮", "香茹", "月婵", "嫦曦", "静香",
	"梦璐", "沛玲", "欣妍", "曼玉", "歆瑶", "凌菲", "靖瑶", "瑾萱"
};

// 全局AINPC uin管理器
class AINPCUinManager 
{
private:
	std::set<int> usedUins;
	std::random_device rd;
	std::mt19937 gen;
	std::uniform_int_distribution<int> dis;
	
public:
	AINPCUinManager() : gen(rd()), dis(900000, 999999) {}
	
	int generateUniqueUin() 
	{
		int uin;
		int attempts = 0;
		const int maxAttempts = 1000;
		
		do {
			uin = dis(gen);
			attempts++;
		} while (usedUins.find(uin) != usedUins.end() && attempts < maxAttempts);
		
		if (attempts >= maxAttempts) {
			uin = 900001;
		}
		
		usedUins.insert(uin);
		return uin;
	}
	
	void releaseUin(int uin) 
	{
		usedUins.erase(uin);
	}
	
	size_t getUsedCount() const 
	{
		return usedUins.size();
	}
};

// 全局实例
static AINPCUinManager g_ainpcUinManager;

// AINPC uin生成器 - 900000~999999范围内随机生成全局唯一uin
int ClientActorMgr::generateUniqueAINPCUin()
{
	int uin = g_ainpcUinManager.generateUniqueUin();
	CLIENTACTORMGR_LOG("Generated unique AINPC uin: %d (total used: %d)", uin, (int)g_ainpcUinManager.getUsedCount());
	return uin;
}

// 获取随机AINPC名字
std::string ClientActorMgr::getRandomAINPCName()
{
	static std::random_device rd;
	static std::mt19937 gen(rd());
	static std::uniform_int_distribution<size_t> dis(0, AINPC_NAMES.size() - 1);
	
	return AINPC_NAMES[dis(gen)];
}

void ClientActorMgr::releaseAINPCUin(int uin)
{
	g_ainpcUinManager.releaseUin(uin);
	CLIENTACTORMGR_LOG("Released AINPC uin: %d (remaining: %d)", uin, (int)g_ainpcUinManager.getUsedCount());
}

bool ClientActorMgr::hasOtherPlayersNearby(ClientPlayer* player, int range)
{
	if (!player) return false;
	
	WCoord playerPos = player->getPosition();
	
	for (auto otherPlayer : m_Players) {
		if (!otherPlayer || otherPlayer == player || otherPlayer->isDead()) {
			continue;
		}
		
		WCoord otherPos = otherPlayer->getPosition();
		float distance = playerPos.distanceTo(otherPos);
		
		if (distance <= range * BLOCK_SIZE) {
			return true;
		}
	}
	
	return false;
}

void ClientActorMgr::checkPlayerAINPCSpawn(ClientPlayer* player)
{
	if (!player || m_World->isRemoteMode()) {
		return;
	}
	
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	
	// 总开关检查
	if (!pLuaConst->K_AINPC_SPAWN_ENABLED) {
		return;
	}
	
	
	// 遗迹内不刷
	bool isInProtectedZone = m_World->IsProtectedZone(player->getPosition() / BLOCK_SIZE);
	if (isInProtectedZone) {
		player->m_noOtherPlayerTimer = 0;
		return;
	}
	
	// 数量限制检查
	int currentPlayerAINPCCount = (int)player->m_personalAINPCs.size();
	if (currentPlayerAINPCCount >= pLuaConst->K_AINPC_MAX_COUNT_PER_PLAYER) {
		return;
	}
	
	// 全服数量限制检查
	if (m_totalPersonalAINPCCount >= pLuaConst->K_AINPC_MAX_COUNT_TOTAL) {
		return;
	}
	
	// 检查周围是否有其他玩家（使用配置范围）
	bool hasOthers = hasOtherPlayersNearby(player, pLuaConst->K_AINPC_SPAWN_CHECK_OTHER_PLAYER_RANGE);
	
	if (hasOthers) {
		player->m_noOtherPlayerTimer = 0;
	} else {
		player->m_noOtherPlayerTimer++;
		if (player->m_noOtherPlayerTimer >= pLuaConst->K_AINPC_SPAWN_DELAY_NO_PLAYER) {
			if (currentPlayerAINPCCount == 0) {
				performPlayerAINPCSpawn(player);
			}
			player->m_noOtherPlayerTimer = 0;
		}
	}
}

WCoord ClientActorMgr::getRandomSpawnPosInFront(const WCoord& playerPos, const WCoord& viewDir, int minDist, int maxDist)
{
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	
	// 如果没有传入参数，使用配置的默认值
	if (minDist <= 0) minDist = pLuaConst->K_AINPC_SPAWN_RANGE_MIN;
	if (maxDist <= 0) maxDist = pLuaConst->K_AINPC_SPAWN_RANGE_MAX;
	
	// 标准化视线方向
	WCoord normalizedDir = viewDir;
	normalizedDir.toVector3().Normalize();
	
	// 随机距离
	int spawnDist = minDist + (rand() % (maxDist - minDist));
	
	// 随机偏移角度(-45度到45度)
	float angleOffset = (rand() % 90 - 45) * 3.14159f / 180.0f;
	
	// 计算旋转后的方向
	WCoord rotatedDir;
	rotatedDir.x = normalizedDir.x * cos(angleOffset) - normalizedDir.z * sin(angleOffset);
	rotatedDir.z = normalizedDir.x * sin(angleOffset) + normalizedDir.z * cos(angleOffset);
	rotatedDir.y = 0;  // 保持在同一水平面
	
	// 计算生成位置
	WCoord spawnPos = playerPos + rotatedDir * spawnDist;
	
	// 调整Y坐标到合适的地面高度
	spawnPos.y = m_World->getTopSolidOrLiquidBlock(spawnPos.x, spawnPos.z) + 1;
	
	return spawnPos;
}

bool ClientActorMgr::CanSpawnAINPCAtPos(const WCoord& pos)
{
	//// 检查是否在有效的地形上
	//int groundHeight = m_World->getTopSolidOrLiquidBlock(pos.x, pos.z);
	//if (abs(pos.y - groundHeight) > 5) {  // 高度差不能太大
	//	return false;
	//}
	//
	//// 检查周围是否有足够的空间
	//WCoord checkPos = pos;
	//for (int i = 0; i < 2; i++) {  // 检查头顶空间
	//	checkPos.y = pos.y + i;
	//	int blockId = m_World->getBlockID(checkPos);
	//	if (blockId != 0) {  // 有方块阻挡
	//		return false;
	//	}
	//}
	
	return true;
}

void ClientActorMgr::performPlayerAINPCSpawn(ClientPlayer* player)
{
	if (!player || m_World->isRemoteMode()) {
		return;
	}
	
	WCoord playerPos = CoordDivBlock(player->getPosition());
	
	// 获取玩家视线方向
	float faceX, faceY, faceZ;
	player->getFaceDir(faceX, faceY, faceZ);
	WCoord viewDir(faceX, faceY, faceZ);
	
	// 在玩家视野前方随机位置生成AINPC
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	WCoord spawnPos = getRandomSpawnPosInFront(playerPos, viewDir, 
		pLuaConst->K_AINPC_SPAWN_RANGE_MIN, pLuaConst->K_AINPC_SPAWN_RANGE_MAX);
	
	// 检查生成位置是否合适
	if (!CanSpawnAINPCAtPos(spawnPos)) {
		CLIENTACTORMGR_LOG("AINPC spawn position not suitable at (%d, %d, %d)", spawnPos.x, spawnPos.y, spawnPos.z);
		return;
	}
	
	// 使用服务器端创建逻辑（参考PlayerCmdCommandImpl.cpp 6623-6647行）
	// 生成唯一的uin（900000~999999范围）
	int ainpcUin = generateUniqueAINPCUin();
	std::string ainpcName = getRandomAINPCName();
	PlayerBriefInfo* info = SANDBOX_NEW(PlayerBriefInfo, ainpcUin);
	AINpcPlayer::initializePlayerBriefInfo(info, ainpcUin, ainpcName, spawnPos);
	AINpcPlayer* ainpc = SANDBOX_NEW(AINpcPlayer);
	
	int playerindex = ComposePlayerIndex(info->model, info->geniuslv, info->skinid);
	ainpc->init(ainpcUin, info->nickname, playerindex, info->customjson);
	ainpc->m_Body->setPlayerFrameId(info->frameid);
	ainpc->setVipInfo(info->vipinfo);
	ainpc->setTeam(0);
	ainpc->setPersonalAINPCFlag(true);
	ainpc->setMasterObjId(player->getObjId());
	World* pworld = GetWorldManagerPtr()->getOrCreateWorld(0, ainpc);
	ainpc->setSpawnPoint(spawnPos);
	ainpc->setNewPlayer(true);
	pworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(ainpc);
	ainpc->release();
	
	GetWorldManagerPtr()->syncPlayerEnter(ainpcUin);
	ainpc->SetOffline(false);
	ainpc->playAnim(SEQ_STAND);
	
	ainpc->gotoBlockPos(pworld, spawnPos, true);
	
	player->m_personalAINPCs.push_back(ainpc->getObjId());
	
	m_totalPersonalAINPCCount++;
	
	CLIENTACTORMGR_LOG("Personal AINPC spawned for player %d at (%d, %d, %d), ainpc_uin=%d, name=%s", 
					  player->getUin(), spawnPos.x, spawnPos.y, spawnPos.z, ainpcUin, ainpcName.c_str());
	
	SANDBOX_DELETE(info);
}

void ClientActorMgr::cleanupIdlePersonalAINPCs(ClientPlayer* player, std::vector<ClientPlayer*>& willDeleteAINPCs)
{
	if (m_World->isRemoteMode() || !player) {
		return;
	}

	if (player->m_personalAINPCs.size() == 0) return;

	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	int range = pLuaConst->K_AINPC_DESPAWN_CHECK_PLAYER_RANGE;
	
	for (int i = (int)player->m_personalAINPCs.size() - 1; i >= 0; i--) {
		WORLD_ID ainpcObjId = player->m_personalAINPCs[i];
		AINpcPlayer* ainpc = dynamic_cast<AINpcPlayer*>(findActorByWID(ainpcObjId));
		
		if (!ainpc) {
			player->m_personalAINPCs.erase(player->m_personalAINPCs.begin() + i);
			continue;
		}
		
		WCoord ainpcPos = CoordDivBlock(ainpc->getPosition());
		bool shouldCleanup = false;
		
		// 检查周围是否有玩家（50格范围）
		bool hasNearbyPlayers = false;
		for (auto otherPlayer : m_Players) {
			if (!otherPlayer || otherPlayer->isRobot()) continue;
			
			float distance = ainpcPos.distanceTo(CoordDivBlock(otherPlayer->getPosition()));
			if (distance < range) {
				hasNearbyPlayers = true;
				break;
			}
		}
		
		if (!hasNearbyPlayers) {
			ainpc->incrementNoPlayerNearbyTimer();
			
			if (ainpc->getNoPlayerNearbyTimer() >= pLuaConst->K_AINPC_DESPAWN_DELAY_NO_PLAYER) {
				shouldCleanup = true;
			}
		} else {
			ainpc->resetNoPlayerNearbyTimer();
		}
		
		if (shouldCleanup) {
			int ainpcUin = ainpc->getUin();
			CLIENTACTORMGR_LOG("Cleaning up personal AINPC: timeout=%d, uin=%d", ainpc->getNoPlayerNearbyTimer(), ainpcUin);
			
			// 1. 先从玩家的个人AINPC列表中移除
			player->m_personalAINPCs.erase(player->m_personalAINPCs.begin() + i);
			
			// 2. 通知网络管理器连接丢失
			GetGameNetManager().onRnHostConnectionLost(ainpcUin);
			
			// 3. 释放UIN以供重复使用
			releaseAINPCUin(ainpcUin);
			
			// 4. 减少全服计数
			if (m_totalPersonalAINPCCount > 0) {
				m_totalPersonalAINPCCount--;
			}

			willDeleteAINPCs.push_back(ainpc);
		}
	}
}
