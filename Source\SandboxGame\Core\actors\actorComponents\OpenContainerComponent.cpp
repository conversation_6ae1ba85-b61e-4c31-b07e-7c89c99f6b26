#include "OpenContainerComponent.h"
#include "ActorVehicleAssemble.h"
#include "ActorTrader.h"
#include "ActorHorse.h"
#include "VehicleWorld.h"
#include "container_tombstone.h"
#include "container_fullycustommodel.h"
#include "backpack.h"
#include "FullyCustomModelMgr.h"
#include "GameNetManager.h"
#include "PlayerAttrib.h"
#include "ClientActorHelper.h"
#include "EffectComponent.h"
#include "ICloudProxy.h"
#include "PlayerControl.h"
#include "SandboxIdDef.h"
#include "Utils/thinkingdata/GameAnalytics.h"
#include "DynamicContainer.h"
#include "LuaInterfaceProxy.h"

EXPORT_SANDBOXENGINE extern void CamdFromPb(CustomAvatarModelData& camd, const game::common::PB_ActorOneAvatarModelData* pbAoamd);
EXPORT_SANDBOXENGINE extern void CamdToPb(const CustomAvatarModelData& camd, game::common::PB_ActorOneAvatarModelData* pbAoamd);
IMPLEMENT_COMPONENTCLASS(OpenContainerComponent)
OpenContainerComponent::OpenContainerComponent()
:m_OpenContainer(WCoord(-1, -1, -1))
,m_OpenContainerID(0)
,m_vehicleObjid(0)
,m_OpenContainerBase(0)
,m_ContainersPassword(10)
{

}

void OpenContainerComponent::cleanupOpenedContainer()
{
	WorldContainer *container = getCurOpenedContainer();
	if (container)
	{
		if (!GetOwner()) return ;
		m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
		if (!m_owner) return ;
		container->removeOpenUIN(m_owner->getUin());
		m_OpenContainer.y = -1;
	}
}


void OpenContainerComponent::onCloseEditActorModel(const PB_CloseEditActorModelCH &pbCeamCH)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	 int operateType = pbCeamCH.operatetype();
	 if (operateType == -1) //普通关闭
	 {
		 WorldContainer *container = getCurOpenedContainer();		 
		 if(container)
			 container->removeOpenUIN(m_owner->getUin());

		 return;
	 }

	 PB_CloseEditActorModelHC pbCeamHC;

	 CustomActorModelData camd;
	 for (size_t i = 0; i < (size_t)pbCeamCH.bonemodels_size(); i++)
	 {
		 PB_ActorOneBoneModelData *pbAobmd = pbCeamHC.mutable_bonemodels()->Add();

		 std::string boneName = pbCeamCH.bonemodels(i).bonename();
		 pbAobmd->set_bonename(boneName);

		 std::vector<CustomAvatarModelData> vCamds;
		 vCamds.clear();
		 for (size_t j = 0; j < (size_t)pbCeamCH.bonemodels(i).avatarmodels_size(); j++)
		 {
			 PB_ActorOneAvatarModelData *pPbAoamd = pbAobmd->mutable_avatarmodels()->Add();

			 PB_ActorOneAvatarModelData pbAoamd = pbCeamCH.bonemodels(i).avatarmodels(j);
			 CustomAvatarModelData _camd;
			 CamdFromPb(_camd, &pbAoamd);
			 vCamds.push_back(_camd);

			 CamdToPb(_camd, pPbAoamd);
		 }

		 camd.models[boneName] = vCamds;
	 }

	 camd.type = (ACTOR_MODEL_TYPE)pbCeamCH.modeltype();
	 if (pbCeamCH.has_skindisplay())
		 camd.skindisplay = pbCeamCH.skindisplay();

	 if (m_OpenContainer.y >= 0)
	 {
		 WorldContainer *container = getCurOpenedContainer();
		 if (container)
		 {
			 container->removeOpenUIN(m_owner->getUin());
			 //更新主机生物模型方块上面的生物模型
			 container->updateCustomActorModelData(camd);
		 }
			
		 //通知所有客机更新生物模型方块上面的生物模型
		 pbCeamHC.set_mapid(m_owner->getCurMapID());
		 PB_Vector3* containerPos = pbCeamHC.mutable_containerpos();
		 containerPos->set_x(m_OpenContainer.x);
		 containerPos->set_y(m_OpenContainer.y);
		 containerPos->set_z(m_OpenContainer.z);

		if (operateType == 1)  //保存并生成生物
		{
			std::string modelMark("");
			if (CustomModelMgr::GetInstancePtr())
				modelMark = CustomModelMgr::GetInstancePtr()->createAndSaveActor(m_owner, m_OpenContainer, camd, pbCeamCH.modelname());


			if (modelMark.empty())
				return;

			//通知所有客机新增的生物模型数据
			pbCeamHC.set_modelname(pbCeamCH.modelname());
			pbCeamHC.set_modelmark(modelMark);
		}

		 pbCeamHC.set_modeltype(pbCeamCH.modeltype());
		 if (pbCeamCH.has_skindisplay())
			 pbCeamHC.set_skindisplay(pbCeamCH.skindisplay());

		 GetGameNetManagerPtr()->sendBroadCast(PB_CLOSE_EDIT_ACTORMODEL_HC, pbCeamHC, 0);
	 }

}

void OpenContainerComponent::onCloseFullyCustomModelUI(const PB_CloseFullyCustomModelUICH &closeFullyCustomModelUICH)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	int uin = m_owner->getUin();
	if (closeFullyCustomModelUICH.operatetype() == CLOSE_EDIT_FCM_UI_TYPE::NORMAL_CLOSE) //普通的关闭
	{
		ContainerFullyCustomModel *container = dynamic_cast<ContainerFullyCustomModel *>(getCurOpenedContainer());
		if (container)
		{
			
			container->removeOpenUIN(uin);
			if (!FullyCustomModelMgr::GetInstancePtr())
				return;
			FullyCustomModelMgr::GetInstancePtr()->syncCloseUI2Client(uin, ONLY_CLOSE_FCM_UI_SUCCESS, m_OpenContainer, m_owner->getWorld()->getCurMapID(), container->getFullyCustomModelKey());
		}
		return;
	}
	else
	{
		//TODO 主机去下载并解析保存数据，然后返回给客机结果
		if (!FullyCustomModelMgr::GetInstancePtr())
			return;
		jsonxx::Object externdataObj;
		externdataObj << "operatetype" << closeFullyCustomModelUICH.operatetype();
		externdataObj << "name" << closeFullyCustomModelUICH.name();
		externdataObj << "desc" << closeFullyCustomModelUICH.desc();
		externdataObj << "uin" << uin;
		externdataObj << "makekey" << closeFullyCustomModelUICH.skey();
		FullyCustomModelMgr::GetInstancePtr()->reqDownloadFile(closeFullyCustomModelUICH.url(), FCM_HOST_DOWNLOAD_BY_EDIT_FINISH, closeFullyCustomModelUICH.version(), externdataObj.json());
	}
}

WorldContainer* OpenContainerComponent::getCurOpenedContainer()
{
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	WorldContainer *container = NULL;
	World *pWorld = m_owner->getWorld();
	if(pWorld && pWorld->getContainerMgr())
		container = pWorld->getContainerMgr()->getContainerExt(m_OpenContainer);
	return container;
}

bool OpenContainerComponent::checkIsOpenContainer(const WCoord &pos,int index)
{
	if (m_OpenContainer == pos) {
		if (!GetOwner()) return false;
		m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
		if (!m_owner) return false;
		if (m_owner->hasUIControl())
		{
			//ge GetGameEventQue().postBackpackChange(index);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("grid_index", index);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			}
		}
		else
			m_owner->addDirtyIndex(index);
		return true;
	}
	return false;
}

bool OpenContainerComponent::checkIsOpenContainer(WORLD_ID objid, int index)
{
	if (m_OpenContainerID == objid) {
		if (!GetOwner()) return false;
		m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
		if (!m_owner) return false;
		if (m_owner->hasUIControl())
		{
			//ge GetGameEventQue().postBackpackChange(index);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("grid_index", index);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			}
		}
		else
			m_owner->addDirtyIndex(index);
		return true;
	}
	return false;
}

void OpenContainerComponent::load(const flatbuffers::Vector<flatbuffers::Offset<FBSave::ContainerPassword>> *containerpasswords)
{
	if (containerpasswords)
	{
		int realsize = 0;
		for (int i = 0; i < (int)containerpasswords->size(); i++)
		{
			auto* containerpassword = containerpasswords->Get(i);
			if (containerpassword)
			{
				setContainersPassword(Coord3ToWCoord(containerpassword->pos()), containerpassword->password());
				realsize++;
				if (realsize >= 10)
				{
					break;
				}
			}
		}
	}
}

flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ContainerPassword>>> OpenContainerComponent::save(flatbuffers::FlatBufferBuilder &builder)
{
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	World* pworld = m_owner->getWorld();

	std::vector<flatbuffers::Offset<FBSave::ContainerPassword>> containerpasswords;
	ContainPasswordHashTable::Element* iter0 = m_ContainersPassword.iterate(NULL);
	while (iter0)
	{
		FBSave::Coord3 coord = WCoordToCoord3(iter0->key);
		WorldStorageBoxPassword* container = dynamic_cast<WorldStorageBoxPassword*>(pworld->getContainerMgr()->getContainer(iter0->key));
		if (container)
		{
			containerpasswords.push_back(FBSave::CreateContainerPassword(builder, &coord, iter0->value));
		}
		iter0 = m_ContainersPassword.iterate(iter0);
	}	
	return builder.CreateVector(containerpasswords);
}

void OpenContainerComponent::setContainersPassword(const WCoord& pos, int password)
{
	ContainPasswordHashTable::Element* ele = m_ContainersPassword.find(pos);
	if (!ele)
	{
		if (password != -1)
			m_ContainersPassword[pos] = password;
	}
	else
	{
		if (password == -1)
			m_ContainersPassword.erase(ele);
		else
			ele->value = password;
	}
}

int OpenContainerComponent::getContainersPassword(const WCoord& pos)
{
	ContainPasswordHashTable::Element* ele = m_ContainersPassword.find(pos);
	if (ele)
	{
		return ele->value;
	}
	else return -1;
}

bool OpenContainerComponent::openContainer(WorldContainer *container)
{
	if (!GetOwner()) return false;
	if (m_OpenContainer == container->m_BlockPos)
	{
		LOG_INFO("OpenContainerComponent::openContainer 重复打开，不处理");
		return true;
	}

	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	int uin            = m_owner->getUin();
	World* pworld      = m_owner->getWorld();	
	BackPack* backpack = m_owner->getBackPack();

	if (!m_owner->isRobot())
	{
		std::ostringstream oss;
		oss << "" << container->m_BlockPos.x << "," << container->m_BlockPos.y << "," << container->m_BlockPos.z << "";
		std::string location = oss.str();

		int type = (int)container->getUnionType();

		GameAnalytics::TrackEvent("chest_open", {
			{"chest_id", (int)container->getBlockMtl()->GetBlockDef()->ID},
			{"chest_type",type},
			{"player_uin",uin},
			{"loc",location}
		}, true, uin);		
	}

	if (!m_owner->hasUIControl())
	{
		//验证密码
		WorldStorageBoxPassword* containerPassword = dynamic_cast<WorldStorageBoxPassword *>(container);
		if (containerPassword)
		{
			int password = getContainersPassword(container->m_BlockPos);
			if (containerPassword->m_nPassWord == -1 || containerPassword->m_nPassWord != password)
			{
				PB_NeedContainerPasswordHC needContainerPasswordHC;
				PB_Vector3* pos = needContainerPasswordHC.mutable_pos();
				pos->set_x(container->m_BlockPos.x);
				pos->set_y(container->m_BlockPos.y);
				pos->set_z(container->m_BlockPos.z);
				if (containerPassword->m_nPassWord == -1)
				{
					needContainerPasswordHC.set_state(0); //房主没有初始化密码
				}
				else if (password == -1)
				{
					needContainerPasswordHC.set_state(1); //请输入密码
				}
				else
				{
					needContainerPasswordHC.set_state(2); //密码输入失败
				}
				setContainersPassword(container->m_BlockPos, -1);
				// 				needContainerPasswordHC.set_vehicleobjid(0);
				auto vehicleworld = dynamic_cast<VehicleWorld*>(container->m_vehicleWorld);
				if (vehicleworld)
				{
					ActorVehicleAssemble* vehicle = vehicleworld->getActorVehicleAssemble();
					if (vehicle)
					{
						needContainerPasswordHC.set_vehicleobjid(vehicle->getObjId());
					}
				}

				if (!GetGameNetManagerPtr()->sendToClient(uin, PB_NEED_CONTAINER_PASSWORD_HC, needContainerPasswordHC, 0, false))
				{
					return false;
				}
				return true;
			}
		}

		PB_OpenContainerHC openContainerHC;
		PB_Vector3* pos = openContainerHC.mutable_pos();
		pos->set_x(container->m_BlockPos.x);
		pos->set_y(container->m_BlockPos.y);
		pos->set_z(container->m_BlockPos.z);
		openContainerHC.set_npcid(0);
		openContainerHC.set_baseindex(container->getBaseIndex());
		// 		openContainerHC.set_vehicleobjid(0);

				//载具上的container
		auto vehicleworld = dynamic_cast<VehicleWorld*>(container->m_vehicleWorld);
		if (vehicleworld)
		{
			ActorVehicleAssemble* vehicle = vehicleworld->getActorVehicleAssemble();
			if (vehicle)
			{
				openContainerHC.set_vehicleobjid(vehicle->getObjId());
			}
		}

		RepeatedPtrField<PB_ItemData>* pItemInfos = openContainerHC.mutable_iteminfo();
		RepeatedField<float>* pAttrInfos = openContainerHC.mutable_attribinfo();
		int gridCount = container->getItemAndAttrib(pItemInfos, pAttrInfos);
		openContainerHC.set_totalitemgrids(gridCount);

		const char *ptext = container->getText();
		if (ptext && ptext[0]) openContainerHC.set_text(ptext);
		else openContainerHC.set_text("");

		if (container->getObjType() == OBJ_TYPE_SIGNS)
		{
			TombStoneContainer *tomb = dynamic_cast<TombStoneContainer *>(container);
			if (tomb)
			{
				std::string text = tomb->getAllText();
				openContainerHC.set_text(text);
			}
		}
		else if (container->getObjType() == OBJ_TYPE_CUBECHEST)
		{
			AirDropStorageBox* chest = dynamic_cast<AirDropStorageBox*>(container);
			if (chest) {
				openContainerHC.set_npcid(chest->getChestId());
				openContainerHC.set_text("OBJ_TYPE_CUBECHEST");
			}
		}
		else if (container->getObjType() == OBJ_TYPE_BOX)
		{
			DynamicContainer* box = dynamic_cast<DynamicContainer*>(container);
			if (box) {
				openContainerHC.set_npcid(box->tip_index);//stringdef.csv的翻译
				openContainerHC.set_text("OBJ_TYPE_PLAYERCORPSE");
			}
		}

		if (!GetGameNetManagerPtr()->sendToClient(uin, PB_OPEN_CONTAINER_HC, openContainerHC, 0, false))
		{
			return false;
		}

		// 同步容器中Item的UserdataStr数据
		container->syncItemUserdata(m_owner);
	}

	closeContainer();
	backpack->attachContainer(container);
	m_OpenContainer = container->m_BlockPos;
	m_OpenContainerID = 0;
	m_OpenContainerBase = container->getBaseIndex();
	if (nullptr != pworld)
	{
		int blockID = pworld->getBlockID(m_OpenContainer);
		if (blockID == 150022)
		{
			int blockData = pworld->getBlockData(m_OpenContainer);
			blockData = blockData | 4;
			pworld->setBlockData(m_OpenContainer, blockData);
		}
		if (!pworld->isRemoteMode())
		{
			auto vehicleworld = dynamic_cast<VehicleWorld*>(container->m_vehicleWorld);
			if (vehicleworld && vehicleworld->getActorVehicleAssemble())
			{
				m_vehicleObjid = vehicleworld->getActorVehicleAssemble()->getObjId();
			}
			container->addOpenUIN(uin);
		}
	}
	return true;
}

bool OpenContainerComponent::openContainer(ActorContainerMob *container)
{
	if (!GetOwner()) return false;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	int uin            = m_owner->getUin();
	World* pworld      = m_owner->getWorld();	
	BackPack* backpack = m_owner->getBackPack();			
	if (!m_owner->hasUIControl())
	{
		PB_OpenContainerHC openContainerHC;
		PB_Vector3* pos = openContainerHC.mutable_pos();
		pos->set_x(0);
		pos->set_y(-1);
		pos->set_z(0);
		openContainerHC.set_npcid(container->getObjId());
		openContainerHC.set_baseindex(container->getBaseIndex());
		openContainerHC.set_text("");

		RepeatedPtrField<PB_ItemData>* pItemInfos = openContainerHC.mutable_iteminfo();
		RepeatedField<float>* pAttrInfos = openContainerHC.mutable_attribinfo();
		int gridCount = container->getItemAndAttrib(pItemInfos, pAttrInfos);
		openContainerHC.set_totalitemgrids(gridCount);

		if (!GetGameNetManagerPtr()->sendToClient(uin, PB_OPEN_CONTAINER_HC, openContainerHC, 0, false))
		{
			return false;
		}
	}

	closeContainer();
	backpack->attachContainer(container);
	m_OpenContainerID = container->getObjId();
	m_OpenContainerBase = container->getBaseIndex();
	if (pworld && !pworld->isRemoteMode())
	{
		container->addOpenUIN(uin);
		if (GetDefManagerProxy()->isNpcTrade(container->m_Def->ID))
		{
			char sNpcID[10];
			sprintf(sNpcID, "%d", container->m_Def->ID);
			//g_pPlayerCtrl->statisticToWorld(getUin(), 30011, "", g_pPlayerCtrl->getCurWorldType(), sNpcID);  服务端已经埋点，删除
		}
	}
	return true;	
}

void OpenContainerComponent::closeContainer()
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	int uin            = m_owner->getUin();
	World* pworld      = m_owner->getWorld();	
	BackPack* backpack = m_owner->getBackPack();	
	
	if (nullptr != pworld)
	{
		if (!pworld->getActorMgr()) return;
		ClientActorMgr* actorMgr = pworld->getActorMgr()->ToCastMgr();
		if (m_OpenContainerID > 0)
		{
			ActorContainerMob* npc = dynamic_cast<ActorContainerMob*>(actorMgr->findActorByWID(m_OpenContainerID));
			if (npc)
			{
				npc->removeOpenUIN(uin);
				if (backpack) backpack->detachContainer(npc);
			}
		}
		else if (m_OpenContainer.y >= 0)
		{
			if (m_vehicleObjid)
			{
				ActorVehicleAssemble* pVehicle = dynamic_cast<ActorVehicleAssemble*>(actorMgr->findActorByWID(m_vehicleObjid));
				if (pVehicle && pVehicle->getVehicleWorld())
				{
					WorldContainer* container = pVehicle->getVehicleWorld()->getContainerMgr()->getContainerExt(m_OpenContainer);
					if (container)
					{
						container->removeOpenUIN(uin);
						if (container->getBaseIndex() > 0 && backpack)
							backpack->detachContainer(container);
					}
				}
			}
			else
			{
				if (pworld->getContainerMgr() != NULL)
				{
					WorldContainer* container = pworld->getContainerMgr()->getContainerExt(m_OpenContainer);
					if (container)
					{
						container->removeOpenUIN(uin);
						if (container->getBaseIndex() > 0 && backpack)
							backpack->detachContainer(container);
					}
				}
			}
		}
		int blockID = pworld->getBlockID(m_OpenContainer);
		if (blockID == 150022)
		{
			int blockData = pworld->getBlockData(m_OpenContainer);
			blockData = blockData & 3;
			pworld->setBlockData(m_OpenContainer, blockData);
		}
	}

	m_OpenContainerID = 0;
	m_OpenContainer.y = -1;
	m_OpenContainerBase = 0;
	m_vehicleObjid = 0;
}

/*
World* pworld      = m_owner->getWorld();	
PlayerAttrib *playerAttrib = m_owner->getPlayerAttrib();
m_owner->removeBackpackItem(int itemid, int num);
m_owner->gainItemsByIndex(int gindex, int num, int priorityType=1, bool ignoreGodMode = true); 
m_owner->addAchievement(1, ACHIEVEMENT_NPCTRADER, npc->getDef()->ID);
m_owner->addOWScore(def->Score);
*/
bool OpenContainerComponent::npcTrade(int op, int index, bool watch_ad/* =false */, int ad_rewardnum/* =1 */)
{
	if (!GetOwner()) return false;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	World* pworld = m_owner->getWorld();	
	if (!pworld) return false;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return false;

	if (m_OpenContainerID == 0) return false;
	ActorTrader *npc = dynamic_cast<ActorTrader *>(actorMgr->findActorByWID(m_OpenContainerID));
	if (npc == NULL)
	{
		m_OpenContainerID = 0;
		return false;
	}

	// 检查交易距离限制	
	WCoord playerPos = m_owner->getPosition();
	WCoord npcPos = npc->getPosition();
	float distance = (playerPos - npcPos).length();
	if (distance > MAX_TRADE_DISTANCE)
	{
		if (m_owner->hasUIControl())
		{
			GetLuaInterfaceProxy().showGameTips("Too far");
		}
		closeContainer();
		return false;
	}

	PlayerAttrib *playerAttrib = m_owner->getPlayerAttrib();
	if (op == 0)	//刷新物品
	{
		int cost = index * EXP_STAR_RATIO;
		if (index <= 0 || cost > playerAttrib->getExp())
		{
			jsonxx::Object cheat;
			cheat << "cost" << cost;
			cheat << "have" << playerAttrib->getExp();
			Rainbow::GetICloudProxyPtr()->InfoLog(m_owner->getUin(), 0, "cheat_OpenContainerComponent_trade", cheat);
			return false;
		}

		npc->resetItems();
		if (!watch_ad)
			playerAttrib->addExp(-cost);
	}
	else if (op == 1) //交易物品
	{
		BackPackGrid *payout = npc->index2Grid(index / 2 * 2);
		BackPackGrid *gaingrid = npc->index2Grid(index / 2 * 2 + 1);

		if (payout && payout->def && gaingrid && gaingrid->def && gaingrid->getDuration() > 0)
		{
			// 预先计算获得物品的数量
			int getNum = gaingrid->getNum();
			if (ad_rewardnum > 0)
				getNum = ad_rewardnum;
			
			// 预判断：检查背包空间是否足够（只对非星星物品检查）
			if (gaingrid->def->ID != STAT_ITEM_ID)
			{
				BackPack* backpack = m_owner->getBackPack();
				if (backpack && !backpack->enoughGridForItem(gaingrid->def->ID, getNum))
				{
					// 背包空间不足，拒绝交易并提示玩家
					if (m_owner->hasUIControl())
					{
						// 使用正确的提示方式显示背包空间不足信息
						GetLuaInterfaceProxy().showGameTips("Backpack space not enough.");
					}
					return false;
				}
			}
			if (payout->def->ID == STAT_ITEM_ID && !watch_ad)
			{
				int hightNum = (int)(size_t)payout->userdata;
				int cost = payout->getNum() + hightNum * 256;
				playerAttrib->addExp(-cost * EXP_STAR_RATIO);
			}
			else if (!watch_ad)
			{
				// 修复：使用完整的高位+低位价格
				int hightNum = (int)(size_t)payout->userdata;
				int fullCost = payout->getNum() + hightNum * 256;
				m_owner->removeBackpackItem(payout->def->ID, fullCost);
			}

			// getNum 已在上面计算过了，这里直接使用

			if (gaingrid->def->ID == STAT_ITEM_ID)
			{
				playerAttrib->addExp(getNum*EXP_STAR_RATIO);
			}
			else
			{
				//耐久
				int locknum = gaingrid->getDuration();
				auto toolDef = GetDefManagerProxy()->getToolDef(gaingrid->def->ID);
				if (toolDef)
				{
					gaingrid->setDuration(toolDef->Duration);
					gaingrid->changeDurationOnRuneOrEnchantChange(0);
				}
				else
				{
					gaingrid->setDuration(-1);
				}

				// 检查是否为枪，如果是枪则在转移前为商店商品设置子弹数量
				// 这样gainItemsByIndex会自动将完整的数据复制到玩家背包
				int originalUserData = (int)(size_t)gaingrid->userdata; // 保存原始商品ID
				if (gaingrid->def && (gaingrid->def->IsDefGun || gaingrid->def->Type == ITEM_TYPE_GUN))
				{
					const GunDef* gunDef = GetDefManagerProxy()->getGunDef(gaingrid->def->ID);
					if (gunDef)
					{
						gaingrid->setUserDataInt(gunDef->Magazines);
					}
				}

				m_owner->gainItemsByIndex(gaingrid->getIndex(), getNum, 1, false);
				
				// 恢复商店商品的原始userdata（商品ID），确保界面显示正常
				gaingrid->userdata = (void*)(size_t)originalUserData;
				gaingrid->setDuration(locknum);

				if (gaingrid->def->ID == ITEM_ROCKET_CRAFT)
				{
					auto effectComponent = npc->getEffectComponent();
					if (effectComponent)
					{
						effectComponent->stopBodyEffect("rocket_cue");
					}
				}
			}

			gaingrid->addDuration(-1);
			npc->afterChangeGrid(index / 2 * 2 + 1);

			// m_owner->addAchievement(1, ACHIEVEMENT_NPCTRADER, npc->getDef()->ID);
			// m_owner->updateTaskSysProcess(TASKSYS_NPC_TRADER, npc->getDef()->ID);
			//auto def = GetDefManagerProxy()->getExtremityScoreDef(ACHIEVEMENT_NPCTRADER, npc->getDef()->ID);
			//if (def)
			//	m_owner->addOWScore(def->Score);

			return true;
		}
	}

	return false;
}

int OpenContainerComponent::storeItem(int frmGrid, int num)
{
	if (frmGrid < 0 || num <= 0) return -1;
	if (!GetOwner()) return -1;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return -1;
	auto backpack = m_owner->getBackPack();
	if (backpack == nullptr) return -1;

	auto grid = backpack->index2Grid(frmGrid);
	if (grid == nullptr) return -1;

	int stNum = backpack->addStorageItem(frmGrid, num, m_OpenContainerBase);

	if (stNum > 0 && !(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
	{
		backpack->removeItem(frmGrid, stNum);
	}
	return stNum;
}

void OpenContainerComponent::openEditActorModelUI(WorldContainer* container)
{
	if (!GetOwner()) return ;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	World* pworld = m_owner->getWorld();
	if (!pworld || pworld->isRemoteMode())
		return;

	if (container->canEditActorModel())
	{
		m_OpenContainer = container->m_BlockPos;

		container->syncCustomActorModelData(m_owner);
		container->addOpenUIN(m_owner->getUin());

		if (m_owner->hasUIControl())
		{
			//ge GetGameEventQue().postOpenEditActorModel();
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_OPEN_EDIT_ACTORMODEL", MNSandbox::SandboxContext(nullptr));
			}
		}
	}
	else
	{
		//已经有别的玩家在编辑了
		m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 12524);
	}

}

void OpenContainerComponent::closeEditActorModel(int operatetype, std::string modelname/* ="" */)
{
	if (!CustomModelMgr::GetInstancePtr())
		return;

	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	CustomModelMgr::GetInstancePtr()->closeEditActorModel(m_owner, operatetype, modelname, m_OpenContainer);

	World* pworld = m_owner->getWorld();
	if (!pworld->isRemoteMode())
	{
		m_OpenContainer.y = -1;
	}

}

void OpenContainerComponent::openFullyCustomModelUI(WorldContainer* container)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	int uin            = m_owner->getUin();
	World* pworld      = m_owner->getWorld();	

	if (!pworld || pworld->isRemoteMode())
		return;

	if (container->canEditFullyCustomModel())
	{
		m_OpenContainer = container->m_BlockPos;
		container->addOpenUIN(uin);
		if (container->syncFullyCustomModelData(m_owner))
		{
			if (m_owner->hasUIControl())
			{
				bool isEdited = false;
				if (FullyCustomModelMgr::GetInstancePtr())
					isEdited = FullyCustomModelMgr::GetInstancePtr()->isEdited();

				//GetGameEventQue().postOpenEditFullyCustomModel(isEdited);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Bool("edited", isEdited);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_OPEN_EDIT_FULLYCUSTOMMODEL", sandboxContext);
				}
			}
		}
	}
	else
	{
		//已经有别的玩家在编辑了
		m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 12524);
	}
}

void OpenContainerComponent::syncOpenFCMUIToClient(const WCoord& blockpos, bool isedited, std::string url, int version/* =0 */, int result/* =0 */)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	PB_OpenEditFullyCustomModelHC openEditFullyCustomModelHC;

	PB_Vector3* containerPos = openEditFullyCustomModelHC.mutable_containerpos();
	containerPos->set_x(blockpos.x);
	containerPos->set_y(blockpos.y);
	containerPos->set_z(blockpos.z);

	openEditFullyCustomModelHC.set_mapid(m_owner->getWorld()->getCurMapID());
	openEditFullyCustomModelHC.set_edited(isedited);
	openEditFullyCustomModelHC.set_url(url);
	openEditFullyCustomModelHC.set_version(version);
	openEditFullyCustomModelHC.set_result(result);

	GetGameNetManagerPtr()->sendToClient(m_owner->getUin(), PB_OPENEDIT_FULLYCUSTOMMODEL_HC, openEditFullyCustomModelHC);
}

void OpenContainerComponent::closeFullyCustomModelUI(int operatetype, std::string name/* ="" */, std::string desc/* ="" */)
{
	if (!FullyCustomModelMgr::GetInstancePtr())
		return;

	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	FullyCustomModelMgr::GetInstancePtr()->closeEditModelUI(m_owner, operatetype, m_OpenContainer, name, desc);

	if (!m_owner->getWorld()->isRemoteMode())
	{
		m_OpenContainer.y = -1;
	}
}
IMPLEMENT_COMPONENTCLASS(ControlOpenContainerComponent)

ControlOpenContainerComponent::ControlOpenContainerComponent()
:OpenContainerComponent()
{

}

bool ControlOpenContainerComponent::openContainer(WorldContainer *container)
{
	if(OpenContainerComponent::openContainer(container))
	{
		return container->doOpenContainer();
	}
	else 
		return false;
}

bool ControlOpenContainerComponent::openContainer(ActorContainerMob *container)
{
	if(OpenContainerComponent::openContainer(container))
	{
		if(dynamic_cast<ActorHorse *>(container))
			MINIW::ScriptVM::game()->setUserTypePointer("OpenedContainerMob", "ActorHorse", container);
		else
			MINIW::ScriptVM::game()->setUserTypePointer("OpenedContainerMob", "ActorContainerMob", container);
		//ge GetGameEventQue().postOpenContainer(container->getBaseIndex());
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("baseindex", container->getBaseIndex()).
			SetData_Number("blockid", 0).
			SetData_Number("posx", 0).
			SetData_Number("posy", 0).
			SetData_Number("posz", 0);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		{
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_OPEN_CONTAINER", sandboxContext);
		}
		return true;
	}
	else 
		return false;
}

bool ControlOpenContainerComponent::setContainerText(int index, const char *text)
{
	if (!GetOwner()) return false;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	BackPack* backpack = m_owner->getBackPack();	
	WorldContainer *container = dynamic_cast<WorldContainer *>(backpack->getContainer(index));
	if(container)
	{
		container->setText(text);
		return true;
	}
	return false;
}
IMPLEMENT_COMPONENTCLASS(MPControlOpenContainerComponent)

MPControlOpenContainerComponent::MPControlOpenContainerComponent()
:ControlOpenContainerComponent()
,m_CurContainer(NULL)
{

}

bool MPControlOpenContainerComponent::npcTrade(int op, int index, bool watch_ad/* =false */, int ad_rewardnum/* =1 */)
{
	if (!GetOwner()) return false;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	World* pworld = m_owner->getWorld();	
	if(pworld)
	{
		if(!pworld->isRemoteMode())
		{
			return ControlOpenContainerComponent::npcTrade(op, index, watch_ad, ad_rewardnum);
		}
		else
		{
			PB_NpcTradeCH npcTradeCH;
			npcTradeCH.set_optype(op);
			npcTradeCH.set_index(index);
			npcTradeCH.set_watchad(watch_ad ? 1 : 0);
			npcTradeCH.set_rewardnum(ad_rewardnum);

			GetGameNetManagerPtr()->sendToHost(PB_NPCTRADE_CH, npcTradeCH);
		}
	}
	return true;
}

void MPControlOpenContainerComponent::closeContainer()
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	World* pworld      = m_owner->getWorld();		
	if(pworld && pworld->isRemoteMode())
	{
		////在使用emitter时, 会调用一次close, 发消息给主机会使主机那边container断开,就不发了
		//if (m_owner->getUsingEmitter())
		//{
		//	return;
		//}
		if(m_CurContainer)
		{
			PB_CloseContainerCH closeContainerCH;
			closeContainerCH.set_baseindex(m_CurContainer->getBaseIndex());

			GetGameNetManagerPtr()->sendToHost(PB_CLOSE_CONTAINER_CH, closeContainerCH);

			onCloseContainer();
		}
	}
	else ControlOpenContainerComponent::closeContainer();
}

int MPControlOpenContainerComponent::storeItem(int frmGrid, int num)
{
	if (!GetOwner()) return 0;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return 0;
	World* pworld      = m_owner->getWorld();			
	if (!pworld->isRemoteMode())
	{
		return ControlOpenContainerComponent::storeItem(frmGrid, num);
	}
	else
	{
		PB_BackPackStoreCH backPackStoreCH;
		backPackStoreCH.set_fromindex(frmGrid);
		backPackStoreCH.set_num(num);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_STORE_CH, backPackStoreCH);
	}
	return 0;
}

void MPControlOpenContainerComponent::onOpenContainer(ClientWorldContainer *container)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	World* pworld      = m_owner->getWorld();	
	BackPack* backpack = m_owner->getBackPack();
	if (!pworld) return;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return;
	backpack->attachContainer(container);
	m_CurContainer = container;

	if(container->m_NpcID > 0)
	{
		ActorContainerMob *npc = dynamic_cast<ActorContainerMob *>(actorMgr->findActorByWID(container->m_NpcID));

		if(dynamic_cast<ActorHorse *>(npc))
			MINIW::ScriptVM::game()->setUserTypePointer("OpenedContainerMob", "ActorHorse", npc);
		else
			MINIW::ScriptVM::game()->setUserTypePointer("OpenedContainerMob", "ActorContainerMob", npc);
	}
}

void MPControlOpenContainerComponent::onCloseContainer()
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	World* pworld      = m_owner->getWorld();	
	BackPack* backpack = m_owner->getBackPack();		
	if (m_CurContainer)
	{
		if(backpack && (m_CurContainer->getBaseIndex() >= 0)) backpack->detachContainer(m_CurContainer);
		m_CurContainer->reset(-1, 0, NULL, 0, NULL, 0, NULL);
		MINIW::ScriptVM::game()->setUserTypePointer("OpenedContainerMob", "ActorContainerMob", NULL);

		m_CurContainer = NULL;
	}
}

bool MPControlOpenContainerComponent::setContainerText(int index, const char *text)
{
	if (!GetOwner()) return false;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	World* pworld      = m_owner->getWorld();		
	if(!pworld->isRemoteMode())
	{
		return ControlOpenContainerComponent::setContainerText(index, text);
	}
	else
	{
		PB_SetContainerTextCH setContainerTextCH;
		setContainerTextCH.set_baseindex(index);
		if (text)
			setContainerTextCH.set_text(text);
		else
			setContainerTextCH.set_text("");

		GetGameNetManagerPtr()->sendToHost(PB_SET_CONTAINERTEXT_CH, setContainerTextCH);
		return true;
	}
}