#include "IPlayerControl.h"
#include "IClientActor.h"
#include "Components/Camera.h"
#include "blocks/special_blockid.h"
#include "chunk.h"
#include "Environment.h"
#include "WorldRender.h"
#include "BlockScene.h"
#include "blocks/BlockMaterialMgr.h"
#include "BlockDecalMesh.h"
#include "ShadowFace/ShadowFace.h"
#include "CurveFace.h"

#include "WorldManager.h"
#include "EffectManager.h"
#include "ShadowFace/ShadowFace.h"
#include "Renderer/RenderLines.h"
#include "SkyPlane.h"
#include "RainSnow.h"
#include "OgreRay.h"
#include <set>
#include "Components/DirectionalLight.h"
#include "DefManagerProxy.h"

#include "Entity/OgreEntity.h"
#include "Renderer/RenderLines.h"

#include "worldMesh/MiniCraftRenderer.h"
#include "Components/Camera.h"
#include "Graphics/ScreenManager.h"
#include "Core/Engine.h"
#include "Core/World.h"
#include "ShadowFace/ShadowFace.h"
#include "GfxDevice/GfxDeviceSetting.h"
#include "luaConstProxy/LuaInterfaceProxy.h"
#include "FirstPersonCameraRenderTarget.h"

#define USE_CAMERA_GIZMO_MANAGER  (GIZMO_DRAW_ENGABLE && !DEDICATED_SERVER)
#define USE_PREVIEW_RENDERBUFFER ( (!DEDICATED_SERVER) && PREVIEW_RENDERBUFFER_ENABLE)

#if USE_CAMERA_GIZMO_MANAGER
#include "Gizmo/GizmoManager.h"
#endif
#if USE_PREVIEW_RENDERBUFFER 
#include "PreviewRenderbuffer/PreviewRenderbufferManager.h"
#endif

#include "SandboxEnvironment.h"
#include "SandboxSkyDome.h"
#include "SandboxAtmosphere.h"

#include "SandstormEffect.h"
#include "Duststorm.h"
#include "Event/MiniGlobalEvent.h"
#include "Billboard/BillboardMeshBatch.h"
#include "Render/Postprocess/PostprocessBloom.h"
#include "Render/Postprocess/PostprocessDof.h"
#include "Render/Postprocess/PostprocessAntialiasing.h"
#include "Render/RenderPasses/CustomRenderPipeline.h"
#include "Render/RenderPasses/CustomForwardShadingPass.h"
#include "Render/RenderPasses/OutlinePass.h"
#include "Components/PostprocessVolume.h"
#if BUILD_MINI_EDITOR_APP
#include "EditorCbeScene.h"
#include "EditorCmScene.h"
#include "EditorOmodScene.h"
#endif

#include "Render/LOD/LODGroupManager.h"
#include "TempestRenderable.h"
#include <set>
#include "Core/display/SandboxRenderSetting.h"
#include "WeatherRender.h"
#include "Decal/TerrainDecalEffect.h"
#include "Optick/optick.h"
#include "Graphics/LegacyGlobalShaderParam.h"
#include "IActorBody.h"
#include <random>
#include "DangerNightManagerInterface.h"
#include "gamemode/GameModeDef.h"
#include "PlayManagerInterface.h"
#include "Render/Postprocess/PostprocessResolve.h"
#include "worldMesh/BlockPlacementPreview.h"


using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
EXPORT_SANDBOXENGINE int g_DayFogNear[3] = {1000, 0, 0};
EXPORT_SANDBOXENGINE int g_DayFogFar[3] = {2000, 0, 100};
EXPORT_SANDBOXENGINE int g_NightFogNear[3] = {1000, 10, 5};
EXPORT_SANDBOXENGINE int g_NightFogFar[3] = {2000, 80, 40};
EXPORT_SANDBOXENGINE float g_NightFogStartTime = 19.0f;
EXPORT_SANDBOXENGINE float g_NightFogEndTime = 21.0f;
EXPORT_SANDBOXENGINE float g_DayFogStartTime = 5.0f;
EXPORT_SANDBOXENGINE float g_DayFogEndTime = 7.0f;
int g_FogRangeNear[3] = { -8,-18,-25};
int g_FogRangeFar[3] = { 2,4,10 };
EXPORT_SANDBOXENGINE Rainbow::ColourValue g_TorchLightColor(1.0f, 0.882f, 0.882f); //255,  225,  225
//Rainbow::ColourValue g_TorchLightColor(1.0f, 0.9215686f, 0.572549f); //255,  225,  225

const int CLOUD_STEP = 15*60*20/100;
const int MIN_CLOUD = 70;
const int MAX_CLOUD = 180;

int WorldRenderer::m_CurFogIndex = 1;
bool WorldRenderer::m_CurShadowOpen = false;

static WCoord eyepos(3250, 4800, 1600);
static Quaternionf rot(0, 0.69f, 0, -0.72f);

//static Rainbow::GameObject* s_TestGameObject;

WorldRenderer::WorldRenderer(World *pworld, bool is_own) : m_World(pworld), m_Scene(NULL), m_BlockLine(NULL), m_ShadowFaces(NULL), m_Sky(NULL), m_RainSnow(NULL), m_IDCounter(1), m_Duststorm(NULL)
	, m_CurveFaces(nullptr)
	, m_CurveScreenFaces(nullptr)
	, m_CameraClipSetting()
	, m_SandstormEffect(nullptr), m_Tick(0)
	, m_WaterFogNearRange(2), m_WaterFogFarRange(45), m_WaterColorVal(Rainbow::ColourValue(65 / 255.0f, 105 / 255.0f, 225 / 255.0f))
	, m_Tempest(NULL), m_EnableGodray(false), m_IsUGCCustomPostEffect(false)
    , m_Light(nullptr)
	, m_PostprocessChain(nullptr)
	, m_WeatherRender(nullptr)
	, m_TerrainDecalEffect(nullptr)
	, m_PostprocessGodray(nullptr)
	, m_CustomRenderPipeline(nullptr)
	, m_CustomLUTEnable(false)
	, m_FirstPersonRenderPipeline(nullptr)
	, m_CustomModelFirstPersonRenderPipeline(nullptr)
	, m_Skylight(nullptr)
	, m_WeaponItemType(ICON_GEN_MESH)
	, m_CurrentSelectBlockPos(WCoord::infinity)
{
	// if(GetWorld().GetCurrentGameScene() == nullptr)
	{
		m_Scene = GetWorld()->CreateGameScene<BlockScene>();
		m_Scene->CreateLocalPhyiscsScene();
		// 当非当前玩家传送地图时，is_own为false，需要创建另一个世界的WorldRenderer，但不能改变当前玩家的当前场景
		if (is_own)
		{
			m_Scene->SetWorld(pworld);

			GetWorld()->SetCurrentGameScene(m_Scene);

			if (GetMapMarkingMgrInterface() != nullptr)
			{
				// 传送时，需要Detach上一个场景
				if (GetMapMarkingMgrInterface()->IsInScene())
				{
					GameScene* gs = GetMapMarkingMgrInterface()->GetScene();
					Assert(gs);
					Assert(gs != m_Scene);
					GetMapMarkingMgrInterface()->DetachFromScene(gs);
				}

				GetMapMarkingMgrInterface()->AttachToScene(m_Scene);
			}
		}
	}
	/*else
	{		
		m_Scene = dynamic_cast<BlockScene*>(GetWorld().GetCurrentGameScene());

		AssertMsg(m_Scene, "Block Scene is NULL !");

		m_Scene->SetWorld(pworld);
	}*/	

#ifndef IWORLD_SERVER_BUILD
	Rainbow::GameObject* cameraGo = Rainbow::GameObject::Create();
	m_Camera = cameraGo->CreateComponent<Camera>();
	const RenderFeatureFlags renderFeatures = kRenderFeatureDefault;
	m_Camera->SetRenderFeatureFlags(renderFeatures);
	m_Camera->SetUserData(Camera::kMainCameraTag);

	auto postprocessVolume = cameraGo->CreateComponent<PostprocessVolume>();
	m_PostprocessChain = createPostprocessChain();
	postprocessVolume->SetPostprocessChain(m_PostprocessChain);

	m_Scene->AddGameObject(cameraGo);


	//create skylight component
	Rainbow::GameObject* objSkylight = Rainbow::GameObject::Create();
	m_Skylight = objSkylight->CreateComponent<Rainbow::SkyLight>();
	SharePtr<Rainbow::Cubemap> cubemap = GetAssetManager().LoadAsset<Rainbow::Cubemap>("sky/env_skylight.png");
	m_Skylight->SetEnvironmentTexture(cubemap);
	m_Skylight->SetIntensity(1.0f);
	m_Skylight->SetLowerHemisphereColor(Rainbow::ColorRGBAf(0.5f, 0.5f, 0.5f, 1.0f));
	m_Scene->AddGameObject(objSkylight);
	/*
	//test create cube
	s_TestGameObject = Rainbow::GameObject::Create();
	Rainbow::MeshRenderer* meshRender = s_TestGameObject->CreateComponent<Rainbow::MeshRenderer>();
	Rainbow::SharePtr<Rainbow::Mesh> mesh = Mesh::CreatePrimitiveCube(Rainbow::Vector3f::zero, Rainbow::Vector3f::one);
	meshRender->SetSharedMesh(mesh);
	//meshRender->SetSharedMaterial
	Rainbow::SharePtr<Rainbow::MaterialInstance> mat = NativeToSharePtr(Rainbow::GetMaterialManager().LoadFromFile("Materials/MiniGame/legacy/legacy_stdmtl_opaque.templatemat")->CreateInstance());
	meshRender->SetSharedMaterial(mat.CastTo<Rainbow::MaterialInterface>());


	s_TestGameObject->GetTransform()->SetWorldPosition(Vector3f(0, 1000, 0));
	s_TestGameObject->GetTransform()->SetLocalScale(Vector3f(1000, 8000, 6000));

	//m_Scene->AddGameObject(s_TestGameObject);
	*/

	//3DUI 测试项
	//fairygui::UI3DScene* ui3dScene = fairygui::UI3DScene::create();
	//cocos2d::Director::getInstance()->runWithScene(ui3dScene);
	//fairygui::UI3DRenderer::getInstance()->init(m_Camera->GetGameObject(),ui3dScene);
	//
	//fairygui::UIPackage::addPackage("FairyGuiRes/UI/MainMenu");
	//auto uiObj = fairygui::UIPackage::createObject("MainMenu", "Main")->as<fairygui::GComponent>();
	//uiObj->setRenderSpaceMode(cocos2d::RenderSpaceMode::WorldSpace);
	//Rainbow::GameObject* ui3dGameObj =ui3dScene->getRoot()->add3DUIChild(uiObj);
	//ui3dGameObj->GetTransform()->SetWorldPosition(Rainbow::Vector3f(6.f,10.f,6.f));
	//ui3dGameObj->GetTransform()->SetWorldScale(100.f);
	//m_Scene->AddGameObject(ui3dGameObj);
	// 3dUI 测试项2
	//using namespace fairygui;
	//fairygui::UI3DScene* ui3dScene = fairygui::UI3DScene::create();
	//cocos2d::Director::getInstance()->runWithScene(ui3dScene);
	//fairygui::UI3DRenderer::getInstance()->init(m_Camera->GetGameObject(), ui3dScene);

	//fairygui::UIPackage::addPackage("FairyGuiRes/UI/Basics");
	//auto uiObj = fairygui::UIPackage::createObject("Basics", "Main")->as<fairygui::GComponent>();
	//uiObj->setRenderSpaceMode(cocos2d::RenderSpaceMode::WorldSpace);
	//GComponent* _demoContainer = uiObj->getChild("container")->as<GComponent>();
	//GController* _cc = uiObj->getController("c1");

	//Rainbow::GameObject* ui3dGameObj = ui3dScene->getRoot()->add3DUIChild(uiObj);
	//ui3dGameObj->GetTransform()->SetWorldPosition(Rainbow::Vector3f(100, 100.f, 100.f));
	//ui3dGameObj->GetTransform()->SetWorldScale(50.f);
	//m_Scene->AddGameObject(ui3dGameObj);

	//auto dragDemoObj = UIPackage::createObject("Basics", "Demo_Drag&Drop")->as<GComponent>();
	//GComponent* obj = dragDemoObj;
	//_demoContainer->removeChildren();
	//_demoContainer->addChild(obj);
	//_cc->setSelectedIndex(1);
	//obj->getChild("a")->setDraggable(true);

	//fairygui::GButton* b = obj->getChild("b")->as<GButton>();
	//b->setDraggable(true);
	//b->addEventListener(UIEventType::UIEventType_DragStart, [b](FUIEventContext* context) {
	//	//Cancel the original dragging, and start a new one with a agent.
	//	context->preventDefault();

	//	DragDropManager::getInstance()->startDragIn3DUI(b->getIcon(), Value(b->getIcon()), context->getInput()->getTouchId());
	//	});

	//GButton* c = obj->getChild("c")->as<GButton>();
	//c->setIcon("");
	//c->addEventListener(UIEventType::UIEventType_Drop, [c](FUIEventContext* context) {
	//	c->setIcon(context->getDataValue().asString());
	//	});

	//GObject* bounds = obj->getChild("n7");
	//Rect rect1 = bounds->transformRect(Rect(Rainbow::Vector2f::zero, bounds->getSize()), UI3DGRoot);

	////---!!Because at this time the container is on the right side of the stage and beginning to move to left(transition), so we need to caculate the final position
	//rect1.origin.x -= obj->getParent()->getX();
	////----

	//GButton* d = obj->getChild("d")->as<GButton>();
	//d->setDraggable(true);
	//d->setDragBounds(rect1);

#if USE_CAMERA_GIZMO_MANAGER
	GetGizmoManager().AttachedCamera(m_Camera);
#endif
#if USE_PREVIEW_RENDERBUFFER && PREVIEW_RENDERBUFFER_ENABLE
	GetPreviewRenderbufferManager().AttachedCamera(m_Camera);
#endif
	m_Camera->SetVerticalFieldOfView(85.0f);
	m_Camera->SetNear(10);
	m_Camera->SetFar(12800);
	Rectf rect = GetScreenManagerPtr()->GetRect();
	float width = rect.GetWidth();
	float height = rect.GetHeight();
	m_Camera->SetAspect(width / height);
	m_Camera->SetOrthographic(false);
	Rainbow::Transform* trans = m_Camera->GetGameObject()->GetTransform();
	trans->SetLocalPosition(eyepos.toVector3());
	trans->SetLocalRotation(rot);

	m_Camera->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), false);
	m_Camera->SetLayerMask(static_cast<LayerIndex>(kLayerMinimap), false);

	GetLODGroupManager().SetMainCamera(m_Camera->GetInstanceID());

	#if BUILD_MINI_EDITOR_APP
	{
		g_WorldMgr->m_pEditorCbeScene->setCamera(m_Camera);
		//g_WorldMgr->m_pEditorCmScene->setCamera(m_Camera);
		g_WorldMgr->m_pEditorOmodScene->setCamera(m_Camera);
	}
	#endif

	GetISandboxActorSubsystem()->AddPlanarReflectionObject(m_Scene, pworld, m_Camera);

	Rainbow::GameObject* billboardBatchObj = GetBillboardMeshBatchObj();
	if (billboardBatchObj && !billboardBatchObj->IsInScene())
	{
		m_Scene->AddGameObject(billboardBatchObj);
	}

	//m_PlanarReflection = PlanarReflectionEffect::Create(m_Camera);
	//m_Scene->AddGameObject(m_PlanarReflection->GetGameObject());
	//m_PlanarReflection->Init(512, 512);
	//m_PlanarReflection->SetWorld(pworld);

	if (is_own)
	{
		m_Camera->SetAllowDynamicResolution(true);
		GetMiniCraftRenderer().SetCamera(m_Camera);
	}
	else
	{
		m_Camera->SetEnable(false);
	}

	SetWeaponItemType(ICON_GEN_MESH);

	// front camera copy form m_Camera except FOV
	//Rainbow::GameObject* frontCameraGo = Rainbow::GameObject::Create();
	//this->m_FrontCamera = frontCameraGo->CreateComponent<Camera>();
	//this->m_Scene->AddGameObject(frontCameraGo);
	////this->m_FrontCamera->SetShadowEnable(this->m_Camera->GetShadowEanble());
	////this->m_FrontCamera->SetFogEnable(this->m_Camera->GetFogEnable());	
	//this->m_FrontCamera->SetVerticalFieldOfView(57);
	//this->m_FrontCamera->SetNear(this->m_Camera->GetNear());		// todo opt
	//this->m_FrontCamera->SetFar(this->m_Camera->GetFar());
	//this->m_FrontCamera->SetAspect(this->m_Camera->GetAspect());
	//this->m_FrontCamera->SetOrthographic(this->m_Camera->GetOrthographic());
	//this->m_FrontCamera->GetTransform()->SetLocalPosition(eyepos.toVector3());
	//this->m_FrontCamera->GetTransform()->SetLocalRotation(rot);
	//this->m_FrontCamera->ClearAllLayerMask();
	//this->m_FrontCamera->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);
	////this->m_FrontCamera->SetBackGroundColor(ColorRGBAf(255, 0, 0, 0));
	//this->m_FrontCamera->SetClearMask(kClearMaskDepth);
	//this->m_FrontCamera->SetRenderFeatureFlags(kRenderFeatureNone);
	//this->m_FrontCamera->SetAllowDynamicResolution(true);
	//GetFirstPersonCameraRenderTarget().InitCameraProperty(this->m_FrontCamera);
	//this->m_FrontCamera->SetClearMask(kClearMaskNone);
	//this->m_FrontCamera->SetEnable(false);
	//this->m_Camera->SetRenderFeatureFlags(kRenderFeatureAllowPostprocess | kRenderFeatureAllowHDR);

	//if (!is_own)
	//{
	//	m_FrontCamera->SetEnable(false);
	//}
	//Vector4f(0.0004f, 0.0004f, 0.0f, 0.0f);// 
	// 重新启用阴影偏移初始化，使用更小的值进一步减少浮空感
	m_ShadowBias = Vector4f(0.001f, 0.002f, 0.0f, 0.0f);  // 进一步减小bias值
	m_InitLightRotation = Vector3f(45.0f, 45.0f, 0.0f);
	Rainbow::GameObject* lightGo = Rainbow::GameObject::Create();
	m_Light = lightGo->CreateComponent<DirectionalLight>();
	//lightGo->GetComponent<Transform>()->SetLocalEulerAngle(Vector3f(135.0f, 45.0, 0.0f));
	lightGo->GetComponent<Rainbow::Transform>()->SetLocalEulerAngle(m_InitLightRotation);
	m_Scene->AddGameObject(lightGo);

	//m_BlockLine = ENG_NEW(BlockDecalMesh)("highlight", GETTEX_WITHDEFAULT);
	m_BlockLine = BlockDecalMesh::Create("highlight", GETTEX_WITHDEFAULT);
	m_BlockLine->SetIsSelectHightLight();
	m_BlockLine->AttachToScene(m_Scene);

	m_ShadowFaces = Rainbow::ShadowFace::Create();
	m_ShadowFaces->AttachToScene(m_Scene);

	m_TerrainDecalEffect = Rainbow::TerrainDecalEffect::Create();
	m_Scene->AddGameObject(m_TerrainDecalEffect->GetGameObject());

	m_CurveFaces = CurveFace::Create();
	m_CurveFaces->SetMainSceneId(m_Scene->GetGameSceneId());
	m_CurveFaces->addMtl(CURVEFACEMTL_RAILHINT, "blocks/rail_hint.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_RAILRED, "blocks/rail_red.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_TEX12006, "itemmods/12006/texture1.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_TEXRED, "itemmods/12007/texture_red.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_TEXBLUE, "itemmods/12007/texture_blue.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_TEXWHITE, "itemmods/12007/texture_white.png");
	auto tex_dep_func_mtl = m_CurveFaces->addMtl(CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, "itemmods/12007/texture_white.png");
	tex_dep_func_mtl->m_Mtl->SetDepthFunc(DepthFunc::kDepthFuncAlways);

	auto mtlEx = m_CurveFaces->addMtl(CURVEFACEMTL_TEXWHITE_EX, "itemmods/12007/texture_white.png");
	mtlEx->m_Mtl->SetDepthFunc(DepthFunc::kDepthFuncNearEqual);
	mtlEx->m_Mtl->SetDepthBias(-1);
	mtlEx->m_Mtl->SetSlopeScaledDepthBias(-0.05f);

	m_CurveFaces->addMtl(CURVEFACEMTL_TEX12008, "itemmods/12008/texture1.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_JIGUANG, "blocks/jiguang.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_COPYRANGELINE, "blocks/copy_range_line.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_CONSTRUCTIONLINE, "blocks/construction_line.png", BLEND_OPAQUE);
	m_CurveFaces->addMtl(CURVEFACEMTL_DISTANCELINE, "blocks/distance_line.png", BLEND_ADD);
	m_CurveFaces->addMtl(CURVEFACEMTL_GREENLINE, "blocks/green_line.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_DIRECTION, "blocks/direction.png", BLEND_ALPHABLEND, InlineSamplerType::point_clamp_sampler);
	m_CurveFaces->addMtl(CURVEFACEMTL_1BLUE, "blocks/l_blue.png", BLEND_OPAQUE);
	m_CurveFaces->addMtl(CURVEFACEMTL_1YELLOW, "blocks/l_yellow.png", BLEND_OPAQUE);
	m_CurveFaces->addMtl(CURVEFACEMTL_1RED, "blocks/l_red.png", BLEND_OPAQUE);
	m_CurveFaces->addMtl(CURVEFACEMTL_ICOVECENTER, "blocks/ico_ve_center.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_YELLOWGREEN, "blocks/l_yellowgreen.png", BLEND_OPAQUE);
	m_CurveFaces->addMtl(CURVEFACEMTL_LIGHTBLUE, "blocks/l_lightblue.png", BLEND_OPAQUE);
	m_CurveFaces->addMtl(CURVEFACEMTL_LIGHTNING2, "particles/texture/lightning2.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_LIGHTNING1, "particles/texture/lightning1.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_LIGHTNING3, "particles/texture/lightning3.png");
	m_CurveFaces->addMtl(CURVEFACEMTL_TRIGGERGUIDE, "blocks/l_white.png", BLEND_ADD);
	m_Scene->AddGameObject(m_CurveFaces->GetGameObject());
 //
	m_CurveScreenFaces = CurveScreen::Create();
	m_CurveScreenFaces->SetMainSceneId(m_Scene->GetGameSceneId());
	m_CurveScreenFaces->addMtl(0, "blocks/phyparts_green.png", BLEND_ADDBLEND);
	m_CurveScreenFaces->addMtl(1, "blocks/phyparts_yellow.png", BLEND_ADDBLEND);
	m_CurveScreenFaces->addMtl(2, "blocks/phyparts_blue.png", BLEND_ADDBLEND);
	m_CurveScreenFaces->addMtl(3, "blocks/l_white.png", BLEND_ADDBLEND);
	m_CurveScreenFaces->addMtl(4, "blocks/l_red.png", BLEND_ADDBLEND);
	m_CurveScreenFaces->addMtl(5, "blocks/phyparts_deepgreen.png", BLEND_ADDBLEND);
	m_CurveScreenFaces->addMtl(6, "blocks/phyparts_deepyellow.png", BLEND_ADDBLEND);
	m_CurveScreenFaces->addMtl(7, "blocks/phyparts_blue.png", BLEND_ADDBLEND);
	m_CurveScreenFaces->addMtl(8, "blocks/phyparts_1.png", BLEND_ALPHABLEND);
	m_CurveScreenFaces->addMtl(9, "blocks/phyparts_2.png", BLEND_ALPHABLEND);
	m_CurveScreenFaces->addMtl(10, "blocks/phyparts_3.png", BLEND_ALPHABLEND);
	m_CurveScreenFaces->addMtl(11, "blocks/phyparts_4.png", BLEND_ALPHABLEND);
	m_CurveScreenFaces->addMtl(12, "blocks/phyparts_5.png", BLEND_ALPHABLEND);
	m_CurveScreenFaces->addMtl(13, "blocks/phyparts_6.png", BLEND_ALPHABLEND);
	m_CurveScreenFaces->addMtl(14, "blocks/phyparts_7.png", BLEND_ALPHABLEND);
	m_CurveScreenFaces->addMtl(15, "blocks/phyparts_8.png", BLEND_ALPHABLEND);

	m_Scene->AddGameObject(m_CurveScreenFaces->GetGameObject());

	if(pworld->hasSky())
	{
		Planet planet ;
		//外星也用动态天空盒 dyy
		 switch(pworld->getCurMapID())
		 {
		 case MAPID_MENGYANSTAR: planet = PLANET_TWINKLE; break;
		 case MAPID_LIEYANSTAR:  planet = PLANET_FLAME;   break;
		 default:
		 case MAPID_GROUND:      planet = PLANET_EARTH;   break;
		 }
		SandboxRenderSetting& setting = GetSandboxRenderSetting();// static_cast<SandboxRenderSetting*>(GetRenderSetting().m_CustomRenderSetting);
		SkyboxType skyboxType = setting.m_Data.m_UsePlaneSkybox ? SkyboxType::Plane : SkyboxType::ProgramCubemap;
		
		m_Sky = Rainbow::SkyPlane::Create(planet, m_Camera,skyboxType);
		m_Scene->AddGameObject(m_Sky->GetGameObject());
		m_Scene->setBackground(m_Sky->GetComponent<MovableObject>());
		
		if (pworld->getCurMapID() == MAPID_GROUND)
		{
			m_WeatherRender = ENG_NEW(WeatherRender)(m_Scene, m_Camera, pworld);
		}
	}

	//if (pworld->getCurMapID() == 0)
	//{
	//	m_SandstormEffect = Rainbow::SandstormEffect::Create(m_Camera);
	//	m_Scene->AddGameObject(m_SandstormEffect->GetGameObject());
	//	m_Scene->AddGameObject(m_SandstormEffect->GetFullScreenEffect()->GetGameObject());
	//}
	//else
	//{
	//	m_SandstormEffect = NULL;
	//}
	m_EnvData.windStrength = m_World->m_Environ->m_WindStrength;
	m_EnvData.shadowIntensity = m_World->m_Environ->m_ShadowIntensity;
	m_EnvData.waterColor = m_World->m_Environ->m_WaterColor;
	m_EnvData.waterWaveSpeed = m_World->m_Environ->m_WaterWaveSpeed;
	m_EnvData.isWaterGlow = m_World->m_Environ->m_IsWaterGlow;

	m_CurCloudDensity = m_TargetCloudDensity = (MIN_CLOUD+MAX_CLOUD)/2*CLOUD_STEP;
#else
	//m_SandstormEffect = NULL;
#endif

	InitBlockPlacementPreview();
}

Rainbow::CustomRenderPipeline* WorldRenderer::createFirstPersonRenderPipeline()
{
	auto customRenderPipeline = ENG_NEW(CustomRenderPipeline)();
	customRenderPipeline->m_StencilStateOverrideData.m_IsOverrided = true;
	//customRenderPipeline->m_StencilStateOverrideData.m_StencilReadMask = 0x2;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilRef = 2;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilCompFunc = kFuncNotEqual;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilPass = kStencilOpKeep;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilFail = kStencilOpKeep;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilZFail = kStencilOpKeep;

	CustomForwardShadingPass* opaquePass = customRenderPipeline->GetRenderPassList().AddRenderPass<CustomForwardShadingPass>(true);
	opaquePass->SetStage(kRenderPassStageBeforeOpaque - 1);
	opaquePass->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);
	opaquePass->m_StencilStateOverrideData.m_IsOverrided = true;
	opaquePass->m_StencilStateOverrideData.m_StencilRef = 2;
	opaquePass->m_StencilStateOverrideData.m_StencilCompFunc = kFuncAlways;
	opaquePass->m_StencilStateOverrideData.m_StencilPass = kStencilOpReplace;
	opaquePass->m_StencilStateOverrideData.m_StencilFail = kStencilOpKeep;
	opaquePass->m_StencilStateOverrideData.m_StencilZFail = kStencilOpKeep;
	opaquePass->m_CameraOverrideData.m_IsOverrided = true;
	opaquePass->m_CameraOverrideData.m_Fov = 57;



	CustomForwardShadingPass* transparentNearPass = customRenderPipeline->GetRenderPassList().AddRenderPass<CustomForwardShadingPass>(false);
	transparentNearPass->m_CameraOverrideData.m_IsOverrided = true;
	transparentNearPass->m_CameraOverrideData.m_Fov = 57;
	transparentNearPass->SetStage(kRenderPassStageAfterTranslucent + 2);
	transparentNearPass->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);


	//不挡住第一人称的手
	transparentNearPass->m_StencilStateOverrideData.m_IsOverrided = true;
	transparentNearPass->m_StencilStateOverrideData.m_StencilRef = 2;
	transparentNearPass->m_StencilStateOverrideData.m_StencilCompFunc = kFuncNotEqual;
	transparentNearPass->m_StencilStateOverrideData.m_StencilPass = kStencilOpKeep;
	transparentNearPass->m_StencilStateOverrideData.m_StencilFail = kStencilOpKeep;
	transparentNearPass->m_StencilStateOverrideData.m_StencilZFail = kStencilOpKeep;

	transparentNearPass->m_DepthStateOverrideData.m_IsOverrided = true;
	transparentNearPass->m_DepthStateOverrideData.m_DepthWrite = kDepthWriteDisable;
	transparentNearPass->m_DepthStateOverrideData.m_DepthFunc = kDepthFuncAlways;


	OutlinePass* outlinePass = customRenderPipeline->GetRenderPassList().AddRenderPass<OutlinePass>();
	outlinePass->SetStage(kRenderPassStageAfterTranslucent);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::yellow), 1);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::white), 2);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::red), 3);

	return customRenderPipeline;
}

Rainbow::CustomRenderPipeline* WorldRenderer::createCustomModelFirstPersonRenderPipeline()
{

	auto customRenderPipeline = ENG_NEW(CustomRenderPipeline)();
	customRenderPipeline->m_StencilStateOverrideData.m_IsOverrided = true;
	//customRenderPipeline->m_StencilStateOverrideData.m_StencilReadMask = 0x2;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilRef = 2;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilCompFunc = kFuncNotEqual;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilPass = kStencilOpKeep;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilFail = kStencilOpKeep;
	customRenderPipeline->m_StencilStateOverrideData.m_StencilZFail = kStencilOpKeep;

	CustomForwardShadingPass* opaquePass = customRenderPipeline->GetRenderPassList().AddRenderPass<CustomForwardShadingPass>(true);
	opaquePass->SetStage(kRenderPassStageBeforeOpaque - 1);
	opaquePass->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);
	opaquePass->m_StencilStateOverrideData.m_IsOverrided = true;
	opaquePass->m_StencilStateOverrideData.m_StencilRef = 2;
	opaquePass->m_StencilStateOverrideData.m_StencilCompFunc = kFuncAlways;
	opaquePass->m_StencilStateOverrideData.m_StencilPass = kStencilOpReplace;
	opaquePass->m_StencilStateOverrideData.m_StencilFail = kStencilOpKeep;
	opaquePass->m_StencilStateOverrideData.m_StencilZFail = kStencilOpKeep;
	opaquePass->m_CameraOverrideData.m_IsOverrided = true;
	opaquePass->m_CameraOverrideData.m_Fov = 57;



	CustomForwardShadingPass* transparentFartherPass = customRenderPipeline->GetRenderPassList().AddRenderPass<CustomForwardShadingPass>(false);
	transparentFartherPass->SetStage(kRenderPassStageAfterTranslucent + 1);
	transparentFartherPass->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);

	//不挡住第一人称的手
	transparentFartherPass->m_StencilStateOverrideData.m_IsOverrided = true;
	transparentFartherPass->m_StencilStateOverrideData.m_StencilRef = 2;
	transparentFartherPass->m_StencilStateOverrideData.m_StencilCompFunc = kFuncNotEqual;
	transparentFartherPass->m_StencilStateOverrideData.m_StencilPass = kStencilOpKeep;
	transparentFartherPass->m_StencilStateOverrideData.m_StencilFail = kStencilOpKeep;
	transparentFartherPass->m_StencilStateOverrideData.m_StencilZFail = kStencilOpKeep;


	transparentFartherPass->m_CameraOverrideData.m_IsOverrided = true;
	transparentFartherPass->m_CameraOverrideData.m_Fov = 57;

	transparentFartherPass->m_DepthStateOverrideData.m_IsOverrided = true;
	transparentFartherPass->m_DepthStateOverrideData.m_DepthWrite = kDepthWriteEnable;
	transparentFartherPass->m_DepthStateOverrideData.m_DepthFunc = kDepthFuncFarther;

	CustomForwardShadingPass* transparentNearPass = customRenderPipeline->GetRenderPassList().AddRenderPass<CustomForwardShadingPass>(false);
	transparentNearPass->SetStage(kRenderPassStageAfterTranslucent + 2);
	transparentNearPass->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);

	transparentNearPass->m_CameraOverrideData.m_IsOverrided = true;
	transparentNearPass->m_CameraOverrideData.m_Fov = 57;

	transparentNearPass->m_DepthStateOverrideData.m_IsOverrided = true;
	transparentNearPass->m_DepthStateOverrideData.m_DepthFunc = kDepthFuncNear;
	transparentNearPass->m_DepthStateOverrideData.m_DepthWrite = kDepthWriteEnable;

	OutlinePass* outlinePass = customRenderPipeline->GetRenderPassList().AddRenderPass<OutlinePass>();
	outlinePass->SetStage(kRenderPassStageAfterTranslucent);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::yellow), 1);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::white), 2);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::red), 3);

	return customRenderPipeline;
}

Rainbow::CustomRenderPipeline* WorldRenderer::createCustomRenderPipeline()
{
	auto customRenderPipeline = ENG_NEW(CustomRenderPipeline)();

	CustomForwardShadingPass* opaqueFartherPass = customRenderPipeline->GetRenderPassList().AddRenderPass<CustomForwardShadingPass>(true);
	opaqueFartherPass->SetStage(kRenderPassStageAfterTranslucent + 1);
	opaqueFartherPass->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);
	opaqueFartherPass->m_DepthStateOverrideData.m_IsOverrided = true;
	opaqueFartherPass->m_DepthStateOverrideData.m_DepthWrite = kDepthWriteEnable;
	opaqueFartherPass->m_DepthStateOverrideData.m_DepthFunc = kDepthFuncFarther;


	CustomForwardShadingPass* opaqueNearPass = customRenderPipeline->GetRenderPassList().AddRenderPass<CustomForwardShadingPass>(true);
	opaqueNearPass->SetStage(kRenderPassStageAfterTranslucent + 2);
	opaqueNearPass->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);
	opaqueNearPass->m_DepthStateOverrideData.m_IsOverrided = true;
	opaqueNearPass->m_DepthStateOverrideData.m_DepthWrite = kDepthWriteEnable;
	opaqueNearPass->m_DepthStateOverrideData.m_DepthFunc = kDepthFuncNear;

	CustomForwardShadingPass* transparentNearPass = customRenderPipeline->GetRenderPassList().AddRenderPass<CustomForwardShadingPass>(false);
	transparentNearPass->SetStage(kRenderPassStageAfterTranslucent + 3);
	transparentNearPass->SetLayerMask(static_cast<LayerIndex>(kLayerIndexCustom_FRONT_SCENE), true);
	transparentNearPass->m_DepthStateOverrideData.m_IsOverrided = true;
	transparentNearPass->m_DepthStateOverrideData.m_DepthWrite = kDepthWriteDisable;
	transparentNearPass->m_DepthStateOverrideData.m_DepthFunc = kDepthFuncAlways;

	OutlinePass* outlinePass = customRenderPipeline->GetRenderPassList().AddRenderPass<OutlinePass>();
	outlinePass->SetStage(kRenderPassStageAfterTranslucent);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::yellow), 1);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::white), 2);
	outlinePass->SetOutlineParams(OutlineParams(3.0f, ColorRGBAf::red), 3);

	return customRenderPipeline;
}


WorldRenderer::~WorldRenderer()
{
	m_Scene->SetWorld(nullptr);
	ClearDynamicRendererData();
	mInViewBlockParticle.clear();
	std::map<int, Rainbow::Entity *>::iterator iter = m_GlobalEffects.begin();
	for(; iter != m_GlobalEffects.end(); iter++)
	{   
		DESTORY_GAMEOBJECT_BY_COMPOENT(iter->second);
	}

	/*std::map<long,Rainbow::RenderLines *>::iterator renderlineIter = m_RenderLineVec.begin();
	for(; renderlineIter != m_RenderLineVec.end(); renderlineIter++)
	{  
		if(renderlineIter->second){
			renderlineIter->second->detachFromScene();
			renderlineIter->second->release();
		}
	}*/

	//ENG_DESTROY_OBJECT(m_PostprocessChain);
	m_PostprocessChain = nullptr;
	//DESTORY_GAMEOBJECT_BY_COMPOENT(m_RainSnow);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Sky);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ShadowFaces);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_CurveFaces);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_CurveScreenFaces);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_BlockLine);
	// 清理多方块轮廓显示 - 参考m_BlockLine的销毁方式
	for (BlockDecalMesh* mesh : m_OutlineBlockMeshes)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(mesh);
	}
	m_OutlineBlockMeshes.clear();
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_BlockPlacementPreview);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_TerrainDecalEffect);
	if (GetMapMarkingMgrInterface())
	{
		GetMapMarkingMgrInterface()->DetachFromScene(m_Scene);
	}
	//OGRE_RELEASE(m_BlockLine);
	ENG_DELETE(m_CustomRenderPipeline);
	ENG_DELETE(m_FirstPersonRenderPipeline);
	ENG_DELETE(m_CustomModelFirstPersonRenderPipeline);
	ENG_DELETE(m_WeatherRender);
	GetWorld()->RemoveGameScene(m_Scene);
#if USE_CAMERA_GIZMO_MANAGER
	GetGizmoManager().AttachedCamera(nullptr);
#endif
#if USE_PREVIEW_RENDERBUFFER && PREVIEW_RENDERBUFFER_ENABLE
	GetPreviewRenderbufferManager().AttachedCamera(nullptr);
#endif

	ENG_DELETE(m_CustomRenderPipeline);


}

Rainbow::SharePtr<PostprocessChain> WorldRenderer::createPostprocessChain()
{
	PostprocessChain* postprocessChain = CreateObject<PostprocessChain>();
	m_PostprocessGodray = postprocessChain->Add<PostprocessGodray>();
	m_PostprocessGodray->SetEnabled(false);

	m_PostprcessAntialiasing = postprocessChain->Add<PostprocessAntialiasing>();
	m_PostprcessAntialiasing->SetEnabled(false);


	m_PostprocessBloom = postprocessChain->Add<PostprocessBloom>();
	m_PostprocessBloom->SetEnabled(false);

	m_PostprocessLUTs = postprocessChain->Add<PostprocessLUTs>();
	m_PostprocessLUTs->SetEnabled(false);

	m_PostprocessDof = postprocessChain->Add<PostprocessDof>();
	m_PostprocessDof->SetEnabled(false);

	m_PostprocessSSAO = postprocessChain->Add<PostprocessSSAO>();
	m_PostprocessSSAO->SetEnabled(false);

#if PLATFORM_WIN
	m_PostprcessAntialiasing->m_Setting.m_AntialiasingMethod = kAntialiasingMethodFXAA;
	m_PostprcessAntialiasing->m_Setting.m_AntialiasingQuality = kAntialiasingQualityHigh;
#else
	m_PostprcessAntialiasing->m_Setting.m_AntialiasingMethod = kAntialiasingMethodFXAA;
	m_PostprcessAntialiasing->m_Setting.m_AntialiasingQuality = kAntialiasingQualityLow;
#endif
	return MoveToSharePtr(postprocessChain);
}

//Rainbow::RenderLines *WorldRenderer::getRenderLines(long container,const WCoord &pos){
//#ifndef IWORLD_SERVER_BUILD
//	Rainbow::RenderLines *renderlines = NULL;
//	std::map<long, Rainbow::RenderLines *>::iterator iter = m_RenderLineVec.find(container);
//	if(iter != m_RenderLineVec.end())
//	{	
//		renderlines = iter->second;	
//	}else{
//		renderlines = ENG_NEW(Rainbow::RenderLines)();
//		renderlines->attachToScene(m_Scene);
//		m_RenderLineVec[container] = renderlines;		
//	}
//	renderlines->setPosition(pos.toWorldPos());
//	return renderlines;
//#else
//	return NULL;
//#endif
//}
//
//void WorldRenderer::setEnableGodray(bool value)
//{
//	m_EnableGodray = value;
//	if (m_EnableGodray)
//	{
//		m_Camera->SetRenderFeatureFlags(kRenderFeatureAllowPostprocess | kRenderFeatureAllowHDR);
//		//GetRenderSetting().SetHDR(true);
//		GetPostprocessSetting().m_GodRaySetting.m_Enable = true;
//	
//	}
//	else
//	{
//		m_Camera->ClearRenderFeatureFlags(kRenderFeatureAllowPostprocess | kRenderFeatureAllowHDR);
//		//GetRenderSetting().SetHDR(false);
//		GetPostprocessSetting().m_GodRaySetting.m_Enable = false;
//	}
//}

void WorldRenderer::removeRenderLines(long container){

	//std::map<long, Rainbow::RenderLines *>::iterator iter = m_RenderLineVec.find(container);
	//if(iter != m_RenderLineVec.end())
	//{
	//	iter->second->detachFromScene();
	//	iter->second->release();
	//	m_RenderLineVec.erase(iter);
	//}
}




int WorldRenderer::addGlobalEffect(const char* path, int id)
{
	if (m_GlobalEffects.find(id) != m_GlobalEffects.end())
	{
		return id;
	}
#ifndef IWORLD_SERVER_BUILD
	Rainbow::Entity* fx = Rainbow::Entity::Create();
	fx->Load(path);
	fx->SetVisibleDistance(10240 * 100.f);
	m_Scene->AddGameObject(fx->GetGameObject());
	//fx->attachToScene(m_Scene);
#else
	Entity* fx = NULL;
#endif
	if (fx)
	{
		m_GlobalEffects[id] = fx;
	}
	return id;
}

void WorldRenderer::removeGlobalEffect(int id)
{
	std::map<int, Rainbow::Entity *>::iterator iter = m_GlobalEffects.find(id);
	if(iter != m_GlobalEffects.end())
	{
#ifndef IWORLD_SERVER_BUILD
		//iter->second->GetGameObject()->RemoveFromScene();
		DESTORY_GAMEOBJECT_BY_COMPOENT(iter->second);
#endif
		m_GlobalEffects.erase(iter);
	}
}

void WorldRenderer::setGlobalEffectPos(int id, int x, int y, int z)
{
#ifndef IWORLD_SERVER_BUILD
	std::map<int, Rainbow::Entity *>::iterator iter = m_GlobalEffects.find(id);
	if(iter != m_GlobalEffects.end())
	{
		WCoord pos(x,y,z);
		iter->second->SetPosition(pos.toWorldPos());
	}
#endif
}

void WorldRenderer::setGlobalEffectScale(int id, float sx, float sy, float sz)
{
#ifndef IWORLD_SERVER_BUILD
	std::map<int, Rainbow::Entity *>::iterator iter = m_GlobalEffects.find(id);
	if(iter != m_GlobalEffects.end())
	{
		iter->second->SetScale(Rainbow::Vector3f(sx, sy, sz));
	}
#endif
}

void WorldRenderer::setRenderCamera(Rainbow::Camera* pcamera)
{
}

void WorldRenderer::tick()
{
    OPTICK_EVENT();
	SANDBOXPROFILING_FUNC("WorldRenderer::tick");
    METRIC_PROFILER("WorldRenderer::tick");

#ifndef IWORLD_SERVER_BUILD
	auto mapId = m_World->getCurMapID();
	if(mapId == GetWorldManagerPtr()->m_RenderEyeMap)
	{
		//添加一个随机逻辑，不需要每次tick都来执行这个随机特效
		int seed = std::rand();
		std::default_random_engine gen(seed);
		std::uniform_int_distribution<int> distribution(1, 4);
		int result = distribution(gen);
		if (result == 2)
		{
			//Rainbow::MaterialInstance::m_bDayTime = m_World->isDaytime();
			WCoord blockpos = CoordDivBlock(GetWorldManagerPtr()->m_RenderEyePos);
			doBlockRandomEffects(blockpos);
		}
	}
	/*int eyeblock = 0;
	if(m_World->getCurMapID() == GetWorldManagerPtr()->m_RenderEyeMap)
	{
		WCoord eyepos = CoordDivBlock(GetWorldManagerPtr()->m_RenderEyePos);
		eyeblock = m_World->getBlockID(eyepos);
		if(GetIPlayerControl() && GetIPlayerControl()->isInPortal()) eyeblock = BLOCK_PORTAL;
	}*/
	m_Tick++;
	if (m_Tick > 10000)
		m_Tick = 0;
	tickEnv();
	GetMiniCraftRenderer().Tick();
#endif
}
static void AddEvnLight(Rainbow::ColourValue& skyLightColor, Rainbow::ColourValue& ambientColor, float fVal)
{
	if (abs(fVal) > 0.000001f) {
		{
			float a = skyLightColor.a + fVal;
			if (a > 1.0f) a = 1.0f;
			else if (a < 0.0f) a = 0.0f;
			skyLightColor.a = a;
		}

		{
			float r = ambientColor.r + fVal;
			float g = ambientColor.g + fVal;
			float b = ambientColor.b + fVal;
			float a = ambientColor.a + fVal;
			if (r > 1.0f) r = 1.0f;
			else if (r < 0.0f) r = 0.0f;
			if (g > 1.0f) g = 1.0f;
			else if (g < 0.0f) g = 0.0f;
			if (b > 1.0f) b = 1.0f;
			else if (b < 0.0f) b = 0.0f;
			if (a > 1.0f) a = 1.0f;
			else if (a < 0.0f) a = 0.0f;
			ambientColor.r = r;
			ambientColor.g = g;
			ambientColor.b = b;
			ambientColor.a = a;
		}
	}
}

void WorldRenderer::tickEnv()
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD
	int eyeblock = 0;
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())// studio编辑的地图生效
	{
		if (m_Skylight)
			m_Skylight->SetEnable(false);
	}
	else
	{
		if (m_Skylight)
			m_Skylight->SetEnable(true);
	}
	if(m_World->getCurMapID() == GetWorldManagerPtr()->m_RenderEyeMap)
	{
		WCoord eyepos = CoordDivBlock(GetWorldManagerPtr()->m_RenderEyePos);
		eyeblock = m_World->getBlockID(eyepos);
		if (GetIPlayerControl())
		{
			auto portalComponent = dynamic_cast<IClientActor*>(GetIPlayerControl())->getActorComponent(ComponentType::COMPONENT_ACTOR_IN_PORTAL);
			if (portalComponent)
			{
				bool result = portalComponent->Event2().Emit<int&>("Portal_eyeblock", eyeblock);
				Assert(result);
			}
		}

		//if(GetIPlayerControl())
		//{
		//	m_ShadowFaces->updateOrigin(GetIPlayerControl()->GetPlayerControlPosition());
		//}
	}
	else return;

	float t = m_World->m_Environ->getCelestialAngle();
	t = t - 0.75f;
	if(t < 0) t += 1.0f;

	if(m_Sky)
	{
		bool bloomActived = false;
		if (m_Camera && m_PostprocessBloom)
		{
			bloomActived = m_Camera->HasRenderFeatureFlag(kRenderFeatureAllowPostprocess) && m_PostprocessBloom->IsEnabled();
		}
		m_Sky->SetBloomActivated(bloomActived);

		m_Sky->update(t);
		m_Sky->setMoonPhase(GetWorldManagerPtr()->getDayNightTime()/TICKS_ONEDAY);
	}
	if (m_WeatherRender) m_WeatherRender->tick();
	float angle = t*360.0f + 90.0f;
	if(angle >= 360.0f) angle -= 360.0f;

	angle = RoundToCeil(angle/5.0f) * 5.0f;

	SandboxRenderSetting& setting = GetSandboxRenderSetting();

	Rainbow::Vector3f dir;
	dir.x = Rainbow::SinByAngle(angle);
	dir.y = -Rainbow::CosByAngle(angle);
	dir.z = 0;// 30.0f / 90.0f;
	float hours = GetWorldManagerPtr()->getHours();
	if (setting.IsNightMode(hours))// 夜晚方向光取反
	{
		dir = -dir;
	}
	//if (!setting.CanShowShadow(hours))
	//{
	//	dir = -dir;
	//}
	dir.NormalizeSafe();
	//if(dir.y < 0) dir = -dir;

	Quaternionf quat;
	setRotateArc(Vector3f(0, 0, -1.0f), dir, quat);

	if (m_CurShadowOpen) 
	{
		Vector3f z = quat.GetAxisZ();
		// 减少动态调整的幅度，避免过度放大bias值
		float depthFactor = Lerp(0.2f, 0.8f, abs(z.y));

		auto& shadowConfig = GetRenderSetting().GetShadowConfig();
		for (int i = 0; i < 4; ++i) {
			shadowConfig.m_ShadowBias[i] = m_ShadowBias.x * depthFactor;
		}
		shadowConfig.m_ShadowSlopeBias = m_ShadowBias.y * depthFactor;
		
		// 调整其他影响阴影贴合的深度参数
		shadowConfig.m_ShadowDepthBias = 3.0f; 
		shadowConfig.m_ShadowSlopeScaleDepthBias = 1.0f;  
	}

	setting.Tick();
	if (m_Light && !m_StudioCtrlLightDirSwitch)
	{
		m_Light->GetComponent<Transform>()->SetLocalRotation(quat);
	}

	//m_Scene->setSkyLightDir(dir);
	m_Scene->setTorchLightColor(getCurTorchLight());

	if(m_World->hasSky() && m_Sky != nullptr)
	{
		Rainbow::ColourValue skyLightColor = getCurSkyLight();
		ColorRGBAf ambient = m_Sky->getAmbient();
		Rainbow::ColourValue ambientColor = Rainbow::ColourValue(ambient.r, ambient.g, ambient.b, ambient.a);// LegacyConvertColorRGBA32(m_Sky->getAmbient());

		//根据环境暗度值来调整亮度
		float a = skyLightColor.a - m_World->m_Environ->getDarkStrength();
		if (a<0.0) a= 0.0;
		skyLightColor.a = a;

		//add by navy
		if (m_World->getCurMapID() == MAPID_LIEYANSTAR)//烈焰星固定调亮
		{
			//skyLightColor.a = skyLightColor.r = skyLightColor.g = skyLightColor.b =1.0f;
			ambientColor.r = ambientColor.g = ambientColor.b = 0.3f;//0.45f;
			//ambientColor.r = Rainbow::Clamp((float)ambientColor.r + 0.9f, (float)ambientColor.r, 1.0f);
			//ambientColor.g = Rainbow::Clamp((float)ambientColor.g + 0.9f, (float)ambientColor.g, 1.0f);
			//ambientColor.b = Rainbow::Clamp((float)ambientColor.b + 0.9f, (float)ambientColor.b, 1.0f);
			//ambientColor.a = Rainbow::Clamp((float)ambientColor.a + 0.9f, (float)ambientColor.a, 1.0f);
		}

		//玩家具有夜视buff,提高亮度
		if(GetIPlayerControl())
		{
			float fVal = 0.f;
			auto pAtt = dynamic_cast<IClientActor*>(GetIPlayerControl())->getActorComponent(ComponentType::COMPONENT_PLAYER_ATTRIB);
			if (pAtt)
			{
				bool result = pAtt->Event2().Emit<float&>("PlayerAttrib_worldvalue", fVal);
				Assert(result);
			}
			AddEvnLight(skyLightColor, ambientColor, fVal);
		}

		m_Scene->setSkyLightColor(skyLightColor);
		m_Scene->setAmbientColor(ColourValue(ambientColor.r, ambientColor.g, ambientColor.b, ambientColor.a));
	}
	else
	{
		m_Scene->setSkyLightColor(Rainbow::ColourValue(0,0,0));
		m_Scene->setAmbientColor(Rainbow::ColourValue(0.5f, 0.4f, 0.4f));
	}

	updateFogColor(eyeblock);


	//add by frankding
	//updateEnvShaderParam();
	SandboxRenderSetting& renderSetting = GetSandboxRenderSetting();

	bool openNoise = renderSetting.m_Data.m_UseTerrainNoiseUV;
	
	//lightwave------------------------------------//
	WCoord blockpos = CoordDivBlock(m_Camera->GetPosition());
	int height = m_World->getWaterSurfaceBlockUnder(blockpos.x, blockpos.y, blockpos.z); 
	int depth = m_Camera->GetPosition().y - height * BLOCK_SIZE;

	
	float causticsPower = 0.45f;
	if (GetIPlayerControl() != nullptr) 
	{
		Vector3f playerPos = GetIPlayerControl()->GetPlayerControlPosition().toVector3();
		const BiomeDef* def = m_World->getBiome(CoordDivBlock(playerPos.x), CoordDivBlock(playerPos.z));
		if (def != nullptr)
		{
			causticsPower = def->CausticsPower;
		}
	}

	for (int bid = 0; bid < m_VecBlockIDOpenlightwave.size(); bid++)
	{
		if (depth > 105 || !openNoise)
		{
			g_BlockMtlMgr.getMaterial(m_VecBlockIDOpenlightwave[bid])->setCausticsEnable(false, causticsPower);
		}
		else
		{
			g_BlockMtlMgr.getMaterial(m_VecBlockIDOpenlightwave[bid])->setCausticsEnable(true, causticsPower);

		}
	}

	//if (m_PlanarReflection != nullptr) 
	//{
	//	m_PlanarReflection->UpdateWaterHeight(GetMiniCraftRenderer().GetCurWaterHeight());
	//}
#endif
}

void WorldRenderer::update(float dtime)
{
	OPTICK_EVENT();
    METRIC_PROFILER("WorldRenderer::update");
#ifndef IWORLD_SERVER_BUILD
	//PROFINY_NAMED_SCOPE("WorldRenderer::Update")
	unsigned int dtick = TimeToTick(dtime);
	if(GetWorldManagerPtr() == NULL) return;
	if(m_Scene == NULL) return;
	WCoord pos = CoordDivSection(GetWorldManagerPtr()->m_RenderEyePos) * (BLOCK_SIZE*16);
	m_Scene->updateFocusArea(pos.toWorldPos(), 1.0f);

	//m_Scene->updateFocusArea(GetWorldManagerPtr()->m_RenderEyePos.toWorldPos(), 1.0f);
	// comment old code m_Scene->update(dtick);
	m_Scene->beginOneFrame();
	// todo UNDONE 
	if (m_ShadowFaces)
	{
		m_ShadowFaces->ResetFaces();
	}
	if (m_CurveFaces)
	{
		m_CurveFaces->resetFaces();
	}
	if (m_CurveScreenFaces)
	{
		m_CurveScreenFaces->resetFaces();
	}
	if (m_TerrainDecalEffect) 
	{
		m_TerrainDecalEffect->OnTick(dtime);
	}
	
	std::map<long, Rainbow::RenderLines *>::iterator iter0 = m_RenderLineVec.begin();
	for(; iter0!=m_RenderLineVec.end(); iter0++)
	{
		iter0->second->Reset();
	}

	//std::map<int, Rainbow::Entity *>::iterator iter = m_GlobalEffects.begin();
	//for(; iter!=m_GlobalEffects.end(); iter++)
	//{
	//	//iter->second->update(dtick);
	//}

	if (m_WeatherRender)
		m_WeatherRender->update(dtick);

	if (m_Sky)
		m_Sky->OnUpdate(dtime);

#if CHUNK_UPLOAD_IN_UPDATE
    GetMiniCraftRenderer().Update(dtime);
#endif//CHUNK_UPLOAD_IN_UPDATE
#endif
}

void WorldRenderer::setCloudDensity(int d)
{
	if(m_Sky) m_Sky->setCloudDensity(d);
}

Rainbow::ColourValue WorldRenderer::getLighting(const WCoord &pos)
{
	WCoord blockpos = CoordDivBlock(pos);

	float lt0, lt1;
	m_World->getBlockLightValue2(lt0, lt1, blockpos);

	Rainbow::ColourValue skylight = getCurSkyLight();
	skylight = skylight*(skylight.a*lt0);

	Rainbow::ColourValue torchlight = getCurTorchLight()*lt1;
	Rainbow::ColourValue lighting(Rainbow::Max(skylight.r,torchlight.r), Rainbow::Max(skylight.g,torchlight.g), Rainbow::Max(skylight.b,torchlight.b), 1.0f);

	if (m_Sky) 
	{
		ColorRGBAf ambientColor = m_Sky->getAmbient();
		return lighting + Rainbow::ColourValue(ambientColor.r, ambientColor.g, ambientColor.b, ambientColor.a);
	} 
	else return lighting + Rainbow::ColourValue(0.4f, 0.3f, 0.2f);
}

void WorldRenderer::setSkyTex(const std::string& path)
{
	if (getSky())
		getSky()->setSkyTex(path);
}

Rainbow::ColourValue WorldRenderer::getCurSkyLight()
{
	if(m_Sky)
	{
		ColorRGBAf skyLight = m_Sky->getSkyLight();
		Rainbow::ColourValue sunlight = ColourValue(skyLight.r, skyLight.g, skyLight.b, skyLight.a);// LegacyConvertColorRGBA32(m_Sky->getSkyLight());
		//sunlight.a = (15 - getSunLightSubtract())/15.0f;
		if(!m_Sky->GetIsUGCCustomSky())
		{
			sunlight.a = m_World->m_Environ->light2Bright(15 - m_World->m_Environ->getSunLightSubtract());
		}
		else
		{
			sunlight.a = 1.0f;
		}
		return sunlight;
	}
	else
	{
		return Rainbow::ColourValue(0,0,0);
	}
}

Rainbow::ColourValue WorldRenderer::getCurTorchLight()
{
	//return Rainbow::ColourValue(1.0f, 1.0f, 1.0f, 1.0f);
	return g_TorchLightColor;
}

Rainbow::ColourValue WorldRenderer::getCurAmbient()
{
	if (m_Sky) 
	{
		ColorRGBAf ambientColor = m_Sky->getAmbient();
		return Rainbow::ColourValue(ambientColor.r, ambientColor.g, ambientColor.b, ambientColor.a);
	} 
	else return Rainbow::ColourValue(0,0,0);
}
void WorldRenderer::updateEnvShaderParam()
{
	float shadowTargetIntensity = m_World->m_Environ->m_ShadowIntensity;
	ColorRGBAf waterWaveTargetColor;
	float windTargetSpeed;

	bool isUGCCustomSky = m_Sky && m_Sky->GetIsUGCCustomSky();
	if (!isUGCCustomSky)
	{
		waterWaveTargetColor = m_World->m_Environ->m_WaterColor;
		windTargetSpeed = m_World->m_Environ->m_WindStrength;
	}
	else
	{
		waterWaveTargetColor = m_Sky->getWaterColor();
		windTargetSpeed = m_Sky->getWindStrength();
		float waterReflectStrength = m_Sky->getWaterReflectStrength();
		GetLegacyGlobalShaderParamManager()->m_WaterReflectStrength = waterReflectStrength;
	}

	Environment::AdjustByStep(m_EnvData.shadowIntensity, shadowTargetIntensity, 0.05f);
	Environment::AdjustByStep(m_EnvData.waterColor, waterWaveTargetColor, 0.05f);
	Environment::AdjustByStep(m_EnvData.windStrength, windTargetSpeed, 0.05f);
	Environment::AdjustByStep(m_EnvData.isWaterGlow, m_World->m_Environ->m_IsWaterGlow, 0.05f);
	//这个参数在shader里差值出来的结果不是线性的，如果差值的话会导致乱飞
	m_EnvData.waterWaveSpeed = m_World->m_Environ->m_WaterWaveSpeed;

	m_Scene->setEnvData(m_EnvData);
}
void WorldRenderer::updateFogColor(int eyeblock)
{	 
#ifndef IWORLD_SERVER_BUILD

	bool needOpenFogEffect = false;
	int depth = 0;

	if (g_WorldMgr && m_World->getCurMapID() == MAPID_GROUND)
	{
		WCoord blockpos = CoordDivBlock(g_WorldMgr->m_RenderEyePos);
		if (GetIPlayerControl())
		{
			auto pAtt = dynamic_cast<IClientActor*>(GetIPlayerControl())->getActorComponent(ComponentType::COMPONENT_PLAYER_ATTRIB);
			if (pAtt)
			{
				bool result = pAtt->Event2().Emit<int&>("PlayerAttrib_WaterDepth", depth);
				Assert(result);
			}
		}
			
		if (m_Sky)
		{
			if (depth > 9)
			{
				m_Sky->setSkyCloudVisible(false);
				m_Sky->setSkySunVisible(false);
				m_Sky->setSkyMoonVisible(false);
			}
			else
			{
				float tempestStrength = m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_TEMPEST_WEATHER);
				float rainStrength = m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_RAIN_WEATHER) + m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_SNOW_WEATHER)  + m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_THUNDER_WEATHER);
				float rainVal = rainStrength >= tempestStrength ? rainStrength : tempestStrength;
				float blizzardStrength = m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_BLIZZARD_WEATHER);

				// 虚空之夜，不显示月亮
				DangerNightManagerInterface* pDNightMgr = GetWorldManagerPtr()->getDangerNightManager();
				bool isVoidNight = pDNightMgr && pDNightMgr->isVoidNight();
				
				// 使用滞后机制避免在临界值附近的闪烁
				static bool lastSunVisible = true;
				bool shouldHideSun = false;
				
				// 联机模式下需要更大的容差值来避免网络精度损失导致的闪烁
				bool isRemoteMode = m_World->isRemoteMode();
				float hideThreshold = isRemoteMode ? 0.4f : 0.35f;   // 联机模式使用更高阈值
				float showThreshold = isRemoteMode ? 0.2f : 0.25f;   // 联机模式使用更低阈值
				
				if (lastSunVisible) {
					// 太阳当前可见，需要更强的天气强度才能隐藏太阳
					shouldHideSun = (rainVal > hideThreshold || blizzardStrength > hideThreshold || isVoidNight);
				} else {
					// 太阳当前隐藏，需要更弱的天气强度才能显示太阳
					shouldHideSun = (rainVal > showThreshold || blizzardStrength > showThreshold || isVoidNight);
				}
				
				if (shouldHideSun)
				{
					m_Sky->setSkySunVisible(false);
					m_Sky->setSkyMoonVisible(false);
					lastSunVisible = false;
				}
				else
				{
					m_Sky->setSkySunVisible(true);
					m_Sky->setSkyMoonVisible(true);
					lastSunVisible = true;
				}
				if (g_WorldMgr && g_WorldMgr->isNewSandboxNodeGame())
				{
					// studio模式通过节点设置
					auto worldScene = m_World->GetWorldScene();
					if (worldScene && worldScene->GetRoot())
					{
						auto environment = worldScene->GetRoot()->GetChildT<EnvironmentNode>();
						if (environment && environment->GetChildT<SandboxSkyDomeNode>())
						{
							AutoRef<SandboxSkyDomeNode> domenode = environment->GetChildT<SandboxSkyDomeNode>();
							bool value;
							domenode->GetCloudsEnable(value);
							domenode->SetCloudsEnable(value);
						}
					}
				}
				else
					m_Sky->setSkyCloudVisible(true);
			}
			if (m_Tick % 2 == 0)
				m_Sky->SetCustomSkylineColor(false);
		}
	}
	Rainbow::LegacyGlobalShaderParamManager* parameter = Rainbow::GetLegacyGlobalShaderParamManager();
	if(IsWaterBlockID(eyeblock) || isWaterPlantID(eyeblock) || (g_WorldMgr && g_WorldMgr->m_CameraIsInWater))
	{
		if (m_Tick % 2 == 0)
			updateWaterFog(depth);
		needOpenFogEffect = true;
		GetSandboxGraphicSettings().SetGodrayParams(m_PostprocessGodray, true);		
		parameter->SetMiniGameSkyFogColorAmount(1.0f);

	}
	else if(IsLavaBlock(eyeblock))
	{
		m_Scene->setFogRange(0, 8);
		m_Scene->setFogColor(Rainbow::ColourValue(0.8f, 0.2f, 0.0f));

		if(m_Sky) m_Sky->setSkyModColor(Rainbow::ColorRGBAf(0.0f, 0.2f, 0.2f));
		needOpenFogEffect = true;
		parameter->SetMiniGameSkyFogColorAmount(1.0f);
		//else GetClientInfoProxy()->setClearColor(0.8f, 0.2f, 0.0f);
	}
	else if(eyeblock == BLOCK_PORTAL)
	{
		m_Scene->setFogRange(0, 32);
		m_Scene->setFogColor(Rainbow::ColourValue(0.47f, 0.247f, 0.6f));
		if(m_Sky) m_Sky->setSkyModColor(Rainbow::ColorRGBAf(0.4f, 0.1f, 0.3f));
		needOpenFogEffect = true;
		parameter->SetMiniGameSkyFogColorAmount(1.0f);
		//else GetClientInfoProxy()->setClearColor(0.47f, 0.247f, 0.6f);
	}
	else if(m_World->getCurMapID() == MAPID_MENGYANSTAR)
	{
//		m_Scene->setFogRange(32, 128);
		updateFogRange();
		updateEnvShaderParam();
		Rainbow::ColourValue fogColor = LegacyConvertColorRGBA32(m_Sky->getSkyFogColor(DNCOLOR_FOG_TWINKLE));
		m_Scene->setFogColor(fogColor);
		parameter->SetMiniGameSkyFogColorAmount(1.0f);

		//if (m_World->m_Environ->isDarking())
		/*
			float c = 1.0f - m_World->m_Environ->getDarkStrength()*0.7f; //sky and clound
			float a = 1.0f - m_World->m_Environ->getDarkStrength(); //sun and star
			if(m_Sky) m_Sky->setSkyModColor(Rainbow::ColourValue(c, c, c, a));
		*/
		//else
		//{
		//	if(m_Sky) m_Sky->setSkyModColor(Rainbow::ColourValue(1.0f, 1.0f, 1.0f));
		//}
	}
	else if(m_World->getCurMapID() == MAPID_LIEYANSTAR)
	{
//		m_Scene->setFogRange(32, 128);
		updateFogRange();
		updateEnvShaderParam();
		Rainbow::ColourValue fogColor = LegacyConvertColorRGBA32(m_Sky->getSkyFogColor(DNCOLOR_FOG_FLAME));
		m_Scene->setFogColor(fogColor);
		parameter->SetMiniGameSkyFogColorAmount(1.0f);
	}
	else
	{
		if (g_WorldMgr && g_WorldMgr->isNewSandboxNodeGame())
		{
			// studio模式通过节点设置
			auto worldScene = m_World->GetWorldScene();
			if (worldScene && worldScene->GetRoot())
			{
				auto environment = worldScene->GetRoot()->GetChildT<EnvironmentNode>();
				if (environment && environment->GetChildT<SandboxAtmosphereNode>())
				{
					AutoRef<SandboxAtmosphereNode> atmosphere = environment->GetChildT<SandboxAtmosphereNode>();
					float fogvalue;
					atmosphere->GetFogStart(fogvalue);
					atmosphere->SetFogStart(fogvalue);

					float endvalue;
					atmosphere->GetFogEnd(endvalue);
					atmosphere->SetFogEnd(endvalue);

					Rainbow::ColorQuad colorquad;
					atmosphere->GetFogColor(colorquad);
					atmosphere->SetFogColor(colorquad);
				}
			}
		}
		else
		{
			Rainbow::ColourValue fogColor;
			if (m_Sky != nullptr)
			{
				if (m_World->IsVacantBossExist()) fogColor = LegacyConvertColorRGBA32(m_Sky->getSkyFogColor(DNCOLOR_FOG_VOLCANO));
				else
				{
					// 虚空之夜，天空盒雾效颜色修改
					DangerNightManagerInterface* pDNightMgr = GetWorldManagerPtr()->getDangerNightManager();
					if (!pDNightMgr || !pDNightMgr->isVoidNight())
					{
						fogColor = LegacyConvertColorRGBA32(m_Sky->getSkyFogColor(DNCOLOR_FOG_EARTH));
					}
				}
			}

			m_Scene->setFogColor(fogColor);
			m_EnableGodray = true;
			// 高级创造，雾效颜色不跟天空底部颜色走
			bool isCustomSky = getIsUGCCustomSky();
			float val = isCustomSky ? 1.0f : 0.0f;
			parameter->SetMiniGameSkyFogColorAmount(val);
			WCoord blockpos = CoordDivBlock(g_WorldMgr->m_RenderEyePos);

			float dustStrength = m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_SANDDUSTSTORM_WEATHER);
			float rainStrength = m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_RAIN_WEATHER) + m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_THUNDER_WEATHER);
			float tempestStrength = m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_TEMPEST_WEATHER);
			float rainVal = rainStrength >= tempestStrength ? rainStrength : tempestStrength;
			float blizzardStrength = m_World->getWeatherMgr()->getWeatherStrength(blockpos, GROUP_BLIZZARD_WEATHER);

			if (dustStrength > 0)
			{
				needOpenFogEffect = true;

				int min = -10;
				int max = 32;
				int nearR = m_World->m_Environ->m_BiomeFogStart * (1.0f - dustStrength) + min;
				int farR = m_World->m_Environ->m_BiomeFogEnd * (1.0f - dustStrength) + max;
				m_Scene->setFogRange(nearR + g_DayFogNear[1], farR + g_DayFogFar[1]);

				float windScaleFactor = 3.0f;
				float windStep = 0.05f;
				float windTargetSpeed = m_World->m_Environ->m_WindStrength * windScaleFactor;
				Environment::AdjustByStep(m_EnvData.windStrength, windTargetSpeed, windStep);

				float shadowScaleFactor = 1.5f;
				m_EnvData.shadowIntensity = m_World->m_Environ->m_ShadowIntensity * (1.0f - dustStrength) * shadowScaleFactor;
				m_Scene->setEnvData(m_EnvData);
			}
			else if (rainVal > 0)
			{
				//int viewRange = GetMiniCraftRenderer().GetClientSetting().m_ViewSectionRange;
				//int range = viewRange * SECTION_BLOCK_DIM;
				//int index = viewRange / 2 - 1;
				//if (index < 0) index = 0;
				//if (index > 2)index = 2;
				int min = -50;
				int max = 80;
				int nearR = (m_World->m_Environ->m_BiomeFogStart - min) * (1.0f - rainVal) + min;
				int farR = m_World->m_Environ->m_BiomeFogEnd;
				if (farR > max) farR = (m_World->m_Environ->m_BiomeFogEnd - max) * (1.0f - rainVal) + max;
				m_Scene->setFogRange(nearR + g_DayFogNear[1], farR + g_DayFogFar[1]);

				float shadowScaleFactor = 1.8f;
				float shadow_step = 0.05f;
				float shadowTargetIntensity = m_World->m_Environ->m_ShadowIntensity * shadowScaleFactor;


				Environment::AdjustByStep(m_EnvData.shadowIntensity, shadowTargetIntensity, shadow_step);

				float windScaleFactor = 5.0f;
				float windStep = 0.05f;
				float windTargetSpeed = m_World->m_Environ->m_WindStrength * windScaleFactor;
				Environment::AdjustByStep(m_EnvData.windStrength, windTargetSpeed, windStep);



				float waveSpeedScaleFactor = 2.0f;
				m_EnvData.waterWaveSpeed = m_World->m_Environ->m_WaterWaveSpeed * waveSpeedScaleFactor;

				m_Scene->setEnvData(m_EnvData);

			}
			else if (blizzardStrength > 0)
			{
				needOpenFogEffect = true;

				int min = -50;
				int max = 80;
				int nearR = m_World->m_Environ->m_BiomeFogStart * (1.0f - blizzardStrength) + min;
				int farR = m_World->m_Environ->m_BiomeFogEnd * (1.0f - blizzardStrength) + max;
				m_Scene->setFogRange(nearR + g_DayFogNear[1], farR + g_DayFogFar[1]);

				float shadowScaleFactor = 1.5f;
				float shadowTargetIntensity = m_World->m_Environ->m_ShadowIntensity * shadowScaleFactor;

				float windScaleFactor = 5.0f;
				float windStep = 0.05f;
				float windTargetSpeed = m_World->m_Environ->m_WindStrength * windScaleFactor;
				Environment::AdjustByStep(m_EnvData.windStrength, windTargetSpeed, windStep);

				float shadow_step = 0.05f;
				Environment::AdjustByStep(m_EnvData.shadowIntensity, shadowTargetIntensity, shadow_step);
				m_Scene->setEnvData(m_EnvData);
			}
			else
			{
				updateFogRange();
				updateEnvShaderParam();
			}
		}

		//SandboxRenderSetting& setting = GetSandboxRenderSetting();// static_cast<SandboxRenderSetting*>(GetRenderSetting().m_CustomRenderSetting);
		//setting.m_Data.m_GodrayParameter.SetSky(m_IsUGCCustomPostEffect);

		//if (m_EnableGodray)
		//{
		//	m_Camera->SetRenderFeatureFlags(kRenderFeatureAllowPostprocess | kRenderFeatureAllowHDR);
		//	GetRenderSetting().m_HDR = true;
		//}
		if (m_PostprocessGodray.IsValid())
		{
			if (!m_IsUGCCustomPostEffect)
			{
				GetSandboxGraphicSettings().SetGodrayParams(m_PostprocessGodray, false);
			}
			else
			{
				GetSandboxGraphicSettings().SetGodrayParamsUGC(m_PostprocessGodray, m_PostprocessGodray->m_Setting.m_BloomTint);
			}
		}
		// 
		//SandboxRenderSetting& setting = GetSandboxRenderSetting();// static_cast<SandboxRenderSetting*>(GetRenderSetting().m_CustomRenderSetting);
		//setting.m_Data.m_GodrayParameter.SetSky();

		if (GetWorldManagerPtr()->getWorldId() == SELECTROLEWORLDID)
		{
			m_Scene->setFogRange(60, 90);
			return;
		}
		
	}

	bool isUGCCustomSky = m_Sky && m_Sky->GetIsUGCCustomSky();
	if (needOpenFogEffect && WorldRenderer::m_CurFogIndex <= 1)
	{
		GetRenderSetting().GetFogConfig().m_FogType = (Rainbow::FogType::kFogTypeLinear);
	}
	else
	{
		if (!isUGCCustomSky)
		{
			if (WorldRenderer::m_CurFogIndex <= 1)
			{
				GetRenderSetting().GetFogConfig().m_FogType = Rainbow::FogType::kFogTypeDisable;
			}
		}
		// 高级创造启用了自定义天空盒，使用内部标志位
		else
		{
			if (m_Sky->getSkyFogEnable())
			{
				GetRenderSetting().GetFogConfig().m_FogType = Rainbow::FogType::kFogTypeLinear;
			}
			else
			{
				GetRenderSetting().GetFogConfig().m_FogType = Rainbow::FogType::kFogTypeDisable;
			}
		}
	}
	// UGC水反标志位
	if (isUGCCustomSky)
	{
		bool waterEnable = m_Sky->getSkyWaterEnable();
		GetISandboxActorSubsystem()->SetEffectEnable(waterEnable);
	}
#endif
}

void WorldRenderer::updateFogRange()
{
	//float r = m_World->getHours();
	//if(r < 12.0f) r = r / 12.0f;
	//else r = (24.0f - r) / 12.0f;

	/*
	float n1 = vr-16;
	float f1 = vr+128;
	float n2 = 1;
	float f2 = 32;
	*/

	// 改成固定用设置里的第三档视野
	//float daynear = float(g_DayFogNear[1]);
	//float dayfar = float(g_DayFogFar[1]);

	//float nearBlock = r * (daynear-g_NightFogNear[m_CurFogIndex]) + g_NightFogNear[m_CurFogIndex];
	//float farBlock = r * (dayfar-g_NightFogFar[m_CurFogIndex]) + g_NightFogFar[m_CurFogIndex];
	bool isUGCCustomSky = m_Sky && m_Sky->GetIsUGCCustomSky();
	if (!isUGCCustomSky)
	{
		float nearblock = 0.0f, farblock = 0.0f;
		// 危险夜晚，雾效变化
		DangerNightManagerInterface* pDNightMgr = GetWorldManagerPtr()->getDangerNightManager();
		if (pDNightMgr && pDNightMgr->isDangerNight() && m_World->getCurMapID() == MAPID_GROUND)
		{
			nearblock = pDNightMgr->getCurFogStartRange() + g_DayFogNear[1];
			farblock = pDNightMgr->getCurFogEndRange() + g_DayFogFar[1];
		}
		else if (m_World && m_World->m_Environ && m_Sky && m_World->getCurMapID() == MAPID_GROUND)
		{
			nearblock = m_World->m_Environ->m_BiomeFogStart + g_DayFogNear[1];
			farblock = m_World->m_Environ->m_BiomeFogEnd + g_DayFogFar[1];
		}
		else
		{
			int viewRange = GetMiniCraftRenderer().GetClientSetting().m_ViewSectionRange;
			int range = viewRange * SECTION_BLOCK_DIM;
			int index = viewRange / 2 - 1;
			if (index < 0) index = 0;
			if (index > 2)index = 2;
			nearblock = range + g_FogRangeNear[index] + g_DayFogNear[1];
			farblock = range + g_FogRangeFar[index] + g_DayFogFar[1];
		}

		//LOG_INFO("cur FogRange: start %f, end %f", nearblock, farblock);
		m_Scene->setFogRange(nearblock, farblock);
	}
	else
	{
		float start = 10, end = 32;
		if (m_Sky)
		{
			start = m_Sky->getSkyFogStart();
			//start = start >= 0 ? start : 10.0f;//某些地形start会小于0
			end = m_Sky->getSkyFogEnd();
			end = end >= 0 ? end : 32.0f;
		}

		m_Scene->setFogRange(start, end);
	}

}

void WorldRenderer::updateShadowEnable()
{
	if (!m_CurShadowOpen) return;
	GfxDeviceRenderer deviceRenderer = GetGfxDevice().GetRenderer();
	if (deviceRenderer == GfxDeviceRenderer::kGfxRendererOpenGLES20)
		return;
	float hours = GetWorldManagerPtr()->getHours();
	//白天才开启阴影
	bool enableShadow = GetSandboxRenderSetting().CanShowShadow(hours);// hours > 4.0f && hours < 20.0f;
	if (enableShadow != GetRenderSetting().GetEnableShadow())
	{
		GetSandboxRenderSetting().SetShadowEnable(enableShadow, false);
		//GetRenderSetting().SetEnableShadow(enableShadow);
		//Rainbow::GetMiniGlobalEvent().CallShadowSwitchCallback(enableShadow);
	}
	
}
void WorldRenderer::SetShadowEnable(bool value, bool showTips)
{
	SandboxRenderSetting& setting = GetSandboxRenderSetting();
	setting.SetShadowEnable(value, showTips);
}
// 传送时，对新地图设置
void WorldRenderer::SetEngineCurrentScene()
{
	Assert(m_Scene && m_World);
	m_Scene->SetWorld(m_World);

	if (GetMapMarkingMgrInterface() != nullptr)	
	{
		bool needAddToScene = true;
		if(GetMapMarkingMgrInterface()->IsInScene())
		{
			GameScene* gs = GetMapMarkingMgrInterface()->GetScene();
			Assert(gs);
			if (gs == m_Scene)
			{
				needAddToScene = false;
			}
			else
			{
				GetMapMarkingMgrInterface()->DetachFromScene(gs);			
			}
		}

		if(needAddToScene)
			GetMapMarkingMgrInterface()->AttachToScene(m_Scene);
	}

	GetCamera()->SetEnable(true);
	//GetFrontCamera()->SetEnable(true);
	GetWorld()->SetCurrentGameScene((getScene()));
	GetMiniCraftRenderer().SetCamera(GetCamera());
}

CameraClipSetting& WorldRenderer::GetCameraClipSetting()
{
	return m_CameraClipSetting;
}

float WorldRenderer::GetActorClipValue()
{

	if (m_CameraClipSetting.m_CullPolicy == Rainbow::CullPolicy::kCullPolicyDistance)
		return m_CameraClipSetting.m_ActorMaxViewDistanceSqr * BLOCK_SIZE;
	else
		return m_CameraClipSetting.m_ActorMinScreenSizeSqr;

}

float WorldRenderer::GetParticleClipValue()
{
	if (m_CameraClipSetting.m_CullPolicy == Rainbow::CullPolicy::kCullPolicyDistance)
		return m_CameraClipSetting.m_ParticleMaxViewDistanceSqr;
	else
		return m_CameraClipSetting.m_ParticleMinScreenSizeSqr;
}

float WorldRenderer::GetConfigParticleClipMax()
{
	return m_CameraClipSetting.m_ConfigParticleMaxViewDistanceSize;
}

UInt32 WorldRenderer::GetCullPolicy()
{
	//判断当前世界类型,玩家自创的玩法不用裁剪,冒险模式裁剪
	WorldManager* worldMgr = GetWorldManagerPtr();
	int gameMode = worldMgr->getGameMode();
	if (gameMode == OWTYPE_SINGLE || gameMode == OWTYPE_FREEMODE || gameMode >= OWTYPE_GAMEMAKER_STUDIO_EDIT)
	{
		return m_CameraClipSetting.m_CullPolicy;
	}
	else
	{
		return CullPolicy::kCullPolicyNone;
	}
}

void WorldRenderer::SetWaterCullMode(Rainbow::CullMode cullmode)
{
	GetISandboxActorSubsystem()->SetWaterCullMode(cullmode);
}

void WorldRenderer::setPostporcessEnable(bool enable)
{
	//// UGC开启后处理效果也打开后处理
	//Assert(m_Camera != nullptr && m_FrontCamera != nullptr);
	//bool isEnable = isEnablePostprocess() || enable;
	//if (isEnable || m_IsUGCCustomPostEffect)
	//{
	//	GetFirstPersonCameraRenderTarget().InitCameraProperty(m_FrontCamera);
	//}
	//else
	//{
	//	GetFirstPersonCameraRenderTarget().DestroyCameraProperty();
	//}

	if (m_Camera)
	{
		enable ? m_Camera->SetRenderFeatureFlags(RenderFeatureFlags::kRenderFeatureAllowPostprocess)
			: m_Camera->SetRenderFeatureFlag(RenderFeatureFlags::kRenderFeatureAllowPostprocess,false);
	}
}

bool WorldRenderer::getPostporcessEnable()
{
	if (m_Camera)
	{
		if (m_Camera->GetRenderFeatureFlags() & RenderFeatureFlags::kRenderFeatureAllowPostprocess)
		{
			return true;
		}
	}

	return false;
}


void WorldRenderer::UpdateHightlightBlockVertexAnimation()
{
	if (m_BlockLine != nullptr) 
	{
		m_BlockLine->UpdateVertexAnimationEnable();
	}
	
	// 同时更新多方块轮廓的顶点动画
	for (BlockDecalMesh* mesh : m_OutlineBlockMeshes)
	{
		if (mesh != nullptr)
		{
			mesh->UpdateVertexAnimationEnable();
		}
	}
}

void WorldRenderer::SetDynamicRendererData(const WCoord& blockWorldPos, bool enable)
{
	if (m_World == nullptr) return;
	
	auto iter = m_DynamicRendererData.find(blockWorldPos);
	DynamicSectionMeshRendererData* data = nullptr;
	bool isSame = false;
	if (iter == m_DynamicRendererData.end())
	{
		data = ENG_NEW(DynamicSectionMeshRendererData)();
		m_DynamicRendererData[blockWorldPos] = data;
	}
	else
	{
		data = iter->second;
		isSame = data->m_Enable == enable;
	}
	//if (isSame) 
	//{
	//	return;
	//}
	//get block res id
	Block block = m_World->getBlock(blockWorldPos);
	if (g_BlockMtlMgr.hasMaterial(block.getResID()))
	{
		BlockMaterial* blockMaterial = g_BlockMtlMgr.getMaterial(block.getResID());
		if (blockMaterial != nullptr)
		{
			blockMaterial->OnSetDynamicRendererData(enable);
		}
	}
	data->m_BlockId = block.getResID();
	data->m_Enable = enable;
	data->m_Dirty = true;

}

void WorldRenderer::RemoveDynamicRenderData(const WCoord& blockWorldPos)
{
	auto iter = m_DynamicRendererData.find(blockWorldPos);
	if (iter != m_DynamicRendererData.end())
	{
		m_DynamicRendererData.erase(blockWorldPos);
	}
}

std::map<WCoord, DynamicSectionMeshRendererData*>& WorldRenderer::GetDynamicRenderData()
{
	return m_DynamicRendererData;
}

void WorldRenderer::SetUseCustomRenderPipeline(bool value)
{
	if (value) 
	{
		ChangeCustomRenderPipelineType(FirstPersonCameraPipelineType::FirstPersonUGC);
	}
	else 
	{
		OnUpdateWeaponItem();
	}
}

void WorldRenderer::ChangeCustomRenderPipelineType(FirstPersonCameraPipelineType type)
{
	if (m_Camera == nullptr) 
	{
		ErrorStringMsg("camera is nullptr");
		Assert(false);
	}
	Rainbow::CustomRenderPipeline* renderPipeline = nullptr;
	switch (type) 
	{
	case FirstPersonCameraPipelineType::FirstPersonNormal:
		if (m_FirstPersonRenderPipeline == nullptr) 
			m_FirstPersonRenderPipeline = createFirstPersonRenderPipeline();
		renderPipeline = m_FirstPersonRenderPipeline;
		break;
	case FirstPersonCameraPipelineType::FirstPersonCustomGenMesh:
		if (m_CustomModelFirstPersonRenderPipeline == nullptr) 
			m_CustomModelFirstPersonRenderPipeline = createCustomModelFirstPersonRenderPipeline();
		renderPipeline = m_CustomModelFirstPersonRenderPipeline;
		break;
	case FirstPersonCameraPipelineType::FirstPersonUGC:
		if (m_CustomRenderPipeline == nullptr)
			m_CustomRenderPipeline = createCustomRenderPipeline();
		renderPipeline = m_CustomRenderPipeline;
		break;
	default:
		if (m_FirstPersonRenderPipeline == nullptr)
			m_FirstPersonRenderPipeline = createFirstPersonRenderPipeline();
		renderPipeline = m_FirstPersonRenderPipeline;
		break;
	}
	Assert(renderPipeline != nullptr);
	m_Camera->SetCustomRenderPipeline(renderPipeline);
}

void WorldRenderer::SetWeaponItemType(int type)
{
	m_WeaponItemType = type;
	OnUpdateWeaponItem();
}

void WorldRenderer::OnUpdateWeaponItem() 
{
	if (m_WeaponItemType == FULLY_CUSTOM_GEN_MESH || m_WeaponItemType == CUSTOM_GEN_MESH)
	{
		ChangeCustomRenderPipelineType(FirstPersonCameraPipelineType::FirstPersonCustomGenMesh);
	}
	else 
	{
		ChangeCustomRenderPipelineType(FirstPersonCameraPipelineType::FirstPersonNormal);
	}
}

void WorldRenderer::setIsUGCCustomPostEffect(bool enable)
{
	m_IsUGCCustomPostEffect = enable;
	//GetRenderSetting().SetHDR(enable);
}

Rainbow::SandstormEffect* WorldRenderer::getSandstormEffect()
{
	if (m_WeatherRender)
		return m_WeatherRender->getSandstormEffect();
	return nullptr;
}

void WorldRenderer::doBlockRandomEffects(const WCoord &center)
{
	int range = 16;
	WCoord blockpos;

	auto mapId = m_World->getCurMapID();
	int blockRange = 15;
	for (int i = 0; i < 1000; i++)
	{
		//使用范围随机，不在随机2次来得到范围了
		blockpos.y = center.y + GenRandomInt(-blockRange, blockRange); //GenRandomInt(range) - GenRandomInt(range);
		if (blockpos.y < 0)
			continue;

		blockpos.x = center.x + GenRandomInt(-blockRange, blockRange); //GenRandomInt(range) - GenRandomInt(range);
		blockpos.z = center.z + GenRandomInt(-blockRange, blockRange); //GenRandomInt(range) - GenRandomInt(range);

		int blockid = m_World->getBlockID(blockpos, true);
		BlockMaterial* blockMtrl = blockid > 0 ? g_BlockMtlMgr.getMaterial(blockid) : nullptr;
		if (blockMtrl)
		{
			blockMtrl->DoOnPlayRandEffect(m_World, blockpos);//onPlayRandEffect(m_World, blockpos);
		}
	}
	auto iter = mInViewBlockParticle.begin();
	while (iter != mInViewBlockParticle.end())
	{
		auto childIter = (iter->second).begin();
		auto childEnd = (iter->second).end();
		if (iter->first == mapId)
		{
			while (childEnd != childIter)
			{
				if (abs((childIter->first).x - center.x) > range ||
					abs((childIter->first).y - center.y) > range ||
					abs((childIter->first).z - center.z) > range)
				{
					int blockid = m_World->getBlockID((childIter->first));
					BlockMaterial* blockMtrl = blockid > 0 ? g_BlockMtlMgr.getMaterial(blockid) : nullptr;
					if (blockMtrl)
					{
						blockMtrl->onStopEffect(m_World, (childIter->first));//onPlayRandEffect(m_World, blockpos);	
					}
					childIter = (iter->second).erase(childIter);
				}
				else
				{
					++childIter;
				}
			}
			++iter;
		}
		else
		{
			while (childEnd != childIter)
			{
				int blockid = m_World->getBlockID(childIter->first);
				BlockMaterial* blockMtrl = blockid > 0 ? g_BlockMtlMgr.getMaterial(blockid) : nullptr;
				if (blockMtrl)
				{
					blockMtrl->onStopEffect(m_World, childIter->first);//onPlayRandEffect(m_World, blockpos);	
				}
				++childIter;
			}
			iter = mInViewBlockParticle.erase(iter);
		}
	}
}

void WorldRenderer::setWireBlockPos(const WCoord &blockpos, bool isOutline)
{
	if (m_BlockLine)
	{
		m_BlockLine->Show(true);
		m_BlockLine->setBlock(m_World, blockpos, 0);
		if (isOutline)
		{
			// 记录当前选中的方块位置
			m_CurrentSelectBlockPos = blockpos;
			removeBlockOutline(m_CurrentSelectBlockPos);
			// 同时启用方块描边效果
			m_BlockLine->SetOutline(1); // 使用轮廓参数索引1（黄色）
		}
		else
		{
			m_BlockLine->SetOutline(0);
			WCoord reshowpos = m_CurrentSelectBlockPos;
			m_CurrentSelectBlockPos = WCoord::infinity;
			setBlockOutline(reshowpos, true);
		}
	}
}

void WorldRenderer::clearWireBlock()
{
	if (m_BlockLine)
	{
		m_BlockLine->Show(false);
		// 同时清除方块描边效果
		m_BlockLine->SetOutline(0);
		if (m_CurrentSelectBlockPos != WCoord::infinity)
		{
			WCoord reshowpos = m_CurrentSelectBlockPos;
			m_CurrentSelectBlockPos = WCoord::infinity;
			setBlockOutline(reshowpos, true);
		}
	}
}

void WorldRenderer::setBlockOutline(const WCoord& blockPos, bool enable)
{
	if (enable)
	{
		if (m_CurrentSelectBlockPos == blockPos) return;
		// 检查该位置是否已经有轮廓显示
		auto it = m_OutlineBlockMap.find(blockPos);
		if (it != m_OutlineBlockMap.end())
		{
			// 已经存在，无需重复创建
			return;
		}
		
		// 寻找可重用的BlockDecalMesh对象
		BlockDecalMesh* outlineMesh = nullptr;
		
		// 先查找是否有未使用的对象可以重用
		for (BlockDecalMesh* mesh : m_OutlineBlockMeshes)
		{
			if (mesh && !mesh->IsShow())
			{
				outlineMesh = mesh;
				break;
			}
		}
		
		// 如果没有可重用的，创建新的
		if (!outlineMesh)
		{
			outlineMesh = BlockDecalMesh::Create("highlight", GETTEX_WITHDEFAULT);
			if (outlineMesh)
			{
				// 参考m_BlockLine的初始化方式
				outlineMesh->SetIsSelectHightLight();
				outlineMesh->AttachToScene(m_Scene);
				
				// 保存到列表中
				m_OutlineBlockMeshes.push_back(outlineMesh);
			}
		}
		
		if (outlineMesh)
		{
			// 设置方块位置和显示
			outlineMesh->setBlock(m_World, blockPos, 0);
			outlineMesh->Show(true);
			
			// 设置轮廓效果 - 使用索引2（白色轮廓）
			outlineMesh->SetOutline(2);
			
			// 记录位置映射
			m_OutlineBlockMap[blockPos] = outlineMesh;
		}
	}
}

void WorldRenderer::removeBlockOutline(const WCoord& blockPos)
{
	auto it = m_OutlineBlockMap.find(blockPos);
	if (it != m_OutlineBlockMap.end())
	{
		BlockDecalMesh* mesh = it->second;
		if (mesh)
		{
			mesh->Show(false);
			mesh->SetOutline(0);
		}
		
		// 从映射中移除，但保留mesh对象以便重用
		m_OutlineBlockMap.erase(it);
	}
}

void WorldRenderer::clearPreviousBlockOutlines()
{
	// 参考clearWireBlock的实现，隐藏而不是销毁对象以便重用
	for (BlockDecalMesh* mesh : m_OutlineBlockMeshes)
	{
		if (mesh)
		{
			mesh->Show(false);
			// 同时清除方块描边效果
			mesh->SetOutline(0);
		}
	}
	
	// 清空映射但保留对象以便重用
	m_OutlineBlockMap.clear();
}

void WorldRenderer::addShadow(const WCoord &center, int radius, float shadowlevel)
{
	if (!m_ShadowFaces || GetIPlayerControl() == nullptr) return;

	WCoord playerPos = GetIPlayerControl()->GetPlayerControlPosition();
	if (center.distanceTo(playerPos) >= 1500) 
	{
		return;
	}
	if (radius > 100) radius = 100.0f;
	//设置阴影半径,太大会生成太多顶点
	//if (radius > 10) radius = 10;

	WCoord blockpos = CoordDivBlock(center);

	int minx = CoordDivBlock(center.x - radius);
	int maxx = CoordDivBlock(center.x + radius);
	int minz = CoordDivBlock(center.z - radius);
	int maxz = CoordDivBlock(center.z + radius);
	for (int x = minx; x <= maxx; x++)
	{
		for (int z = minz; z <= maxz; z++)
		{
			WCoord curblock(x, blockpos.y, z);
			for (int i = 0; i < 3; i++)
			{
				curblock.y--;
				if (curblock.y < 0) break;

				int blockid = m_World->getBlockID(curblock);
				if (BlockMaterial::m_IsOpaqueCube[blockid])
				{
					int dy = center.y - (curblock.y + 1) * BLOCK_SIZE;
					if (dy < 0) dy = 0;
					float uvscale = (1.0f + dy / (BLOCK_FSIZE * 2.0f)) / (radius * 2);
					int alpha = dy * 255 / (BLOCK_SIZE * 3);
					if (alpha > 255) alpha = 255;

					//add vertex to shadow face
					m_ShadowFaces->AddShadowQuad(TopCoord(curblock), center, uvscale, 255 - int((255 - alpha) * shadowlevel));
					break;
				}
			}
		}
	}
}

void WorldRenderer::addTerrainDecalEffect(const Vector3f& position, int radius, const char* texture_path, const char* inner_texture, Rainbow::ColorRGBAf tintColor, float duration)
{
	WCoord center = WorldPos::fromVector3(position);
	TerrainDecalData* effectData = m_TerrainDecalEffect->GenerateEffectData(texture_path, inner_texture, duration, tintColor);
	if (effectData == nullptr) return;
	WCoord blockpos = CoordDivBlock(center);
	int minx = CoordDivBlock(center.x - radius);
	int maxx = CoordDivBlock(center.x + radius);
	int minz = CoordDivBlock(center.z - radius);
	int maxz = CoordDivBlock(center.z + radius);

	int topRadius = 8;
	WCoord curPos = blockpos;
	int xSize = maxx - minx + 1;
	int zSize = maxz - minz + 1;
	effectData->uvXOffset = 1.0f / xSize;
	effectData->uvZOffset = 1.0f / zSize;

	for (int x = minx; x <= maxx; x++)
	{
		for (int z = minz; z <= maxz; z++)
		{
			curPos.x = x;
			curPos.z = z;

			int startY = topRadius;
			//先向上打射线，得到uptop值,再向下打
			for (int offsety = 1; offsety <= topRadius; offsety++)
			{
				curPos.y = blockpos.y + offsety;
				int blockid = m_World->getBlockID(curPos);
				if (BlockMaterial::m_IsOpaqueCube[blockid])
				{
					startY = offsety - 1;
					break;
				}
			}
			for (int offsety = startY; offsety >= -topRadius; offsety--)
			{
				curPos.y = blockpos.y + offsety;
				int blockid = m_World->getBlockID(curPos);
				if (BlockMaterial::m_IsOpaqueCube[blockid])
				{
					effectData->m_BlockPositions.push_back(TopCoord(curPos));
					Vector2f uv;
					uv.x = (x - minx) * 1.0f / xSize;
					uv.y = (z - minz) * 1.0f / zSize;
					effectData->m_BlockUVs.push_back(uv);
					break;
				}
			}
		}
	}
	effectData->m_Dirty = true;
}

void WorldRenderer::updateWaterFog(int depth)
{
	if (!m_Scene) return;
	//if (GetIPlayerControl() && GetIPlayerControl()->getLocoMotion()->m_IsInBoatWaterMask) return;
	// 根据水深变化
	float farRange = m_WaterFogFarRange - depth * 0.75f;
	if (GetIPlayerControl())
	{
		auto actor = dynamic_cast<IClientActor*>(GetIPlayerControl());
		if (actor->GetIBody())
		{
			// 根据俯仰角变化
			float pitch = actor->GetIBody()->getLookTargetPitch();
			if (pitch < 0) pitch = 0;
			float val = 10.0f - depth / 4.5f;

			farRange -= pitch / 90 * val;
		}

		if (farRange < 15.0f) farRange = 15.0f;

		auto pAtt = actor->getActorComponent(ComponentType::COMPONENT_PLAYER_ATTRIB);
		if (pAtt)
		{
			bool result = pAtt->Event2().Emit<float&>("PlayerAttrib_farRange", farRange);
			Assert(result);
		}
	}

	m_Scene->setFogRange(m_WaterFogNearRange, farRange);

	float red = m_WaterColorVal.r;
	float green = m_WaterColorVal.g;
	float blue = m_WaterColorVal.b;

	if (g_WorldMgr && m_World)
	{
		// 根据配置的颜色区间控制雾效的颜色变化
		WCoord _pos = g_WorldMgr->m_RenderEyePos;
		//const BiomeDef* def = m_World->getBiome(CoordDivBlock(_pos.x), CoordDivBlock(_pos.z));
		const BiomeDef* def = GetDefManagerProxy()->getBiomeDef(BIOME_OCEAN);// 线上def野问题，暂时先默认用海洋配置，code_by：zhangyusong 2023-03-31
		if (def != nullptr) {
			Rainbow::ColorQuad minC(def->WaterFogColor[1]);
			Rainbow::ColorQuad maxC(def->WaterFogColor[0]);
			int maxDepth = 6200;
			int minDepth = 4200;
			for (int i = 0; i < MAX_BIOME_WATER_FOG; i++)
			{
				if (i + 1 < MAX_BIOME_WATER_FOG)
				{
					maxDepth = def->WaterFogDepth[i] * 100;
					minDepth = def->WaterFogDepth[i + 1] * 100;
					if (maxDepth != -100)
					{
						if (_pos.y > maxDepth)
						{
							break;
						}
						else if (maxDepth >= _pos.y && minDepth < _pos.y)
						{
							maxC.set(def->WaterFogColor[i]);
							if (minDepth != -1)
							{
								minC.set(def->WaterFogColor[i + 1]);
							}
							else
							{
								minC.set(def->WaterFogColor[i]);
							}
							break;
						}
					}
					else
					{
						if (i > 0)
						{
							maxC.set(def->WaterFogColor[i - 1]);
							minC.set(def->WaterFogColor[i - 1]);
						}
						maxDepth = 100;
						minDepth = 0;
						break;
					}
				}
				else
				{
					if (def->WaterFogDepth[i] != -1)
					{
						maxC.set(def->WaterFogColor[i]);
						minC.set(def->WaterFogColor[i]);
					}
					maxDepth = 100;
					minDepth = 0;
				}
			}

			float factor = 0;
			if (_pos.y > minDepth)
			{
				factor = (_pos.y - minDepth) * 1.0f / (maxDepth - minDepth) * 1.0f;
				factor = 1.0f - factor;
				if (factor > 1.0f) factor = 1.0f;
				if (factor < 0) factor = 0;
			}

			red = (maxC.r - (maxC.r - minC.r) * factor) / 255.0f;
			green = (maxC.g - (maxC.g - minC.g) * factor) / 255.0f;
			blue = (maxC.b - (maxC.b - minC.b) * factor) / 255.0f;
		}
	}

	m_Scene->setFogColor(Rainbow::ColourValue(red, green, blue));
	ColorRGBAf color(red, green, blue);
	m_Sky->SetCustomSkylineColor(true, color);
}


bool WorldRenderer::isEnablePostprocess()
{
	if (m_PostprocessGodray.IsValid() && m_PostprocessGodray->IsEnabled()) return true;
	if (m_PostprocessBloom.IsValid() && m_PostprocessBloom->IsEnabled()) return true;
	if (m_PostprocessLUTs.IsValid() && m_PostprocessLUTs->IsEnabled()) return true;
	if (m_PostprcessAntialiasing.IsValid() && m_PostprcessAntialiasing->IsEnabled()) return true;
	if (m_PostprocessDof.IsValid() && m_PostprocessDof->IsEnabled()) return true;
	if (m_PostprocessSSAO.IsValid() && m_PostprocessSSAO->IsEnabled()) return true;

	return false;
}

void WorldRenderer::ClearDynamicRendererData()
{
	std::map<WCoord, DynamicSectionMeshRendererData*>::iterator iter = m_DynamicRendererData.begin();
	while (iter != m_DynamicRendererData.end())
	{
		DynamicSectionMeshRendererData* data = iter->second;
		ENG_DELETE(data);
		iter++;
	}
	m_DynamicRendererData.clear();
}

void WorldRenderer::setGodrayEnable(bool value)
{
	if (!m_PostprocessGodray.IsValid()) return;
	m_PostprocessGodray->SetEnabled(value);
}

void WorldRenderer::setBloomEnable(bool value)
{
	if (!m_PostprocessBloom.IsValid()) return;
	m_PostprocessBloom->SetEnabled(value);
}

void WorldRenderer::setLUTsEnable(bool value)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->SetEnabled(value);
}

void WorldRenderer::setCustomLUTEnable(bool value)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_CustomLUTEnable = value;
	m_PostprocessLUTs->SetEnabled(value);
}

void WorldRenderer::setDofEnable(bool value)
{
	if (!m_PostprocessDof.IsValid()) return;
	m_PostprocessDof->SetEnabled(value);
}

void WorldRenderer::setAntialiasing(bool value)
{
	if (!m_PostprcessAntialiasing.IsValid()) return;
	if (GetGfxDevice().GetRenderer() == GfxDeviceRenderer::kGfxRendererOpenGLES20)
		return;
	m_PostprcessAntialiasing->SetEnabled(value);
}
bool WorldRenderer::getGodrayEnable()
{
	if (!m_PostprocessGodray.IsValid()) return false;
	return m_PostprocessGodray->IsEnabled();
}

bool WorldRenderer::getBloomEnable()
{
	if (!m_PostprocessBloom.IsValid()) return false;
	return m_PostprocessBloom->IsEnabled();
}

bool WorldRenderer::getLUTsEnable()
{
	if (!m_PostprocessLUTs.IsValid()) return false;
	return m_PostprocessLUTs->IsEnabled();
}

bool WorldRenderer::getDofEnable()
{
	if (!m_PostprocessDof.IsValid()) return false;
	return m_PostprocessDof->IsEnabled();
}

void WorldRenderer::setGodrayBloomScale(float f)
{
	if (!m_PostprocessGodray.IsValid()) return;
	m_PostprocessGodray->m_Setting.m_BloomScale = f;
}

float WorldRenderer::getGodrayBloomScale()
{
	if (!m_PostprocessGodray.IsValid()) return 0.0f;
	return m_PostprocessGodray->m_Setting.m_BloomScale;
}

void WorldRenderer::setGodrayColor(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessGodray.IsValid()) return;
	m_PostprocessGodray->m_Setting.m_BloomTint = color;
}

Rainbow::ColorRGBAf WorldRenderer::getGodrayColor()
{
	if (!m_PostprocessGodray.IsValid()) return ColorRGBAf::white;;
	return m_PostprocessGodray->m_Setting.m_BloomTint;
}

void WorldRenderer::setBloomIntensity(float f)
{
	if (!m_PostprocessBloom.IsValid()) return;
	PostprocessBloomSetting &bloomSetting = m_PostprocessBloom->m_Settings[m_PostprocessBloom->m_Quality];
	bloomSetting.m_BloomIntensity = f;
}

float WorldRenderer::getBloomIntensity()
{
	if (!m_PostprocessBloom.IsValid()) return 0.0f;
	PostprocessBloomSetting& bloomSetting = m_PostprocessBloom->m_Settings[m_PostprocessBloom->m_Quality];
	return bloomSetting.m_BloomIntensity;
}



void WorldRenderer::setBloomThreadhold(float f)
{
	if (!m_PostprocessBloom.IsValid()) return;
	PostprocessBloomSetting& bloomSetting = m_PostprocessBloom->m_Settings[m_PostprocessBloom->m_Quality];
	bloomSetting.m_BloomThreadhold = f;
}

void WorldRenderer::setLUTsContrast(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Base.m_Contrast = color;
}

Rainbow::ColorRGBAf WorldRenderer::getLUTsContrast()
{
	if (!m_PostprocessLUTs.IsValid()) return ColorRGBAf::white;
	return m_PostprocessLUTs->m_Setting.m_Base.m_Contrast;
}

void WorldRenderer::setLUTsSaturation(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Base.m_Saturation = color;
}

Rainbow::ColorRGBAf WorldRenderer::getLUTsSaturation()
{
	if (!m_PostprocessLUTs.IsValid()) return ColorRGBAf::white;
	return m_PostprocessLUTs->m_Setting.m_Base.m_Saturation;
}

void WorldRenderer::setLUTsGain(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Base.m_Gain = color;
}

Rainbow::ColorRGBAf WorldRenderer::getLUTsGain()
{
	if (!m_PostprocessLUTs.IsValid()) return ColorRGBAf::white;
	return m_PostprocessLUTs->m_Setting.m_Base.m_Gain;
}

void WorldRenderer::setLUTsGamma(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Base.m_Gamma = color;
}

Rainbow::ColorRGBAf WorldRenderer::getLUTsGamma()
{
	if (!m_PostprocessLUTs.IsValid()) return ColorRGBAf::white;
	return m_PostprocessLUTs->m_Setting.m_Base.m_Gamma;
}

void WorldRenderer::setLUTsOffset(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Base.m_Offset = color;
}

void WorldRenderer::setLUTsShadowContrast(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Shadow.m_Contrast = color;
}

void WorldRenderer::setLUTsShadowSaturation(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Shadow.m_Saturation = color;
}

void WorldRenderer::setLUTsShadowGain(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Shadow.m_Gain = color;
}

void WorldRenderer::setLUTsShadowGamma(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Shadow.m_Gamma = color;
}

void WorldRenderer::setLUTsShadowOffset(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Shadow.m_Offset = color;
}

void WorldRenderer::setLUTsMidtoneContrast(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Midtone.m_Contrast = color;
}

void WorldRenderer::setLUTsMidtoneSaturation(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Midtone.m_Saturation = color;
}

void WorldRenderer::setLUTsMidtoneGain(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Midtone.m_Gain = color;
}

void WorldRenderer::setLUTsMidtoneGamma(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Midtone.m_Gamma = color;
}

void WorldRenderer::setLUTsMidtoneOffset(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Midtone.m_Offset = color;
}

void WorldRenderer::setLUTsHighlightContrast(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Highlight.m_Contrast = color;
}

void WorldRenderer::setLUTsHighlightSaturation(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Highlight.m_Saturation = color;
}

void WorldRenderer::setLUTsHighlightGain(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Highlight.m_Gain = color;
}

void WorldRenderer::setLUTsHighlightGamma(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Highlight.m_Gamma = color;
}

void WorldRenderer::setLUTsHighlightOffset(Rainbow::ColorRGBAf color)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_Highlight.m_Offset = color;
}

void WorldRenderer::SetDefaultLUTTexture(Rainbow::SharePtr<Rainbow::Texture2D> texture)
{
	if(m_PostprocessChain)
		m_PostprocessChain->SetDefaultLUTs(texture);
}

void WorldRenderer::setLUTsLUTTex(Rainbow::SharePtr<Rainbow::Texture2D> texture)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	if (!texture)
	{
		m_PostprocessLUTs->m_Setting.m_ColorGradingLUT = nullptr;
	}
	else
	{
		m_PostprocessLUTs->m_Setting.m_ColorGradingLUT = texture;
	}
}

Rainbow::SharePtr<Rainbow::Texture2D> WorldRenderer::getLUTsLUTTex()
{
	if (!m_PostprocessLUTs.IsValid()) return nullptr;
	return m_PostprocessLUTs->m_Setting.m_ColorGradingLUT;
}

void WorldRenderer::setLUTsTemperatureType(Rainbow::TemperatureType type)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_TemperatureType = type;
}

void WorldRenderer::setLUTsWhiteTemp(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_WhiteTemp = f;
}

void WorldRenderer::setLUTsWhiteTint(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_WhiteTint= f;
}

void WorldRenderer::setLUTsColorCorrectionShadowsMax(float f) 
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_ColorCorrectionShadowsMax = f;
}

void WorldRenderer::setLUTsColorCorrectionHighlightsMin(float f) 
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_ColorCorrectionHighlightsMin = f;
}

void WorldRenderer::setLUTsBlueCorrection(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_BlueCorrection = f;
}

void WorldRenderer::setLUTsExpandGamut(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_ExpandGamut = f;
}

void WorldRenderer::setLUTsToneCurveAmout(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_ToneCurveAmount = f;
}

void WorldRenderer::setFilmicTonemapSlope(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_FilmicTonemap.m_Slope = f;
}

void WorldRenderer::setFilmicTonemapToe(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_FilmicTonemap.m_Toe = f;
}

void WorldRenderer::setFilmicTonemapShoulder(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_FilmicTonemap.m_Shoulder = f;
}

void WorldRenderer::setFilmicTonemapBlackClip(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_FilmicTonemap.m_BlackClip = f;
}

void WorldRenderer::setFilmicTonemapWhiteClip(float f)
{
	if (!m_PostprocessLUTs.IsValid()) return;
	m_PostprocessLUTs->m_Setting.m_FilmicTonemap.m_WhiteClip = f;
}

void WorldRenderer::setDofFocalRegion(float value)
{
	if (!m_PostprocessDof.IsValid()) return;
	m_PostprocessDof->m_Setting.m_FocalRegion = value;
}

void WorldRenderer::setDofNearTransitionRegion(float value)
{
	if (!m_PostprocessDof.IsValid()) return;
	m_PostprocessDof->m_Setting.m_NearTransitionRegion = value;
}

void WorldRenderer::setDofFarTransitionRegion(float value)
{
	if (!m_PostprocessDof.IsValid()) return;
	m_PostprocessDof->m_Setting.m_FarTransitionRegion = value;
}

void WorldRenderer::setDofFocalDistance(float value)
{
	if (!m_PostprocessDof.IsValid()) return;
	m_PostprocessDof->m_Setting.m_FocalDistance = value;
}

void WorldRenderer::setDofScale(float value)
{
	if (!m_PostprocessDof.IsValid()) return;
	m_PostprocessDof->m_Setting.m_Scale = value;
}

void WorldRenderer::setGTAOActive(bool value)
{
	if (!m_PostprocessSSAO.IsValid()) return;
	m_PostprocessSSAO->SetEnabled(value);
}

void WorldRenderer::setGTAOThicknessblend(float value)
{
	if (!m_PostprocessSSAO.IsValid()) return;
	m_PostprocessSSAO->m_Setting.m_GTAOSetting.ThicknessBlend = value;
}

void WorldRenderer::setGTAOFalloffStartRatio(float value)
{
	if (!m_PostprocessSSAO.IsValid()) return;
	m_PostprocessSSAO->m_Setting.m_GTAOSetting.FalloffStartRatio = value;
}

void WorldRenderer::setGTAOFalloffEnd(float value)
{
	if (!m_PostprocessSSAO.IsValid()) return;
	m_PostprocessSSAO->m_Setting.m_GTAOSetting.FalloffEnd = value;
}

void WorldRenderer::setGTAOFadeoutDistance(float value)
{
	if (!m_PostprocessSSAO.IsValid()) return;
	m_PostprocessSSAO->m_Setting.m_GTAOSetting.fadeoutDistance = value;
}

void WorldRenderer::setGTAOFadeoutRadius(float value)
{
	if (!m_PostprocessSSAO.IsValid()) return;
	m_PostprocessSSAO->m_Setting.m_GTAOSetting.fadeoutRadius = value;
}

void WorldRenderer::setGTAOIntensity(float value)
{
	if (!m_PostprocessSSAO.IsValid()) return;
	m_PostprocessSSAO->m_Setting.m_GTAOSetting.intensity = value;
}

void WorldRenderer::setGTAOPower(float value)
{
	if (!m_PostprocessSSAO.IsValid()) return;
	m_PostprocessSSAO->m_Setting.m_GTAOSetting.power = value;
}

void WorldRenderer::setChromaticAberrationIntencity(float value)
{
	if (!m_PostprocessChain.IsValid()) return;
	auto resolve = m_PostprocessChain->GetPostprocessResolve();
	if (resolve)
	{
		Rainbow::ChromaticAberration setting = resolve->GetChromaticAberration();
		setting.Intensity = value;
		resolve->SetChromaticAberration(setting);
	}
}	
void WorldRenderer::setChromaticAberrationIterationSamples(float value)
{
	if (!m_PostprocessChain.IsValid()) return;
	auto resolve = m_PostprocessChain->GetPostprocessResolve();
	if (resolve)
	{
		Rainbow::ChromaticAberration setting = resolve->GetChromaticAberration();
		setting.IterationSamples = value;
		resolve->SetChromaticAberration(setting);
	}
}

void WorldRenderer::setChromaticAberrationStartOffset(float value)
{
	if (!m_PostprocessChain.IsValid()) return;
	auto resolve = m_PostprocessChain->GetPostprocessResolve();
	if (resolve)
	{
		Rainbow::ChromaticAberration setting = resolve->GetChromaticAberration();
		setting.StartOffset = value;
		resolve->SetChromaticAberration(setting);
	}
}

void WorldRenderer::setChromaticAberrationIterationStep(float value)
{
	if (!m_PostprocessChain.IsValid()) return;
	auto resolve = m_PostprocessChain->GetPostprocessResolve();
	if (resolve)
	{
		Rainbow::ChromaticAberration setting = resolve->GetChromaticAberration();
		setting.IterationStep = value;
		resolve->SetChromaticAberration(setting);
	}
}

bool WorldRenderer::getIsUGCCustomPostEffect()
{
	bool realFlag = true;
	if (m_Camera)
	{
		realFlag = m_Camera->HasRenderFeatureFlag(RenderFeatureFlags::kRenderFeatureAllowPostprocess);
	}
	return m_IsUGCCustomPostEffect && realFlag;
}

bool WorldRenderer::getIsUGCCustomSky()
{
	if (m_Sky)
	{
		return m_Sky->GetIsUGCCustomSky();
	}

	return false;
}
void WorldRenderer::setAntialiasingMethod(Rainbow::AntialiasingMethod method)
{
	if (!m_PostprcessAntialiasing.IsValid())
	{
		return;
	}

	m_PostprcessAntialiasing->m_Setting.m_AntialiasingMethod = method;
}

bool WorldRenderer::getAntialiasingMethod(Rainbow::AntialiasingMethod& method)
{
	if (!m_PostprcessAntialiasing.IsValid())
	{
		return false;
	}

	method = m_PostprcessAntialiasing->m_Setting.m_AntialiasingMethod;
	return true;
}

void WorldRenderer::setAntialiasingQuality(Rainbow::AntialiasingQuality quality)
{
	if (!m_PostprcessAntialiasing.IsValid())
	{
		return;
	}

	m_PostprcessAntialiasing->m_Setting.m_AntialiasingQuality = quality;
}

bool WorldRenderer::getAntialiasingQuality(Rainbow::AntialiasingQuality& quality)
{
	if (!m_PostprcessAntialiasing.IsValid())
	{
		return false;
	}

	quality = m_PostprcessAntialiasing->m_Setting.m_AntialiasingQuality;
	return true;
}

void WorldRenderer::InitBlockPlacementPreview()
{
    m_BlockPlacementPreview = BlockPlacementPreview::Create();
    if (m_BlockPlacementPreview)
    {
        m_BlockPlacementPreview->Initialize(m_World);
        //m_Scene->AddGameObject(m_BlockPlacementPreview->GetGameObject());
		m_BlockPlacementPreview->AttachToScene(m_Scene);
	}
}

