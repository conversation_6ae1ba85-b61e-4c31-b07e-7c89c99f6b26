function OnPlayerEnterStarStationCabin(status,starStationID, cabinPosX, cabinPosY, cabinPosZ)
	GetInst("StarStationManager"):setCabinPos(cabinPosX,cabinPosY,cabinPosZ)
	local destStarStationID
	local destMapID
	local unfinishedTransferRecord = StarStationTransferMgr:getUnfinishedTransferRecord(starStationID, cabinPosX, cabinPosY, cabinPosZ)
	if unfinishedTransferRecord then
		destStarStationID = unfinishedTransferRecord.destStarStationID
		destMapID = unfinishedTransferRecord.destMapID

		if StarStationTransferMgr.addStarStationTransferDesc and starStationID > 0 and destStarStationID > 0 and AccountManager:getUin() > 0 then
			StarStationTransferMgr:addStarStationTransferDesc(starStationID, destStarStationID, AccountManager:getUin())
		end
	end

	local param = {status = status, starStationID = starStationID, destStarStationID=destStarStationID, destMapID=destMapID, disableOperateUI = false}
	GetInst("StarStationManager"):openStarStationView(3,param)
	-- if GetInst("UIManager") then
	-- 	if GetInst("UIManager"):GetCtrl("StarStationInfo") then
	-- 		GetInst("UIManager"):GetCtrl("StarStationInfo"):Init(param)
	-- 		GetInst("UIManager"):Show("StarStationInfo")
	-- 		GetInst("UIManager"):GetCtrl("StarStationInfo"):Refresh()
	-- 	else
	-- 		GetInst("UIManager"):Open("StarStationInfo", param)
	-- 	end
	-- end
end	

function OnPlayerLeaveStarStationCabin(status)
	if GetInst("MiniUIManager"):GetCtrl("StarStationInfoFrame") then
		GetInst("MiniUIManager"):GetCtrl("StarStationInfoFrame"):OnPlayerLeaveStarStationCabin(status)
	end
	GetInst("StarStationManager"):closeStarStationView(2)
	GetInst("StarStationManager"):closeStarStationView(1)
	GetInst("StarStationManager"):setCabinPos(0,0,0)
end	


function OnStarStationActivated(curMapId, curStarStationID, biomeName)
	local starStationName = GetStarStationName(curMapId, curStarStationID, biomeName)
	if GetInst("UIManager") and GetInst("UIManager"):GetCtrl("StarStationInfo") then
        if GetInst("MiniUIManager"):GetCtrl("StarStationInfoFrame") then
            GetInst("MiniUIManager"):GetCtrl("StarStationInfoFrame"):OnStarStationActivated(curStarStationID)
        end
	end
	return starStationName
end

function OnStarStationConsoleRemoved(starStationID)
	if GetInst("UIManager") and GetInst("UIManager"):GetCtrl("StarStationInfo") then
        if GetInst("MiniUIManager"):GetCtrl("StarStationInfoFrame") then
            GetInst("MiniUIManager"):GetCtrl("StarStationInfoFrame"):OnStarStationConsoleRemoved(starStationID)
        end
	end
end

function  OnStarStationCabinRemoved(mapID, cabinPosX, cabinPosY, cabinPosZ)
	if GetInst("MiniUIManager"):GetCtrl("StarStationInfoFrame") then
		GetInst("MiniUIManager"):GetCtrl("StarStationInfoFrame"):OnStarStationCabinRemoved(mapID, cabinPosX, cabinPosY, cabinPosZ)
	end
end

function OnBlockStarStationConsoleTrigger(isActive)
	-- if isActive and GetInst("UIManager") then
	-- 	if GetInst("UIManager"):GetCtrl("StarStationStarTravel") then
	-- 		GetInst("UIManager"):GetCtrl("StarStationStarTravel"):Init({readOnly=true})
	-- 		GetInst("UIManager"):Show("StarStationStarTravel")
	-- 		GetInst("UIManager"):GetCtrl("StarStationStarTravel"):Refresh()
	-- 	else
	-- 		GetInst("UIManager"):Open("StarStationStarTravel", {readOnly=true})
	-- 	end
	-- end
	GetInst("StarStationManager"):openStarStationView(1,{readOnly=true})
	
end


function OnLeaveWorld()
	-- if GetInst("UIManager") then
		-- GetInst("UIManager"):Close("StarStationInfo")
		if GetInst("StarStationManager") then
			GetInst("StarStationManager"):closeStarStationView(3)
			GetInst("StarStationManager"):closeStarStationView(2)
			GetInst("StarStationManager"):closeStarStationView(1)
		end
		-- GetInst("UIManager"):Close("StarStationTransfer")
	-- end
end

function OnStarStationTeleportSuccess(destMapID)
	local gDestStarStationID = GetInst("StarStationManager"):getDefine("gDestStarStationID")
	if gDestStarStationID==-1 then
		return
	end
	
	if gDestStarStationID  == 0 then
		local strMapName = ""
		for i = 1, #StarInfoList do
			if destMapID == StarInfoList[i].id then
				strMapName = GetS(StarInfoList[i].name)
				break
			end
		end

		if ShowGameTips then
			ShowGameTips(GetS(80034, strMapName), 3)
		end
	else
		local starStationDef = StarStationTransferMgr:getStarStationDef(gDestStarStationID)
		if not starStationDef then
			return
		end
		
		if ShowGameTips then
			ShowGameTips(GetS(80033, starStationDef.starStationName), 3)
		end
	end	

	-- 传送到其他星球，恢复高级创造模式的天空盒及后处理设置
	local ugcRuntime = UGCRuntime:GetInst()
	if UGCEnvMgr and ugcRuntime and ugcRuntime.IsUGCGame and ugcRuntime:IsUGCGame() then
		GetInst("UGCEnvInterface"):RefreshPostProcessData()
		if destMapID == StarList.STAR_EARTH then
			GetInst("UGCEnvInterface"):RefreshSkyData()
		end
	end

	GetInst("StarStationManager"):setDefine("gDestStarStationID",-1)
end

function GetStarStationTransferCostStar(srcMapID, destMapID)
	if srcMapID == nil or destMapID == nil then
		return nil
	end

	local costStar
	if srcMapID == destMapID then
		costStar = StarStationTransferCost.TRANSFERCOST_SAMESTAR
	else
		if destMapID == StarList.STAR_EARTH then
			costStar = StarStationTransferCost.TRANSFERCOST_CROSSSTAR_DEST_EARTH
		elseif destMapID == StarList.STAR_LIEYAN then
			costStar = StarStationTransferCost.TRANSFERCOST_CROSSSTAR_DEST_LIEYAN
		elseif destMapID == StarList.STAR_MENGYAN then
			costStar = StarStationTransferCost.TRANSFERCOST_CROSSSTAR_DEST_MENGYAN
		end
	end

	return costStar
end


function OnStarStationTransferResult(transferType, result)
	if GetInst("MiniUIManager"):GetCtrl("StarStationTransferFrame") then
		GetInst("MiniUIManager"):GetCtrl("StarStationTransferFrame"):OnStarStationTransferResult(transferType, result)
	end
end

--三角门道具使用的倒计时
function SanJiaoMenCountDown(player, x, y, z)
	-- if not player or not x or not y or not z then
	-- 	return
	-- end

	-- player:gotoPosEx(player:getWorld(), x, y, z)
	-- local tooldef = ToolDefCsv:get(ITEM_SANJIAOMEN)
	-- if tooldef and tooldef.ID > 0 and tooldef.SkillCD > 0 then
	-- 	player:setSkillCD(tooldef.ID, tooldef.SkillCD)
	-- end
end
