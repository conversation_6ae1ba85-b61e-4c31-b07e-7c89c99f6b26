#include "PlayerControl.h"
#include "GameCamera.h"
#include "CameraModel.h"
#include "CameraManager.h"
#include "GameUI.h"
#include "GComponent.h"
#include "ClientItem.h"
#include "backpack.h"
#include "ClientActorManager.h"
#include "BlockScene.h"
#include "TouchControlLua.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "PlayerAnimation.h"
#include "PlayerStateController.h"
#include "FullyCustomModelMgr.h"
#include "PackingFullyCustomModelMgr.h"
#include "IClientGameManagerInterface.h"
#include "PCControlLua.h"
#include "ActorLocoMotion.h"
//#include "MpGameSurvive.h"
#include "IRecordInterface.h"
//#include "MapEditManager.h"
#include "ActorVillager.h"
#include "BlockGodStatue.h"
#include "PlayerAttrib.h"
#include "chunk.h"
#include "Entity/OgreModel.h"
//#include "MusicalInstrumentsCsv.h"
//#include "TriggerObjLibManager.h"
#include "GameNetManager.h"
#include "RiddenComponent.h"
#include "EffectComponent.h"
#include "FindComponent.h"
#include "ActionAttrStateComponent.h"
#include "BasketballStateAction.h"
#include "CommonUtil.h"
#include "ClientInfoProxy.h"
#include "DefManagerProxy.h"
#include "BlockBed.h"
#include "PixelMapMgr.h"
#include "AvatarEffectMgr.h"
//#include "CrashReportMgr.h"
#include "ActorBotNpc.h"
#include "ThornBallComponent.h"
#include "ActorBody.h"
#include "special_blockid.h"
#include "SandboxActorSubsystem.h"
#include "Play/common/InputInfo.h"
#include "Utils/luaConstProxy/LuaInterfaceProxy.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

void PlayerControl::addAchievement(int flags, ACHIEVEMENT_TYPE achievetype, int target_id, int num)
{
	ClientPlayer::addAchievement(flags, achievetype, target_id, num);
	if (flags & 2)
	{
		MNSandbox::GetGlobalEvent().Emit<int, int, int>("AchievementManager_setTotalGameStatistics", achievetype, num, target_id);
	}
}

void PlayerControl::addOWScore(float score)
{
	GetClientInfoProxy()->addOWScore(m_pWorld->getOWID(), score);
}


//void PlayerControl::beginChargeKickBall()
//{
//	ClientPlayer::beginChargeKickBall();
//}

//void PlayerControl::doKickBall(int type, float charge, ClientActor *ball)
//{
//	ClientPlayer::doKickBall(type, charge, ball);
//}

//bool PlayerControl::doCatchBall(ClientActor *ball)
//{
//	if (m_CatchBallCDTick <= 0)
//	{
//		m_CatchBallCDTick = g_WorldMgr->m_SurviveGameConfig->ballconfig.catch_cd_tick;
//
//		WCoord blockpos;
//		WORLD_ID objId;
//		bool hasBall = false;
//		auto findComponent = getFindComponent();
//		if (findComponent)
//		{
//			hasBall = findComponent->findNearestActor(objId, ClientPlayer::m_ViewRangeSetting, OBJ_TYPE_THROWBLOCK);
//		}
//		if (hasBall)
//		{
//			ClientActor *actor = m_pWorld->getActorMgr()->findActorByWID(objId);
//			if (actor)
//			{
//				WCoord pos = actor->getPosition();
//				setCameraRotate((int)m_pCamera->m_RotateX, pos.x, pos.y, pos.z);
//				//playBodyEffect("ball_guide");
//
//				m_CameraModel->showMoveDir(true);
//				m_CameraModel->setMoveTarget(pos, 1);
//			}
//		}
//
//		return ClientPlayer::doCatchBall(ball);
//	}
//
//	return false;
//}

//bool PlayerControl::doTackle()
//{
//	if (m_TackleCDTick <= 0)
//	{
//		m_TackleCDTick = g_WorldMgr->m_SurviveGameConfig->ballconfig.tackle_cd_tick;
//		return ClientPlayer::doTackle();
//	}
//
//	return false;
//}

//void PlayerControl::endTackle()
//{
//	ClientPlayer::endTackle();
//}

//bool PlayerControl::doBlockShot()
//{
//	if (m_BlockShotCDTick <= 0)
//	{
//		m_BlockShotCDTick = g_WorldMgr->m_SurviveGameConfig->basketballConfig.blockShot_cd_tick;
//	//	return basketBallOPStart(PLAYEROP_BASKETBALL_BLOCK_SHOT, NULL);
//		return BasketballStateAction::basketBallOPStart(this, PLAYEROP_BASKETBALL_BLOCK_SHOT, NULL);
//	}
//	return false;
//}

//bool PlayerControl::checkBasketBallGrab()
//{
//	if (0 >= m_nGrabCDTick)
//	{
//		m_nGrabCDTick = g_WorldMgr->m_SurviveGameConfig->basketballConfig.grab_cd_tick;
//		return true;
//	}
//	return false;
//}

//bool PlayerControl::doRunDribbleRunBasketBall(ClientActor* ball)
//{
//	if (m_cdRunTime <= 0)
//	{
//		m_cdRunTime = g_WorldMgr->m_SurviveGameConfig->basketballConfig.rush_cd_tick;
//
//
//		return ClientPlayer::doRunDribbleRunBasketBall(ball);
//	}
//	return false;
//}

void PlayerControl::starConvert(int num)
{
	ClientPlayer::starConvert(num);
}


//bool PlayerControl::basketBallOPStart(int type, ClientActor *ball)
//{
//	return ClientPlayer::basketBallOPStart(type, ball);
//}
//
//void PlayerControl::basketBallOPEnd(int type, ClientActor *ball)
//{
//	ClientPlayer::basketBallOPEnd(type, ball);
//}

//void PlayerControl::beginChargeThrowBall()
//{
//	ClientPlayer::beginChargeThrowBall();
//}

//void PlayerControl::doKickBasketBall(int type, BasketballFall result, float charge, ClientActor* ball, const WCoord& pos, float cameraYaw, float cameraPitch, int selectedActorUin)
//{
//	ClientPlayer::doKickBasketBall(type, result, charge, ball, pos, cameraYaw, cameraPitch, selectedActorUin);
//}

void PlayerControl::setBasketBallLockState(bool state, float wx, float wy)
{
	if (!m_bIsShowBasketBallLockHint)
		state = false;

	if (GetClientInfoProxy()->isMobile()) {
		if (m_TouchCtrl) m_TouchCtrl->setBasketBallLockState(state, wx, wy);
	}
	else
	{
		if (m_PCCtrl) m_PCCtrl->setBasketBallLockState(state, wx, wy);
	}
}

void PlayerControl::checkNewbieWorldProgress(int curprogress, const char *name)
{
	int nGuideTask = 0;
	MNSandbox::GetGlobalEvent().Emit<int&>("ClientAccountMgr_getCurNoviceGuideTask", nGuideTask);
	if (m_pWorld && m_pWorld->getOWID() == NEWBIEWORLDID && nGuideTask == curprogress - 1)
	{
		if (curprogress == 7)
		{
			MINIW::ScriptVM::game()->callFunction("AddGuideTaskCurNum", "ii", 6, 1);
			GetClientInfoProxy()->finishTask(7, "lumber");
			setTraceBlockState(false);
		}
		else GetClientInfoProxy()->finishTask(curprogress, name);
	}
}

void PlayerControl::checkNewbieWorldProgress(int curLv, int curStep)
{
	if (m_pWorld && m_pWorld->getOWID() == NEWBIEWORLDID && GetClientInfoProxy()->getCurGuideLevel() == curLv && GetClientInfoProxy()->getCurGuideStep() == curStep)
	{
		MNSandbox::GetGlobalEvent().Emit<int>("ClientAccountMgr_setCurGuideStep", curStep + 1);
		if (curStep + 1 == 7)
			MINIW::ScriptVM::game()->callFunction("UpdateNoviceStoryFrame", "i", 2);
		else if (curStep + 1 == 16)
			MINIW::ScriptVM::game()->callFunction("UpdateNoviceStoryFrame", "i", 3);
		else
			MINIW::ScriptVM::game()->callFunction("ImplementNoviceGuide", "");
	}
}

void PlayerControl::checkNewbieWorldTask(int itemid)
{
	MINIW::ScriptVM::game()->callFunction("CheckTask", "i", itemid);
}


bool PlayerControl::beginTraceBlock(int id, int type/* =1 */)
{
	WCoord blockpos;
	bool hasBlock = false;
	auto findComponent = getFindComponent();
	if (type == 1)
		hasBlock = findComponent ? findComponent->findNearestBlock(blockpos, id, ClientPlayer::GetCurViewRange(this)/*ClientPlayer::m_ViewRangeSetting*/ * 16) : false;
	else if (type == 2)
		hasBlock = findNearestClientItem(blockpos, id);
	if (hasBlock)
	{
		m_CameraModel->showMoveDir(true);
		m_CameraModel->setMoveTarget(BlockCenterCoord(blockpos));
		return true;
	}
	return false;
}

void PlayerControl::setTraceBlockState(bool b)
{
	m_CameraModel->showMoveDir(b);
}

bool PlayerControl::isShowMoveDir()
{
	return m_CameraModel->isShowMoveDir();
}

void PlayerControl::enterWorld(World *pworld)
{
    long long worldId = pworld == NULL ? 0 : pworld->getOWID();
    GetIWorldConfigProxy()->SetReportCrashMgrWorldId(worldId);

	if (pworld)
		loadWorldUploadStatus(pworld->getOWID(), pworld->getMapSpecialType());
	ClientPlayer::enterWorld(pworld);
	//addAchievement(1, ACHIEVEMENT_ENTER_WORLD, pworld->getCurMapID()); //进入世界是，触发冒险成就任务 code_by:huangfubin
	m_PickType = 0;
	m_SwitchTick = 0;
	if (m_CameraModel == NULL) return;
	//if (m_MoveDirective == NULL) return;
	m_CameraModel->onEnterWorld(pworld);

	//初始化为非工具模式
	if (GetWorldManagerPtr())
		GetWorldManagerPtr()->quitToolMode();


	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_OnEnterWorld",SandboxContext(nullptr).SetData_Usertype("world", pworld));
	//m_MoveDirective->AttachToScene(pworld->getScene());

	WorldMapData *mapdata = GetWorldManagerPtr()->getMapData(pworld->getCurMapID());
	if (mapdata && !mapdata->bosses.empty())
	{
		for (unsigned int i = 0; i < mapdata->bosses.size(); i++)
		{
			int missionflags = mapdata->bosses[i].flags;
			if (missionflags > 0)
			{
				for (int j = 0; j < 16; j++)
				{
					if (missionflags & (1 << j)) 
					{
						//ge GetGameEventQue().postMissionComplete(j + 1);
						MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
							SetData_Number("id", j + 1);
						if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
							MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_MISSION_COMPLETE", sandboxContext);
					}
					
				}
			}
		}
	}

	MINIW::ScriptVM *scriptvm = MINIW::ScriptVM::game();
	GetSandboxActorSubsystem()->HandleLuaCurWorld(scriptvm, pworld);
	//scriptvm->setUserTypePointer("CurWorld", "World", pworld);

	scriptvm->setUserTypePointer("VehicleControlInput", "VehicleControlInputs", m_VehicleControlInputs);
	scriptvm->callFunction("initDevWorld", "i", pworld->getCurMapID());

	//ge GetGameEventQue().postEnterWorld(pworld->getCurMapID());
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("mapid", pworld->getCurMapID());
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	    MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("GIE_ENTER_WORLD", sandboxContext);

	if (getOWID() != NEWBIEWORLDID)
	{
		if (m_NeedRevertToFPS > 0)
		{
			setViewMode(CAMERA_TPS_BACK);
		}

		//FPS
		if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
		{
			CameraManager::GetInstance().switchCameraControleType(TPSControlBack);
		}
		else if (getViewMode() == 0)
		{
			CameraManager::GetInstance().switchCameraControleType(FPSControl);
		}
		else  if (getViewMode() == 1 || 3 == getViewMode())
		{
			CameraManager::GetInstance().switchCameraControleType(TPSControlBack);
		}
		else  if (getViewMode() == 2)
			CameraManager::GetInstance().switchCameraControleType(TPSControlFront);
		else  if (getViewMode() == 5 || getViewMode() == 8)
			CameraManager::GetInstance().switchCameraControleType(CustomCamera);
	}
	updateGameCamera(0);

	if (GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isInGame())
	{
		GetClientInfoProxy()->setRenderContent(pworld->getScene());
	}

	if (!GetClientInfoProxy()->isMobile())
	{
		//GameUI::getInstance()->ShowCursor(false);
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("GameUI_ShowCursor", SandboxContext(nullptr).SetData_Bool("showcursor", false));
	}
		

	auto RidComp = getRiddenComponent();
	ClientActor *curriding = NULL; 
	if (RidComp)
	{
		curriding = RidComp->getRidingActor();
	}

	if (curriding)
	{
		auto curridingComp = curriding->getRiddenComponent();
		if (curridingComp && curridingComp->getRiddenChangeFPSView())
		{
			if (getViewMode() == CAMERA_FPS)
			{
				m_NeedRevertToFPS |= 1;
				setViewMode(CAMERA_TPS_BACK);
			}
		}
	}

	if (FullyCustomModelMgr::GetInstancePtr() && FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr())
	{
		int itemid = getCurToolID();
		FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr()->onShortcutChange(itemid);
	}
	
	PixelMapMgr::GetInstancePtr()->OnEnterWorld(pworld);
	ActorBotNpc::Get(pworld);

#ifdef VERSION_MINICODE
	SandboxEventDispatcherManager::GetGlobalInstance().Emit(
		"minicode_onPlayerCtrlEnterWorld",
		SandboxContext(nullptr).SetData_UserObject("params", this));
#endif
}

void PlayerControl::leaveWorld(bool keep_inchunk)
{
	if (g_WorldMgr) g_WorldMgr->m_CameraIsInWater = false;
	setShakeCamera(false);
	ClientActor *actor = getCatchGravityActor();
	if (actor)
		doPutGravityActor(actor);
	m_TipUI->setVisible(false);
	long long OWorldId = m_pWorld->getOWID();
	saveWorldUploadStatus(OWorldId, m_pWorld->getMapSpecialType());

	//m_MoveDirective->DetachFromScene();
	ClientPlayer::leaveWorld(keep_inchunk);

	if (m_CameraModel) m_CameraModel->onLeaveWorld();
	MINIW::ScriptVM *scriptvm = MINIW::ScriptVM::game();
	scriptvm->setUserTypePointer("VehicleControlInput", "VehicleControlInputs", NULL);
	//ge GetGameEventQue().postLeaveWorld(OWorldId);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("mapid", OWorldId);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	    MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("GIE_LEAVE_WORLD", sandboxContext);
	
	m_pWorld = NULL;

	PixelMapMgr::GetInstancePtr()->OnLeaveWorld();
	GetIWorldConfigProxy()->SetReportCrashMgrWorldId(0);
#ifdef VERSION_MINICODE
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("minicode_onLeaveWorld", MNSandbox::SandboxContext(nullptr));
#endif	
}

void PlayerControl::changeGameMode(bool scenefallback/* =true */)
{
	if (GetWorldManagerPtr() == NULL)
	{
		return;
	}

	bool isEdu = false;
#ifdef VERSION_MINICODE
	isEdu = true;
#endif
	if (isEdu)
		scenefallback = true;

	//20210724: 触发器新API  codeby:wangshuai
	setShakeCamera(false);
	resetGameCameraUGC();

	m_TipTime.clear();

	if (isSittingInStarStationCabin())
	{
		standUpFromChair();
	}

	if (!GetWorldManagerPtr()->toggleGameMode(scenefallback))
	{
		return;
	}

	if (isEdu)
	{
		return;
	}

	auto thornComponent = getThornBallComponent();
	if (thornComponent != nullptr)
	{
		//切换模式存在在生成
		thornComponent->createThornBall();
	}
	if (GetWorldManagerPtr()->getGameMode() == OWTYPE_CREATE)
	{
		GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(GetDefManagerProxy()->getStringDef(3196));
	}
	else if (GetWorldManagerPtr()->getGameMode() == OWTYPE_CREATE_RUNGAME)
	{
		GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(GetDefManagerProxy()->getStringDef(3195));
		//ge GetGameEventQue().postPlayerAttrChange();
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}
	else if (GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER)
	{
		GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(GetDefManagerProxy()->getStringDef(3241));
	}
	else if (GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER_RUN)
	{
		GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(GetDefManagerProxy()->getStringDef(3240));
		//ge GetGameEventQue().postPlayerAttrChange();
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}
}

// 20220809 联机房间下切换地图模式，仅创造模式的创造/冒险地图，主机可用 by huangrulin
bool PlayerControl::changeMpGameMode(bool scenefallback/* =true */)
{
	if (g_WorldMgr == NULL)
	{
		return false;
	}

	//20220809 参考changeGameMode中liusijia的作弊判断代码 这里只允许联机主机调用 by huangrulin
	if (m_pWorld != NULL && !this->isHost())
	{
		jsonxx::Object log;
		log << "current_mode" << g_WorldMgr->getGameMode();
		g_pPlayerCtrl->SendActionLog2Host(true, "cheat_client_gamemode", log.json_nospace());
		return false;
	}

	int oldGameMode = g_WorldMgr->getGameMode();

	if (!g_WorldMgr->hostToggleMpGameMode())
	{
		return false;
	}

	int newGameMode = g_WorldMgr->getGameMode();

	if (oldGameMode != newGameMode)
	{
		PB_GameModeChangeHC gameModeChangeHC;
		gameModeChangeHC.set_oldgamemode(oldGameMode);
		gameModeChangeHC.set_newgamemode(newGameMode);
		GameNetManager::getInstance()->sendBroadCast(PB_GAME_MODE_CHANGE, gameModeChangeHC);
	}

	return true;
}


void PlayerControl::postInfoTips(int tip)
{
	//ge GetGameEventQue().postInfoTips(tip);
	CommonUtil::GetInstance().PostInfoTips(tip);
}

void PlayerControl::ShowGameSocTips(const std::string& text, int time, int level)
{
	MINIW::ScriptVM* scriptvm = MINIW::ScriptVM::game();
	if (!scriptvm) return;

	scriptvm->callFunction("ShowGameSocTips","Sii", text.c_str(), text.length(), time, level);
}

void PlayerControl::updateSwimTipsLogic()
{
	if (m_IsEyeInWater && isInWater())
	{
		bool hasJumpInput = (m_InputInfo && m_InputInfo->jump);
		if (hasJumpInput) {
			m_InWaterNoJumpTicks = 0;
		} else {
			m_InWaterNoJumpTicks++;

			const unsigned int NO_JUMP_THRESHOLD = 60;
			if (m_InWaterNoJumpTicks >= NO_JUMP_THRESHOLD)
			{
				GetLuaInterfaceProxy().showGameTips(6225);
				m_InWaterNoJumpTicks = 0;
			}
		}
	}
	else
	{
		// 不在水中，重置计数器
		m_InWaterNoJumpTicks = 0;
	}
}

//
//bool PlayerControl::isCanGiveItemToWild()
//{
//	if (this->getPlayerAttrib() == NULL) return false;
//	int equipid = this->getPlayerAttrib()->getEquipItem(EQUIP_WEAPON);
//	if (equipid > 0)
//	{
//		const ToolDef * tool = GetDefManagerProxy()->getToolDef(equipid);
//
//		if (this->m_PickResult.intersect_block)
//		{
//			Block * block = getWorld()->getBlock(this->m_PickResult.block);
//			if (block && block->getResID() == 1201) // 石碑判断
//			{
//				WorldContainer* container = dynamic_cast<WorldContainer*>(getWorld()->getContainerMgr()->getContainer(this->m_PickResult.block));
//				if (container && container->getMobGiftNum() <= 0 && equipid >= 12595 && equipid <= 12597)
//				{
//					return true;
//				}
//			}
//		}
//
//		if (this->m_PickResult.intersect_actor)
//		{
//			ActorVillager* actor = NULL;
//			if (g_WorldMgr)
//			{
//				ClientMob* mob = g_WorldMgr->findMobByWID(this->m_PickResult.m_PickObjId);
//				if (mob && mob->getObjType() == OBJ_TYPE_VILLAGER)
//					actor = dynamic_cast<ActorVillager*>(mob);
//			}
//
//			if (!actor) { return false; }
//			VillagerAttrib *attrib = actor->getVillagerAttrib();
//			if (!attrib) { return false; }
//			//授予职业判断
//			if (actor->getTamed() && actor->getProfession() == 0 && attrib->getExtremisVal() >= 100)
//			{
//				if (tool && actor->getDefID() == 3200 && (tool->Type == 1 || tool->Type == 6))  // 斧子近战道具
//				{
//					return true;
//				}
//				else if (tool && actor->getDefID() == 3201 && (tool->Type == 4 || IsLongRangeWeaponID(equipid))) //锄头远程道具
//				{
//					return true;
//				}
//				else if (actor->getDefID() == 3202 && IsToolsHammer(equipid))  // 工具箱
//				{
//					return true;
//				}
//			}
//			//解除职业判断
//			else if (actor->getTamed() && actor->getProfession() > 0 && attrib->getExtremisVal() >= 100 && equipid == 12549) // 牛奶糖
//			{
//				return true;
//			}
//			else if (actor->getTamed() && attrib->getExtremisVal() >= 100 && (equipid == 11321 || equipid == 12598)) //西瓜汁 命名牌
//			{
//				return true;
//			}
//			else if (actor->getTamed() && attrib->getExtremisVal() < 100 && (equipid == 12594)) // 急救绷带
//			{
//				return true;
//			}
//		}
//	}
//	return false;
//}

bool PlayerControl::findNearestClientItem(WCoord &blockpos, int itemid)
{
	std::vector<CHUNK_INDEX>indices;
	WCoord center = CoordDivBlock(getPosition());
	ChunkViewer::makeViewChunks(indices, BlockDivSection(center.x), BlockDivSection(center.z), ClientPlayer::GetCurViewRange(this)/*ClientPlayer::m_ViewRangeSetting*/);

	bool finditem = false;
	int mindist = MAX_INT;
	for (size_t i = 0; i < indices.size(); i++)
	{
		Chunk *pchunk = m_pWorld->getChunk(indices[i]);
		if (pchunk == NULL) continue;
		ClientItem *pitem = static_cast<ClientItem*>(pchunk->findNearestItem(center, itemid));
		if (pitem)
		{
			int dist = center.squareDistanceTo(pitem->getPosition());
			if (dist < mindist)
			{
				blockpos = CoordDivBlock(pitem->getPosition());
				mindist = dist;
				finditem = true;
			}
		}
	}

	return finditem;
}

bool PlayerControl::findNearOneClientItemPos(int &x, int &y, int &z, int itemid)
{
	std::vector<IClientActor *>actors;
	CollideAABB box;
	getCollideBox(box);
	box.expand(BLOCK_SIZE * 5, 0, BLOCK_SIZE * 5);

	m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_DROPITEM, itemid);

	if (actors.size() > 0)
	{
		x = actors[0]->getPosition().x;
		y = actors[0]->getPosition().y;
		z = actors[0]->getPosition().z;

		return true;
	}

	return false;
}


void PlayerControl::closePlotDialogue()
{
	ClientPlayer::closePlotDialogue();
}

//是否在毒气区
bool PlayerControl::isInsideNoOxygenBlock()
{
	if (m_pWorld == NULL) return false;
	WCoord eyepos = getEyePosition();
	WCoord blockpos = CoordDivBlock(eyepos);
	int blockid = m_pWorld->getBlockID(blockpos);

	//水不算, 流沙不算
	if (IsWaterBlockID(blockid) || isDriftsandBlockID(blockid) || isWaterPlantID(blockid))
		return false;

	// 萌眼星取消毒气设置 code-by:lizb
	if (m_pWorld->getCurMapID() >= MAPID_MENGYANSTAR)
		return false;

	if (getLocoMotion()) return getLocoMotion()->isInsideNoOxygenBlock();

	return false;
}

void PlayerControl::sendViewModeToSpectator()
{
	if (m_pWorld && m_nSpectatorUin)
	{
		if (!m_pWorld->isRemoteMode())
		{
			PB_SendMyViewmodeToSpectatorHC sendMyViewmodeToSpectatorHC;
			sendMyViewmodeToSpectatorHC.set_spectatoruin(m_nSpectatorUin);
			sendMyViewmodeToSpectatorHC.set_tospectatoruin(getUin());
			sendMyViewmodeToSpectatorHC.set_myviewmode(m_ViewMode);

			GetGameNetManagerPtr()->sendToClient(m_nSpectatorUin, PB_SEND_VIEWMODE_SPECTATOR_HC, sendMyViewmodeToSpectatorHC);
		}
		else
		{
			PB_SendMyViewmodeToSpectatorCH sendMyViewmodeToSpectatorCH;
			sendMyViewmodeToSpectatorCH.set_spectatoruin(m_nSpectatorUin);
			sendMyViewmodeToSpectatorCH.set_tospectatoruin(getUin());
			sendMyViewmodeToSpectatorCH.set_myviewmode(m_ViewMode);

			GetGameNetManagerPtr()->sendToHost(PB_SEND_VIEWMODE_SPECTATOR_CH, sendMyViewmodeToSpectatorCH);
		}
	}
}

void PlayerControl::setSpectatorMode(PLAYER_SPECTATOR_MODE spectmod)
{
	if (m_pWorld != nullptr  && GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWMODE))
	{
		ClientPlayer::setSpectatorMode(spectmod);
		if (!m_pWorld->isRemoteMode())
		{
			CheckSpectatorPlayerShow();
			revive(0);
			// ͬ同步给其他客机
			PB_SetSpectatorModeHC setSpectatorModeHC;
			setSpectatorModeHC.set_uin(getUin());
			setSpectatorModeHC.set_spectatormode(spectmod);

			GetGameNetManagerPtr()->sendBroadCast(PB_SET_SPECTATORMODE_HC, setSpectatorModeHC);

			if (g_WorldMgr)
				g_WorldMgr->signChangedToSync(getUin(), BIS_INSPECTATOR);
		}
		else
		{
			// 同步到主机，让主机同步给其他客机
			PB_SetSpectatorModeCH setSpectatorModeCH;
			setSpectatorModeCH.set_uin(getUin());
			setSpectatorModeCH.set_spectatormode(spectmod);

			GetGameNetManagerPtr()->sendToHost(PB_SET_SPECTATORMODE_CH, setSpectatorModeCH);
		}

		if (GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWTYPE) == SPECTATOR_TYPE_FOLLW && spectmod)
		{
			if (m_pWorld->isRemoteMode())
			{
				ClientPlayer::setSpectatorMode((PLAYER_SPECTATOR_MODE)(int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWMODE));
			}
			ClientPlayer::setSpectatorType(SPECTATOR_TYPE_FOLLW);
			setSpectatorType(SPECTATOR_TYPE_FOLLW);
		}
	}
}

void PlayerControl::setSpectatorType(PLAYER_SPECTATOR_TYPE specttype)
{
	if (m_pWorld != nullptr  && GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr
		&& (getSpectatorMode() == SPECTATOR_MODE_JUDGE || (GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWMODE)
			&& (GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWTYPE) == 2 || GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWTYPE) == specttype))))
	{
		if (!m_pWorld->isRemoteMode())
		{
			ClientPlayer::setSpectatorType(specttype);
			CheckSpectatorPlayerShow();
			// 同步给其他客机
			PB_SetSpectatorTypeHC setSpectatorTypeHC;
			setSpectatorTypeHC.set_uin(getUin());
			setSpectatorTypeHC.set_spectatortype(specttype);

			GetGameNetManagerPtr()->sendBroadCast(PB_SET_SPECTATORTYPE_HC, setSpectatorTypeHC);
		}
		else
		{
			// 同步到主机，让主机同步给其他客机
			PB_SetSpectatorTypeCH setSpectatorTypeCH;
			setSpectatorTypeCH.set_uin(getUin());
			setSpectatorTypeCH.set_spectatortype(specttype);

			GetGameNetManagerPtr()->sendToHost(PB_SET_SPECTATORTYPE_CH, setSpectatorTypeCH);
		}
	}
}

void PlayerControl::setToSpectatorPlayerUin(int uin)
{
	ClientPlayer* player = getToSpectatorPlayer();
	if (player && m_nToSpectatorUin != uin)
	{
		player->getBody()->show(true);
	}

	if (m_nToSpectatorUin != uin)
	{
		ClientPlayer* target = getToSpectatorPlayer();
		if (target)
		{
			target->setSpectatorUin(0);
		}
		m_nToSpectatorUin = uin;
		target = getToSpectatorPlayer();
		if (target)
		{
			CheckSpectatorPlayerShow();
			getLocoMotion()->setPosition(target->getLocoMotion()->getPosition().x, target->getLocoMotion()->getPosition().y, target->getLocoMotion()->getPosition().z);
			target->setSpectatorUin(getUin());
		}
		else if (GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isInGame())
		{
			auto game = GetIClientGameManagerInterface()->getICurGame(GameType::MpGameSurvive);
			if (game)
			{
				PlayerBriefInfo *info = game->findPlayerInfoByUin(uin);
				if (info)
				{
					PB_ActorTeleportCH actorTeleportCH;
					actorTeleportCH.set_objid(getUin());
					actorTeleportCH.set_targetmap(getCurMapID());

					PB_Vector3* targetPos = actorTeleportCH.mutable_targetpos();
					targetPos->set_x(info->x);
					targetPos->set_y(info->y);
					targetPos->set_z(info->z);
					GetGameNetManagerPtr()->sendToHost(PB_ACTOR_TELEPORT_CH, actorTeleportCH);
					//getLocoMotion()->setPosition(info->x, info->y, info->z);
				}
			}
		}
		setMainPlayerAttrib();
		if (m_pWorld->isRemoteMode())
		{
			//观战信息同步到主机
			PB_SetSpectatorPlayerCH setSpectatorPlayerCH;
			setSpectatorPlayerCH.set_spectatoruin(getUin());
			setSpectatorPlayerCH.set_tospectatoruin(uin);

			GetGameNetManagerPtr()->sendToHost(PB_SET_SPECTATOR_PLAYER_CH, setSpectatorPlayerCH);
		}
		else
		{
			PB_SetSpectatorPlayerHC setSpectatorPlayerHC;
			setSpectatorPlayerHC.set_spectatoruin(getUin());
			setSpectatorPlayerHC.set_tospectatoruin(uin);

			GetGameNetManagerPtr()->sendToClient(uin, PB_SET_SPECTATOR_PLAYER_HC, setSpectatorPlayerHC);
		}
	}
}

void PlayerControl::CheckSpectatorPlayerShow()
{
	//如果是运行的sandbox 环境的player 则player 走 scenePlayerObject 不走 ClientPlayer 了，所以这里的逻辑不需要了
	if (IsRunSandboxPlayer()) return; 

	int viewMode = m_ViewMode;
	if (isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FOLLW)
	{
		viewMode = m_ViewMode_ToSpectator;
	}

	m_pCamera->setMode(CameraControlMode(viewMode));

	if (viewMode == CameraControlMode::CAMERA_FPS
		|| (isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FOLLW))
	{
		if (!isRestInBed())
		{
			if (getBody())
				getBody()->show(false);
		}
	}
	else
	{
		if (getBody())
			getBody()->show(true);
	}

	if (isInSpectatorMode())
	{
		ClientPlayer* player = getToSpectatorPlayer();
		if (viewMode == CameraControlMode::CAMERA_FPS)
		{
			m_pCamera->setBobbing(false);
			if (!player)
				return;
			if (getSpectatorType() == SPECTATOR_TYPE_FOLLW)
			{
				if (player->getBody())
					player->getBody()->show(false);
			}
			else
			{
				if (player->getBody())
					player->getBody()->show(true);
			}
			if (player->getBody() && !(m_CameraModel->getPlayerIndex() == player->getBody()->getPlayerIndex()
				&& m_CameraModel->getMutatemob() == player->getBody()->getMutateMob()
				&& strcmp(m_CameraModel->getCustomjson(), player->getBody()->getCustomSkins()) == 0))
				setPlayerCamera(player->getBody()->getPlayerIndex(), player->getBody()->getMutateMob(), player->getBody()->getCustomSkins());
			else
				switchCurrentItem();
		}
		else
		{
			if (player && player->getBody())
				player->getBody()->show(true);
		}
	}

}

//p1 == "param_to_str" 0.35.5版本之后的新增的埋点参数全部使用string类型
void PlayerControl::statisticToWorld(int uin, int id, const char *fristname /* = "" */, int worldtype /* = -1 */, const char *p1 /* = "" */, const char *p2 /* = "" */, const char *p3 /* = "" */, const char *p4 /* = "" */, const char *p5 /* = "" */, const char *p6 /* = "" */, const char *p7 /* = "" */)
{
	bool isOpenStaitstic = true;
	MINIW::ScriptVM::game()->callFunction("if_open_statistic_by_world", "i>b", id, &isOpenStaitstic);
	if (!isOpenStaitstic) return;

	if (uin == getUin())
	{
		bool isGuideState = true;
		MNSandbox::GetGlobalEvent().Emit<bool&, const char*>("ClientAccountMgr_getNoviceGuideState", isGuideState, fristname);
		if (fristname[0] == '\0' || !isGuideState)
		{
			//ge GetGameEventQue().postStatistic(id, worldtype, p1, p2, p3, p4, p5, p6, p7);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("id", id).
				SetData_Number("worldtype", worldtype).
				SetData_String("param1", p1).
				SetData_String("param2", p2).
				SetData_String("param3", p3).
				SetData_String("param4", p4).
				SetData_String("param5", p5).
				SetData_String("param6", p6).
				SetData_String("param7", p7);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_STATISTIC", sandboxContext);
			if (fristname[0] != '\0')
			{
				MNSandbox::GetGlobalEvent().Emit<const char*, bool>("ClientAccountMgr_setNoviceGuideState", fristname, true);
			}
		}

	}
	else if (!m_pWorld->isRemoteMode())
	{
		PB_StatisticHC statisticHC;
		statisticHC.set_eventid(id);
		statisticHC.set_fristname(fristname);
		statisticHC.set_worldtype(worldtype);
		statisticHC.set_param1(p1);
		statisticHC.set_param2(p2);
		statisticHC.set_param3(p3);
		statisticHC.set_param4(p4);
		statisticHC.set_param4(p5);
		statisticHC.set_param4(p6);
		statisticHC.set_param4(p7);
		GetGameNetManagerPtr()->sendToClient(uin, PB_STATISTIC_HC, statisticHC);
	}
}

int PlayerControl::getCurWorldType()
{
	auto worldDesc = GetClientInfoProxy()->getCurWorldDesc();
	if (worldDesc)
		return worldDesc->worldtype;

	return 0;
}

void PlayerControl::completeTask(int taskid)
{
	ClientPlayer::completeTask(taskid);
}

void PlayerControl::onToggleGameMode()
{
	World *pworld = g_pPlayerCtrl->getWorld();
	if (!pworld)
		return;

	//重新load角色视野范围内的chunk数据
	leaveWorld(false);
	g_pPlayerCtrl->enterWorld(pworld);

	//重置队伍
	setTeam(0);
	ResettingPlayerModel();

	//还原角色的一些属性
	setAiInvulnerableProb(-1);
	auto attr = getAttrib();
	if (attr)
		attr->onToggleGameMode();

	//清理玩家身上的特效
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		effectComponent->clearEffectForTrigger();
	}

	if (getBackPack())
		getBackPack()->clearPack("onToggleGameMode");
	loadFileByToggleGameMakerMode(pworld->getOWID(), getUin(), true, pworld->getMapSpecialType());
	//ge GetGameEventQue().postBackpackChange(-1);
    MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
	SetData_Number("grid_index", -1);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
	}
	//g_pPlayerCtrl->updateChunkView();

	auto ActionAttrStateComp = getActionAttrStateComponent();
	if (ActionAttrStateComp)
	{
		ActionAttrStateComp->resetActionAttrState();
	}

}

bool PlayerControl::isShowHomeTaskDir()
{
	return m_CameraModel->isShowHomeTaskDir();
}
void PlayerControl::setHomeTaskDir(bool b)
{
	m_CameraModel->showHomeTaskDir(b);
}
void PlayerControl::setHomeTaskTarget(float x, float y, float z)
{
	m_CameraModel->setMoveTarget(WCoord(x, y, z));
}

void PlayerControl::ResettingPlayerModel() //玩法转编辑重置玩家初始外观
{
	//角色恢复外观模型
	const ROLEINFO& roleinfo = GetClientInfoProxy()->getAccountInfo()->RoleInfo;
	int rolemodel = roleinfo.Model;
	if (rolemodel == 0) rolemodel = 2;//修改默认模型为卡卡ID为2，之前是酋长ID是1，和存档界面保持一致 fixby：hexianggui
	int geniuslv = GetClientInfoProxy()->getGenuisLv(rolemodel);
	if (geniuslv < 0) geniuslv = 0;

	int playerindex = ComposePlayerIndex(rolemodel, geniuslv, roleinfo.SkinID);
	int nindex = 0;
	MNSandbox::GetGlobalEvent().Emit<int&>("ClientAccountMgr_playerindex", nindex);
	if (nindex > 0)
	{
		playerindex = nindex;
	}
	setCustomModel("");
	setCustomModelScale(1.0f);
	if (getBody())
	{
		getBody()->initPlayer(playerindex, 0, roleinfo.CustomSkin);
		if (!m_strCustomjson.empty())
		{
			MINIW::ScriptVM::game()->callFunction("ClientGetRoleAvatarInfo", "is", getUin(), m_strCustomjson.c_str());
		}
		getBody()->playAnim(0);
	}
}