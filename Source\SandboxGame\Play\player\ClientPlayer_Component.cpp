#include "ClientPlayer.h"
#include "LivingLocoMotion.h"
#include "world.h"
#include "MpActorManager.h"
#include "PlayerAttrib.h"
#include "backpack.h"
#include "CommonUtil.h"
#include "DefManagerProxy.h"
#include "ActorBall.h"
#include "ActorBasketBall.h"
#include "PlayerTaskManager.h"
#include "VehicleAssembleLocoMotion.h"
#include "ClientActorProjectile.h"
#include "ActorVehicleAssemble.h"
#include "SkillCDComponent.h"
#include "ItemSkillComponent.h"
#include "AccountHorseComponent.h"
#include "PetFollowListComponent.h"
#include "PetAccountComponent.h"
#include "PetSummonComponent.h"
#include "ChangeColorComponent.h"
#include "TransformersSkinComponent.h"
#include "InteractTamedMobComponent.h"
#include "OpenContainerComponent.h"
#include "AttackingTargetComponent.h"
#include "HPProgressComponent.h"
#include "TeamComponent.h"
#include "BindActorComponent.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "WeaponSkilledComponent.h"
#include "ActorInPortal.h"
#include "ActorBindVehicle.h"
#include "ActorUpdateFrequency.h"
#include "GameNetManager.h"
#include "ClientInfoProxy.h"
#include "FishingComponent.h"
#include "GameModeDef.h"
#include "WeaponSkinMgr.h"
#include "AvatarSummonComponent.h"
#include "PlayerControl.h"
#include "ActorPushSnowBall.h"
#include "ActorBody.h"
#include "SkillComponent.h"
#include "CustomGunUseComponent.h"
#include "WorldEventManager.h"
#include "GameAnalytics.h"
#include "Utilities/Logs/LogAssert.h"

#include "BlockMaterialMgr.h"
#include "BlockResearch.h"
#include "container_decomposition.h"
#include "container_socworkbench.h"

using namespace MNSandbox;
using namespace Rainbow;
using namespace MINIW;
//
////------------------------baskerball---start---------------------------//
//bool ClientPlayer::basketBallOPStart(int type, ClientActor *ball)
//{
//	WORLD_ID objId = ball ? ball->getObjId() : 0;
//	//����������
//	if (!m_pWorld->isRemoteMode())
//	{
//		PB_BasketBallOperate basketballOperatorHC;
//		basketballOperatorHC.set_type(type);
//		basketballOperatorHC.set_actorid(objId);
//		basketballOperatorHC.set_extenddata(0);
//		basketballOperatorHC.set_uin(getUin());
//		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BASKETBALL_OPERATE_HC, basketballOperatorHC, this, false);
//	}
//	if (PLAYEROP_BASKETBALL_BLOCK_SHOT == type)
//	{
//		m_Body->stopAnim(SEQ_BASKETBALL_BLOCK_SHOT);
//		playSound("ent.3420.tackle", 1, 1, 4);
//		int blockShot_height = g_WorldMgr->m_SurviveGameConfig->basketballConfig.blockShot_height;
//		int defID = ClientAccountMgr::GetInstance().getRoleModel();
//		if (defID == 10)
//		{
//			//�����ɫ�߶Ȳ�һ��
//			blockShot_height = g_WorldMgr->m_SurviveGameConfig->basketballConfig.bolckShot_special_height;
//		}
//		Rainbow::Vector3f motion(0, (float)blockShot_height, 0);
//		LivingLocoMotion* living = static_cast<LivingLocoMotion*>(getLocoMotion());
//		motion.y = living->calGravityMotionY(motion.y);
//		getLocoMotion()->addMotion(motion.x, motion.y, motion.z);
//		m_Body->playAnim(SEQ_BASKETBALL_BLOCK_SHOT);
//		if (!m_pWorld->isRemoteMode())
//		{
//			getLocoMotion()->doPickThrough();
//		}
//	}
//	else if (PLAYEROP_BASKETBALL_OBSTRUCT == type)
//	{
//		playSound("ent.3420.tackle", 1, 1, 4);
//		// 		if (this != g_pPlayerCtrl)
//		// 		{
//		// 			PlayerLocoMotion *pLoc = dynamic_cast<PlayerLocoMotion*>(this->getLocoMotion());
//		// 			if (pLoc)
//		// 			{
//		// 				pLoc->detachPhysActor();
//		// 				pLoc->attachPhysActorForRect(1.5 * BLOCK_SIZE, 1.5 * BLOCK_SIZE);
//		// 			}
//		// 		}
//		m_Body->playEffect(BASKETBALL_OBSTRUCT);
//	}
//	else if (PLAYEROP_BASKETBALL_GRAB == type)
//	{
//		m_Body->playEffect(BASKETBALL_GRAB);
//	}
//	else
//	{
//		//������Ч
//		playSound("ent.3420.tackle", 1, 1, 4);
//	}
//	setOperate(type);
//	return true;
//}
//
//void ClientPlayer::basketBallOPEnd(int type, ClientActor *ball)
//{
//	setOperate(PLAYEROP_NULL);
//	WORLD_ID objId = ball ? ball->getObjId() : 0;
//	//����������
//	if (!m_pWorld->isRemoteMode())
//	{
//		PB_BasketBallOperate basketballOperatorHC;
//		basketballOperatorHC.set_type(type);
//		basketballOperatorHC.set_actorid(objId);
//		basketballOperatorHC.set_extenddata(0);
//		basketballOperatorHC.set_uin(getUin());
//
//		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BASKETBALL_OPERATE_HC, basketballOperatorHC, this, false);
//	}
//
//	if (PLAYEROP_BASKETBALL_OBSTRUCT_END == type)
//	{
//		// 		if (this != g_pPlayerCtrl)
//		// 		{
//		// 			PlayerLocoMotion *pLoc = dynamic_cast<PlayerLocoMotion*>(this->getLocoMotion());
//		// 			if (pLoc)
//		// 			{
//		// 				pLoc->detachPhysActor();
//		// 				pLoc->attachPhysActor();
//		// 			}
//		// 		}
//		m_Body->stopEffect(BASKETBALL_OBSTRUCT);
//	}
//	if (PLAYEROP_BASKETBALL_DRIBBLERUN_END == type)
//	{
//		m_Body->stopEffect(BODYFX_BASKETBALL_DRIBBLERUSH);
//		//setOperate(PLAYEROP_BASKETBALL_DRIBBLERUN_END);
//	}
//	if (PLAYEROP_BASKETBALL_GRAB_END == type)
//	{
//		m_Body->stopEffect(BASKETBALL_GRAB);
//		// 		m_Body->resetSeqAnimDesc();
//	}
//}

//void ClientPlayer::doBasketBallGrabMove()
//{
//	Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
//
//	// 	Rainbow::Vector3f motion(dir.x*g_WorldMgr->m_SurviveGameConfig->ballconfig.tackle_initial_v, 0, dir.z*g_WorldMgr->m_SurviveGameConfig->ballconfig.tackle_initial_v);
//	Rainbow::Vector3f motion(dir.x*g_WorldMgr->m_SurviveGameConfig->basketballConfig.grab_initial_v, 0, dir.z*g_WorldMgr->m_SurviveGameConfig->basketballConfig.grab_initial_v);
//	//setMotionChange(motion, true);
//	getLocoMotion()->addMotion(motion.x, motion.y, motion.z);
//	if (!m_pWorld->isRemoteMode())
//	{
//		getLocoMotion()->doPickThrough();
//	}
//}

//void ClientPlayer::beginChargeThrowBall()
//{
//	m_Body->playEffect(BODYFX_BASKETBALL_CHARGE);
//	playSound("ent.3420.charge", 1, 1, 4);
//	setOperate(PLAYEROP_BASKETBALL_CHARGE_BEGIN);
//
//	if (!m_pWorld->isRemoteMode())
//	{
//		PB_BasketBallOperate ballOperateHC;
//		ballOperateHC.set_type(PLAYEROP_BASKETBALL_CHARGE_BEGIN);
//		ballOperateHC.set_actorid(0);
//		ballOperateHC.set_extenddata(0);
//		ballOperateHC.set_uin(getUin());
//		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BASKETBALL_OPERATE_HC, ballOperateHC, this, false);
//	}
//}

//void ClientPlayer::doKickBasketBall(int type, BasketballFall result, float charge, ClientActor* ball, const WCoord& target_pos, float cameraYaw, float cameraPitch, int selectedActorUin)
//{
//	WORLD_ID objId = ball ? ball->getObjId() : 0;
//
//	if (!m_pWorld->isRemoteMode())
//	{
//		PB_BasketBallOperate basketBallOperateHC;
//		basketBallOperateHC.set_type(type);
//		basketBallOperateHC.set_fallresult(result);
//		basketBallOperateHC.set_actorid(objId);
//		basketBallOperateHC.set_extenddata((int)charge);
//		basketBallOperateHC.set_uin(getUin());
//		PB_Vector3* pos = basketBallOperateHC.mutable_pos();
//		if (pos && (target_pos.x != 0 || target_pos.y != 0 || target_pos.z != 0))
//		{
//			pos->set_x(target_pos.x);
//			pos->set_y(target_pos.y);
//			pos->set_z(target_pos.z);
//		}
//		basketBallOperateHC.set_yaw(cameraYaw);
//		basketBallOperateHC.set_selectedactorid(selectedActorUin);
//		basketBallOperateHC.set_pitch(cameraPitch);
//		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BASKETBALL_OPERATE_HC, basketBallOperateHC, this, false);
//	}
//
//	kickBasketBall(type, result, charge, ball, target_pos, cameraYaw, cameraPitch, selectedActorUin);
//}

//bool ClientPlayer::doBlockShot()
//{
//	return true;
//}

//bool ClientPlayer::doRunDribbleRunBasketBall(ClientActor* ball)
//{
//	if (!m_pWorld->isRemoteMode())
//	{
//		PB_BasketBallOperate basketballOperatorHC;
//		basketballOperatorHC.set_type(PLAYEROP_BASKETBALL_DRIBBLERUN);
//		basketballOperatorHC.set_actorid(ball->getObjId());
//		basketballOperatorHC.set_extenddata(0);
//		basketballOperatorHC.set_uin(getUin());
//		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BASKETBALL_OPERATE_HC, basketballOperatorHC, this, false);
//	}
//	playSound("ent.3420.tackle", 1, 1, 4);
//	Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);;
//	Rainbow::Vector3f motion(dir.x * (g_WorldMgr->m_SurviveGameConfig->basketballConfig.rush_speed * BLOCK_SIZE), 10, dir.z * (g_WorldMgr->m_SurviveGameConfig->basketballConfig.rush_speed * BLOCK_SIZE));
//	getLocoMotion()->addMotion(motion.x, motion.y, motion.z);
//	if (!m_pWorld->isRemoteMode())
//	{
//		getLocoMotion()->doPickThrough();
//	}
//	m_Body->playEffect(BODYFX_BASKETBALL_DRIBBLERUSH);
//	setOperate(PLAYEROP_BASKETBALL_DRIBBLERUN);
//	return true;
//}

//void ClientPlayer::doKickBall(int type, float charge, ClientActor *ball)
//{
//	//if (!ball) return;
//
//	WORLD_ID objId = ball ? ball->getObjId() : 0;
//	if (!m_pWorld->isRemoteMode())
//	{
//		PB_BallOperateHC ballOperateHC;
//		ballOperateHC.set_type(type);
//		ballOperateHC.set_actorid(objId);
//		ballOperateHC.set_extenddata((int)charge);
//		ballOperateHC.set_uin(getUin());
//
//		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BALL_OPERATE_HC, ballOperateHC, this, false);
//	}
//
//	if (ball)
//	{
//		if (type == PLAYEROP_SHOOT)
//		{
//			setOperate(PLAYEROP_SHOOT, 100, (int)charge, objId);
//		}
//		else if (type == PLAYEROP_PASS_BALL)
//		{
//			setOperate(PLAYEROP_PASS_BALL, 100, (int)charge, objId);
//		}
//
//		m_Body->playAnim(SEQ_KICK_BALL);
//	}
//	else
//		kickBall(type, charge, ball);
//
//
//}

void ClientPlayer::kickBall(int type, float charge, ClientActor *ball)
{
	//if (!ball) return;

	if (ball)
	{
		ActorBall *ballactor = dynamic_cast<ActorBall *>(ball);
		if (ballactor) ballactor->kickedByPlayer(type, charge, this);
	}

	setOperate(PLAYEROP_NULL);
	stopEffect(BODYFX_BALL_CHARGE);

	if (charge >= g_WorldMgr->m_SurviveGameConfig->ballconfig.strength_charge)
	{
		playEffect(BODYFX_BALL_SHOOT_RELEASE);
	}
}

void ClientPlayer::kickPushSnowBall(int type, float charge, ClientActor* ball)
{
	//if (!ball) return;

	if (ball)
	{
		ActorPushSnowBall* ballactor = dynamic_cast<ActorPushSnowBall*>(ball);
		if (ballactor) ballactor->kickedByPlayer(type, charge, this);
	}

	setOperate(PLAYEROP_NULL);
	//stopEffect(BODYFX_BALL_CHARGE);

	if (charge >= g_WorldMgr->m_SurviveGameConfig->ballconfig.strength_charge)
	{
		//playEffect(BODYFX_BALL_SHOOT_RELEASE);
	}
}

ClientActor *ClientPlayer::getCatchBall()
{
	auto bindActorComp = m_pBindActorComponent;
	if (bindActorComp) return bindActorComp->getCatchBall();
	else return NULL;
}
/**
 * @brief 获取足球模式滑铲的距离
 * @return int 滑铲距离
 */
int ClientPlayer::getTackleRange(){
	if (!g_WorldMgr)
		return 0;
	return g_WorldMgr->m_SurviveGameConfig->ballconfig.tackle_initial_v;
}
/**
 * @brief 获取篮球模式抢断的闪现距离
 * @return int 抢断距离
 */
int ClientPlayer::getGrabRange(){
	if (!g_WorldMgr)
		return 0;
	return g_WorldMgr->m_SurviveGameConfig->basketballConfig.grab_initial_v;
}
/**
 * @brief 获取篮球模式带球冲刺的闪现距离
 * @return int 带球冲刺距离
 */
int ClientPlayer::getDribbleRange(){
	if (!g_WorldMgr)
		return 0;
	return g_WorldMgr->m_SurviveGameConfig->basketballConfig.rush_speed * BLOCK_SIZE;
}
//void ClientPlayer::doPutBall(ClientActor *ball)
//{
//	if (!ball) return;
//	auto ballbindAComponent = ball->getBallComponent();
//	if (ballbindAComponent)
//	{
//		ballbindAComponent->setBindInfo(-getObjId(), WCoord(0, 0, 0));
//	}
//	else
//	{
//		auto basketballBindAComponent = ball->getBasketBallComponent();
//		if (basketballBindAComponent)
//		{
//			basketballBindAComponent->setBindInfo(-getObjId(), WCoord(0, 0, 0));
//		}
//	}
//}

void ClientPlayer::throwBall(int itemid)
{
	doActualRangeAttack(NULL);
}

void ClientPlayer::callAirDrop(int itemid, int eventId, int x, int z)
{
	LOG_INFO("callAirDrop::itemid: %d eventId:%d x:%d y:%d", itemid, eventId,x , z );
	PluginManager* pluginManager = GetPluginManagerPtr();
	if (pluginManager)
	{
		WorldEventManager* worldEventManager = pluginManager->FindSubsystem<WorldEventManager>();
		if (worldEventManager)
		{
			// 直接调用TriggerAirDropEvent方法触发空投事件
			// 参数：事件ID，位置X，位置Y，位置Z
			if (worldEventManager->TriggerAirDropEvent(eventId, x, 0, z))
			{
				LOG_INFO("callAirDrop: 成功触发空投事件");
			}
			else
			{
				LOG_WARNING("callAirDrop: 触发空投事件失败");
				// 发送自定义文本提示
				this->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 0, 0, "当前已有玩家召唤空投，请稍后再试");
				 
			}
		}
		else
		{
			LOG_WARNING("callAirDrop: 无法获取WorldEventManager");
		}
	}
}

//------------------------baskerball---end---------------------------//

//GravityActor begin
void ClientPlayer::doThrowGravityActor(int type, float charge, ClientActor *actor)
{
	WORLD_ID objId = actor ? actor->getObjId() : 0;
	if (!m_pWorld->isRemoteMode())
	{
		PB_GravityOperateHC gravityOperateHC;
		gravityOperateHC.set_type(type);
		gravityOperateHC.set_actorid(objId);
		gravityOperateHC.set_extenddata((int)charge);
		gravityOperateHC.set_uin(getUin());

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_GRAVITY_OPERATE_HC, gravityOperateHC, this, false);
	}

	//if (actor)
	//{
	//	if (type == PLAYEROP_SHOOT)
	//	{
	//		setOperate(PLAYEROP_SHOOT, 100, charge, objId);
	//	}
	//	else if (type == PLAYEROP_PASS_BALL)
	//	{
	//		setOperate(PLAYEROP_PASS_BALL, 100, charge, objId);
	//	}
	//}
	//else
	throwGravityActor(type, charge, actor);

	getBody()->playAnim(SEQ_KICK_BALL);
}

void ClientPlayer::throwGravityActor(int type, float charge, ClientActor *actor)
{
	if (actor)
	{
		//ActorBall *ballactor = dynamic_cast<ActorBall *>(ball);
		//if (ballactor) ballactor->kickedByPlayer(type, charge, this);

		PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
		if (loc && loc->m_PhysActor)
		{
			this->doPutGravityActor(actor);

			//float charge = 10;
			float unit = 1.8f;
			float motionY = 12;
			float minInitialV = 135;
			Rainbow::Vector3f dir = Yaw2FowardDir(this->getLocoMotion()->m_RotateYaw);
			loc->m_Motion = Rainbow::Vector3f(dir.x*(unit*charge + minInitialV), motionY, dir.z*(unit*charge + minInitialV));
			loc->m_PhysActor->SetAngularVelocity(Rainbow::Vector3f(
				(float)(GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularX_v),
				(float)(GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularY_v),
				(float)(GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularZ_v)));
			loc->m_PhysActor->SetLinearVelocity(loc->m_Motion * 10.0f);

			if (charge >= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.strength_charge)
			{
				ClientActorProjectile *projectile = dynamic_cast<ClientActorProjectile *>(actor);
				if (projectile)
				{
					projectile->playMotion("ball_power_high");
				}
				else
				{
					auto effectComponent = actor->getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect("horsechange_3451");
					}
				}
			}
			else
			{
				ClientActorProjectile *projectile = dynamic_cast<ClientActorProjectile *>(actor);
				if (projectile)
				{
					projectile->playMotion("ball_power_low");
				}
				else
				{
					auto effectComponent = actor->getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect("ball_power_low");
					}
				}
			}
		}
	}

	setOperate(PLAYEROP_NULL);
	stopEffect(BODYFX_BALL_CHARGE);
	if (charge >= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.strength_charge)
	{
		playEffect(BODYFX_BALL_SHOOT_RELEASE);
	}
}

void ClientPlayer::beginChargeThrowGravityActor()
{
	playEffect(BODYFX_BALL_CHARGE);
	setOperate(PLAYEROP_BALL_CHARGE_BEGIN);
	auto sound = getSoundComponent();
	if (sound)
	{
		sound->playSound("ent.3420.charge", 1, 1, 4);
	}
	if (!m_pWorld->isRemoteMode())
	{
		PB_GravityOperateHC gravityOperateHC;
		gravityOperateHC.set_type(PLAYEROP_GRAVITY_CHARGE_BEGIN);
		gravityOperateHC.set_actorid(0);
		gravityOperateHC.set_extenddata(0);
		gravityOperateHC.set_uin(getUin());

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_GRAVITY_OPERATE_HC, gravityOperateHC, this, false);
	}
}

bool ClientPlayer::doCatchGravityActor(ClientActor *actor, int bindDistance, int bindHeight)
{
	//getBody()->playAttack();

	if (!m_pWorld->isRemoteMode())
	{
		PhysicsLocoMotion *loc = NULL;
		if (actor)
		{
			loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
			if (loc && loc->m_PhysActor)
			{
				ClientPlayer *player = nullptr;
				auto bindAComponent = actor->getBindActorCom();
				if (bindAComponent)
				{
					bindAComponent->setBindInfo(getObjId(), WCoord(0, 0, 0));
					player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
				}
				if (player)
				{
					player->doPutGravityActor(actor);
				}
				if (bindAComponent)
				{
					bindAComponent->setBindInfo(getObjId(), WCoord(0, 0, 0));
				}
				if (bindDistance > 0) loc->m_bindDistance = bindDistance;
				if (bindHeight > 0) loc->m_bindHeight = bindHeight;

				Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);

				WCoord pos1 = getPosition();
				WCoord pos = pos1 + WCoord(dir*(BLOCK_FSIZE*1.0f));
				ActorVehicleAssemble* pvehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
				if (pvehicle)
				{
					VehicleAssembleLocoMotion* locoVehicle = dynamic_cast<VehicleAssembleLocoMotion*>(pvehicle->getLocoMotion());
					if (locoVehicle && locoVehicle->m_PhysActorVec.size() > 0)
					{
						locoVehicle->m_PhysActorVec[0]->GetTransform()->SetWorldPosition(pos.toVector3());
					}
				}
				else
				{
					actor->getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
				}
			}
		}

		WORLD_ID objId = actor && loc && loc->m_PhysActor ? actor->getObjId() : 0;
		PB_GravityOperateHC gravityOperateHC;
		gravityOperateHC.set_type(PLAYEROP_CATCH_GRAVITYACTOR);
		gravityOperateHC.set_actorid(objId);
		gravityOperateHC.set_extenddata(0);
		gravityOperateHC.set_uin(getUin());

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_GRAVITY_OPERATE_HC, gravityOperateHC, this, false);
	}

	return true;
}

ClientActor *ClientPlayer::getCatchGravityActor()
{
	auto bindActorComp = m_pBindActorComponent;
	if (bindActorComp) return bindActorComp->getCatchGravityActor();
	else return NULL;
}

void ClientPlayer::doPutGravityActor(ClientActor *actor)
{
	if (!actor) return;
	auto bindAComponent = actor->getBindActorCom();
	if (bindAComponent)
	{
		bindAComponent->setBindInfo(-getObjId(), WCoord(0, 0, 0));
	}

	PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
	if (loc && loc->m_PhysActor)
	{
		loc->m_PhysActor->SetLinearVelocity(Rainbow::Vector3f(0, 1.0f, 0));
		loc->m_bindDistance = 0;
		loc->m_bindHeight = 0;
	}
}

//GravityActor end

#pragma region Logo todo:delete
//SkillCDComponent begin
void ClientPlayer::saveSkillCDCompToPB(game::common::PB_SkillCDData* skillCDData)
{	
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) skillCDComp->saveToPB(skillCDData);
}

void ClientPlayer::setSkillCD(int itemid, float cd)
{	
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) skillCDComp->setSkillCD(itemid, cd);

	addWeaponSkilledPoint(WEAPON_SKILLED_TYPE_CDSKILL, itemid);
}

void ClientPlayer::syncSkillCD(int itemid, float cd)
{	
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) skillCDComp->syncSkillCD(itemid, cd);
}

void ClientPlayer::setItemSkillCD(int itemid, float cd)
{
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) skillCDComp->setItemSkillCD(itemid, cd);
}

float ClientPlayer::getSkillCD(int itemid)
{	
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) return skillCDComp->getSkillCD(itemid);
	else return 0.0f;
}

float ClientPlayer::getTotalSkillCD(int itemid)
{	
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) return skillCDComp->getTotalSkillCD(itemid);
	else return 0.0f;
}
//SkillCDComponent end
#pragma endregion

#pragma region Logo todo:delete
//m_pItemSkillComp begin
ItemSkillComponent* ClientPlayer::getItemSkillComponent()
{
	return m_pItemSkillComponent;
}

ActorInPortal*  ClientPlayer::sureActorInPortal()
{
	if (!m_pActorInPortal){
		m_pActorInPortal = CreateComponent<PlayerInPortal>("PlayerInPortal");
	}
	/*if (m_pActorInPortal == nullptr) {
		return nullptr;
	}*/
	//return static_cast<ActorInPortal*>(m_pActorInPortal);
	return m_pActorInPortal;
}
ActorBindVehicle* ClientPlayer::getActorBindVehicle()
{
	if (!m_pActorBindVehicle){
		m_pActorBindVehicle = CreateComponent<PlayerBindVehicle>("PlayerBindVehicle");
	}
	
	return m_pActorBindVehicle;
}

ActorUpdateFrequency* ClientPlayer::getUpdateFrequencyCom()
{
	if (!m_pActorUpdateFrequency) {
		m_pActorUpdateFrequency = CreateComponent<EmptyUpdateFrequency>("EmptyUpdateFrequency");
	}
	if (m_pActorUpdateFrequency == nullptr) {
		return nullptr;
	}
	return static_cast<EmptyUpdateFrequency*>(m_pActorUpdateFrequency);
}

int ClientPlayer::getCurItemSkillID()
{
	return getItemSkillComponent()->getCurItemSkillID();
}

void ClientPlayer::setCurItemSkillID(int skillid)
{
	getItemSkillComponent()->setCurItemSkillID(skillid);
}

bool ClientPlayer::useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos, std::string clientParam)
{
	return getItemSkillComponent()->useItemSkill(itemid, status, skillid, currentEyePos, currentDir, blockpos, obj, centerPos, clientParam);
}

const ItemSkillDef* ClientPlayer::getCurItemSkillDef()
{
	return getItemSkillComponent()->getCurItemSkillDef();
}
//m_pItemSkillComp end
#pragma endregion

#pragma  region Logo todo:delete
//m_pAccountHorseComp begin

AccountHorseComponent* ClientPlayer::sureAccountHorseComponent()
{
	auto comp = GetComponent<AccountHorseComponent>();
	if (!comp)
	{
		comp = CreateComponent<AccountHorseComponent>("AccountHorseComponent");
	}

	return comp;
}

void ClientPlayer::accountHorseEgg()
{		
	auto comp = sureAccountHorseComponent();//by__Logo
	if (comp)
		comp->accountHorseEgg();
}
void ClientPlayer::summonAccountHorse(int horseid)
{	
	auto comp = sureAccountHorseComponent();//by__Logo
	if (comp)
		comp->summonAccountHorse(horseid);
}
ClientActor *ClientPlayer::summonShapeShiftHorse(int horseid)
{	
	auto comp = sureAccountHorseComponent();//by__Logo
	if (comp)
		return comp->summonShapeShiftHorse(horseid);
	else
		return NULL;
}
ClientActor* ClientPlayer::getCurAccountHorse()
{
	World* pWorld = getWorld();
	
	auto comp = GetComponent<AccountHorseComponent>();//by__Logo
	if (comp && pWorld)
	{
		if (comp->getCurAccountHorse() != 0)
		{
			return static_cast<ClientActorMgr*>(pWorld->getActorMgr())->findActorByWID(comp->getCurAccountHorse());
		}
	}
		
	return NULL;
}
void ClientPlayer::setCurAccountHorse(long long objId)
{
	auto comp = sureAccountHorseComponent();
	if (comp)
		return comp->setCurAccountHorse(objId);
}

void ClientPlayer::updateAccountHorse(int horseid, float hp, int addlivetick, int shieldcoolingticks)
{	
	auto comp = GetComponent<AccountHorseComponent>();//by__Logo
	if (comp)
		comp->updateAccountHorse(horseid, hp, addlivetick, shieldcoolingticks);
}

void ClientPlayer::resetAccountHorseLiveTick(int horseid, int t)
{	
	auto comp = sureAccountHorseComponent();//by__Logo
	if (comp)
		comp->resetAccountHorseLiveTick(horseid, t);
}

void ClientPlayer::setAccountHorseEquip(int horseid, int index, int itemid)
{	
	auto comp = sureAccountHorseComponent();//by__Logo
	if (comp)
		comp->setAccountHorseEquip(horseid, index, itemid);
}

int ClientPlayer::getAccountHorseLiveAge(int horseid)
{
	auto comp = GetComponent<AccountHorseComponent>();//by__Logo
	if (comp)
		return comp->getAccountHorseLiveAge(horseid);
	else
		return 0;
}

void ClientPlayer::clearAccountHorseLiveAge(int horseid)
{	
	auto comp = GetComponent<AccountHorseComponent>();//by__Logo
	if (comp)
		comp->clearAccountHorseLiveAge(horseid);
}

bool ClientPlayer::isMyAccountHorse(long long objid)
{	
	auto comp = GetComponent<AccountHorseComponent>();//by__Logo
	if (comp)
		return comp->getCurAccountHorse() == objid;
	else
		return false;
}
//m_pAccountHorseComp end
#pragma endregion

PetFollowListComponent* ClientPlayer::surePetFollowListComponent()
{
	auto petFollowListComp = GetComponent<PetFollowListComponent>();
	if (!petFollowListComp)
	{
		petFollowListComp = CreateComponent<PetFollowListComponent>("PetFollowListComponent");
	}

	return petFollowListComp;
}

#pragma region Logo todo:delete
//m_pPetFollowListComp begin
bool ClientPlayer::addMobToTamedFollows(ClientMob* mob)
{	
	auto petFollowListComp = surePetFollowListComponent();
	if (petFollowListComp) return petFollowListComp->addMobToTamedFollows(mob);
	else return false;
}

void ClientPlayer::removeMobFromTamedFollows(ClientMob* mob)
{	
	auto petFollowListComp = GetComponent<PetFollowListComponent>();
	if (petFollowListComp) petFollowListComp->removeMobFromTamedFollows(mob);
}

bool ClientPlayer::isInTamedFollows(ClientMob * mob)
{	
	auto petFollowListComp = GetComponent<PetFollowListComponent>();
	if (petFollowListComp) return petFollowListComp->isInTamedFollows(mob);
	else return false;
}
//m_pPetFollowListComp end
#pragma endregion

#pragma region Logo todo:delete
//m_pPetAccountComp begin

PetAccountComponent* ClientPlayer::surePetAccountComponent()
{
	auto comp = GetComponent<PetAccountComponent>();
	if (!comp)
	{
		comp = CreateComponent<PetAccountComponent>("PetAccountComponent");
	}

	return comp;
}

void ClientPlayer::summonAccountPet(int petid)
{	
	auto petAccountComp = surePetAccountComponent();
	if (petAccountComp) petAccountComp->summonAccountPet(petid);
}

ClientActor *ClientPlayer::getCurPet()
{
	auto petAccountComp = GetComponent<PetAccountComponent>();
	if (petAccountComp) return petAccountComp->getCurPet();
	else return NULL;

}

void ClientPlayer::hideAccountPet()
{
	auto petAccountComp = GetComponent<PetAccountComponent>();
	if (petAccountComp) petAccountComp->hideAccountPet();
}

void ClientPlayer::showAccountPet()
{	
	auto petAccountComp = GetComponent<PetAccountComponent>();
	if (petAccountComp) petAccountComp->showAccountPet();
}
//m_pPetAccountComp end

//m_pPetSummonComp begin
PetSummonComponent* ClientPlayer::surePetSummonComponent()
{
	auto comp = GetComponent<PetSummonComponent>();
	if (!comp)
	{
		comp = CreateComponent<PetSummonComponent>("PetSummonComponent");
	}

	return comp;
}

void ClientPlayer::summonPet(int monsterid, std::string serverid, int petid, int stage, int quality, std::string petName/* = ""*/)
{
	auto petSummonComp = surePetSummonComponent();
	if (petSummonComp) petSummonComp->summonPet(monsterid, serverid, petid, stage, quality, petName);
}

void ClientPlayer::setCurSummonPetID(std::string& petId)
{	
	auto petSummonComp = surePetSummonComponent();
	if (petSummonComp) petSummonComp->setCurSummonPetID(petId);
}
void ClientPlayer::setCurSummonPetInfo(int monsterid, int petid, int stage, int quality)
{	
	auto petSummonComp = surePetSummonComponent();
	if (petSummonComp) petSummonComp->setCurSummonPetInfo(monsterid, petid, stage, quality);
}

std::string ClientPlayer::getCurSummonPetID()
{	
	auto petSummonComp = GetComponent<PetSummonComponent>();
	if (petSummonComp) return petSummonComp->getCurSummonPetID();
	else return "";
}

SummonPetInfomation ClientPlayer::getCurSummonPetInfo()
{		
	auto petSummonComp = GetComponent<PetSummonComponent>();
	if (petSummonComp) return petSummonComp->getCurSummonPetInfo();
	else return SummonPetInfomation();
}

//PetSummonComponent* ClientPlayer::getPetSummonComp()
//{
//	if (m_pPetSummonComp)
//		return m_pPetSummonComp;
//	m_pPetSummonComp = SANDBOX_NEW(PetSummonComponent, this);
//	return m_pPetSummonComp;
//}
//m_pPetSummonComp end
#pragma endregion

#pragma region Logo todo:delete
//m_pChangeColorComp begin
ChangeColorComponent* ClientPlayer::sureChangeColorComponent()
{
	auto changColorComp = GetComponent<ChangeColorComponent>();
	if (!changColorComp)
	{
		return CreateComponent<ChangeColorComponent>("ChangeColorComponent");
	}
	else
	{
		return changColorComp;
	}
}

void ClientPlayer::onHandlePlayerBodyColor2Client(const PB_PlayerBodyColorHC &playerBodyColorHC)
{	
	auto changColorComp = sureChangeColorComponent();
	if (changColorComp)
	{
		changColorComp->onHandlePlayerBodyColor2Client(playerBodyColorHC);
	}
}
//m_pChangeColorComp end

//m_pTransformersSkinComp begin
bool ClientPlayer::onInteractByActorSkinNpc(long long mainPlayer, int changeModel)
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) return transformsSkinComp->onInteractByActorSkinNpc(mainPlayer, changeModel);
	else return false;
}

void ClientPlayer::restoreSkin()
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->restoreSkin();
}

// ��ԭ����
// reason = 1 ��װ��������ԭ
// reason = 2 ��װ��������ԭ
// reason = 3 ��װ�类����ԭ
void ClientPlayer::resetDeformation(int reason)
{	
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->resetDeformation(reason);
}

void ClientPlayer::DeformationSkin(RoleSkinDef * skinDef)
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->DeformationSkin(skinDef);
}

bool ClientPlayer::trySplitDisguise(short id)
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) return transformsSkinComp->trySplitDisguise(id);
	else return false;
}

void ClientPlayer::eraseSkinSubPlayer(long long objid)
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->eraseSkinSubPlayer(objid);
}

void ClientPlayer::insertSkinSubPlayer(long long objid)
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->m_vecSkinSubPlayers.push_back(objid);
}

void ClientPlayer::transformSkinReason(int transformReason)
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->m_TransformReason = transformReason;
}

bool ClientPlayer::InTransform()
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) return transformsSkinComp->m_bInTransform;
	else return false;
}
void ClientPlayer::restoreSkinByReason(int transformReason)
{	
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->restoreSkinByReason(transformReason);
}

void ClientPlayer::resetActorBody()	//���κ�ԭģ��
{
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->resetActorBody();
}
void ClientPlayer::onHandlePlayerTransformSkinModel2Client(const PB_PlayerTransformSkinHC &changeModelHC)
{	
	auto transformsSkinComp = m_TransformerSkinComponent;
	if (transformsSkinComp) transformsSkinComp->onPlayerTransformSkinModel(changeModelHC.reason(), changeModelHC.mainplayerid(), getObjId());

	if (changeModelHC.reason() == 1)
	{
		changeViewMode(GetClientInfoProxy()->getGameData("view") - 1, false, true);
	}

	ClientPlayer::changePlayerModel(changeModelHC.playerindex(), 0, changeModelHC.customskins().c_str());
}

void ClientPlayer::moveMobItem(int gridIndex, int moveType)
{
	auto interactTaedMobComp = SureInteractTamedMobComponent();
	if (interactTaedMobComp) interactTaedMobComp->moveMobItem(gridIndex, moveType);
}

bool ClientPlayer::InteractMobItem(int fromIndex, int toIndex)
{
	auto interactTaedMobComp = SureInteractTamedMobComponent();
	if (interactTaedMobComp) return interactTaedMobComp->InteractMobItem(fromIndex, toIndex);
	else return false;
}

void ClientPlayer::InteractMobPack(const std::string& name, const std::string& param, long long mobID)
{	
	auto interactTaedMobComp = SureInteractTamedMobComponent();
	if (interactTaedMobComp) interactTaedMobComp->InteractMobPack(name, param, mobID);
}

void ClientPlayer::PickMobBackpack(int gridIndex, int moveType, int toGridIndex)
{	
	auto interactTaedMobComp = SureInteractTamedMobComponent();
	if (interactTaedMobComp) interactTaedMobComp->PickMobBackpack(gridIndex, moveType ,toGridIndex);
}

InteractTamedMobComponent* ClientPlayer::SureInteractTamedMobComponent()
{
	auto interactTaedMobComp = GetComponent<InteractTamedMobComponent>();
	if (!interactTaedMobComp)
	{
		interactTaedMobComp = CreateComponent<InteractTamedMobComponent>("InteractTamedMobComponent");
	}
	return interactTaedMobComp;
}

//m_pInteractTamedMobComp end

//PlayerAttackingTargetComponent begin
void ClientPlayer::setAttackAnim(ATTACK_TYPE attacktype)
{
	auto interactTaedMobComp = getAttackingTargetComponent();
	if (interactTaedMobComp) interactTaedMobComp->setAttackAnim(attacktype);
}

void ClientPlayer::setAttackAnim(ATTACK_TYPE attacktype, int animTicks)
{
	auto interactTaedMobComp = getAttackingTargetComponent();
	if (interactTaedMobComp) interactTaedMobComp->setAttackAnim(attacktype, animTicks);
}

//void ClientPlayer::setUid(const std::string& uid)
//{
//	m_uid = uid;
//}
//
//std::string ClientPlayer::getUid()
//{
//	return m_uid;
//}

//PlayerAttackingTargetComponent end
#pragma endregion

#pragma region Logo todo:delete
//m_pOpenContainerComp begin
OpenContainerComponent*  ClientPlayer::getOpenContainerCom()
{
	return GetComponent<OpenContainerComponent>();
}

int ClientPlayer::getContainersPassword(const WCoord& pos)
{
	return getOpenContainerCom()->getContainersPassword(pos);
}

void ClientPlayer::setContainersPassword(const WCoord& pos, int password)
{
	getOpenContainerCom()->setContainersPassword(pos, password);
}


bool ClientPlayer::openContainer(WorldContainer *container)
{
	if (m_pWorld && container)
	{
		//打开方块容器任务上报
		updateTaskSysProcess(TASKSYS_OPENCONTAINER, m_pWorld->getBlockID(container->m_BlockPos));
	}
	return getOpenContainerCom()->openContainer(container);
}

bool ClientPlayer::openContainer(ActorContainerMob *container)
{
	return getOpenContainerCom()->openContainer(container);
}

void ClientPlayer::closeContainer()
{

	OpenContainerComponent* comp = getOpenContainerCom();
	if (comp)
	{
		int baseindex = comp->getOpenContainerBaseIndex();
		comp->closeContainer();

		if (baseindex > 0) {
			if (hasUIControl())
			{
				if (baseindex == ACTORMODEL_START_INDEX)
				{
					//ge GetGameEventQue().postCloseEditActorModel();
					if (SandboxCoreDriver::GetInstancePtr()) {
						SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_CLOSE_EDIT_ACTORMODEL", MNSandbox::SandboxContext(nullptr));
					}
				}
				else
				{
					//ge GetGameEventQue().postCloseContainer(getBaseIndex());
					MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
						SetData_Number("baseindex", baseindex);
					if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
						MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_CLOSE_CONTAINER", sandboxContext);
					}
				}
			}
			else
			{
				PB_CloseContainerHC closeContainerHC;
				closeContainerHC.set_baseindex(baseindex);

				GetGameNetManagerPtr()->sendToClient(getUin(), PB_CLOSE_CONTAINER_HC, closeContainerHC);
			}
		}
	}
}

void ClientPlayer::npcTrade(int op, int index, bool watch_ad/* =false */, int ad_rewardnum/* =1 */)
{
	getOpenContainerCom()->npcTrade(op, index, watch_ad, ad_rewardnum);
}

int ClientPlayer::storeItem(int frmGrid, int num)
{
	return getOpenContainerCom()->storeItem(frmGrid, num);
}

void ClientPlayer::socMoveItem(int fromindex, int toindex, int mouseindex)
{
	if (!getBackPack())
		return;

	BackPackGrid* togrid = getBackPack()->index2Grid(toindex);
	BackPackGrid* mousegrid = getBackPack()->index2Grid(mouseindex);
	if (!(togrid && mousegrid))
	{
		return;
	}
	int mouseitemid = mousegrid->getItemID();
	if (mouseitemid == 0)
	{
		return;
	}

	int toitemid = togrid->getItemID();
	//=0 是空格直接全部移动
	if (toitemid == 0)
	{
		//埋点
		TrackMove(fromindex, toindex, mousegrid->getItemID() , mousegrid->getNum());
		getBackPack()->moveItem(mouseindex, toindex, mousegrid->getNum());
		return;
	}

	int fromid = 0;
	int fromnum = 0;
	BackPackGrid* fromgrid = getBackPack()->index2Grid(fromindex);
	if (fromgrid) {
		fromid = fromgrid->getItemID();
		fromnum = fromgrid->getNum();
	}

	ItemDef* itemdef = GetDefManagerProxy()->getItemDef(toitemid);
	//检测合并
	if (toitemid == mouseitemid && itemdef && itemdef->StackMax > 1)
	{
		//超过上限合并一个最大
		int mousecount = mousegrid->getNum();
		int tocount = togrid->getNum();
		if (mousecount + tocount > itemdef->StackMax)
		{
			getBackPack()->setItem(mouseitemid, toindex, itemdef->StackMax);
			getBackPack()->setItem(0, mouseindex, 0);
			//埋点
			TrackMove(fromindex, toindex, mouseitemid,fromnum + mousecount + tocount - itemdef->StackMax);
			//加入了拆分逻辑fromindex不一定时是空格要加上原来的num, 如果fromnum有值肯定mouseitemid == fromid
			getBackPack()->setItem(mouseitemid, fromindex, fromnum + mousecount + tocount - itemdef->StackMax);
		}
		else
		{
			//埋点
			TrackMove(fromindex, toindex, toitemid, mousecount);
			getBackPack()->setItem(mouseitemid, toindex, mousecount + tocount);
			getBackPack()->setItem(0, mouseindex, 0);
		}
		return;
	}

	//from 格子有东西 或者 from格子是装备格子并且目标格子不是装备 走一遍入背包流程
	if (fromid != 0 || 
		(
			itemdef && 
			itemdef->Type != ITEM_TYPE_EQUIP && 
			fromindex >= EQUIP_START_INDEX && fromindex < EQUIP_START_INDEX + 8)
		)
	{
		int sum = togrid->getNum();
		GridCopyData gridcopydata(togrid);
		BackPackGrid throwgrid(*togrid);
		//覆盖目标格子,让目标格子的物品重新入库
		getBackPack()->setItem(0, toindex, 0);
		//埋点
		TrackMove(fromindex, toindex, mousegrid->getItemID(), mousegrid->getNum());
		getBackPack()->moveItem(mouseindex, toindex, mousegrid->getNum());
		int n = getBackPack()->addItemWithPickUp_bySocGridCopyData(gridcopydata);
		TrackMove(toindex, fromindex, gridcopydata.resid,n);
		//塞不下
		if (sum - n > 0)
		{
			throwgrid.setNum(sum - n);
			throwgrid.m_effects = togrid->m_effects;
			throwItem(throwgrid);
		}
		return;
	}

	//埋点
	TrackMove(toindex, fromindex, togrid->getItemID(), togrid->getNum());
	getBackPack()->moveItem(toindex, fromindex, togrid->getNum());
	//埋点
	TrackMove(fromindex, toindex, mousegrid->getItemID() , mousegrid->getNum());
	getBackPack()->moveItem(mouseindex, toindex, mousegrid->getNum());
	return;
}

void ClientPlayer::TrackMove(int fromindex, int toindex, int itemid ,int num)
{
	//现有规则下都是打开一个容器, 快捷栏 背包 其他容器的三种交换  快捷栏和背包当成一个整体
	int frombaseindex = fromindex / GRID_INDEX_BASIS * GRID_INDEX_BASIS;
	int tobaseindex = toindex / GRID_INDEX_BASIS * GRID_INDEX_BASIS;
	//忽略鼠标格子
	if (tobaseindex == MOUSE_PICKITEM_INDEX || frombaseindex == MOUSE_PICKITEM_INDEX) return;
	//一样说明是相同的容器不埋点
	if (frombaseindex == tobaseindex) return;
	WorldContainer* cur_container = getCurOpenedContainer();
	//目前的规则没打开容器不可能有容器的交换
	if (!cur_container) return;
	//快捷栏和背包当成一个整体
	if ((frombaseindex == BACKPACK_START_INDEX && tobaseindex == SHORTCUT_START_INDEX) ||
		(tobaseindex == BACKPACK_START_INDEX && frombaseindex == SHORTCUT_START_INDEX))
		return;

	//其中一个必是快捷栏或背包
	int src_type = -1;
	int dst_type = -1;
	if (frombaseindex == BACKPACK_START_INDEX || frombaseindex == SHORTCUT_START_INDEX)
	{
		//0表示背包和快捷栏
		src_type = 0;
		dst_type = GetSocContainerType(cur_container, toindex);
	}
	else
	{
		//不是frombaseindex那么tobaseindex 一定是表示背包和快捷栏
		dst_type = 0;
		src_type = GetSocContainerType(cur_container, fromindex);
	}

	std::string location = "";
	location += std::to_string(cur_container->m_BlockPos.x);
	location += ",";
	location += std::to_string(cur_container->m_BlockPos.y);
	location += ",";
	location += std::to_string(cur_container->m_BlockPos.z);
	location += "";

	std::string items = "[{";
	items += "item_id:";
	items += std::to_string(itemid);
	items += ",";
	items += "count:";
	items += std::to_string(num);
	items += "}]";

	GameAnalytics::TrackEvent("item_transfer", {
		{"src_type",src_type},
		{"dst_type",dst_type},
		{"items",items},
		{"loc",location}
	}, true, getUin());
	return;
}

// 0 背包
// 1 分解机
// 2 工作台
// 3 研究台
// 4 维修台

// 10 箱子
// 11 熔炉
// 12 氧气提炼装置
// 13 篝火
// 14 大熔炉
// 15 炼油机

int ClientPlayer::GetSocContainerType(WorldContainer* container, int index)
{
	if (index == BACKPACK_START_INDEX || index == SHORTCUT_START_INDEX)
		return 0;

	int blockid = m_pWorld->getBlockID(container->m_BlockPos);
	//BlockMaterial* ma = g_BlockMtlMgr.getMaterial(blockid);
	//箱子的index 分解机 工作台 研究台 维修台
	if (index == STORAGE_START_INDEX)
	{
		//分解机
		if (dynamic_cast<ContainerDecomposition*>(container))
		{
			return 1;
		}
		//工作台
		if (dynamic_cast<ContainerSocWorkbench*>(container))
		{
			return 2;
		}
		//研究台
		//if (ma && dynamic_cast<BlockResearch*>(ma))
		//{
		//	return 3;
		//}
		//先用id吧 维修台 没法动态判断干脆一起用id
		//研究台
		if (blockid == 2413)
		{
			return 3;
		}
		//维修台
		if (blockid == 2412)
		{
			return 4;
		}
		

		//排除以上其他的应该都是真箱子
		return 10;
	}

	//熔炉的index
	if (index == FURNACE_START_INDEX)
	{
		BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
		if (blockdef)
		{
			//熔炉
			if (blockdef->EditType == 0)
			{
				return 11;
			}
			//氧气提炼装置
			if (blockdef->EditType == 1)
			{
				return 12;
			}
			//篝火
			if (blockdef->EditType == 2)
			{
				return 13;
			}
			//大熔炉
			if (blockdef->EditType == 3)
			{
				return 14;
			}
			//炼油机
			if (blockdef->EditType == 4)
			{
				return 15;
			}
		}
	}

	//未知
	return -1;
}

void ClientPlayer::openEditActorModelUI(WorldContainer* container)
{
	getOpenContainerCom()->openEditActorModelUI(container);
}

void ClientPlayer::closeEditActorModel(int operatetype, std::string modelname/* ="" */)
{
	getOpenContainerCom()->closeEditActorModel(operatetype, modelname);
}

void ClientPlayer::openFullyCustomModelUI(WorldContainer* container)
{
	getOpenContainerCom()->openFullyCustomModelUI(container);
}

void ClientPlayer::syncOpenFCMUIToClient(const WCoord& blockpos, bool isedited, std::string url, int version/* =0 */, int result/* =0 */)
{
	getOpenContainerCom()->syncOpenFCMUIToClient(blockpos, isedited, url, version, result);
}

void ClientPlayer::closeFullyCustomModelUI(int operatetype, std::string name/* ="" */, std::string desc/* ="" */)
{
	getOpenContainerCom()->closeFullyCustomModelUI(operatetype, name, desc);
}


bool ClientPlayer::checkIsOpenContainer(const WCoord &pos, int index)
{
	return getOpenContainerCom()->checkIsOpenContainer(pos, index);
}

bool ClientPlayer::checkIsOpenContainer(WORLD_ID objid, int index)
{
	return getOpenContainerCom()->checkIsOpenContainer(objid, index);
}

WorldContainer* ClientPlayer::getCurOpenedContainer()
{
	return getOpenContainerCom()->getCurOpenedContainer();
}

WCoord ClientPlayer::getCurOpenedContainerPos()
{
	return getOpenContainerCom()->getCurOpenedContainerPos();
}

void ClientPlayer::onCloseFullyCustomModelUI(const PB_CloseFullyCustomModelUICH &closeFullyCustomModelUICH)
{
	getOpenContainerCom()->onCloseFullyCustomModelUI(closeFullyCustomModelUICH);
}

void ClientPlayer::onCloseEditActorModel(const PB_CloseEditActorModelCH &closeEditActorModelCH)
{
	getOpenContainerCom()->onCloseEditActorModel(closeEditActorModelCH);
}

void ClientPlayer::cleanupOpenedContainer()
{
	getOpenContainerCom()->cleanupOpenedContainer();
}

int ClientPlayer::getOpenContainerBaseIndex()
{
	return getOpenContainerCom()->getOpenContainerBaseIndex();
}
//m_pOpenContainerComp end

HPProgressComponent* ClientPlayer::getHPProgressComponent()
{
	return GetComponent<PlayerHPProgressComponent>();
}
#pragma endregion

//WeaponSkilledComponent start
void ClientPlayer::addWeaponSkilledPoint(int pointType, int itemId)
{
	int weaponType = 0;
	int operatePlayerUin = getUin();
	int curItemId = itemId != 0 ? itemId : this->getCurToolID();
	SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("WeaponSkin_HelperModule", "GetOwndSkinItemType", "ii>i", operatePlayerUin, curItemId, &weaponType);

	if (weaponType < 0) return;

	if (pointType == WEAPON_SKILLED_TYPE_KILLED) //击杀生物后恢复生命值
	{
		const char* attrType = "WeaponSkin_System_HealthPoint";
		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != NULL)
		{
			float fpercent = weaponSkinMgr->GetSkinAdditionPercent(attrType, this, curItemId);
			PlayerAttrib* playerAttrib = getPlayerAttrib();
			if (playerAttrib != NULL)
			{
				float currHp = playerAttrib->getHP();
				float maxHp = playerAttrib->getMaxHP();
				float totalHp = currHp + (maxHp - currHp) * fpercent;
				playerAttrib->setHpForTrigger(totalHp);
			}
		}
	}

	//当前玩家是操作玩家则直接添加熟练度
	if (g_pPlayerCtrl && g_pPlayerCtrl->getUin() == operatePlayerUin)
	{
		WeaponSkilledComponent::addSkilledPoint(this, pointType, weaponType - 1);
	}
	else
	{
		// 将熟练度同步到客机
		PB_WeaponPointHC weaponPointHC;
		weaponPointHC.set_targetuin(operatePlayerUin);
		weaponPointHC.set_pointtype(pointType);
		weaponPointHC.set_itemid(curItemId);

		GameNetManager::getInstance()->sendToClient(operatePlayerUin, PB_WEAPON_POINT_HC, weaponPointHC);
	}
}
//getWeaponSkilledComponent end

//-------enchant,rune--------- begin
bool ClientPlayer::canEnchant(int tgtGrid, int frmGrid, int enchants[MAX_ITEM_ENCHANTS])
{
	if (tgtGrid < 0 || frmGrid < 0) return false;
	auto backpack = getBackPack();
	if (backpack == nullptr) return false;

	// �����Ʒ�����Ƿ����
	auto tgtItem = backpack->getGridItem(tgtGrid);
	auto frmItem = backpack->getGridItem(frmGrid);
	if (tgtItem == 0 || frmItem == 0) return false;

	if (backpack->getGridEnchantNum(frmGrid) <= 0) return false; //���ϲ�����ħ

	// ���������Ʒ���ͺ�λ���Ƿ���ȷ
	auto tgtDef = GetDefManagerProxy()->getItemDef(tgtItem);
	auto frmDef = GetDefManagerProxy()->getItemDef(frmItem);
	if (!tgtDef || !frmDef) return false; // ��Ʒ�޶���

	if (tgtDef->EnchantTag <= 0) return false; // Ŀ����Ӳ�������ħ
	if (frmDef->ID != 11807 && //���ϸ��Ӳ����鲢�Ҳ��ϻ����Ͳ�ͬ
		(tgtDef->Type != frmDef->Type || tgtDef->StuffType != frmDef->StuffType)
		)
		return false;

	// ����Ƿ����и�ħ���Ǵ��ڵ�
	const EnchantDef* defs[MAX_ITEM_ENCHANTS];
	memset(defs, 0, sizeof(defs));
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		defs[i] = GetDefManagerProxy()->getEnchantDef(enchants[i]);
		if (enchants[i] > 0 && !defs[i]) return false;
	}

	// ���5����ħ���з��ͻ
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		if (!defs[i]) continue;
		for (int j = i + 1; j < MAX_ITEM_ENCHANTS; ++j)
		{
			if (!defs[j]) continue;
			if (defs[i]->ID == defs[j]->ID) return false; //ͬһ����ħ������
			if (defs[i]->ConflictID > 0 && defs[i]->ConflictID == defs[j]->ConflictID) // ��ͻ�ĸ�ħ������
				return false;
		}
	}

	//--begin �ҳ�2����Ʒ�ϲ�������п��и�ħ
	int allEnchants[20];
	memset(allEnchants, 0, sizeof(allEnchants));
	int aidx = 0;
	for (int i = 0, _num = backpack->getGridEnchantNum(tgtGrid); i < _num; ++i)
	{
		int eid = backpack->getGridEnchantId(tgtGrid, i);
		bool added = false;
		for (int j = 0; j < aidx; ++j)
		{
			if (eid / 100 == allEnchants[j] / 100) // ͬ�ָ�ħ
			{
				if (eid % 100 == allEnchants[j] % 100) // �ȼ�һ����ϲ�
				{
					allEnchants[j] = eid + 1;
					added = true;
				}
				else
				{
					allEnchants[j] = eid > allEnchants[j] ? eid : allEnchants[j];
					added = true;
				}
				break;
			}
		}
		if (!added) allEnchants[aidx++] = eid;
	}
	for (int i = 0, _num = backpack->getGridEnchantNum(frmGrid); i < _num; ++i)
	{
		int eid = backpack->getGridEnchantId(frmGrid, i);
		bool added = false;
		for (int j = 0; j < aidx; ++j)
		{
			if (eid / 100 == allEnchants[j] / 100) // ͬ�ָ�ħ
			{
				if (eid % 100 == allEnchants[j] % 100) // �ȼ�һ����ϲ�
				{
					allEnchants[j] = eid + 1;
					added = true;
				}
				else
				{
					allEnchants[j] = eid > allEnchants[j] ? eid : allEnchants[j];
					added = true;
				}
				break;
			}
		}
		if (!added) allEnchants[aidx++] = eid;
	}
	// �����ȼ����޵� �ɵ�1��
	for (int i = 0; i < aidx; ++i)
	{
		int level = allEnchants[i];
		while (level % 100 > 0 && !GetDefManagerProxy()->getEnchantDef(level))
			--level;
		if (level % 100 <= 0) return false;
		allEnchants[i] = level;
	}
	//--end �ҳ�2����Ʒ�ϲ�������п��и�ħ

	// ����ǲ��Ǵ������ĸ�ħ���ڿ��и�ħ��
	bool sat[MAX_ITEM_ENCHANTS];
	memset(sat, 0, sizeof(sat));
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		if (enchants[i] == 0) sat[i] = true;
		else for (int j = 0; j < aidx; ++j)
		{
			if (allEnchants[j] == enchants[i])
			{
				sat[i] = true;
				break;
			}
		}
	}
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
		if (sat[i] != true) return false;

	return true;
}

int ClientPlayer::calcEnchantCost(int tgtGrid, int enchants[MAX_ITEM_ENCHANTS])
{
	if (tgtGrid < 0) return -1;
	auto backpack = getBackPack();
	if (backpack == nullptr) return -1;

	auto itemDef = GetDefManagerProxy()->getItemDef(backpack->getGridItem(tgtGrid));

	if (itemDef == nullptr) return -1;
	auto costDef = GetDefManagerProxy()->getEnchantMentDef(itemDef->StuffType);
	if (costDef == nullptr) return -1;

	int ret = 0;
	int curEnchants[MAX_ITEM_ENCHANTS];
	int num = backpack->getGridEnchantNum(tgtGrid);
	for (int i = 0; i < num; ++i)
		curEnchants[i] = backpack->getGridEnchantId(tgtGrid, i);

	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		if (enchants[i] <= 0) continue;
		int tgtLvl = enchants[i] % 100;
		bool found = false;

		for (int j = 0; j < num; ++j)
		{
			if (curEnchants[j] / 100 == enchants[i] / 100)
			{
				found = true;
				int curLvl = curEnchants[j] % 100;
				if (tgtLvl < curLvl) return -1;

				if (tgtLvl == curLvl) break; // ���
				for (int k = curLvl; k < tgtLvl; ++k) // �ۼ��������
					ret += costDef->MergeCost[k];
				break;
			}
		}
		if (!found)
		{
			for (int k = 0; k < tgtLvl; ++k)
				ret += costDef->MergeCost[k];
		}
	}
	return ret;
}

int ClientPlayer::enchant(int tgtGridIdx, int frmGridIdx, int enchants[MAX_ITEM_ENCHANTS])
{
	auto backpack = getBackPack();
	if (backpack == nullptr) return -1;

	// ����ǲ������µ�/��ͬ�ĸ�ħ����
	bool same = true;
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		if (enchants[i] == 0) continue;
		bool found = false;
		for (int j = 0, _n = backpack->getGridEnchantNum(tgtGridIdx); j < _n; ++j)
		{
			if (enchants[i] == backpack->getGridEnchantId(tgtGridIdx, j))
			{
				found = true;
				break;
			}
		}
		if (!found) { same = false; break; }
	}
	if (same) return -1;

	// �Ƿ���������
	if (!canEnchant(tgtGridIdx, frmGridIdx, enchants)) return -1;

	// �������
	int cost = calcEnchantCost(tgtGridIdx, enchants);
	auto playerAttr = getAttrib() == nullptr ? nullptr : dynamic_cast<PlayerAttrib*>(getAttrib());
	if (playerAttr == nullptr) return -1;
	int curExp = playerAttr->getExp();
	if (curExp / EXP_STAR_RATIO < cost) return -1; // �ж����Ƿ����Ƿ��㹻

	// ���¸�ħ
	auto grid = backpack->index2Grid(tgtGridIdx);
	if (grid == nullptr) return -1;
	if (grid->getNum() > 1)
	{
		int rsId = GetDefManagerProxy()->getItemDef(grid->def->ID, true)->EnchantAfterID;
		if (rsId <= 0) return -1;

		int rid = -1;
		rid = backpack->getEmptyShortcutIndex();
		if (rid < 0) rid = backpack->getEmptyBagIndex();
		if (rid < 0) return -1; //��������

		playerAttr->addExp(-cost * EXP_STAR_RATIO); //�۳�����
		backpack->removeItem(frmGridIdx, 1); //ɾ������
		backpack->removeItem(tgtGridIdx, 1); //�۳�1��ԭ��Ʒ
		int tmpEnc[MAX_ITEM_ENCHANTS];
		memset(tmpEnc, 0, sizeof(tmpEnc));
		int eCnt = 0;
		for (int i = 0; i < MAX_ITEM_ENCHANTS; i++)
		{
			if (enchants[i] > 0)
			{
				tmpEnc[eCnt] = enchants[i];
				eCnt++;
			}
		}

		//backpack->replaceItem(rid, rsId, 1, -1, eCnt, tmpEnc); //��������Ʒ����
		GridCopyData data;
		data.resid = rsId;
		data.num = 1;
		data.enchantnum = eCnt;
		data.enchants = tmpEnc;
		backpack->replaceItem_byGridCopyData(data, rid);

		if (m_pWorld && !m_pWorld->isRemoteMode())
			addSFActivity(SFACTIVITY_ENCHANT, 1, 1, !this->hasUIControl());
		return rid; // ���������ڵĿո��idx
	}
	else
	{
		playerAttr->addExp(-cost * EXP_STAR_RATIO); //�۳�����
		backpack->removeItem(frmGridIdx, 1); //ɾ������
		backpack->clearEnchant(tgtGridIdx);

		for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
		{
			if (enchants[i] > 0) backpack->enchant(tgtGridIdx, enchants[i]);
		}
		if (m_pWorld && !m_pWorld->isRemoteMode())
			addSFActivity(SFACTIVITY_ENCHANT, 1, 1, !this->hasUIControl());
		return tgtGridIdx; // ����Ŀ�����idx
	}
	return -1;
}

int ClientPlayer::enchantRandom(int tgtGrid)
{
	if (tgtGrid < 0) return -1;
	auto backpack = getBackPack();
	if (backpack == nullptr) return -1;

	int originCnt = backpack->getGridEnchantNum(tgtGrid);
	if (originCnt > 0) return -1;

	auto itemDef = GetDefManagerProxy()->getItemDef(backpack->getGridItem(tgtGrid));

	if (itemDef == nullptr) return -1;
	auto costDef = GetDefManagerProxy()->getEnchantMentDef(itemDef->StuffType);
	int cost = costDef->Cost;
	auto playerAttr = getAttrib() == nullptr ? nullptr : dynamic_cast<PlayerAttrib*>(getAttrib());
	if (playerAttr == nullptr) return -1;
	int curExp = playerAttr->getExp();
	if (curExp / EXP_STAR_RATIO < cost) return -1; // �ж����Ƿ����Ƿ��㹻

	int total = 0;
	int probsLower[MAX_ITEM_ENCHANTS];
	int probsHigher[MAX_ITEM_ENCHANTS];
	memset(probsLower, 0, sizeof(probsLower));
	memset(probsHigher, 0, sizeof(probsHigher));
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		probsLower[i] = total + 1;
		total += costDef->AttrWeight[i];
		probsHigher[i] = total;
	}

	if (total <= 0) total = 1;
	int luckyNumber = m_pWorld->genRandomInt(1, total);

	int randomCnt = 0;
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		if (luckyNumber <= probsHigher[i] && luckyNumber >= probsLower[i])
		{
			randomCnt = i + 1;
			break;
		}
	}
	if (randomCnt > MAX_ITEM_ENCHANTS) randomCnt = MAX_ITEM_ENCHANTS;

	int toolType = backpack->getGridToolType(tgtGrid);
	if (toolType < 0) return -1;
	GetDefManagerProxy()->setCurAccordEnchants(toolType);

	std::vector<const EnchantDef*> availables;
	for (int i = 0, _num = GetDefManagerProxy()->getCurAccordEnchantsNum(); i < _num; ++i)
	{
		availables.push_back(GetDefManagerProxy()->getCurAccordEnchantDef(i));
	}
	// ѡ��randomCnt�ָ�ħ����
	randomSelect(availables, randomCnt);
	if (availables.size() <= 0) return -1;

	if (backpack->getGridNum(tgtGrid) > 1)
	{
		int rsId = itemDef->EnchantAfterID;
		if (rsId <= 0) return -1; // ��������1���Ҹ�ħ��������Ʒ������bug
		int rid = -1;
		rid = backpack->getEmptyShortcutIndex();
		if (rid < 0) rid = backpack->getEmptyBagIndex();
		if (rid < 0) return -1; //��������

		backpack->removeItem(tgtGrid, 1); //ɾ��ԭ����һ��
		//backpack->replaceItem(rid, rsId, 1, -1); //�����������Ʒ
		GridCopyData data;
		data.resid = rsId;
		data.num = 1;
		backpack->replaceItem_byGridCopyData(data, rid);
		tgtGrid = rid; // ���·�����Ʒ�ĸ��Ӹ�ħ
	}

	// ���ԭ��ħ
	backpack->clearEnchant(tgtGrid);
	playerAttr->addExp(-cost * EXP_STAR_RATIO);

	// ��������ȼ�	
	int lprobLower[5];
	int lprobHigher[5];
	int probsCnt = 0;
	total = 0;
	memset(lprobLower, 0, sizeof(lprobLower));
	memset(lprobHigher, 0, sizeof(lprobHigher));
	for (int i = 0; i < 5; ++i)
	{
		lprobLower[i] = total + 1;
		total += costDef->LevelWeight[i];
		lprobHigher[i] = total;
	}

	for (int i = 0, _n = availables.size(); i < _n; ++i)
	{
		int randNum = m_pWorld->genRandomInt(1, total);
		int lvl = 0;
		for (int j = 0; j < 5; ++j)
		{
			if (randNum >= lprobLower[j] && randNum <= lprobHigher[j])
			{
				lvl = j + 1;
				break;
			}
		}
		// �����ȼ����޵� �ɵ�1��
		int eid = availables[i]->ID * 100 + lvl;
		while (eid % 100 > 0 && !GetDefManagerProxy()->getEnchantDef(eid))
			--eid;
		if (eid % 100 <= 0) continue;
		backpack->enchant(tgtGrid, eid);
	}
	if (m_pWorld && !m_pWorld->isRemoteMode())
		addSFActivity(SFACTIVITY_ENCHANT, 1, 1, !this->hasUIControl());
	return tgtGrid;
}

//-------enchant,rune--------- end

// task ---begin

void ClientPlayer::removeTask(int taskid)
{
	if (m_pWorld->isRemoteMode())
	{
		PB_AnswerTaskCH answerTaskCH;
		answerTaskCH.set_taskid(taskid);
		answerTaskCH.set_plotid(0);
		answerTaskCH.set_type(2);

		GetGameNetManagerPtr()->sendToHost(PB_ANSWERTASK_CH, answerTaskCH);;
	}

	if (m_pTaskMgr)
	{
		m_pTaskMgr->removeTask(taskid);
	}
}

void ClientPlayer::addTask(int taskid, int plotid)
{
	if (m_pWorld->isRemoteMode())
	{
		PB_AnswerTaskCH answerTaskCH;
		answerTaskCH.set_taskid(taskid);
		answerTaskCH.set_plotid(plotid);
		answerTaskCH.set_type(1);

		GetGameNetManagerPtr()->sendToHost(PB_ANSWERTASK_CH, answerTaskCH);;
	}

	if (m_pTaskMgr)
	{
		m_pTaskMgr->addTask(taskid, plotid);
		if (hasUIControl())
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound("npc.task_pickup", 1.0, 1.0, 1);
			}
		}
	}

}

bool ClientPlayer::hasTask(int taskid)
{
	if (m_pTaskMgr)
		return m_pTaskMgr->hasTask(taskid);

	return false;
}

void ClientPlayer::updateTask(PLAYERTASK_TYPE type, int id, int num)
{
	bool needSync = false;

	if (m_pTaskMgr)
		needSync = m_pTaskMgr->updateTask(type, id, num);

	if (m_pWorld && !m_pWorld->isRemoteMode() && !hasUIControl() && needSync && type >= KILL_MOB)
	{
		PB_UpdateTaskHC updateTaskHC;
		updateTaskHC.set_type(type);
		updateTaskHC.set_id(id);
		updateTaskHC.set_num(num);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_UPDATETASK_HC, updateTaskHC);
	}
}

void ClientPlayer::completeTask(int taskid)
{
	if (m_pWorld && !m_pWorld->isRemoteMode() && !hasUIControl())
	{
		PB_CompleteTaskHC completeTaskHC;
		completeTaskHC.set_taskid(taskid);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_COMPLETE_TASK_HC, completeTaskHC);
	}

	if (m_pTaskMgr)
	{
		if (hasUIControl())
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound("npc.task_complete", 1.0, 1.0, 1);
			}
			const NpcTaskDef* def = GetDefManagerProxy()->getNpcTaskDef(taskid);

			if (def && def->ShowInNote)
			{
				char buf[128];
				const char* info = GetDefManagerProxy()->getStringDef(11208);
				sprintf(buf, "%s%s", m_pTaskMgr->convertDialogueStr(def->Name.c_str()), info);
				//GetGameEventQue().postInfoTips(buf);
				CommonUtil::GetInstance().PostInfoTips(buf);
			}
		}


		m_pTaskMgr->completeTask(taskid);
	}
}

int ClientPlayer::getTaskState(int taskid)
{
	if (m_pTaskMgr)
		return m_pTaskMgr->getTaskState(taskid);

	return TASK_UNCOMPLETED;
}

int ClientPlayer::getTaskNum()
{
	if (m_pTaskMgr)
		return m_pTaskMgr->getTaskNum();

	return 0;
}

TaskInfo* ClientPlayer::getTaskInfo(int taskid)
{
	if (m_pTaskMgr)
		return m_pTaskMgr->getTaskInfo(taskid);

	return NULL;
}

TaskInfo* ClientPlayer::getTaskInfoByPlot(int plotid, int& taskid)
{
	taskid = 0;
	if (m_pTaskMgr)
		return m_pTaskMgr->getTaskInfoByPlot(plotid, taskid);

	return NULL;
}

TaskInfo* ClientPlayer::getTaskInfoByIndex(int index, int& taskid)
{
	taskid = 0;
	if (m_pTaskMgr)
		return m_pTaskMgr->getTaskInfoByIndex(index, taskid);

	return NULL;
}

void ClientPlayer::syncTaskByEnterWorld(const RepeatedPtrField<PB_TaskInfoData>* pdatas)
{
	if (m_pTaskMgr)
		m_pTaskMgr->syncTaskByEnterWorld(pdatas);
}

// task ---end

// avatarSummon start

void ClientPlayer::avatarSummon(int summonid)
{
	auto avatarSummonComp = GetComponent<AvatarSummonComponent>();
	if (!avatarSummonComp)
	{
		avatarSummonComp = CreateComponent<AvatarSummonComponent>("AvatarSummonComponent");
	}
	if (avatarSummonComp) avatarSummonComp->avatarSummon(summonid);
}

// avatarSummon end

// fishing ---begin

bool ClientPlayer::isFishing()
{
	auto* pFishingCom = m_pFishingComponent;
	if (pFishingCom)
	{
		return pFishingCom->isFishing();
	}
	return false;
}

bool ClientPlayer::isEndingFishing()
{
	auto* pFishingCom = m_pFishingComponent;
	if (pFishingCom)
	{
		return pFishingCom->getFishingState() == FishingComponent::FishingState::ENDANIM;
	}
	return false;
}

int ClientPlayer::getFishResultItemId()
{
	auto* pFishingCom = m_pFishingComponent;
	if (pFishingCom)
	{
		return pFishingCom->getFishResultItemId();
	}
	return 0;
}

int ClientPlayer::clearFishResult()
{
	auto* pFishingCom = m_pFishingComponent;
	if (pFishingCom)
	{
		return pFishingCom->clearFishResult();
	}
	return 0;
}

long long ClientPlayer::getFishhookObjId()
{
	auto* pFishingCom = m_pFishingComponent;
	if (pFishingCom)
	{
		return pFishingCom->getFishhookObjId();
	}
	return 0;
}
// fishing ---end

//SkillCompontCD start
void ClientPlayer::getSkillCDNew(int itemid, float& cd, float& maxCD)
{
	auto comp = getSkillComponent();
	if (!comp)
	{
		cd = 0;
		maxCD = 1;
		return;
	}
	comp->getTotalSkillCD(itemid, cd, maxCD);
}

void ClientPlayer::saveSkillExtendCDCompToPB(game::common::PB_SkillExpandCDDataGather* gather)
{
	if (getSkillComponent()) getSkillComponent()->saveToPB(gather);
}

void ClientPlayer::loadSkillExtendCDCompFromPB(const game::common::PB_SkillExpandCDDataGather* gather)
{
	if (!getSkillComponent())
	{
		CreateComponent<SkillComponent>("SkillComponent");
	}
	if (getSkillComponent()) getSkillComponent()->loadFromPB(gather);
}

//CustomGunUseComponent
CustomGunUseComponent* ClientPlayer::sureCustomGunComponent()
{
	if (!m_pCustomGunComponent)
	{
		return CreateComponent<CustomGunUseComponent>("CustomGunUseComponent");
	}
	else
	{
		return m_pCustomGunComponent;
	}
}