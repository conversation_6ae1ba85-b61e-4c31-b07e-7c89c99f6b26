#ifndef ACTOR_MANAGER_INTERFACE_H
#define ACTOR_MANAGER_INTERFACE_H

#include "SandboxEngine.h"
#include "Math/Vector3f.h"
#include "Geometry/AABB.h"
#include "SceneManagement/SceneMGTNode.hpp"
#include "SceneManagement/GenericOctreeMGT.hpp"
#include "world_types.h"
#include "chunkrandom.h"
#include "world_struct.h"
#include "ActorTypes.h"
#include "blocks/container.h"
#include "Collision.h"
#include "boundaryGeometry/BoundaryHolder.h"

namespace MNSandbox
{
	class SandboxNode;
}
class IClientActor;
class IClientMob;
class IActorBoss;
class IClientPlayer;
class IClientItem;
class WorldMapData;
class World;
class Ecosystem;
class VoxelModel;
class WorldBed;
class SpaceActorManager;
class ClientActorMgr;


enum SelectMobCallback
{
	NONE_CALLBACK,
	NORMAL_MOB,
};
typedef void (*SpawnMobDelayCallback)(IClientMob* mob);
typedef std::function<void(const WCoord&, int, bool, bool, float, SpawnMobDelayCallback)> SpawnMobCallback;

class EXPORT_SANDBOXENGINE ActorOctreeSemantics
{
public:
	static const int MaxNodeDepth = 12;
	static const int MaxElementsPerLeaf = 8;
	static const int MinInclusiveElementsPerNode = 3;
	static Rainbow::AABB GetBoundingBox(IClientActor* Obj);
	static const Rainbow::SceneMGTNode::Handle& GetElementHandle(IClientActor* Obj);
	static void SetElementHandle(IClientActor* Obj, Rainbow::SceneMGTNode::ConstReferenceHandle handle);
	//octree���ڵ���ƶ�
	static void ApplyOffset(IClientActor* Obj, const Rainbow::Vector3f& inOffset) {}
};
typedef Rainbow::GenericOctreeMGT<IClientActor*, ActorOctreeSemantics> ActorMGT;

class EXPORT_SANDBOXENGINE ActorManagerInterface //tolua_export
{//tolua_export
	
public:
	ActorManagerInterface();
	virtual ~ActorManagerInterface();

	virtual ClientActorMgr* ToCastMgr() = 0;

	virtual void addSceneActorObject(MNSandbox::SandboxNode* pNode) {};
	virtual void removeSceneActorObject(MNSandbox::SandboxNode* pNode) {};

	virtual void setMobGen(bool hostile, bool animal) = 0;

	virtual void OnDrawGizmo() = 0;

	virtual void prepareTick() {};
	virtual void tick() {};
	virtual void tickBlockLine() {};
	virtual void tickActorLine() {};
	virtual void reset() = 0;
	virtual void update(float dtime) {};

	virtual ActorMGT* GetActorMGT() { return nullptr; }

	virtual IClientActor* iFindActorByWID(long long wid) = 0;
	virtual IClientMob* iFindMobByWID(long long wid) = 0;
	virtual void SpawnPlayerAddRef(IClientPlayer* player, bool isTeleport = false) = 0;
	/*
		ͨ��serverid�ҵ���Ӧ��ClientMob����԰����
		serverid:����������Ĺ���id
		Warning:�˺���Ч�ʺܵͣ��������ã���԰Ҳ����ʹ�ã��������ʹ�ã�
	*/
	virtual IClientMob* iFindMobByServerID(const std::string& serverid) = 0;

	virtual bool iIsActorExist(IClientActor* actor) = 0;

	virtual bool IsActorHorse(IClientActor* actor) = 0;
	virtual bool IsActorBasketBall(IClientActor* actor) = 0;

	virtual void onChunkLoad(Chunk* pchunk) = 0;
	virtual void onChunkUnload(Chunk* pchunk) = 0;

	virtual int getNumPlayer() = 0;
	virtual int getActorCount() = 0;
	virtual IClientPlayer* iGetIthPlayer(int i) = 0;

	virtual IClientPlayer* iFindPlayerByUin(int uin) = 0;
	//virtual IClientPlayer* iFindPlayerByUid(const std::string& uid) = 0;

	virtual IClientPlayer* IGetOccupiedPlayer(const WCoord& blockpos, int flag) = 0;
	virtual unsigned getNumBoss() = 0;
	virtual void AddBossToMapData(WorldMapData* destdata) = 0;

	virtual void spawnPlayer(IClientPlayer* player) = 0;

	virtual void resetByToggleGameMakerMode() = 0;

	virtual void resetLiveMobAttr() = 0;

	virtual void toggleMobPresetDebugLines() = 0;

	virtual bool CanRemoveChunk(CHUNK_INDEX& chunkIdx) = 0;

	virtual void emptyActorChange(WORLD_ID id, bool flag) = 0;
	virtual int GetGameObjDelay() = 0;
	virtual void IncGameObjDelay() = 0;
	virtual void performWorldGenSpawning(Ecosystem* biome, int ox, int oz, int rangex, int rangez, ChunkRandGen& randgen) = 0;

	virtual IClientMob* iSpawnMob(const WCoord& pos, int monsterid, bool mobtype_check, bool mob_check, float yaw = -1, int mobtype = 0, std::string monsterName = "", bool trigger = true) = 0;

	virtual bool spawnDesertTradeCaravan(long long uin, WCoord explorePos, long long saveTime) = 0;

	virtual IClientMob* CreateMobFromDef(int monsterid, int mobtype = 0, bool trigger = true, bool init = true) = 0;

	virtual void spawnActor(IClientActor* actor, long long objid = 0) = 0;	//20211103: ��������objid codeby:lulei

	virtual void HandleDesertVillageBuildingNpc(std::vector<DesertVillageNpcInfo>& npcinfo, int type, const WCoord& villagePos, const WCoord& startPos) = 0;

	virtual void HandleFishingVillageNpc(const  WCoord& fishingmanPos, const  WCoord& wharfPos, 
		const  WCoord& newWharfPos, std::map<WCoord, int>& spawnPos) = 0;

	virtual void HandleIslandBuildNpc(int type, const  WCoord& startPos) = 0;

	virtual void HandleIcePlantBuildNpc(int type, const  WCoord& startPos) = 0;

	virtual void HandleIceAltarBuildNpc(int type, const  WCoord& startPos, VoxelModel* BigBuildVM) = 0;

	virtual bool HandleEcosysUnitDesertVillageNpc(std::vector<IClientActor*>& nearMobs, const WCoord& gatherPos, const WCoord& gatherCenterPos) = 0;
	
	virtual void HandleBlockDragonEggBoss(int resid, int curtool) = 0;

	virtual bool HandleBlockButtonActorCollide(int blockid, const WCoord&  blockpos, int &contactActorID, bool includeVehicle, CollisionDetect& cd) = 0;
	virtual void BlockBedForPlayer(const  WCoord& blockpos) = 0;
	virtual void BlockBedBindActor(const  WCoord& blockpos, WORLD_ID bindactor, WorldBed* container) = 0;
	virtual void BlockBedBodyShow(const  WCoord& blockpos, bool occupied) = 0;
	virtual bool getMobGen(MOB_TYPE mobtype)  = 0;

	virtual void addMobSpawnNum(int mobtype, int num) = 0;
	virtual void addMobSpawnNumByID(int mobId, int num) = 0;

	virtual void spawnMobDelay(const WCoord& pos, int delay_ticks, int monsterid, bool mobtype_check, bool mob_check, unsigned char mob_type, SpawnMobDelayCallback pfunc = NULL, float yaw = -1, SpawnMobCallback pfCreate = NULL, unsigned int presetPosKey = 0) = 0;

	virtual void resetActorsByCustomModel(int id) = 0;
	virtual void resetActorsByFullyCustomModel(std::string skey) = 0;

	virtual void reLoadMobAI() = 0;
	virtual void resetActorsByImportModel() = 0;
	virtual bool areAllPlayersAsleep() = 0;
	virtual void wakeAllPlayers() = 0;
	virtual std::vector<int> findPlayersByRange(int range = 5) = 0;
	//Ҭ��������ҹ��
	virtual bool areAllPlayersSkipNight() = 0;
	virtual void wakeSkipNightPlayers() = 0;
	virtual IClientItem* SpawnIClientItem(const WCoord& worldpos, int itemid, int num, int protodata = 0, long long objid = 0) = 0;
	virtual IClientItem* SpawnIClientItem(const WCoord& worldpos, const BackPackGrid& grid, int protodata = 0, long long objid = 0) = 0;

	virtual SpaceActorManager* GetSpaceManager() = 0;

	virtual void selectNearAllIClientMobs(std::vector<IClientMob*>& mobs, const WCoord& pos, int range, SelectMobCallback callbacktype = NONE_CALLBACK, void* data = NULL) = 0;
	virtual IClientPlayer* selectNearIPlayer(const WCoord& pos, int range) = 0;
	virtual void reTrackActor() = 0;
	virtual void clearTrackActor() = 0;
	virtual bool addActorByChunk(IClientActor* actor) = 0;
	virtual void removeActorByChunk(IClientActor* iactor, bool keep_inchunk = false) = 0;
	virtual bool IsSandwormExist() = 0;

	virtual bool SelectNearActors(BoundaryBoxGeometry& box, int maxRange, IClientActor* actor) = 0;
	virtual void SelectNearbyActor(BoundaryBoxGeometry& box, int maxRange, std::vector<IClientActor*>& actors, IClientActor* actorMain) = 0;
	virtual void SelectNeighborMobs(const BoundaryGeometryHolder& holder, const WCoord& centerPos, int exrange, std::vector<IClientActor*>& mobs, IClientActor* actor) = 0;
	virtual void DespawnClientActor(IClientActor* actor, bool isTeleport = false) = 0;
};//tolua_export

//EXPORT_SANDBOXENGINE \
//ActorManagerInterface* GetActorManagerInterface();

#endif