PixelMapInterface = {};
PixelMapInterface.__private = {};
local __private = PixelMapInterface.__private;

function PixelMapInterface:OnEnterWorld()
     if PixelMapInterface:IsShowCompass() then
        PixelMapInterface:HideCompass();  --重新打开雷达
        PixelMapInterface:ShowCompass();
        PixelMapInterface:HideMiniMap();
    end
end

function PixelMapInterface:IsSwitchOpenMap()
    return true
end

function PixelMapInterface:IsEnterSocMap()
    --判断是否是创造模式
    local ugcEditing = UGCModeMgr
    if UGCModeMgr then
        return false
    end

    if LuaInterface:getApiId() ~= 999 then
        return true
    end

    return true
end

function PixelMapInterface:OnLeaveWorld()
    
end

function PixelMapInterface:UseNewSmallMap()
    local curOwid = nil
    if Cur<PERSON>orld then
        curOwid = CurWorld:getOWID();
    end
    return PixelMapMgr:GetInstanceLua():IsEnable() and (not IsNewbieWorld(curOwid));
end

function PixelMapInterface:ShowMiniMap()
    GetInst("MiniUIManager"):HideUI("playermainAutoGen")
    if PixelMapInterface:UseNewSmallMap() then
        if not PixelMapInterface:IsEnterSocMap() then
            GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/c_ingame","miniui/miniworld/common_comp"},"pixelmapAutoGen")
            GetInst("MiniUIManager"):OpenUI("pixelmap","miniui/miniworld/adventure","pixelmapAutoGen",{disableOperateUI = true})
            HideNewSleepNoticeFrame(true);

            local ctrl =  GetInst("MiniUIManager"):GetCtrl("main_minimap")
            if ctrl then
                local infos = ctrl:GetAllTriggerGraphsInfo()
                local ctrl1 = GetInst("MiniUIManager"):GetCtrl("pixelmap")
                if ctrl1 then
                    ctrl1:RefMapGraphs(infos)
                end
            end
            return
        else
            --local pixelmap_ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
            --if not pixelmap_ctrl then
            --    GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/c_ingame","miniui/miniworld/common_comp"},"pixelmapAutoGen")
            --    -- GetInst("MiniUIManager"):OpenUI("main_map","miniui/miniworld/adventure","pixelmapAutoGen",{fullScreen={Type="Normal", bkgName="bkg"}})
            --    GetInst("MiniUIManager"):OpenUI("pixelmap","miniui/miniworld/adventure","pixelmapAutoGen",{disableOperateUI = true})
            --end
            --GetInst("MiniUIManager"):ShowUI("pixelmapAutoGen")
        end

        HideNewSleepNoticeFrame(true);
        -- ClientCurGame:setOperateUI(true); --uibaseCtrl有做这个事

        --local ctrl =  GetInst("MiniUIManager"):GetCtrl("main_minimap")
        --if ctrl then
        --    local infos = ctrl:GetAllTriggerGraphsInfo()
        --    local ctrl1 = GetInst("MiniUIManager"):GetCtrl("pixelmap")
        --    if ctrl1 then
        --        ctrl1:RefMapGraphs(infos)
        --    end
        --end

    else
        if ClientCurGame and ClientCurGame.enableMinimap then
            ClientCurGame:enableMinimap(true);
        end
        getglobal("MapFrame"):Show();
    end
end

function PixelMapInterface:IsShowMiniMap()
    if PixelMapInterface:UseNewSmallMap() then
        return GetInst("MiniUIManager"):IsShown("pixelmapAutoGen");
    else
	    return getglobal("MapFrame"):IsShown();
    end
end
 
function PixelMapInterface:HideMiniMap()
    GetInst("MiniUIManager"):ShowUI("playermainAutoGen")
    if PixelMapInterface:UseNewSmallMap() then
        if getglobal("MapFrame"):IsShown() then
            getglobal("MapFrame"):Hide();
            ClientCurGame:enableMinimap(false);
        end
        if not PixelMapInterface:IsEnterSocMap() then
            GetInst("MiniUIManager"):CloseUI("pixelmapAutoGen");
        else
            --GetInst("MiniUIManager"):HideUI("pixelmapAutoGen");
        end

        -- ClientCurGame:setOperateUI(false); --uibaseCtrl有做这个事
    else
        if ClientCurGame and ClientCurGame.enableMinimap then
            ClientCurGame:enableMinimap(false);
        end
        getglobal("MapFrame"):Hide();
    end
end

function PixelMapInterface:ShowCompass()  --主界面的 指南针
    --do
    --    --[[
    --    local switchAutogen = GetInst('MiniUIManager'):GetUI('mid_compassAutoGen')
    --    if not switchAutogen then
    --        GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/adventure"},"mid_compassAutoGen")
    --        GetInst("MiniUIManager"):OpenUI("mid_compass","miniui/miniworld/adventure","mid_compassAutoGen", {disableOperateUI = true})
    --    else 
    --        GetInst("MiniUIManager"):ShowUI("mid_compassAutoGen")
    --    end
    --    ]]
    --      -- PixelMapInterface:ShowMiniMap() @soc2024  
    --    --return
    --end 
    if PixelMapInterface:UseNewSmallMap() and not PixelMapInterface:IsEnterSocMap() then
        GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/c_ingame","miniui/miniworld/common_comp"},"main_minimapAutoGen")
		GetInst("MiniUIManager"):OpenUI("main_minimap","miniui/miniworld/adventure","main_minimapAutoGen",{})
    end
end

function PixelMapInterface:IsShowCompass()
    if PixelMapInterface:UseNewSmallMap() then
        return GetInst("MiniUIManager"):IsShown("main_minimapAutoGen");
    else
         return getglobal("Compass"):IsShown();
    end
end

function PixelMapInterface:HideCompass()
    --if PixelMapInterface:UseNewSmallMap() then
    --    GetInst("MiniUIManager"):CloseUI("main_minimapAutoGen");
    --    --GetInst("MiniUIManager"):HideUI("main_server_line_switchAutoGen") --新
    --    --GetInst("MiniUIManager"):HideUI("mid_compassAutoGen")
    --else
	--    getglobal("Compass"):Hide();
    --end
end

function PixelMapInterface:InitOfficialMakerBorth(pData)
    --出生点
    -- local x,y,z = CoordDivBlock(WorldMgr:getSpawnPoint(0,0,0));
    local coord = MapInfoRefreshCenter:GetBirthPos(CurWorld._cptr);
    local x,y,z = CoordDivBlock(coord.x,coord.y,coord.z);
    table.insert(pData,{type = "Birth",posX = x,posZ = z});
end

function PixelMapInterface:InitOfficialMakerStation(pData)
     --星站
    local transferDefnum = StarStationTransferMgr:getStarStationDefNum();
    for i=1, transferDefnum do
        local StarStationDef = StarStationTransferMgr:getStarStationDefByIndex(i-1);
        if CurWorld and CurWorld:getCurMapID() == StarStationDef.mapID then
            if StarStationDef.isSign then
                table.insert(pData,{type = "StarStation",posX = StarStationDef.consolePosX,posZ = StarStationDef.consolePosZ});
            end  
        end
    end
end 

function PixelMapInterface:InitOfficialMakerDeath(pData)
    if WorldMgr and WorldMgr:isSurviveMode() then --冒险模式不显示最后一次死亡标记
        local customtype = ModPackMgr:GetCustomReviveFlag() or 0
        if customtype <= 0 then -- 如果开启自定义消耗 并且第2位》0 
            return
        end
    end

     --死亡点
    if CurMainPlayer.m_DieRecordTicks>=0 then
        local x,y,z = CoordDivBlock(CurMainPlayer.m_DiePos.x,0,CurMainPlayer.m_DiePos.z);
        table.insert(pData,{type = "Death",posX = x,posZ = z});
    end
end

function PixelMapInterface:InitOfficialMakerTotem(pData)
    --图腾
    local totemnum = WorldMgr:getTotemPointNum()
    for i = 1,totemnum do
        if CurWorld and CurWorld:getCurMapID() == WorldMgr:getTotemPointMap(i-1) then
            local coord =  WorldMgr:getTotemPoint(i-1)
            table.insert(pData,{type = "Totem",posX =  coord.x,posZ =  coord.z});
        end
    end
end

function PixelMapInterface:InitOfficialMakerTransfer(pData)
    --传送门
    local portalnum = TransferMgr:getTransferDefNum()
    for i = 1,portalnum do
        local potaldef = TransferMgr:getTransferDef(i-1,true)
        if CurWorld and CurWorld:getCurMapID() == potaldef.MapID then
            table.insert(pData,{type = "Portal",posX =  potaldef.x,posZ =  potaldef.z});
        end
    end
end

function PixelMapInterface:InitOfficialMakerOtherPlayer(pData)
     --其他玩家
    if not ClientCurGame or not ClientCurGame.getNumPlayerBriefInfo then return end
    local enemy_hide = WorldMgr:isGameMakerRunMode() and (ClientCurGame:getRuleOptionVal(GMRULE_MINIMAP_TEAMS) == 1);
    local nplayers = ClientCurGame:getNumPlayerBriefInfo()
    for i = 1,nplayers do
        local player = ClientCurGame:getPlayerBriefInfo(i-1)
        if player.uin  ~= AccountManager:getUin() and player.exposePosToOther and CurMainPlayer:getCurWorldMapId() == player.mapid  then
            if enemy_hide and (CurMainPlayer:getTeam() ~=  player.teamid or player.teamid == 0) then 
            else
                x,y,z = CoordDivBlock(player.x,player.y,player.z);
                table.insert(pData,{type = "OtherPlayer", posX = x, posZ = z, pixelMapScale = 0.5});
            end
        end
    end
end

function PixelMapInterface:InitOfficialMakerBoss(pData)
    if not CurWorld then return end
    local actorManage = GetCurWorldActorMgr();
    if actorManage == nil then
        return
    end
    if not actorManage.getNumBoss then
        return
    end
    local bossNum = actorManage:getNumBoss();
    for i=1,bossNum do
        local boss = actorManage:getBoss(i-1);
        local pos = tolua.cast(boss,"ClientActor"):getPosition();
        x, y, z = CoordDivBlock(pos.x, pos.y, pos.z);
        table.insert(pData,{type = "Boss",posX =  x,posZ =  z});
    end
    local pos = WCoord();
    local isHave = false;
    isHave = CurWorld:getBossInfo(pos);
    if isHave and bossNum == 0 then  --boss 指引
        table.insert(pData,{type = "Boss",posX =  pos.x,posZ =  pos.z});
    end
end

function PixelMapInterface:InitOfficialMakerDeathJar(pData)
    if not CurWorld then return end

    if not WorldMgr or not WorldMgr:isSurviveMode() then -- 冒险模式显示死亡掉落罐子标识
        return
    end

    local jarNum = CurWorld:getDeathJarNum();
    for i=1,jarNum do
        local blockpos = CurWorld:getDeathJarPosByIdx(i-1);
        local jarType = (i == jarNum) and "DeathJarNewest" or "DeathJar"
        
        table.insert(pData,{type = jarType, posX =  blockpos.x, posZ =  blockpos.z});
    end
end

function PixelMapInterface:InitOfficialMaker(pData)
    self:InitOfficialMakerBorth(pData);
    self:InitOfficialMakerStation(pData);
    self:InitOfficialMakerDeath(pData);
    self:InitOfficialMakerTotem(pData);
    self:InitOfficialMakerTransfer(pData);
    self:InitOfficialMakerOtherPlayer(pData);
    self:InitOfficialMakerBoss(pData);
    self:InitOfficialMakerDeathJar(pData);
     --补充数据
    for k,v in pairs(pData) do
        self:FillMakerData(v);
    end
end

-- OfficialMakerConfig  官方配置
-- defaultCustomMarkerConfig
function PixelMapInterface:FillMakerData(pMakerData)
    pMakerData.name = pMakerData.name and pMakerData.name or OfficialMakerConfig[pMakerData.type].name;
    pMakerData.iconName = pMakerData.iconName and pMakerData.iconName or OfficialMakerConfig[pMakerData.type].iconName;
    pMakerData.zoomMin = pMakerData.zoomMin and pMakerData.zoomMin or OfficialMakerConfig[pMakerData.type].zoomMin;
    pMakerData.zoomMax = pMakerData.zoomMax and pMakerData.zoomMax or OfficialMakerConfig[pMakerData.type].zoomMax;
    pMakerData.isTrace = pMakerData.isTrace ~= nil and pMakerData.isTrace or OfficialMakerConfig[pMakerData.type].isTrace;
    pMakerData.isRadarTrace = pMakerData.isRadarTrace ~= nil and pMakerData.isRadarTrace or OfficialMakerConfig[pMakerData.type].isRadarTrace;
    pMakerData.appearInBlockFog = pMakerData.appearInBlockFog ~= nil and pMakerData.appearInBlockFog or OfficialMakerConfig[pMakerData.type].appearInBlockFog;
    pMakerData.isCanUI = pMakerData.isCanUI and pMakerData.isCanUI or OfficialMakerConfig[pMakerData.type].isCanUI;
    pMakerData.desc = pMakerData.desc and pMakerData.desc or OfficialMakerConfig[pMakerData.type].desc;
    pMakerData.level = pMakerData.level and pMakerData.level or OfficialMakerConfig[pMakerData.type].level;
    --如果是官方数据还得填充用户自定义的行为
    if pMakerData.type ~= "CustomMark" then
        local owid = CurWorld:getOWID();
        local mapId = CurWorld:getCurMapID();
        local path = "data/".."pixelMapMarker/w"..owid.."/".."m_"..mapId;
        local localData = GetInst("LocalDataManager"):CreatePlayerData("root",path):SetEncryptFlag("xxtea_64");
        local localDataChunk = localData:GetDataChunk();

        if not localDataChunk.officialMarkerDataFill then
            return;
        else
            local fillData = self:FindCustomOfficialData(localDataChunk.officialMarkerDataFill,pMakerData.posX,pMakerData.posZ,pMakerData.type);
            if fillData then
                for k,v in pairs(fillData) do
                    pMakerData[k] = v;
                end
            end
        end
    end
end

function PixelMapInterface:FindCustomOfficialData(pData,pX,pZ,pType)
    if not pData then
        return nil;
    end
    for k,data in pairs(pData) do
        if data.posX == pX and data.posZ == pZ and data.type == pType then
            return data;
        end
    end
end

function PixelMapInterface:newShape(shapeid,type, isshow, r, g, b,a)
    if PixelMapInterface:UseNewSmallMap() then
        local ctrl =  GetInst("MiniUIManager"):GetCtrl("main_minimap")
        if ctrl then
            ctrl:newShape(shapeid,type, isshow, r,g,b,a) --收缩区域
        end
        local ctrl1 =  GetInst("MiniUIManager"):GetCtrl("pixelmap")
        if ctrl1 then
            ctrl1:newShape(shapeid,type, isshow, r,g,b,a) --收缩区域
        end
    end
end

function PixelMapInterface:updateCircle(shapeid, cx, cz, r)
    if PixelMapInterface:UseNewSmallMap() then
        local ctrl =  GetInst("MiniUIManager"):GetCtrl("main_minimap")
        if ctrl then
            ctrl:updateCircle(shapeid, cx, cz, r) --收缩区域
        end
        local ctrl1 =  GetInst("MiniUIManager"):GetCtrl("pixelmap")
        if ctrl1 then
            ctrl1:updateCircle(shapeid, cx, cz, r) --收缩区域
        end
    end
end

function PixelMapInterface:showShape(shapeid, showflag)
    if PixelMapInterface:UseNewSmallMap() then
        local ctrl = GetInst("MiniUIManager"):GetCtrl("main_minimap")
        if ctrl then
            ctrl:showShape(shapeid, showflag) --收缩区域
        end
        
        local ctrl1 = GetInst("MiniUIManager"):GetCtrl("pixelmap")
        if ctrl1 then
            ctrl1:showShape(shapeid, showflag) --收缩区域
        end
    end
end

function PixelMapInterface:deleteShape(shapeid)
    if PixelMapInterface:UseNewSmallMap() then
        local ctrl = GetInst("MiniUIManager"):GetCtrl("main_minimap")
        if ctrl then
            ctrl:deleteShape(shapeid)
        end

        local ctrl1 = GetInst("MiniUIManager"):GetCtrl("pixelmap")
        if ctrl1 then
            ctrl1:deleteShape(shapeid)
        end
    end
end

function PixelMapInterface:updateLine(shapeid,sx, sz, ex, ez)
    if PixelMapInterface:UseNewSmallMap() then
        local ctrl = GetInst("MiniUIManager"):GetCtrl("main_minimap")
        if ctrl then
            ctrl:updateLine(shapeid,sx, sz, ex, ez)
        end

        local ctrl1 = GetInst("MiniUIManager"):GetCtrl("pixelmap")
        if ctrl1 then
            ctrl1:updateLine(shapeid,sx, sz, ex, ez)
        end
    end
end

function PixelMapInterface:updateRectangle(shapeid,sx, sz, w, h)
    if PixelMapInterface:UseNewSmallMap() then
        local ctrl = GetInst("MiniUIManager"):GetCtrl("main_minimap")
        if ctrl then
            ctrl:updateRectangle(shapeid,sx, sz, w, h)
        end
        local ctrl1 = GetInst("MiniUIManager"):GetCtrl("pixelmap")
        if ctrl1 then
            ctrl1:updateRectangle(shapeid,sx, sz, w, h)
        end
    end
end

function PixelMapInterface:setShapeColor(shapeid, r, g,b,a)
    if PixelMapInterface:UseNewSmallMap() then
        local ctrl = GetInst("MiniUIManager"):GetCtrl("main_minimap")
        if ctrl then
            ctrl:setShapeColor(shapeid, r, g,b,a)
        end
    end
end

function PixelMapInterface:GetPixelmapCtrl()
    local ret = GetInst("MiniUIManager"):GetCtrl("pixelmap")
    if not ret then
        GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/c_ingame","miniui/miniworld/common_comp"},"pixelmapAutoGen")
        GetInst("MiniUIManager"):OpenUI("pixelmap","miniui/miniworld/adventure","pixelmapAutoGen",{disableOperateUI = true})
        ret = GetInst("MiniUIManager"):GetCtrl("pixelmap")
    end

    if not GetInst("MiniUIManager"):IsShown("pixelmapAutoGen") then
        GetInst("MiniUIManager"):ShowUI("pixelmapAutoGen")
    end

    return ret
end

function PixelMapInterface:ShowReviveFrame()
	local pixelmap_ctrl = self:GetPixelmapCtrl()
	pixelmap_ctrl:ShowReviveFrame()
end

function PixelMapInterface:ShowMainMap()
	local pixelmap_ctrl = self:GetPixelmapCtrl()
	pixelmap_ctrl:ShowMainMap()
end

function PixelMapInterface:ShowMainMiniMap()
	local pixelmap_ctrl = self:GetPixelmapCtrl()
    pixelmap_ctrl:ShowMainMiniMap()
end